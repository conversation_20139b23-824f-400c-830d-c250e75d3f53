---
alwaysApply: true
type: "always_apply"
---
## 🛡️ Checklist de Segurança e Compliance (Crítico)

1.  **Proteger <PERSON>ves e Dados Sensíveis:** **Nunca** fazer commit de segredos. Usar variáveis de ambiente (`.env`) e o template `.env.example`.
2.  **Não Expor APIs no Frontend:** Toda a lógica sensível e chamadas de API com chaves devem ser feitas no backend (Edge Functions).
3.  **Validação de Dados de Entrada:** Validar **TODOS** os inputs no frontend (Zod) e no backend (Edge Functions).
4.  **Autenticação e Autorização (RLS):** **RLS é obrigatório em todas as tabelas**. Verificar permissões em todas as rotas e queries sensíveis.
5.  **Logging Seguro:** Utilizar o `secureLogger` que evita o log de dados sensíveis.

---

## 🧪 Estratégia de Testes

- **Foco em Testes de Integração:** Priorizar testes que simulam o comportamento real do usuário com `React Testing Library`.
- **Mocking de API com MSW:** Padronizar o uso do **Mock Service Worker (MSW)** para interceptar chamadas de API.
- **Testes Unitários para Lógica Pura:** Cobrir com `Vitest` toda a lógica de negócio isolada (validadores Zod, funções utilitárias, hooks complexos).
- **Verificação da UI:** Os testes devem verificar o resultado final na interface (ex: um toast de sucesso aparece), não apenas se uma função foi chamada.

---

## 🤖 Diretrizes para IA Assistente

1.  **Analisar Antes de Agir:** Sempre analisar o código existente para entender a estrutura e as convenções antes de propor uma solução.
2.  **Priorizar Padrões Existentes:** Antes de criar lógica customizada, verificar se a funcionalidade pode ser implementada com `useCrudOperations`, `FormWrapper`, `PageHeader`, etc.
3.  **Explicar o "Porquê":** Justificar as decisões técnicas, especialmente em relação à arquitetura e segurança.
4.  **Aplicar Checklists:** Ser proativo na aplicação de **TODOS** os pontos dos checklists de segurança e boas práticas.
5.  **Incluir Testes:** Para novas funcionalidades, sempre incluir um teste (unitário ou de integração) seguindo a estratégia definida.

---

_Última atualização: 06/07/2025_