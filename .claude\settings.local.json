{"permissions": {"allow": ["Bash(claude mcp install:*)", "Bash(ls:*)", "Bash(supabase status:*)", "Bash(npm ls:*)", "Bash(npx:*)", "Bash(npm search:*)", "Bash(grep:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(tsc --noEmit)", "Bash(find:*)", "Bash(rm:*)", "Bash(npm cache clean:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(claude mcp:*)", "mcp__supabase__list_projects", "mcp__supabase__list_migrations", "mcp__supabase__execute_sql", "mcp__supabase__apply_migration", "mcp__supabase__list_edge_functions", "mcp__supabase__get_project", "Bash(supabase db:*)", "Bash(supabase functions deploy:*)", "Bash(node:*)", "Bash(git add:*)", "mcp__supabase__list_tables", "mcp__supabase__deploy_edge_function", "mcp__supabase__get_logs", "Bash(npm install:*)", "Bash(npm test:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(sed:*)", "mcp__supabase-obras-ai__list_projects", "mcp__supabase-obras-ai__list_migrations", "mcp__supabase-obras-ai__list_tables", "mcp__supabase-obras-ai__execute_sql", "mcp__supabase-obras-ai__apply_migration", "mcp__supabase-obras-ai__list_edge_functions", "mcp__supabase-obras-ai__get_project", "WebFetch(domain:supabase.com)", "WebFetch(domain:github.com)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}