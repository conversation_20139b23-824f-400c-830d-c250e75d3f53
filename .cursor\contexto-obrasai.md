# 🏗️ Contexto Técnico - ObrasAI 2.2

## 📋 VISÃO GERAL DO SISTEMA

O **ObrasAI** é uma plataforma web completa para gestão de obras na construção civil, desenvolvida com tecnologias modernas e inteligência artificial especializada. O sistema oferece controle total de projetos, custos, fornecedores, contratos, despesas, notas fiscais, vendas e leads, com automação de processos, integrações robustas e insights inteligentes.

## 🛠️ ARQUITETURA IMPLEMENTADA

### Frontend (React + TypeScript)

```bash
src/
├── components/          # Componentes reutilizáveis
│   ├── ui/             # Sistema de design (Shadcn/UI)
│   ├── dashboard/      # Componentes do dashboard (KPIs, métricas)
│   ├── landing/        # Landing page + Chatbot IA
│   ├── ai/             # Componentes de IA (chat, sugestões)
│   ├── orcamento/      # Componentes do orçamento paramétrico
│   ├── sinapi/         # Componentes de consulta SINAPI
│   ├── layouts/        # Layouts e wrappers (DashboardLayout)
│   ├── error/          # ErrorBoundary, Fallbacks
│   └── shared/         # Componentes compartilhados (PageHeader, FormWrapper)
├── pages/              # Páginas principais
│   ├── admin/          # Páginas administrativas
│   └── dashboard/      # Módulos (Obras, Contratos, Despesas, Vendas, etc.)
├── hooks/              # Hooks customizados (useObras, useContratoAI, useCrudOperations)
├── services/           # Serviços e APIs (obrasApi, aiApi, etc.)
├── lib/                # Utilitários (utils, validators, query-keys)
├── contexts/           # Contextos globais (Auth, Theme, Loading)
├── types/              # Tipos centralizados (forms, api, supabase)
├── integrations/       # Configuração de clientes (Supabase, n8n)
```

### Backend (Supabase + Edge Functions)

```bash
supabase/
├── functions/          # 27+ Edge Functions
│   ├── ai-chat/        # IA para chat interno contextual
│   ├── contrato-ai-assistant/ # IA para contratos inteligentes
│   ├── gerar-contrato-pdf/    # Geração de PDF de contratos
│   ├── ai-calculate-budget/   # Orçamento paramétrico
│   ├── sinapi-semantic-search/# Busca semântica SINAPI
│   ├── analise-viabilidade-venda/ # Análise de viabilidade de vendas com IA
│   ├── lead-capture/          # Processamento de leads do chatbot
│   └── ...                    # Outras funções especializadas
└── migrations/         # Migrações do banco de dados (IaC)
```

## 📊 ESTRUTURA DO BANCO DE DADOS (Principais Tabelas)

- **obras**: Gerenciamento das obras (inclui dados de vendas: valor_venda, data_venda, status_venda, comissao_corretor_percentual, outras_despesas_venda).
- **fornecedores_pj / fornecedores_pf**: Cadastros de fornecedores.
- **despesas**: Controle de custos detalhado por obra.
- **notas_fiscais**: Armazenamento e gestão de notas fiscais.
- **contratos**: Contratos inteligentes com histórico e status.
- **ia_contratos_interacoes**: Log e analytics de todas as interações com a IA de contratos.
- **sinapi_manutencoes**: Base de dados oficial SINAPI para manutenções (25k+ registros).
- **embeddings_conhecimento**: Vetores de embeddings para busca semântica.
- **leads**: Captura de leads do chatbot da landing page.

### Row Level Security (RLS)
- **Multi-tenant**: Isolamento completo de dados por `tenant_id`.
- **Policies**: Controle granular de acesso (SELECT, INSERT, UPDATE, DELETE) para cada tabela.

## 🤖 SISTEMA DE INTELIGÊNCIA ARTIFICIAL

### Chatbot de Captura de Leads
- **Componente**: `src/components/landing/LeadChatbot.tsx`
- **Backend**: Edge Function `lead-capture`.
- **Fluxo**: Captura dados, envia para webhook n8n que processa e salva no Supabase e Google Sheets, e notifica por email.

### IA Contextual (Dashboard)
- **Backend**: Edge Function `ai-chat`.
- **Capacidades**: Acessa dados reais das obras, finanças e despesas do usuário para fornecer insights e análises personalizadas.

### Assistente de Contratos com IA
- **Backend**: Edge Function `contrato-ai-assistant`.
- **Capacidades**: Gera e sugere cláusulas contratuais com base em normas técnicas (ABNT), legislação brasileira e boas práticas do setor. Registra todas as interações para fins de auditoria e melhoria contínua.

### Análise de Viabilidade de Vendas com IA
- **Backend**: Edge Function `analise-viabilidade-venda`.
- **Capacidades**: Analisa dados financeiros da obra (custos, orçamento, margem) e gera relatórios detalhados sobre viabilidade de venda, estratégias de precificação, identificação de economias e recomendações estratégicas.
- **Integração**: Conecta-se diretamente aos dados reais da obra para análises precisas e personalizadas.

### Busca Semântica (Embeddings)
- **Backend**: Edge Function `gerar-embeddings-documentacao` e script `enviar_chunks_embeddings.py`.
- **Tecnologia**: `pgvector` no Supabase para armazenar e consultar embeddings gerados pela OpenAI.
- **Aplicação**: Alimenta a IA com conhecimento profundo da documentação do projeto, permitindo respostas mais precisas.

## 🔗 PADRÕES DE DESENVOLVIMENTO E BOAS PRÁTICAS

- **DRY (Don't Repeat Yourself)**: Uso intensivo de hooks e componentes genéricos para eliminar duplicação de código.
  - **`useCrudOperations`**: Hook genérico que centraliza a lógica de CRUD (Create, Read, Update, Delete) para qualquer entidade do sistema (obras, fornecedores, etc.), integrando-se com TanStack Query e o sistema de notificações.
  - **`FormWrapper`**: Componente que encapsula a estrutura de formulários, incluindo `Card`, `Form`, botões de ação e estado de `loading`.
  - **`PageHeader`**: Componente padronizado para cabeçalhos de página.
- **Segurança**: Validação dupla (frontend com Zod, backend em Edge Functions), RLS obrigatório, proteção de chaves de API, CSP.
- **Testes**: Estratégia focada em testes de integração com `Vitest` e `React Testing Library`, utilizando `MSW` para mockar APIs.
- **Limite de Linhas**: Arquivos de código são mantidos abaixo de 500 linhas para garantir a legibilidade e manutenibilidade, forçando a refatoração e componentização.

--- 

**Última Atualização**: 06/07/2025
**Versão**: 2.2
**Status**: Produção - Sistema Completo e Funcional