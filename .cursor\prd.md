# 📋 ObrasAI 2.2 - Product Requirements Document (PRD)

## 🎯 VISÃO GERAL DO PRODUTO

### Produto
**ObrasAI** - Plataforma Inteligente para Gestão de Obras na Construção Civil

### Versão
**2.2** (Julho 2025)

### Missão
Revolucionar a gestão de obras na construção civil brasileira através de tecnologia avançada, inteligência artificial especializada e automação de processos, proporcionando controle total de custos, cronogramas e recursos.

## 📊 STATUS DE IMPLEMENTAÇÃO: ✅ 100% Implementado e Funcional

### M01 - GESTÃO DE OBRAS E CADASTROS (✅ COMPLETO)
- **CRUD de Obras**: Gerenciamento completo de projetos.
- **Gestão de Fornecedores PJ/PF**: Cadastro e controle de fornecedores.
- **Sistema de Despesas**: Categorização por 21 etapas e 150+ insumos.
- **Gestão de Notas Fiscais**: Upload e associação a despesas/obras.
- **Sistema de Vendas**: Controle de vendas com cálculo de lucratividade, ROI e análise de viabilidade.
- **Padrões Aplicados**: `useCrudOperations` para toda a lógica de dados, `FormWrapper` para formulários.

### M02 - INTELIGÊNCIA ARTIFICIAL ESPECIALIZADA (✅ COMPLETO)
- **Chat Contextual**: IA com acesso aos dados reais das obras do usuário via Edge Function `ai-chat`.
- **Análise Financeira**: Comparação em tempo real de orçamento vs. gastos.
- **Segurança**: Rate limiting e logging de interações.

### M03 - SISTEMA DE CAPTURA DE LEADS (✅ COMPLETO)
- **Chatbot na Landing Page**: Interface conversacional para captura de leads.
- **Automação com n8n**: Fluxo que envia dados para Google Sheets, Supabase (tabela `leads`) e notifica por email.
- **Backend**: Edge Function `lead-capture` para validação e segurança.

### M04 - ORÇAMENTO PARAMÉTRICO COM IA (✅ COMPLETO)
- **Cálculo Automático**: Edge Function `ai-calculate-budget` gera orçamentos detalhados.
- **Base SINAPI**: Integração com preços oficiais e atualizados.
- **Wizard Guiado**: Formulário multi-etapas (`WizardComposition`) para coleta de dados.

### M05 - SISTEMA SINAPI INTEGRADO (✅ COMPLETO)
- **Busca Semântica**: Edge Function `sinapi-semantic-search` para consulta inteligente.
- **Base de Manutenções**: Tabela `sinapi_manutencoes` com 25k+ registros para orçamentos de reforma.

### M06 - SISTEMA DE ASSINATURAS (✅ COMPLETO)
- **3 Planos**: Básico, Profissional, Empresarial.
- **Integração Stripe**: Webhooks (`stripe-webhook`), checkout e portal do cliente via Edge Functions.

### M07 - RELATÓRIOS E DASHBOARDS (✅ COMPLETO)
- **Dashboard Principal**: Métricas consolidadas em tempo real.
- **Listagens Avançadas**: `DataTable` com filtros, busca e paginação.
- **Componentes Reutilizáveis**: `PageHeader` e `GradientCard` para consistência visual.

### M08 - SISTEMA DE CONTRATOS INTELIGENTES COM IA (✅ COMPLETO)
- **Assistente IA**: Edge Function `contrato-ai-assistant` com conhecimento em normas ABNT e legislação.
- **Interface Split-Screen**: Formulário (`ContratoComIA.tsx`) + Chat IA em tempo real.
- **Geração de Documentos**: Edge Function `gerar-contrato-pdf` cria PDFs profissionais.
- **Assinatura Digital**: Fluxo seguro com token via email.
- **Analytics**: Tabela `ia_contratos_interacoes` para logging e feedback.

### M09 - SISTEMA DE VENDAS E ANÁLISE DE VIABILIDADE (✅ COMPLETO)
- **Gestão de Vendas**: Interface completa para controle de vendas com status, valores e comissões.
- **Cálculo de Lucratividade**: Análise automática de ROI, lucro líquido e margem de contribuição.
- **Análise de Viabilidade com IA**: Edge Function `analise-viabilidade-venda` gera relatórios detalhados.
- **Dashboard de Vendas**: Métricas em tempo real e indicadores de performance.
- **Integração Completa**: Conecta dados de orçamento, despesas e custos para análises precisas.

### M10 - SISTEMA DE EMBEDDINGS DE DOCUMENTAÇÃO (✅ COMPLETO)
- **Processamento de Documentos**: Script Python para criar chunks de documentação.
- **Geração de Embeddings**: Edge Function `gerar-embeddings-documentacao`.
- **Armazenamento Vetorial**: Tabela `embeddings_conhecimento` com `pgvector`.
- **Aplicação**: Alimenta a busca semântica e a IA contextual.

## 🛠️ STACK TECNOLÓGICA

- **Frontend**: React 18, TypeScript, Vite, Tailwind CSS, shadcn/ui, TanStack Query, React Hook Form, Zod.
- **Backend**: Supabase (PostgreSQL, Auth, Storage), 27+ Edge Functions (Deno/TypeScript).
- **IA**: DeepSeek API, OpenAI API (Embeddings).
- **Automação**: n8n Cloud.
- **Pagamentos**: Stripe.

## 🔒 REQUISITOS DE SEGURANÇA E QUALIDADE

- **RLS Obrigatório**: Todas as tabelas possuem políticas de Row Level Security para isolamento multi-tenant.
- **Validação Dupla**: Zod no frontend e validações nas Edge Functions.
- **Proteção de Chaves**: Nenhum segredo no código-fonte; uso exclusivo de variáveis de ambiente.
- **Testes**: Estratégia de testes de integração com Vitest, RTL e MSW.
- **Código Limpo**: Limite de 400-500 linhas por arquivo, uso de hooks genéricos (`useCrudOperations`) e componentes reutilizáveis (`FormWrapper`) para manter o código DRY.

---

**Última Atualização**: 06/07/2025