---
# METADADOS PARA PROCESSAMENTO POR IA
version: 3.1 # Versão com padrões de código específicos do ObrasAI
linguagem: pt-BR
codificacao: UTF-8
ultima_atualizacao: 2025-07-06T14:00:00-03:00
contexto_ia:
  - tipo_regras: diretrizes_desenvolvimento_avancadas_com_padroes_obrasai
  - dominio: plataforma_gestao_obras_obrasai
  - complexidade: alta
  - tecnologias: [react, typescript, supabase, n8n, vitest, msw, zod, tanstack-query]
# CATEGORIAS PRINCIPAIS
regras:
  compreensao_codigo:
    prioridade: alta
    diretrizes:
      - "analise_codigo_existente_antes_solucao: Antes de propor soluções, analisar o código existente para entender a estrutura e convenções."
      - "identificacao_causa_raiz_antes_correcao: Ao encontrar problemas, identificar a causa raiz antes de sugerir correções."
      - "priorizacao_simplicidade: No ObrasAI, sempre priorizar soluções simples e funcionais, utilizando os padrões existentes."

  estilo_codigo_e_boas_praticas:
    prioridade: critica
    diretrizes:
      - "dry_principle_obrasai: Evitar duplicação de código (DRY) utilizando os hooks e componentes genéricos do projeto: `useCrudOperations`, `useFormMutation`, `FormWrapper`, `PageHeader`, `GradientCard`."
      - "limite_tamanho_arquivos_critico: Arquivos não devem exceder 400-500 linhas. Refatorar arquivos maiores em módulos menores, aplicando os padrões do projeto."
      - "typescript_consistente: Proibir `any`, tipar todas as props, usar `interface` para objetos e `type` para uniões/aliases, e utilizar os tipos centralizados em `src/types`."
      - "componentes_bem_estruturados: Manter componentes pequenos e focados, com responsabilidade única."
      - "gerenciamento_estado_eficiente: Usar TanStack Query (abstraído por `useCrudOperations` e `useTenantQuery`) para estado do servidor. Evitar 'prop drilling'."
      - "separacao_logica_apresentacao: Isolar lógica de negócio em hooks (`/hooks`) e serviços (`/services`), mantendo componentes focados na UI."
      - "estrutura_organizacao_projeto: Manter a estrutura de arquivos e pastas definida e usar o linter de imports (`npm run organize:imports`)."

  seguranca:
    prioridade: critica
    diretrizes:
      - "proteger_chaves_dados_sensiveis: Nunca fazer commit de segredos. Usar variáveis de ambiente e `.env.example`."
      - "nao_expor_apis_frontend: Toda a lógica com chaves deve ocorrer no backend (Edge Functions)."
      - "validacao_entrada_dupla: Validar TODOS os inputs no frontend (Zod) e no backend."
      - "rls_obrigatorio: Implementar Row Level Security (RLS) no Supabase de forma rigorosa para todas as tabelas."
      - "logging_seguro: Utilizar o `secureLogger` para evitar a exposição de dados sensíveis em logs."

  implementacao_e_testes:
    prioridade: alta
    diretrizes:
      - "divisao_tarefas_complexas_etapas: Dividir implementações complexas em etapas incrementais e testáveis."
      - "estrategia_testes_robusta: Para cada nova funcionalidade, implementar testes (unitários ou de integração) seguindo o padrão do projeto: Vitest + Testing Library + MSW."
      - "tratamento_erros_robusto: Utilizar o sistema de tratamento de erros centralizado: `useErrorHandler`, `ErrorBoundary`, e `wrapAsync`."
      - "commits_atomicos_significativos: Fazer commits pequenos, focados em uma única mudança lógica."

  formato_respostas_ia:
    prioridade: media
    diretrizes:
      - "detalhamento_tecnico_funcionamento_codigo: Fornecer explicações técnicas detalhadas sobre como e por que o código proposto funciona, mencionando os padrões ObrasAI aplicados."
      - "exemplos_praticos_obrasai: Fornecer exemplos práticos contextualizados para o domínio de gestão de obras, utilizando os componentes e hooks do projeto."
---