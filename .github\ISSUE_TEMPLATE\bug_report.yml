name: 🐛 Bug Report
description: Reportar um bug ou problema no ObrasAI
title: "[BUG] "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        ## 🐛 Obrigado por reportar um bug!
        
        Por favor, preencha as informações abaixo para nos ajudar a entender e corrigir o problema.

  - type: textarea
    id: description
    attributes:
      label: 📝 Descrição do Bug
      description: Descreva claramente o que aconteceu
      placeholder: Ex: Ao tentar criar uma nova obra, o formulário não salva os dados...
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: 🔄 Passos para Reproduzir
      description: Como podemos reproduzir este bug?
      placeholder: |
        1. Vá para '...'
        2. Clique em '...'
        3. Preencha '...'
        4. Veja o erro
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: ✅ Comportamento Esperado
      description: O que deveria acontecer?
      placeholder: O formulário deveria salvar os dados e redirecionar para a lista de obras
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: ❌ Comportamento Atual
      description: O que realmente acontece?
      placeholder: O formulário mostra um erro e não salva os dados
    validations:
      required: true

  - type: dropdown
    id: severity
    attributes:
      label: 🚨 Severidade
      description: Qual o impacto deste bug?
      options:
        - 🔴 Crítico - Sistema não funciona
        - 🟠 Alto - Funcionalidade principal afetada
        - 🟡 Médio - Funcionalidade secundária afetada
        - 🟢 Baixo - Problema cosmético ou menor
    validations:
      required: true

  - type: dropdown
    id: module
    attributes:
      label: 📦 Módulo Afetado
      description: Qual módulo do sistema está afetado?
      options:
        - 🏗️ Obras
        - 📋 Contratos
        - 💰 Despesas
        - 🏢 Fornecedores
        - 📊 Licitações
        - 🤖 IA/Chat
        - 💹 Vendas
        - ⚙️ Configurações
        - 🔐 Autenticação
        - 📱 Dashboard
        - 🔧 Outro

  - type: dropdown
    id: browser
    attributes:
      label: 🌐 Navegador
      description: Qual navegador você está usando?
      options:
        - Chrome
        - Firefox
        - Safari
        - Edge
        - Opera
        - Outro

  - type: input
    id: browser-version
    attributes:
      label: 📱 Versão do Navegador
      description: Qual versão do navegador?
      placeholder: Ex: Chrome 120.0.6099.109

  - type: dropdown
    id: device
    attributes:
      label: 💻 Dispositivo
      description: Qual tipo de dispositivo?
      options:
        - Desktop
        - Tablet
        - Mobile
        - Outro

  - type: textarea
    id: console-errors
    attributes:
      label: 🔍 Erros do Console
      description: Copie e cole quaisquer erros do console do navegador (F12)
      placeholder: |
        Uncaught TypeError: Cannot read property 'map' of undefined
            at Component.render (app.js:123:45)
      render: shell

  - type: textarea
    id: screenshots
    attributes:
      label: 📸 Screenshots
      description: Adicione screenshots se ajudarem a explicar o problema
      placeholder: Cole ou arraste imagens aqui

  - type: textarea
    id: additional
    attributes:
      label: ℹ️ Informações Adicionais
      description: Qualquer outra informação relevante
      placeholder: |
        - Este bug começou após a última atualização
        - Acontece apenas com usuários específicos
        - Funciona em ambiente de desenvolvimento

  - type: checkboxes
    id: checklist
    attributes:
      label: ✅ Checklist
      description: Confirme que você verificou os itens abaixo
      options:
        - label: Verifiquei se já existe um issue similar
          required: true
        - label: Testei em modo incógnito/privado
          required: false
        - label: Limpei o cache do navegador
          required: false
        - label: Testei em outro navegador
          required: false
