name: ✨ Feature Request
description: Sugerir uma nova funcionalidade para o ObrasAI
title: "[FEATURE] "
labels: ["enhancement", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        ## ✨ Obrigado por sugerir uma nova funcionalidade!
        
        Suas ideias nos ajudam a melhorar o ObrasAI. Por favor, forneça o máximo de detalhes possível.

  - type: textarea
    id: summary
    attributes:
      label: 📝 Resumo da Funcionalidade
      description: Descreva brevemente a funcionalidade que você gostaria de ver
      placeholder: Ex: Gostaria de poder exportar relatórios de obras em formato PDF
    validations:
      required: true

  - type: textarea
    id: problem
    attributes:
      label: 🎯 Problema que Resolve
      description: Que problema esta funcionalidade resolveria?
      placeholder: |
        Atualmente, só posso visualizar relatórios na tela, mas preciso compartilhar 
        com clientes e stakeholders que preferem documentos em PDF.
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: 💡 Solução Proposta
      description: Como você imagina que esta funcionalidade funcionaria?
      placeholder: |
        1. Adicionar um botão "Exportar PDF" nos relatórios
        2. Permitir personalizar o layout do PDF
        3. Incluir logo da empresa no cabeçalho
        4. Gerar automaticamente e fazer download
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: 🚀 Prioridade
      description: Qual a importância desta funcionalidade para você?
      options:
        - 🔴 Crítica - Bloqueando meu trabalho
        - 🟠 Alta - Muito importante para produtividade
        - 🟡 Média - Seria útil ter
        - 🟢 Baixa - Nice to have
    validations:
      required: true

  - type: dropdown
    id: module
    attributes:
      label: 📦 Módulo Relacionado
      description: A qual módulo esta funcionalidade se relaciona?
      options:
        - 🏗️ Obras
        - 📋 Contratos
        - 💰 Despesas
        - 🏢 Fornecedores
        - 📊 Licitações
        - 🤖 IA/Chat
        - 💹 Vendas
        - ⚙️ Configurações
        - 🔐 Autenticação
        - 📱 Dashboard
        - 📊 Relatórios
        - 🔧 Novo Módulo

  - type: dropdown
    id: user-type
    attributes:
      label: 👤 Tipo de Usuário
      description: Que tipo de usuário se beneficiaria desta funcionalidade?
      options:
        - 👨‍💼 Gestor de Obras
        - 👷‍♂️ Engenheiro/Arquiteto
        - 💼 Administrador
        - 📊 Analista Financeiro
        - 🏢 Proprietário da Empresa
        - 👥 Todos os Usuários

  - type: textarea
    id: alternatives
    attributes:
      label: 🔄 Alternativas Consideradas
      description: Você considerou outras formas de resolver este problema?
      placeholder: |
        - Tentei usar screenshots, mas a qualidade não é boa
        - Pensei em copiar dados para Excel, mas é muito manual
        - Considerei usar ferramentas externas, mas seria mais trabalho

  - type: textarea
    id: mockups
    attributes:
      label: 🎨 Mockups/Wireframes
      description: Se você tem ideias visuais, compartilhe aqui (imagens, links, etc.)
      placeholder: Cole ou arraste imagens aqui, ou descreva como imagina a interface

  - type: textarea
    id: acceptance-criteria
    attributes:
      label: ✅ Critérios de Aceitação
      description: Como saberemos que esta funcionalidade está completa?
      placeholder: |
        - [ ] Usuário pode clicar em "Exportar PDF" em qualquer relatório
        - [ ] PDF é gerado com layout profissional
        - [ ] PDF inclui logo da empresa se configurado
        - [ ] Download inicia automaticamente
        - [ ] Funciona em todos os navegadores principais

  - type: textarea
    id: business-value
    attributes:
      label: 💼 Valor de Negócio
      description: Como esta funcionalidade agregaria valor ao negócio?
      placeholder: |
        - Melhora apresentação para clientes
        - Reduz tempo gasto em relatórios manuais
        - Aumenta profissionalismo da empresa
        - Facilita compartilhamento de informações

  - type: dropdown
    id: complexity
    attributes:
      label: 🔧 Complexidade Estimada
      description: Na sua opinião, qual a complexidade desta funcionalidade?
      options:
        - 🟢 Simples - Pequena mudança
        - 🟡 Média - Algumas alterações
        - 🟠 Complexa - Mudanças significativas
        - 🔴 Muito Complexa - Reestruturação major
        - 🤷 Não sei estimar

  - type: textarea
    id: additional
    attributes:
      label: ℹ️ Informações Adicionais
      description: Qualquer outra informação relevante
      placeholder: |
        - Links para ferramentas similares
        - Referências de outras aplicações
        - Considerações técnicas
        - Impacto em outras funcionalidades

  - type: checkboxes
    id: checklist
    attributes:
      label: ✅ Checklist
      description: Confirme que você verificou os itens abaixo
      options:
        - label: Verifiquei se já existe uma solicitação similar
          required: true
        - label: Esta funcionalidade se alinha com os objetivos do ObrasAI
          required: true
        - label: Considerei o impacto em outros usuários
          required: false
        - label: Pensei em possíveis alternativas
          required: false
