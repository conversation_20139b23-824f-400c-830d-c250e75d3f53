# 🔒 CodeQL Configuration - ObrasAI 2.2
# Configuração para análise de segurança automatizada

name: "ObrasAI Security Analysis"

# Desabilitar queries padrão para usar apenas as customizadas
disable-default-queries: false

# Queries adicionais para análise de segurança
queries:
  # Queries de segurança padrão
  - uses: security-and-quality
  
  # Queries específicas para JavaScript/TypeScript
  - uses: security-extended
  
  # Queries customizadas (se houver)
  # - name: custom-queries
  #   uses: ./.github/codeql/queries/

# Configurações específicas para JavaScript/TypeScript
paths:
  # Incluir apenas código fonte relevante
  - src/
  - supabase/functions/
  
# Excluir arquivos que não precisam de análise
paths-ignore:
  - "**/*.test.ts"
  - "**/*.test.tsx"
  - "**/*.spec.ts"
  - "**/*.spec.tsx"
  - "**/node_modules/"
  - "**/dist/"
  - "**/build/"
  - "**/*.d.ts"
  - "**/coverage/"
  - "**/*.config.js"
  - "**/*.config.ts"
  - "**/public/"
  - "**/*.md"
  - "**/*.json"
  - "**/*.yml"
  - "**/*.yaml"

# Configurações de build para análise
build-mode: none

# Configurações específicas para o projeto
packs:
  # Pack de segurança para JavaScript
  - codeql/javascript-queries:Security
  
  # Pack de qualidade para JavaScript  
  - codeql/javascript-queries:Quality
  
  # Pack experimental (opcional)
  # - codeql/javascript-queries:experimental
