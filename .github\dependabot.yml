# 🤖 Dependabot Configuration - ObrasAI 2.2
# Automatiza atualizações de dependências para manter o projeto seguro e atualizado

version: 2

updates:
  # 📦 NPM Dependencies
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "America/Sao_Paulo"
    
    # Configurações de PR
    open-pull-requests-limit: 5
    
    # Agrupamento de atualizações
    groups:
      # Atualizações de desenvolvimento
      development-dependencies:
        patterns:
          - "@types/*"
          - "@typescript-eslint/*"
          - "eslint*"
          - "prettier"
          - "vitest"
          - "@testing-library/*"
          - "msw"
        
      # Atualizações de produção críticas
      production-critical:
        patterns:
          - "react"
          - "react-dom"
          - "@tanstack/react-query"
          - "@supabase/*"
        
      # Atualizações de UI
      ui-dependencies:
        patterns:
          - "@radix-ui/*"
          - "tailwindcss"
          - "lucide-react"
          - "framer-motion"
        
      # Atualizações de build/tooling
      build-tools:
        patterns:
          - "vite"
          - "@vitejs/*"
          - "typescript"
          - "postcss"
          - "autoprefixer"
    
    # Configurações de commit
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    
    # Labels para PRs
    labels:
      - "dependencies"
      - "automated"
    
    # Reviewers automáticos
    reviewers:
      - "obrasai-team"
    
    # Ignorar atualizações específicas
    ignore:
      # Ignorar major versions de React (requer revisão manual)
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-dom"
        update-types: ["version-update:semver-major"]
      
      # Ignorar atualizações de Node.js (controlado manualmente)
      - dependency-name: "@types/node"
        update-types: ["version-update:semver-major"]

  # 🐳 Docker Dependencies
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "10:00"
      timezone: "America/Sao_Paulo"
    
    open-pull-requests-limit: 2
    
    commit-message:
      prefix: "docker"
      include: "scope"
    
    labels:
      - "docker"
      - "dependencies"
      - "automated"

  # 🔧 GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "wednesday"
      time: "11:00"
      timezone: "America/Sao_Paulo"
    
    open-pull-requests-limit: 3
    
    commit-message:
      prefix: "ci"
      include: "scope"
    
    labels:
      - "github-actions"
      - "ci/cd"
      - "automated"
    
    # Agrupar atualizações de actions
    groups:
      github-actions:
        patterns:
          - "*"
