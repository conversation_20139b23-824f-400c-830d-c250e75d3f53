name: CI/CD - ObrasAI 2.2

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '20'
  SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
  SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

jobs:
  # Job 1: Validações de Qualidade
  quality-checks:
    name: 🔍 Quality Checks
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: npm ci

      - name: 🧹 Lint Check
        run: npm run lint

      - name: 🔧 TypeScript Strict Mode Check
        run: npx tsc --noEmit

      - name: 📊 Schema Validation
        run: npm run validate:schema
        continue-on-error: true

      - name: 📋 Organization Check
        run: npm run check:organization
        continue-on-error: true

  # Job 2: Testes Automatizados
  tests:
    name: 🧪 Automated Tests
    runs-on: ubuntu-latest
    needs: quality-checks

    strategy:
      matrix:
        test-type: [unit, integration]

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: npm ci

      - name: 🧪 Run ${{ matrix.test-type }} Tests
        run: npm run test:${{ matrix.test-type }}
        continue-on-error: true

      - name: 📊 Generate Test Coverage
        if: matrix.test-type == 'unit'
        run: npm run test:coverage

      - name: 📤 Upload Coverage Reports
        if: matrix.test-type == 'unit'
        uses: actions/upload-artifact@v4
        with:
          name: coverage-reports
          path: coverage/
          retention-days: 30

  # Job 3: Build e Validação
  build:
    name: 🏗️ Build & Validate
    runs-on: ubuntu-latest
    needs: [quality-checks, tests]

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: npm ci

      - name: 🏗️ Build Project
        run: npm run build

      - name: 📤 Upload Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: dist/
          retention-days: 7

  # Job 4: Security Scan
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    needs: quality-checks

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: npm ci

      - name: 🔍 Audit Dependencies
        run: npm audit --audit-level=high
        continue-on-error: true

      - name: 🛡️ CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: 🔍 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # Job 5: Deploy (apenas para main)
  deploy:
    name: 🚀 Deploy to Production
    runs-on: ubuntu-latest
    needs: [quality-checks, tests, build, security]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    environment:
      name: production
      url: https://obrasai.com.br

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 📥 Download Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: dist/

      - name: 🚀 Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./
          vercel-args: '--prod'

  # Job 6: Notificações e Relatórios
  notifications:
    name: 📢 Notifications & Reports
    runs-on: ubuntu-latest
    needs: [quality-checks, tests, build, security, deploy]
    if: always()

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 📊 Generate Pipeline Report
        run: |
          echo "# 📋 Pipeline Report - ObrasAI 2.2" > pipeline-report.md
          echo "" >> pipeline-report.md
          echo "**Commit:** ${{ github.sha }}" >> pipeline-report.md
          echo "**Branch:** ${{ github.ref_name }}" >> pipeline-report.md
          echo "**Trigger:** ${{ github.event_name }}" >> pipeline-report.md
          echo "**Timestamp:** $(date -u)" >> pipeline-report.md
          echo "" >> pipeline-report.md
          echo "## 🎯 Job Results" >> pipeline-report.md
          echo "- Quality Checks: ${{ needs.quality-checks.result }}" >> pipeline-report.md
          echo "- Tests: ${{ needs.tests.result }}" >> pipeline-report.md
          echo "- Build: ${{ needs.build.result }}" >> pipeline-report.md
          echo "- Security: ${{ needs.security.result }}" >> pipeline-report.md
          echo "- Deploy: ${{ needs.deploy.result }}" >> pipeline-report.md

      - name: 📤 Upload Pipeline Report
        uses: actions/upload-artifact@v4
        with:
          name: pipeline-report
          path: pipeline-report.md
          retention-days: 30

      - name: 💬 Comment on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');

            let comment = `## 🚀 Pipeline Results\n\n`;
            comment += `| Job | Status |\n`;
            comment += `|-----|--------|\n`;
            comment += `| Quality Checks | ${{ needs.quality-checks.result == 'success' ? '✅' : '❌' }} ${{ needs.quality-checks.result }} |\n`;
            comment += `| Tests | ${{ needs.tests.result == 'success' ? '✅' : '❌' }} ${{ needs.tests.result }} |\n`;
            comment += `| Build | ${{ needs.build.result == 'success' ? '✅' : '❌' }} ${{ needs.build.result }} |\n`;
            comment += `| Security | ${{ needs.security.result == 'success' ? '✅' : '❌' }} ${{ needs.security.result }} |\n`;

            if ('${{ needs.deploy.result }}' !== 'skipped') {
              comment += `| Deploy | ${{ needs.deploy.result == 'success' ? '✅' : '❌' }} ${{ needs.deploy.result }} |\n`;
            }

            comment += `\n**Commit:** \`${{ github.sha }}\`\n`;
            comment += `**Branch:** \`${{ github.ref_name }}\`\n`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });