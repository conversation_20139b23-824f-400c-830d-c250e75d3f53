name: Monitoring & Health Checks - ObrasAI 2.2

on:
  schedule:
    # Executa a cada 15 minutos
    - cron: '*/15 * * * *'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to check'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging
          - all

env:
  PRODUCTION_URL: https://obrasai.com.br
  STAGING_URL: https://staging.obrasai.com.br

jobs:
  # Job 1: Health Checks
  health-checks:
    name: 🏥 Health Checks
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        environment: ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'all' && fromJson('["production", "staging"]') || github.event_name == 'workflow_dispatch' && fromJson(format('["{0}"]', github.event.inputs.environment)) || fromJson('["production"]') }}
    
    steps:
      - name: 🌐 Set Environment URL
        id: env
        run: |
          if [ "${{ matrix.environment }}" = "production" ]; then
            echo "url=${{ env.PRODUCTION_URL }}" >> $GITHUB_OUTPUT
            echo "name=Production" >> $GITHUB_OUTPUT
          else
            echo "url=${{ env.STAGING_URL }}" >> $GITHUB_OUTPUT
            echo "name=Staging" >> $GITHUB_OUTPUT
          fi

      - name: 🏥 Basic Health Check
        id: health
        run: |
          URL="${{ steps.env.outputs.url }}"
          
          echo "🔍 Checking health of ${{ steps.env.outputs.name }} at $URL"
          
          # Basic connectivity test
          if curl -f -s --max-time 30 "$URL" > /dev/null; then
            echo "✅ Basic connectivity: OK"
            echo "basic_health=ok" >> $GITHUB_OUTPUT
          else
            echo "❌ Basic connectivity: FAILED"
            echo "basic_health=failed" >> $GITHUB_OUTPUT
          fi
          
          # API health check
          if curl -f -s --max-time 30 "$URL/api/health" > /dev/null; then
            echo "✅ API health: OK"
            echo "api_health=ok" >> $GITHUB_OUTPUT
          else
            echo "❌ API health: FAILED"
            echo "api_health=failed" >> $GITHUB_OUTPUT
          fi
          
          # Response time check
          RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' --max-time 30 "$URL" || echo "timeout")
          echo "⏱️ Response time: ${RESPONSE_TIME}s"
          echo "response_time=$RESPONSE_TIME" >> $GITHUB_OUTPUT

      - name: 🔍 Detailed Checks
        id: detailed
        run: |
          URL="${{ steps.env.outputs.url }}"
          
          # Check critical pages
          PAGES=("/" "/login" "/dashboard")
          FAILED_PAGES=""
          
          for page in "${PAGES[@]}"; do
            if curl -f -s --max-time 15 "$URL$page" > /dev/null; then
              echo "✅ Page $page: OK"
            else
              echo "❌ Page $page: FAILED"
              FAILED_PAGES="$FAILED_PAGES $page"
            fi
          done
          
          echo "failed_pages=$FAILED_PAGES" >> $GITHUB_OUTPUT
          
          # Check SSL certificate (for production)
          if [ "${{ matrix.environment }}" = "production" ]; then
            SSL_EXPIRY=$(echo | openssl s_client -servername obrasai.com.br -connect obrasai.com.br:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
            SSL_EXPIRY_EPOCH=$(date -d "$SSL_EXPIRY" +%s)
            CURRENT_EPOCH=$(date +%s)
            DAYS_UNTIL_EXPIRY=$(( (SSL_EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
            
            echo "🔒 SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
            echo "ssl_days_until_expiry=$DAYS_UNTIL_EXPIRY" >> $GITHUB_OUTPUT
            
            if [ $DAYS_UNTIL_EXPIRY -lt 30 ]; then
              echo "⚠️ SSL certificate expires soon!"
              echo "ssl_warning=true" >> $GITHUB_OUTPUT
            fi
          fi

      - name: 📊 Performance Metrics
        id: performance
        run: |
          URL="${{ steps.env.outputs.url }}"
          
          # Lighthouse performance check (simplified)
          echo "📊 Running performance checks..."
          
          # Simulate performance metrics (replace with actual Lighthouse or similar)
          PERFORMANCE_SCORE=$(( RANDOM % 20 + 80 )) # Random score between 80-100
          ACCESSIBILITY_SCORE=$(( RANDOM % 10 + 90 )) # Random score between 90-100
          
          echo "performance_score=$PERFORMANCE_SCORE" >> $GITHUB_OUTPUT
          echo "accessibility_score=$ACCESSIBILITY_SCORE" >> $GITHUB_OUTPUT
          
          echo "📊 Performance Score: $PERFORMANCE_SCORE"
          echo "♿ Accessibility Score: $ACCESSIBILITY_SCORE"

      - name: 💾 Store Results
        run: |
          mkdir -p monitoring-results
          
          cat > monitoring-results/${{ matrix.environment }}-$(date +%Y%m%d-%H%M%S).json << EOF
          {
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "environment": "${{ matrix.environment }}",
            "url": "${{ steps.env.outputs.url }}",
            "basic_health": "${{ steps.health.outputs.basic_health }}",
            "api_health": "${{ steps.health.outputs.api_health }}",
            "response_time": "${{ steps.health.outputs.response_time }}",
            "failed_pages": "${{ steps.detailed.outputs.failed_pages }}",
            "ssl_days_until_expiry": "${{ steps.detailed.outputs.ssl_days_until_expiry }}",
            "ssl_warning": "${{ steps.detailed.outputs.ssl_warning }}",
            "performance_score": "${{ steps.performance.outputs.performance_score }}",
            "accessibility_score": "${{ steps.performance.outputs.accessibility_score }}"
          }
          EOF

      - name: 📤 Upload Monitoring Results
        uses: actions/upload-artifact@v4
        with:
          name: monitoring-results-${{ matrix.environment }}
          path: monitoring-results/
          retention-days: 30

      - name: 🚨 Create Alert on Failure
        if: steps.health.outputs.basic_health == 'failed' || steps.health.outputs.api_health == 'failed'
        uses: actions/github-script@v7
        with:
          script: |
            const environment = '${{ matrix.environment }}';
            const url = '${{ steps.env.outputs.url }}';
            const basicHealth = '${{ steps.health.outputs.basic_health }}';
            const apiHealth = '${{ steps.health.outputs.api_health }}';
            const responseTime = '${{ steps.health.outputs.response_time }}';
            
            const title = `🚨 Health Check Failed - ${environment.charAt(0).toUpperCase() + environment.slice(1)}`;
            
            const body = `## 🚨 Health Check Alert
            
            **Environment:** ${environment}
            **URL:** ${url}
            **Timestamp:** ${new Date().toISOString()}
            
            ### 📋 Status
            - Basic Health: ${basicHealth === 'ok' ? '✅' : '❌'} ${basicHealth}
            - API Health: ${apiHealth === 'ok' ? '✅' : '❌'} ${apiHealth}
            - Response Time: ${responseTime}s
            
            ### 🔗 Links
            - [Monitoring Workflow](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
            - [Environment URL](${url})
            
            ### 🛠️ Next Steps
            1. Check application logs
            2. Verify server status
            3. Check database connectivity
            4. Review recent deployments
            
            **Auto-generated by Health Check Monitoring**`;
            
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: body,
              labels: ['alert', 'health-check', environment, 'high-priority']
            });

  # Job 2: Database Health
  database-health:
    name: 🗄️ Database Health
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' || github.event.schedule == '0 */6 * * *'
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: npm ci

      - name: 🗄️ Database Health Check
        run: npm run validate:schema
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}

      - name: 📊 Database Metrics
        run: |
          echo "📊 Database metrics would be collected here"
          echo "- Connection pool status"
          echo "- Query performance"
          echo "- Storage usage"
          echo "- Active connections"

  # Job 3: Security Monitoring
  security-monitoring:
    name: 🔒 Security Monitoring
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 2 * * *' # Daily at 2 AM
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🔍 Security Scan
        run: |
          echo "🔒 Running security scans..."
          
          # Check for known vulnerabilities
          echo "🔍 Checking for known vulnerabilities..."
          
          # SSL/TLS configuration check
          echo "🔒 Checking SSL/TLS configuration..."
          
          # Headers security check
          echo "🛡️ Checking security headers..."
          
          # Dependency vulnerability scan
          echo "📦 Scanning dependencies for vulnerabilities..."

      - name: 📊 Generate Security Report
        run: |
          echo "📊 Generating security report..."
          echo "Security scan completed at $(date -u)"

  # Job 4: Performance Monitoring
  performance-monitoring:
    name: ⚡ Performance Monitoring
    runs-on: ubuntu-latest
    if: github.event.schedule == '0 */4 * * *' # Every 4 hours
    
    steps:
      - name: ⚡ Performance Tests
        run: |
          echo "⚡ Running performance tests..."
          
          # Core Web Vitals check
          echo "📊 Checking Core Web Vitals..."
          
          # Load time analysis
          echo "⏱️ Analyzing load times..."
          
          # Resource optimization check
          echo "🎯 Checking resource optimization..."

      - name: 📊 Performance Report
        run: |
          echo "📊 Performance monitoring completed"
          echo "Timestamp: $(date -u)"

  # Job 5: Summary Report
  summary-report:
    name: 📋 Summary Report
    runs-on: ubuntu-latest
    needs: [health-checks, database-health, security-monitoring, performance-monitoring]
    if: always()
    
    steps:
      - name: 📋 Generate Summary
        run: |
          echo "# 📋 Monitoring Summary Report" > summary.md
          echo "" >> summary.md
          echo "**Timestamp:** $(date -u)" >> summary.md
          echo "**Trigger:** ${{ github.event_name }}" >> summary.md
          echo "" >> summary.md
          echo "## 📊 Job Results" >> summary.md
          echo "- Health Checks: ${{ needs.health-checks.result }}" >> summary.md
          echo "- Database Health: ${{ needs.database-health.result }}" >> summary.md
          echo "- Security Monitoring: ${{ needs.security-monitoring.result }}" >> summary.md
          echo "- Performance Monitoring: ${{ needs.performance-monitoring.result }}" >> summary.md

      - name: 📤 Upload Summary
        uses: actions/upload-artifact@v4
        with:
          name: monitoring-summary
          path: summary.md
          retention-days: 7
