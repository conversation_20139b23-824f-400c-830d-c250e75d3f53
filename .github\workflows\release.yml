name: Release - ObrasAI 2.2

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.2.3)'
        required: true
        type: string
      prerelease:
        description: 'Mark as pre-release'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '20'

jobs:
  # Job 1: Validação de Release
  validate-release:
    name: 🔍 Validate Release
    runs-on: ubuntu-latest
    
    outputs:
      version: ${{ steps.version.outputs.version }}
      is_prerelease: ${{ steps.version.outputs.is_prerelease }}
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🏷️ Extract Version Info
        id: version
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            VERSION="${{ github.event.inputs.version }}"
            IS_PRERELEASE="${{ github.event.inputs.prerelease }}"
          else
            VERSION="${{ github.ref_name }}"
            IS_PRERELEASE="false"
          fi
          
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "is_prerelease=$IS_PRERELEASE" >> $GITHUB_OUTPUT
          
          echo "🏷️ Version: $VERSION"
          echo "🔖 Pre-release: $IS_PRERELEASE"

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: npm ci

      - name: 🔍 Validate Package Version
        run: |
          PACKAGE_VERSION=$(node -p "require('./package.json').version")
          RELEASE_VERSION="${{ steps.version.outputs.version }}"
          
          # Remove 'v' prefix if present
          RELEASE_VERSION=${RELEASE_VERSION#v}
          
          echo "📦 Package.json version: $PACKAGE_VERSION"
          echo "🏷️ Release version: $RELEASE_VERSION"
          
          if [ "$PACKAGE_VERSION" != "$RELEASE_VERSION" ]; then
            echo "❌ Version mismatch! Package.json: $PACKAGE_VERSION, Release: $RELEASE_VERSION"
            exit 1
          fi
          
          echo "✅ Version validation passed"

      - name: 🧪 Run Full Test Suite
        run: |
          npm run lint
          npx tsc --noEmit
          npm run test:unit
          npm run test:integration

  # Job 2: Build Release
  build-release:
    name: 🏗️ Build Release
    runs-on: ubuntu-latest
    needs: validate-release
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: npm ci

      - name: 🏗️ Build Production
        run: npm run build
        env:
          VITE_ENVIRONMENT: production
          VITE_VERSION: ${{ needs.validate-release.outputs.version }}

      - name: 📦 Create Release Archive
        run: |
          tar -czf obrasai-${{ needs.validate-release.outputs.version }}.tar.gz dist/
          zip -r obrasai-${{ needs.validate-release.outputs.version }}.zip dist/

      - name: 📤 Upload Release Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: release-artifacts
          path: |
            obrasai-*.tar.gz
            obrasai-*.zip
            dist/
          retention-days: 90

  # Job 3: Generate Release Notes
  generate-release-notes:
    name: 📝 Generate Release Notes
    runs-on: ubuntu-latest
    needs: validate-release
    
    outputs:
      release_notes: ${{ steps.notes.outputs.release_notes }}
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📝 Generate Release Notes
        id: notes
        run: |
          VERSION="${{ needs.validate-release.outputs.version }}"
          
          # Get previous tag
          PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
          
          echo "# 🚀 ObrasAI $VERSION" > release_notes.md
          echo "" >> release_notes.md
          echo "**Release Date:** $(date -u '+%Y-%m-%d %H:%M:%S UTC')" >> release_notes.md
          echo "" >> release_notes.md
          
          if [ -n "$PREVIOUS_TAG" ]; then
            echo "## 📋 Changes since $PREVIOUS_TAG" >> release_notes.md
            echo "" >> release_notes.md
            
            # Get commits since last tag
            git log --pretty=format:"- %s (%h)" $PREVIOUS_TAG..HEAD >> release_notes.md
          else
            echo "## 📋 Initial Release" >> release_notes.md
            echo "" >> release_notes.md
            echo "- Initial release of ObrasAI 2.2" >> release_notes.md
          fi
          
          echo "" >> release_notes.md
          echo "## 🎯 Key Features" >> release_notes.md
          echo "- ✅ Sistema de gestão de obras completo" >> release_notes.md
          echo "- ✅ IA integrada para orçamentos e análises" >> release_notes.md
          echo "- ✅ Integração SINAPI com busca semântica" >> release_notes.md
          echo "- ✅ Módulos de contratos, fornecedores e licitações" >> release_notes.md
          echo "- ✅ Dashboard analytics e relatórios" >> release_notes.md
          echo "" >> release_notes.md
          echo "## 🛠️ Technical Improvements" >> release_notes.md
          echo "- ✅ TypeScript Strict Mode ativado" >> release_notes.md
          echo "- ✅ Testes automatizados implementados" >> release_notes.md
          echo "- ✅ Pipeline CI/CD robusto" >> release_notes.md
          echo "- ✅ Validação de schema automatizada" >> release_notes.md
          echo "" >> release_notes.md
          echo "## 📦 Installation" >> release_notes.md
          echo "\`\`\`bash" >> release_notes.md
          echo "# Download and extract" >> release_notes.md
          echo "wget https://github.com/obrasai/obrasai2.2/releases/download/$VERSION/obrasai-$VERSION.tar.gz" >> release_notes.md
          echo "tar -xzf obrasai-$VERSION.tar.gz" >> release_notes.md
          echo "\`\`\`" >> release_notes.md
          
          # Set output for next job
          {
            echo 'release_notes<<EOF'
            cat release_notes.md
            echo EOF
          } >> $GITHUB_OUTPUT

      - name: 📤 Upload Release Notes
        uses: actions/upload-artifact@v4
        with:
          name: release-notes
          path: release_notes.md
          retention-days: 90

  # Job 4: Create GitHub Release
  create-release:
    name: 🎉 Create GitHub Release
    runs-on: ubuntu-latest
    needs: [validate-release, build-release, generate-release-notes]
    
    steps:
      - name: 📥 Download Release Artifacts
        uses: actions/download-artifact@v4
        with:
          name: release-artifacts

      - name: 📥 Download Release Notes
        uses: actions/download-artifact@v4
        with:
          name: release-notes

      - name: 🎉 Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ needs.validate-release.outputs.version }}
          name: ObrasAI ${{ needs.validate-release.outputs.version }}
          body_path: release_notes.md
          prerelease: ${{ needs.validate-release.outputs.is_prerelease }}
          files: |
            obrasai-*.tar.gz
            obrasai-*.zip
          token: ${{ secrets.GITHUB_TOKEN }}

  # Job 5: Deploy Release
  deploy-release:
    name: 🚀 Deploy Release
    runs-on: ubuntu-latest
    needs: [validate-release, build-release, create-release]
    if: needs.validate-release.outputs.is_prerelease == 'false'
    
    environment:
      name: production
      url: https://obrasai.com.br
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 📥 Download Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: release-artifacts
          path: ./

      - name: 🚀 Deploy to Production
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./
          vercel-args: '--prod'

  # Job 6: Post-Release Actions
  post-release:
    name: 📢 Post-Release Actions
    runs-on: ubuntu-latest
    needs: [validate-release, create-release, deploy-release]
    if: always()
    
    steps:
      - name: 📊 Update Release Metrics
        run: |
          echo "📊 Release metrics would be updated here"
          echo "Version: ${{ needs.validate-release.outputs.version }}"
          echo "Pre-release: ${{ needs.validate-release.outputs.is_prerelease }}"
          echo "Deploy status: ${{ needs.deploy-release.result }}"

      - name: 📢 Notify Team
        run: |
          echo "📢 Team notification would be sent here"
          echo "Release ${{ needs.validate-release.outputs.version }} completed!"
          # Integração com Slack, Discord, email, etc.
