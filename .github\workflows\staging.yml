name: Staging Deploy - ObrasAI 2.2

on:
  push:
    branches: [develop]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deploy even if tests fail'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '20'
  SUPABASE_URL: ${{ secrets.SUPABASE_STAGING_URL }}
  SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_STAGING_ANON_KEY }}

jobs:
  # Job 1: Validações Rápidas para Staging
  quick-checks:
    name: 🚀 Quick Quality Checks
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: npm ci

      - name: 🧹 Quick Lint Check
        run: npm run lint
        continue-on-error: ${{ github.event.inputs.force_deploy == 'true' }}

      - name: 🔧 TypeScript Check
        run: npx tsc --noEmit
        continue-on-error: ${{ github.event.inputs.force_deploy == 'true' }}

      - name: 🧪 Quick Tests
        run: npm run test:unit
        continue-on-error: ${{ github.event.inputs.force_deploy == 'true' }}

  # Job 2: Build para Staging
  build-staging:
    name: 🏗️ Build for Staging
    runs-on: ubuntu-latest
    needs: quick-checks
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install Dependencies
        run: npm ci

      - name: 🏗️ Build for Staging
        run: npm run build
        env:
          VITE_ENVIRONMENT: staging
          VITE_SUPABASE_URL: ${{ secrets.SUPABASE_STAGING_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_STAGING_ANON_KEY }}

      - name: 📤 Upload Staging Build
        uses: actions/upload-artifact@v4
        with:
          name: staging-build
          path: dist/
          retention-days: 3

  # Job 3: Deploy para Staging
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [quick-checks, build-staging]
    if: always() && (needs.quick-checks.result == 'success' || github.event.inputs.force_deploy == 'true')
    
    environment:
      name: staging
      url: https://staging.obrasai.com.br
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 📥 Download Staging Build
        uses: actions/download-artifact@v4
        with:
          name: staging-build
          path: dist/

      - name: 🚀 Deploy to Vercel Staging
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./
          vercel-args: '--target staging'

      - name: 🧪 Run Smoke Tests
        run: |
          echo "Running smoke tests against staging environment..."
          # Aqui você pode adicionar testes de smoke específicos
          curl -f https://staging.obrasai.com.br/health || exit 1
        continue-on-error: true

  # Job 4: Notificação de Deploy
  notify-staging:
    name: 📢 Notify Staging Deploy
    runs-on: ubuntu-latest
    needs: [quick-checks, build-staging, deploy-staging]
    if: always()
    
    steps:
      - name: 📊 Generate Staging Report
        run: |
          echo "# 🚀 Staging Deploy Report" > staging-report.md
          echo "" >> staging-report.md
          echo "**Environment:** Staging" >> staging-report.md
          echo "**Commit:** ${{ github.sha }}" >> staging-report.md
          echo "**Branch:** ${{ github.ref_name }}" >> staging-report.md
          echo "**Timestamp:** $(date -u)" >> staging-report.md
          echo "**Force Deploy:** ${{ github.event.inputs.force_deploy }}" >> staging-report.md
          echo "" >> staging-report.md
          echo "## 📋 Results" >> staging-report.md
          echo "- Quick Checks: ${{ needs.quick-checks.result }}" >> staging-report.md
          echo "- Build: ${{ needs.build-staging.result }}" >> staging-report.md
          echo "- Deploy: ${{ needs.deploy-staging.result }}" >> staging-report.md
          echo "" >> staging-report.md
          echo "🔗 **Staging URL:** https://staging.obrasai.com.br" >> staging-report.md

      - name: 📤 Upload Staging Report
        uses: actions/upload-artifact@v4
        with:
          name: staging-report
          path: staging-report.md
          retention-days: 7

      - name: 💬 Create Issue on Failure
        if: needs.deploy-staging.result == 'failure'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: `🚨 Staging Deploy Failed - ${new Date().toISOString().split('T')[0]}`,
              body: `## 🚨 Staging Deploy Failure
              
              **Commit:** \`${{ github.sha }}\`
              **Branch:** \`${{ github.ref_name }}\`
              **Workflow:** ${{ github.run_id }}
              
              ### 📋 Job Results
              - Quick Checks: ${{ needs.quick-checks.result }}
              - Build: ${{ needs.build-staging.result }}
              - Deploy: ${{ needs.deploy-staging.result }}
              
              ### 🔗 Links
              - [Workflow Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
              - [Commit](https://github.com/${{ github.repository }}/commit/${{ github.sha }})
              
              ### 🛠️ Next Steps
              1. Check the workflow logs for detailed error messages
              2. Verify staging environment configuration
              3. Test locally before retrying deploy
              
              **Auto-generated by GitHub Actions**`,
              labels: ['bug', 'staging', 'deploy-failure']
            });

  # Job 5: Cleanup (opcional)
  cleanup:
    name: 🧹 Cleanup
    runs-on: ubuntu-latest
    needs: [deploy-staging, notify-staging]
    if: always()
    
    steps:
      - name: 🗑️ Cleanup Old Artifacts
        run: |
          echo "Cleanup would happen here if needed"
          # Exemplo: remover builds antigos, limpar cache, etc.

      - name: 📊 Update Deployment Metrics
        run: |
          echo "Deployment metrics would be updated here"
          # Exemplo: enviar métricas para monitoring, analytics, etc.
