# Design Document - Obras de Condomínio

## Overview

A funcionalidade de Obras de Condomínio introduz uma arquitetura hierárquica no sistema ObrasAI, permitindo que projetos de condomínio (obra-mãe) contenham múltiplas unidades (obras-filhas). Esta implementação mantém compatibilidade com o sistema existente enquanto adiciona capacidades avançadas de gerenciamento de projetos complexos.

A solução utiliza uma abordagem de auto-referência na tabela `obras` existente, evitando a necessidade de novas tabelas e mantendo a simplicidade da arquitetura atual.

## Architecture

### Database Schema Changes

#### Tabela `obras` - Extensões
- **parent_obra_id**: `UUID` (nullable, foreign key para `obras.id`)
- **tipo_projeto**: `ENUM('UNICO', 'CONDOMINIO_MASTER', 'UNIDADE_CONDOMINIO')` (default: 'UNICO')
- **identificador_unidade**: `TEXT` (nullable, para identificar unidades específicas)

#### Novo Enum Type
```sql
CREATE TYPE project_type AS ENUM ('UNICO', 'CONDOMINIO_MASTER', 'UNIDADE_CONDOMINIO');
```

#### Índices para Performance
- Índice em `parent_obra_id` para otimizar consultas de relacionamento
- Índice composto em `(tenant_id, tipo_projeto)` para filtragem eficiente

#### Constraints de Integridade
- Foreign key constraint em `parent_obra_id` referenciando `obras.id`
- Check constraint garantindo que apenas `CONDOMINIO_MASTER` pode ter `parent_obra_id` NULL
- Check constraint garantindo que `UNIDADE_CONDOMINIO` deve ter `parent_obra_id` NOT NULL

### Backend Architecture

#### RPC Function: `create_condominio_project`
Função transacional em PL/pgSQL que:
1. Valida dados de entrada
2. Cria a obra-mãe (`CONDOMINIO_MASTER`)
3. Itera sobre array de unidades criando obras-filhas (`UNIDADE_CONDOMINIO`)
4. Garante atomicidade da operação

```sql
CREATE OR REPLACE FUNCTION create_condominio_project(
  condominio_data JSONB,
  unidades_data JSONB[]
) RETURNS JSONB
```

#### API Modifications

##### Listagem de Obras
- Filtro automático: `parent_obra_id IS NULL` na query principal
- Endpoint específico para buscar unidades de um condomínio

##### Detalhes de Obra
- Query condicional baseada em `tipo_projeto`
- Para `CONDOMINIO_MASTER`: inclui agregações das unidades
- Para `UNIDADE_CONDOMINIO`: inclui dados do condomínio pai

### Frontend Architecture

#### Hook Customizado: `useObrasCondominio`
Extensão do hook `useObras` existente com funcionalidades específicas:
- `createCondominio()`: Chama RPC function
- `getUnidadesCondominio(obraId)`: Busca unidades de um condomínio
- `getCondominioDashboard(obraId)`: Dados agregados do condomínio

#### Componentes Novos

##### `CondomínioFormSection`
- Renderização condicional no formulário de nova obra
- Configuração dinâmica de unidades
- Validação específica para dados de condomínio

##### `CondominioDashboard`
- Dashboard agregado com métricas consolidadas
- Gráficos de progresso por unidade
- Estatísticas de custos totais

##### `ListaUnidades`
- Tabela de unidades usando `DataTable` existente
- Navegação para detalhes de cada unidade
- Ações em lote para unidades selecionadas

## Components and Interfaces

### Database Interfaces

```typescript
// Extensão do tipo Obra existente
interface Obra {
  // ... campos existentes
  parent_obra_id?: string;
  tipo_projeto: 'UNICO' | 'CONDOMINIO_MASTER' | 'UNIDADE_CONDOMINIO';
  identificador_unidade?: string;
}

interface CondomínioData {
  obra_mae: Omit<Obra, 'id' | 'tipo_projeto'>;
  unidades: Array<{
    identificador_unidade: string;
    nome: string;
    // outros campos específicos da unidade
  }>;
}
```

### API Interfaces

```typescript
interface CreateCondomínioRequest {
  condominio_data: CondomínioData['obra_mae'];
  unidades_data: CondomínioData['unidades'];
}

interface CondomínioDashboardResponse {
  obra_mae: Obra;
  unidades: Obra[];
  estatisticas: {
    total_unidades: number;
    progresso_medio: number;
    custo_total: number;
    unidades_concluidas: number;
  };
}
```

### Frontend Components

```typescript
interface CondomínioFormSectionProps {
  isCondominio: boolean;
  onUnidadesChange: (unidades: UnidadeFormData[]) => void;
}

interface CondominioDashboardProps {
  obraId: string;
  data: CondomínioDashboardResponse;
}

interface ListaUnidadesProps {
  condomínioId: string;
  unidades: Obra[];
  onUnidadeSelect: (unidadeId: string) => void;
}
```

## Data Models

### Modelo de Dados Hierárquico

```
CONDOMINIO_MASTER (parent_obra_id: NULL)
├── UNIDADE_CONDOMINIO (parent_obra_id: condominio_id)
├── UNIDADE_CONDOMINIO (parent_obra_id: condominio_id)
└── UNIDADE_CONDOMINIO (parent_obra_id: condominio_id)
```

### Validações de Negócio

1. **Hierarquia Válida**: Apenas 2 níveis permitidos (condomínio → unidade)
2. **Tipos Consistentes**: `UNIDADE_CONDOMINIO` deve ter parent do tipo `CONDOMINIO_MASTER`
3. **Identificadores Únicos**: `identificador_unidade` único dentro do mesmo condomínio
4. **Tenant Isolation**: Todas as operações respeitam isolamento por `tenant_id`

### Queries Otimizadas

#### Listagem Principal (sem unidades)
```sql
SELECT * FROM obras 
WHERE tenant_id = $1 
  AND parent_obra_id IS NULL
ORDER BY created_at DESC;
```

#### Condomínio com Unidades
```sql
SELECT 
  c.*,
  json_agg(u.*) as unidades
FROM obras c
LEFT JOIN obras u ON u.parent_obra_id = c.id
WHERE c.id = $1 AND c.tenant_id = $2
GROUP BY c.id;
```

## Error Handling

### Database Level
- **Constraint Violations**: Tratamento específico para violações de FK e check constraints
- **Transaction Rollback**: Rollback automático em caso de falha na criação de qualquer unidade
- **Deadlock Prevention**: Ordenação consistente de locks para evitar deadlocks

### Application Level
- **Validation Errors**: Validação Zod tanto no frontend quanto no backend
- **Business Logic Errors**: Mensagens específicas para regras de negócio violadas
- **Network Errors**: Retry automático para operações de criação de condomínio

### User Experience
- **Loading States**: Indicadores de progresso durante criação de condomínios
- **Error Messages**: Mensagens claras e acionáveis para o usuário
- **Rollback UI**: Interface para desfazer operações em caso de erro parcial

## Testing Strategy

### Database Tests
- **Migration Tests**: Verificar que migrações aplicam corretamente
- **Constraint Tests**: Validar que constraints impedem dados inválidos
- **Performance Tests**: Verificar performance de queries com grandes volumes

### Backend Tests (Deno)
- **RPC Function Tests**: Testes unitários da função `create_condominio_project`
- **Integration Tests**: Testes end-to-end do fluxo de criação
- **Error Handling Tests**: Cenários de falha e rollback

### Frontend Tests (Vitest + React Testing Library)
- **Component Tests**: Renderização condicional dos novos componentes
- **Hook Tests**: Funcionalidades do `useObrasCondominio`
- **Integration Tests**: Fluxo completo de criação via UI
- **Accessibility Tests**: Conformidade a11y dos novos componentes

### End-to-End Tests
- **User Journey Tests**: Fluxo completo de criação e navegação
- **Performance Tests**: Tempo de carregamento com muitas unidades
- **Cross-browser Tests**: Compatibilidade em diferentes navegadores

## Security Considerations

### Tenant Isolation
- **RLS Policies**: Extensão das políticas existentes para novos tipos de obra
- **Query Filtering**: Filtros automáticos de `tenant_id` em todas as consultas
- **Authorization**: Verificação de propriedade antes de operações sensíveis

### Data Validation
- **Input Sanitization**: Sanitização de todos os inputs usando `detectMaliciousPatterns`
- **Schema Validation**: Validação Zod sincronizada entre frontend e backend
- **Business Rules**: Validação de regras de negócio no nível de aplicação

### Audit Trail
- **Operation Logging**: Log de todas as operações de criação/edição de condomínios
- **Change Tracking**: Rastreamento de mudanças em unidades individuais
- **Access Logging**: Log de acessos a dados de condomínios

## Performance Optimizations

### Database Optimizations
- **Índices Estratégicos**: Índices em colunas frequentemente consultadas
- **Query Optimization**: Uso de JOINs eficientes e agregações otimizadas
- **Connection Pooling**: Reutilização de conexões para operações em lote

### Frontend Optimizations
- **Lazy Loading**: Carregamento sob demanda de unidades
- **Memoization**: Cache de dados agregados do dashboard
- **Virtual Scrolling**: Para listas grandes de unidades
- **Optimistic Updates**: Atualizações otimistas para melhor UX

### Caching Strategy
- **Query Caching**: Cache de consultas frequentes via TanStack Query
- **Invalidation Strategy**: Invalidação inteligente baseada em relacionamentos
- **Background Refresh**: Atualização em background de dados crítico