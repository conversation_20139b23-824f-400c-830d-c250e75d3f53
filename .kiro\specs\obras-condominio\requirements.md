# Requirements Document - Obras de Condomínio

## Introduction

A funcionalidade de Obras de Condomínio expande o sistema ObrasAI para permitir que construtores gerenciem projetos complexos de condomínios. Atualmente, o sistema suporta apenas obras únicas. Esta nova funcionalidade introduz uma estrutura hierárquica onde um projeto de condomínio (obra-mãe) pode conter múltiplas unidades ou casas (obras-filhas), permitindo melhor organização, controle de custos e gestão de projetos de grande escala.

## Requirements

### Requirement 1

**User Story:** Como um construtor, eu quero criar um projeto de condomínio com múltiplas unidades, para que eu possa gerenciar todas as unidades de forma organizada e centralizada.

#### Acceptance Criteria

1. WHEN o usuário seleciona "Condomínio" no formulário de nova obra THEN o sistema SHALL exibir campos específicos para configuração do condomínio
2. WHEN o usuário preenche os dados do condomínio e das unidades THEN o sistema SHALL criar uma obra-mãe do tipo "CONDOMINIO_MASTER" e obras-filhas do tipo "UNIDADE_CONDOMINIO"
3. WHEN a criação do condomínio é solicitada THEN o sistema SHALL garantir que a operação seja atômica (todas as unidades são criadas ou nenhuma é criada)
4. WHEN o usuário define o número de unidades THEN o sistema SHALL permitir configurar identificadores únicos para cada unidade

### Requirement 2

**User Story:** Como um construtor, eu quero visualizar o dashboard do condomínio com informações agregadas de todas as unidades, para que eu possa ter uma visão geral do progresso e custos do projeto.

#### Acceptance Criteria

1. WHEN o usuário acessa uma obra do tipo "CONDOMINIO_MASTER" THEN o sistema SHALL exibir um dashboard agregado com estatísticas consolidadas
2. WHEN o dashboard é carregado THEN o sistema SHALL mostrar métricas como: total de unidades, progresso geral, custos totais, e status de cada unidade
3. WHEN o usuário visualiza o condomínio THEN o sistema SHALL exibir uma lista navegável de todas as unidades do condomínio
4. WHEN o usuário clica em uma unidade específica THEN o sistema SHALL navegar para os detalhes daquela unidade

### Requirement 3

**User Story:** Como um construtor, eu quero gerenciar individualmente cada unidade do condomínio, para que eu possa controlar custos, progresso e detalhes específicos de cada unidade.

#### Acceptance Criteria

1. WHEN o usuário acessa uma obra do tipo "UNIDADE_CONDOMINIO" THEN o sistema SHALL exibir os detalhes específicos da unidade
2. WHEN o usuário está visualizando uma unidade THEN o sistema SHALL mostrar um link de navegação de volta para o condomínio principal
3. WHEN o usuário edita dados de uma unidade THEN o sistema SHALL permitir modificações independentes sem afetar outras unidades
4. WHEN o usuário visualiza uma unidade THEN o sistema SHALL mostrar o identificador da unidade e sua relação com o condomínio

### Requirement 4

**User Story:** Como um construtor, eu quero que a listagem principal de obras não seja poluída com unidades individuais, para que eu possa focar nos projetos principais.

#### Acceptance Criteria

1. WHEN o usuário acessa a listagem de obras THEN o sistema SHALL exibir apenas obras únicas e condomínios principais (não unidades individuais)
2. WHEN o sistema carrega a lista de obras THEN o sistema SHALL filtrar automaticamente obras com parent_obra_id não nulo
3. WHEN o usuário busca por obras THEN o sistema SHALL incluir apenas obras-mãe nos resultados de busca
4. WHEN o usuário acessa estatísticas gerais THEN o sistema SHALL contar condomínios como uma única obra, não como múltiplas unidades

### Requirement 5

**User Story:** Como um construtor, eu quero que o sistema mantenha a integridade referencial entre condomínios e unidades, para que eu não perca dados por operações inconsistentes.

#### Acceptance Criteria

1. WHEN uma obra-mãe é excluída THEN o sistema SHALL impedir a exclusão se existirem unidades vinculadas
2. WHEN o sistema detecta inconsistências de dados THEN o sistema SHALL aplicar constraints de chave estrangeira para manter integridade
3. WHEN operações de criação ou edição são realizadas THEN o sistema SHALL validar que apenas obras do tipo correto podem ser vinculadas como parent/child
4. WHEN o usuário tenta criar uma unidade THEN o sistema SHALL validar que o parent_obra_id referencia uma obra do tipo "CONDOMINIO_MASTER"

### Requirement 6

**User Story:** Como um construtor, eu quero que o sistema seja performático ao carregar condomínios com muitas unidades, para que eu possa trabalhar eficientemente mesmo com projetos grandes.

#### Acceptance Criteria

1. WHEN o sistema carrega um condomínio com suas unidades THEN o sistema SHALL usar consultas otimizadas com índices apropriados
2. WHEN o usuário acessa o dashboard do condomínio THEN o sistema SHALL carregar dados agregados em uma única consulta quando possível
3. WHEN o sistema executa consultas relacionadas a condomínios THEN o sistema SHALL utilizar índices na coluna parent_obra_id
4. WHEN o usuário navega entre unidades THEN o sistema SHALL implementar paginação se o número de unidades exceder um limite configurável