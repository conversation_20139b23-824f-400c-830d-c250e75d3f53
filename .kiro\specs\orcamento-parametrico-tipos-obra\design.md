# Design Document

## Overview

O sistema de orçamento paramétrico será refatorado para utilizar tipos de obra específicos, permitindo cálculos mais precisos e realistas. A arquitetura manterá a estrutura atual baseada em Edge Functions do Supabase, mas implementará lógicas de cálculo diferenciadas para cada tipo de construção.

## Architecture

### High-Level Architecture

```mermaid
graph TD
    A[Frontend - Formulário] --> B[API Service]
    B --> C[Edge Function - ai-calculate-budget-v11]
    C --> D{Tipo de Obra}
    D -->|R1_UNIFAMILIAR| E[calcularOrcamentoUnifamiliar]
    D -->|R4_MULTIFAMILIAR| F[calcularOrcamentoMultifamiliar]
    D -->|COMERCIAL_GALPAO| G[calcularOrcamentoGalpao]
    E --> H[Consulta SINAPI]
    F --> I[Consulta SINAPI Direcionada]
    G --> J[Consulta SINAPI Específica]
    H --> K[<PERSON>var Resultado]
    I --> K
    J --> K
    K --> L[Banco de Dados - orcamentos_parametricos]
```

### Data Flow

1. **Input**: Frontend envia objeto completo do formulário (não apenas ID)
2. **Processing**: Edge Function recebe dados e determina tipo de obra
3. **Calculation**: Função específica calcula orçamento baseado no tipo
4. **Output**: Resultado salvo com parâmetros de cálculo para auditoria

## Components and Interfaces

### Frontend Components

#### useWizardOrcamento Hook

- **Modificação**: Alterar `handleSubmit` para enviar objeto completo
- **Interface**:

```typescript
interface WizardCompleto {
  tipo_obra: string;
  padrao_obra: string;
  area_total: number;
  // outros campos do formulário
}
```

#### orcamentoApi Service

- **Modificação**: Função `calcular` aceita objeto de dados completo
- **Interface**:

```typescript
async function calcular(
  dadosFormulario: WizardCompleto
): Promise<OrcamentoResult>;
```

### Backend Components

#### Edge Function Structure

```typescript
// Estrutura principal da Edge Function
export default async function handler(req: Request) {
  const dadosFormulario = await req.json();
  const { tipo_obra } = dadosFormulario;

  let custoEstimadoFinal;

  switch (tipo_obra) {
    case "R4_MULTIFAMILIAR":
      custoEstimadoFinal = await calcularOrcamentoMultifamiliar(
        dadosFormulario
      );
      break;
    case "COMERCIAL_GALPAO":
      custoEstimadoFinal = await calcularOrcamentoGalpao(dadosFormulario);
      break;
    case "R1_UNIFAMILIAR":
    default:
      custoEstimadoFinal = await calcularOrcamentoUnifamiliar(dadosFormulario);
      break;
  }

  return new Response(JSON.stringify(custoEstimadoFinal));
}
```

#### Calculation Functions

##### calcularOrcamentoMultifamiliar

- **Fator de Complexidade**: 1.35 (35% mais caro que unifamiliar)
- **Consulta SINAPI**: Termos específicos como "estrutura de concreto armado", "elevador", "instalações prediais"
- **Fatores de Padrão**: `{ POPULAR: 1.0, NORMAL: 1.4, ALTO: 1.9, LUXO: 2.5 }`
- **Composição de Custos**: Maior percentual para estrutura e instalações

##### calcularOrcamentoGalpao

- **Características**: Foco em estrutura metálica, grandes vãos, sistemas industriais
- **Consulta SINAPI**: Termos como "estrutura metálica", "cobertura industrial", "piso industrial"
- **Fatores específicos**: Baseados em características de galpões

##### calcularOrcamentoUnifamiliar

- **Lógica**: Mantém a lógica atual existente
- **Refatoração**: Move código atual para função específica

## Data Models

### Existing Table Enhancement

```sql
-- Tabela orcamentos_parametricos (modificações)
ALTER TABLE orcamentos_parametricos
ADD COLUMN IF NOT EXISTS parametros_calculo JSONB;

-- Exemplo de dados em parametros_calculo:
{
  "fator_complexidade": 1.35,
  "cub_utilizado": 1850.00,
  "fatores_padrao": {
    "NORMAL": 1.4
  },
  "consultas_sinapi": [
    "estrutura de concreto armado",
    "elevador",
    "instalações prediais"
  ],
  "composicao_custos": {
    "estrutura": 0.35,
    "instalacoes": 0.25,
    "acabamentos": 0.40
  }
}
```

### Input Data Model

```typescript
interface DadosFormulario {
  tipo_obra: "R1_UNIFAMILIAR" | "R4_MULTIFAMILIAR" | "COMERCIAL_GALPAO";
  padrao_obra: "POPULAR" | "NORMAL" | "ALTO" | "LUXO";
  area_total: number;
  // outros campos existentes
}
```

### Output Data Model

```typescript
interface OrcamentoResult {
  custo_total: number;
  custo_por_m2: number;
  parametros_utilizados: {
    fator_complexidade?: number;
    cub_base: number;
    fator_padrao: number;
    consultas_sinapi: string[];
  };
  detalhamento: {
    estrutura: number;
    instalacoes: number;
    acabamentos: number;
  };
}
```

## Error Handling

### Validation Errors

- **Tipo de obra inválido**: Retornar erro 400 com lista de tipos válidos
- **Dados incompletos**: Validar presença de campos obrigatórios
- **Área inválida**: Validar se area_total > 0

### Calculation Errors

- **Falha na consulta SINAPI**: Fallback para valores padrão com warning
- **CUB não encontrado**: Usar valor padrão regional com log de erro
- **Timeout**: Implementar timeout de 30 segundos para cálculos

### Database Errors

- **Falha ao salvar**: Retry automático até 3 tentativas
- **Constraint violations**: Validação prévia dos dados

## Testing Strategy

### Unit Tests

- **Cada função de cálculo**: Testes isolados com dados mockados
- **Validação de entrada**: Testes para todos os cenários de erro
- **Lógica de negócio**: Verificar fatores e multiplicadores corretos

### Integration Tests

- **Edge Function completa**: Testes end-to-end com dados reais
- **Consulta SINAPI**: Testes com base de dados real
- **Persistência**: Verificar salvamento correto no banco

### Performance Tests

- **Tempo de resposta**: Máximo 10 segundos para cálculos complexos
- **Concorrência**: Suporte a múltiplas requisições simultâneas
- **Memory usage**: Monitorar uso de memória da Edge Function

### Acceptance Tests

- **Cenário 1**: Orçamento unifamiliar 100m² padrão NORMAL
- **Cenário 2**: Orçamento multifamiliar 100m² padrão NORMAL (deve ser ~35% mais caro)
- **Cenário 3**: Orçamento galpão 500m² padrão NORMAL
- **Cenário 4**: Verificar salvamento de parâmetros no banco

## Security Considerations

- **Input Validation**: Sanitização de todos os dados de entrada
- **Rate Limiting**: Limitar requisições por usuário/IP
- **Authentication**: Manter autenticação existente do Supabase
- **Data Privacy**: Não logar dados sensíveis dos orçamentos

## Deployment Strategy

1. **Fase 1**: Deploy da Edge Function modificada
2. **Fase 2**: Atualização do frontend (backward compatible)
3. **Fase 3**: Migração do banco de dados (adicionar coluna)
4. **Fase 4**: Testes em produção com feature flag
5. **Fase 5**: Rollout completo
