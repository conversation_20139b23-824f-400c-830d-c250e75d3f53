# Requirements Document

## Introduction

Este documento especifica os requisitos para implementar um sistema de orçamento paramétrico que utiliza o tipo de obra para gerar estimativas de custo mais precisas e realistas. O sistema atual calcula orçamentos de forma genérica, mas precisa ser aprimorado para considerar as especificidades de diferentes tipos de construção como residências multifamiliares, galpões comerciais e residências unifamiliares.

## Requirements

### Requirement 1

**User Story:** Como um usuário do sistema de orçamentos, eu quero que o cálculo paramétrico considere o tipo de obra selecionado, para que eu obtenha estimativas de custo mais precisas e alinhadas com a realidade do mercado.

#### Acceptance Criteria

1. WHEN o usuário seleciona um tipo de obra no formulário THEN o sistema SHALL utilizar esse tipo para aplicar lógicas de cálculo específicas
2. WHEN o tipo de obra for "R4_MULTIFAMILIAR" THEN o sistema SHALL aplicar um fator de complexidade de 35% sobre o CUB base
3. WHEN o tipo de obra for "COMERCIAL_GALPAO" THEN o sistema SHALL utilizar composições SINAPI específicas para galpões
4. WHEN o tipo de obra for "R1_UNIFAMILIAR" THEN o sistema SHALL utilizar a lógica de cálculo padrão existente

### Requirement 2

**User Story:** Como desenvolvedor do sistema, eu quero que a Edge Function receba dados completos do formulário, para que não seja necessário fazer consultas adicionais ao banco de dados durante o cálculo.

#### Acceptance Criteria

1. WHEN a função de cálculo for chamada THEN o sistema SHALL enviar o objeto completo com dados do formulário incluindo tipo_obra, padrao_obra e area_total
2. WHEN a Edge Function receber os dados THEN ela SHALL ter acesso imediato a todos os parâmetros necessários sem consultas ao banco
3. IF os dados do formulário estiverem incompletos THEN o sistema SHALL retornar erro de validação

### Requirement 3

**User Story:** Como um usuário interessado em construções multifamiliares, eu quero que o sistema considere a complexidade adicional deste tipo de obra, para que o orçamento reflita custos realistas de áreas comuns, estruturas robustas e sistemas prediais complexos.

#### Acceptance Criteria

1. WHEN o tipo de obra for "R4_MULTIFAMILIAR" THEN o sistema SHALL aplicar consultas SINAPI direcionadas para "estrutura de concreto armado", "elevador" e "instalações prediais"
2. WHEN o padrão de obra for definido para multifamiliar THEN o sistema SHALL aplicar fatores de ajuste mais agressivos: POPULAR: 1.0, NORMAL: 1.4, ALTO: 1.9, LUXO: 2.5
3. WHEN calcular custos para multifamiliar THEN o sistema SHALL alocar percentual maior para estrutura e instalações em relação a acabamentos

### Requirement 4

**User Story:** Como administrador do sistema, eu quero que os parâmetros de cálculo sejam armazenados junto com o orçamento, para que eu possa auditar e depurar os cálculos realizados.

#### Acceptance Criteria

1. WHEN um orçamento for calculado THEN o sistema SHALL salvar os campos tipo_obra e padrao_obra na tabela orcamentos_parametricos
2. WHEN um orçamento for salvo THEN o sistema SHALL armazenar os fatores e CUBs utilizados em uma coluna parametros_calculo (JSONB)
3. WHEN consultar um orçamento existente THEN o sistema SHALL permitir visualizar como o cálculo foi realizado

### Requirement 5

**User Story:** Como desenvolvedor do sistema, eu quero que a arquitetura seja escalável, para que novos tipos de obra possam ser facilmente adicionados no futuro.

#### Acceptance Criteria

1. WHEN implementar a lógica de cálculo THEN o sistema SHALL utilizar uma estrutura de decisão (switch/case) baseada no tipo_obra
2. WHEN adicionar um novo tipo de obra THEN o sistema SHALL permitir implementar nova função de cálculo sem modificar código existente
3. WHEN a lógica for organizada THEN cada tipo de obra SHALL ter sua função específica de cálculo (calcularOrcamentoMultifamiliar, calcularOrcamentoGalpao, etc.)

### Requirement 6

**User Story:** Como usuário do sistema, eu quero que o frontend continue funcionando normalmente, para que a experiência de uso não seja impactada pelas melhorias no backend.

#### Acceptance Criteria

1. WHEN as modificações forem implementadas THEN o frontend SHALL manter a mesma interface e fluxo de uso
2. WHEN o usuário submeter o formulário THEN o sistema SHALL enviar dados completos para a API em vez de apenas o ID
3. WHEN a API for chamada THEN ela SHALL aceitar o objeto de dados completo no corpo da requisição
