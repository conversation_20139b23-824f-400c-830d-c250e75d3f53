# Implementation Plan

- [ ] 1. Set up database schema modifications

  - Add parametros_calculo JSONB column to orcamentos_parametricos table
  - Create migration script for the new column
  - Test migration on development environment
  - _Requirements: 4.2_

- [ ] 2. Refactor existing Edge Function structure

  - [ ] 2.1 Create base calculation function structure

    - Modify ai-calculate-budget-v11 Edge Function to accept complete form data
    - Implement switch/case structure for tipo_obra routing
    - Create error handling for invalid tipo_obra values
    - _Requirements: 1.1, 5.1_

  - [ ] 2.2 Extract current logic to calcularOrcamentoUnifamiliar function
    - Move existing calculation logic to dedicated function
    - Maintain backward compatibility with current calculations
    - Add unit tests for the extracted function
    - _Requirements: 1.4, 5.3_

- [ ] 3. Implement calcularOrcamentoMultifamiliar function

  - [ ] 3.1 Create multifamiliar-specific calculation logic

    - Implement 1.35 complexity factor for multifamiliar buildings
    - Create enhanced pattern factors: POPULAR: 1.0, NORMAL: 1.4, ALTO: 1.9, LUXO: 2.5
    - Implement cost composition logic with higher structure/installations percentage
    - _Requirements: 1.2, 3.1, 3.3_

  - [ ] 3.2 Implement directed SINAPI queries for multifamiliar

    - Create SINAPI search terms for "estrutura de concreto armado", "elevador", "instalações prediais"
    - Implement query logic to fetch relevant compositions
    - Add fallback mechanism for failed SINAPI queries
    - _Requirements: 3.2_

  - [ ] 3.3 Add calculation parameters storage
    - Store complexity factors, CUB values, and pattern factors in parametros_calculo
    - Include SINAPI query terms and cost composition in stored parameters
    - Implement JSON structure for audit trail
    - _Requirements: 4.1, 4.3_

- [ ] 4. Implement calcularOrcamentoGalpao function

  - [ ] 4.1 Create galpao-specific calculation logic

    - Implement calculation factors specific to commercial warehouses
    - Create SINAPI queries for "estrutura metálica", "cobertura industrial", "piso industrial"
    - Implement cost composition for large span structures
    - _Requirements: 1.3, 5.3_

  - [ ] 4.2 Add galpao parameters storage
    - Store galpao-specific factors in parametros_calculo
    - Include industrial-specific SINAPI terms
    - Implement cost breakdown for warehouse construction
    - _Requirements: 4.2_

- [ ] 5. Update frontend API integration

  - [ ] 5.1 Modify useWizardOrcamento hook

    - Update handleSubmit function to send complete form object instead of just ID
    - Ensure all form fields are included in the API call
    - Maintain error handling for API responses
    - _Requirements: 2.1, 6.2_

  - [ ] 5.2 Update orcamentoApi service
    - Modify calcular function to accept complete form data object
    - Update API call to send form data in request body
    - Ensure backward compatibility during transition
    - _Requirements: 2.2, 6.3_

- [ ] 6. Implement comprehensive error handling

  - [ ] 6.1 Add input validation

    - Validate tipo_obra against allowed values
    - Validate required fields presence (area_total, padrao_obra)
    - Implement proper error responses with descriptive messages
    - _Requirements: 2.3_

  - [ ] 6.2 Add calculation error handling
    - Implement fallback values for failed SINAPI queries
    - Add timeout handling for long-running calculations
    - Create retry logic for database operations
    - _Requirements: Error handling from design_

- [ ] 7. Create comprehensive test suite

  - [ ] 7.1 Write unit tests for calculation functions

    - Test calcularOrcamentoUnifamiliar with various inputs
    - Test calcularOrcamentoMultifamiliar complexity factors
    - Test calcularOrcamentoGalpao specific logic
    - Verify parametros_calculo storage for each function
    - _Requirements: All requirements validation_

  - [ ] 7.2 Write integration tests

    - Test complete Edge Function flow with different tipo_obra values
    - Verify database storage of results and parameters
    - Test API integration from frontend to database
    - _Requirements: 6.1, 4.3_

  - [ ] 7.3 Create acceptance test scenarios
    - Test scenario: R1_UNIFAMILIAR 100m² NORMAL standard
    - Test scenario: R4_MULTIFAMILIAR 100m² NORMAL standard (verify 35% higher cost)
    - Test scenario: COMERCIAL_GALPAO 500m² NORMAL standard
    - Verify parametros_calculo contains expected values for each scenario
    - _Requirements: 1.1, 1.2, 1.3, 4.2_

- [ ] 8. Deploy and validate implementation

  - [ ] 8.1 Deploy Edge Function updates

    - Deploy modified ai-calculate-budget-v11 function to Supabase
    - Verify function deployment and basic connectivity
    - Test function with sample data
    - _Requirements: All backend requirements_

  - [ ] 8.2 Deploy frontend changes

    - Deploy updated useWizardOrcamento hook and orcamentoApi service
    - Verify frontend integration with updated backend
    - Test complete user flow from form submission to result display
    - _Requirements: 6.1, 6.2, 6.3_

  - [ ] 8.3 Validate end-to-end functionality

    - Execute complete test scenarios in production environment
    - Verify database records contain correct tipo_obra and parametros_calculo
    - Confirm cost calculations are significantly different between tipos_obra

    - Validate audit trail functionality through parametros_calculo
    - _Requirements: All requirements final validation_

## Tarefas Complementares (Gap Analysis)

- [ ] 9. Refatorar Edge Function para lógica por tipo de obra

  - Implementar estrutura switch/case para tipo_obra
  - Chamar funções específicas conforme tipo_obra
  - _Requisitos: 1.1, 5.1_

- [ ] 10. Implementar funções de cálculo específicas

  - calcularOrcamentoUnifamiliar: lógica padrão existente
  - calcularOrcamentoMultifamiliar: aplicar fator 1.35, padrões agressivos, consulta SINAPI direcionada
  - calcularOrcamentoGalpao: lógica para galpão, consulta SINAPI industrial
  - _Requisitos: 1.2, 1.3, 3.1, 3.3, 5.3_

- [ ] 11. Armazenar parâmetros de cálculo em coluna JSONB

  - Salvar parâmetros utilizados em parametros_calculo
  - Permitir auditoria dos cálculos
  - _Requisitos: 4.1, 4.2, 4.3_

- [ ] 12. Direcionar consultas SINAPI conforme tipo de obra

  - Implementar termos de busca específicos para multifamiliar e galpão
  - Adicionar fallback para erro de consulta
  - _Requisitos: 3.2, 4.3_

- [ ] 13. Adicionar validação de entrada específica

  - Validar tipo_obra, padrao_obra, area_total
  - Retornar erro 400 para dados inválidos
  - _Requisitos: 2.3, 6.1_

- [ ] 14. Adicionar fallback para erros de consulta e timeout

  - Implementar timeout de 30s
  - Usar valores padrão em caso de erro
  - _Requisitos: Error handling from design_

- [ ] 15. Criar testes unitários e integração para cada função

  - Testar funções de cálculo individualmente
  - Testar fluxo completo da Edge Function
  - _Requisitos: 7.1, 7.2_

- [ ] 16. Adicionar tarefas para performance, segurança, feature flag, auditoria e documentação
  - Testes de performance e stress
  - Implementar sanitização, rate limiting e privacidade
  - Implementar feature flag para rollout controlado
  - Criar endpoint/interface para auditoria dos parâmetros
  - Atualizar documentação técnica e de uso
  - _Requisitos: Design, Security, Deployment_
