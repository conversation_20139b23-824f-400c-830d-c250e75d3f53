{"mcpServers": {"context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": []}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--project-ref=anrphijuostbgbscxmzx", "--features=account,database,debug,development,docs,functions,storage,branching"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}, "disabled": false, "autoApprove": ["list_projects", "list_tables", "execute_sql", "apply_migration", "list_migrations", "deploy_function", "create_function", "update_function", "delete_function", "list_functions", "create_table", "update_table", "delete_table", "insert_data", "update_data", "delete_data"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "env": {}, "disabled": true, "autoApprove": []}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": ["sequentialthinking", "sequentialthinking"]}}}