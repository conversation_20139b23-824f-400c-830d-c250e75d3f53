{"[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "deno.enablePaths": ["supabase/functions"], "deno.lint": true, "deno.unstable": ["bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "emmet.includeLanguages": {"typescriptreact": "html"}, "eslint.format.enable": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "files.associations": {"*.ts": "typescript", "*.tsx": "typescriptreact"}, "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.preferences.organizeImportsIgnoreCase": false, "typescript.updateImportsOnFileMove.enabled": "always", "typescript.suggest.autoImports": true, "python.defaultInterpreterPath": "python", "python.terminal.activateEnvironment": true, "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "extensions.ignoreRecommendations": false, "extensions.autoUpdate": true, "extensions.autoCheckUpdates": true, "typescript.autoClosingTags": false, "npm.packageManager": "npm"}