# Getting started with Notebooks
```warp-runnable-command
echo "Hello, welcome to Notebooks! Press the run button to try out this command."
```
This is your first Notebook\. Think of a notebook as an interactive playbook you can use to\:
* Organize and execute terminal commands\.
* Create executable markdown docs for onboarding and incident response\.
* Take personal notes\.
***
## Commands and code blocks
```warp-runnable-command
echo "This is a shell command. You can include parameters. Wrap them in {{}}. Isn’t that {{cool}}?"
```
```js
function helloWorld() {
 console.log("Code blocks can also have syntax highlighting!");
}
```
**Keyboard Tip\:** Use `CMD + ↑/↓` on macOS or `CTRL-SHIFT + ↑/↓` on Linux to jump between command blocks and `CMD + ENTER` on macOS or `CTRL-ENTER` on Linux to run the block\.
***
## More supported markdown types
### Font decorations
**bold**\, *italic*\, `inline code`\, [links](https://warp.dev/)
### Headings
### Lists
1. Numbered lists
* Bulleted Lists
- [x] Task List
***
[Learn more about using Notebooks in Warp ↗ ](https://docs.warp.dev/features/warp-drive/notebooks) 