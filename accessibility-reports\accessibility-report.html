
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Acessibilidade - ObrasAI</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .header { text-align: center; margin-bottom: 30px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #2563eb; }
        .metric-label { color: #6b7280; margin-top: 5px; }
        .test-results { margin-bottom: 30px; }
        .test-item { background: #f8f9fa; margin: 10px 0; padding: 15px; border-radius: 6px; }
        .test-passed { border-left: 4px solid #10b981; }
        .test-failed { border-left: 4px solid #ef4444; }
        .recommendations { background: #fef3c7; padding: 20px; border-radius: 6px; }
        .violation { background: #fee2e2; padding: 10px; margin: 5px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Relatório de Acessibilidade - ObrasAI</h1>
            <p>Gerado em: 02/07/2025, 13:33:07</p>
        </div>
        
        <div class="metrics">
            <div class="metric">
                <div class="metric-value">0/4</div>
                <div class="metric-label">Testes Aprovados</div>
            </div>
            <div class="metric">
                <div class="metric-value">0%</div>
                <div class="metric-label">Cobertura</div>
            </div>
            <div class="metric">
                <div class="metric-value">0</div>
                <div class="metric-label">Violações Críticas</div>
            </div>
            <div class="metric">
                <div class="metric-value">0</div>
                <div class="metric-label">Violações Sérias</div>
            </div>
        </div>
        
        <div class="test-results">
            <h2>📋 Resultados dos Testes</h2>
            
                <div class="test-item test-failed">
                    <h3>Header.accessibility.test.tsx</h3>
                    <p><strong>Status:</strong> ❌ Reprovado</p>
                    <p><strong>Duração:</strong> 2902ms</p>
                    
                </div>
            
                <div class="test-item test-failed">
                    <h3>Forms.accessibility.test.tsx</h3>
                    <p><strong>Status:</strong> ❌ Reprovado</p>
                    <p><strong>Duração:</strong> 3488ms</p>
                    
                </div>
            
                <div class="test-item test-failed">
                    <h3>Images.accessibility.test.tsx</h3>
                    <p><strong>Status:</strong> ❌ Reprovado</p>
                    <p><strong>Duração:</strong> 5495ms</p>
                    
                </div>
            
                <div class="test-item test-failed">
                    <h3>LandingPage.accessibility.test.tsx</h3>
                    <p><strong>Status:</strong> ❌ Reprovado</p>
                    <p><strong>Duração:</strong> 3506ms</p>
                    
                </div>
            
        </div>
        
        <div class="recommendations">
            <h2>💡 Recomendações</h2>
            <p>• Aumentar cobertura de testes de acessibilidade para pelo menos 80%</p><p>• Implementar testes manuais de navegação por teclado</p><p>• Testar com screen readers (NVDA, JAWS, VoiceOver)</p><p>• Validar contraste de cores com ferramentas como Lighthouse</p><p>• Adicionar skip links para navegação rápida</p><p>• Implementar live regions para anúncios dinâmicos</p><p>• Treinar equipe em práticas de acessibilidade</p><p>• Integrar testes de acessibilidade no pipeline CI/CD</p>
        </div>
    </div>
</body>
</html>