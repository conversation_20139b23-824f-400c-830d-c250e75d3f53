-- Script para aplicar correções de segurança no banco de dados
-- Execute este script no Management Control Panel do Supabase

-- 1. Aplicar migração de segurança
SELECT 'Aplicando correções de segurança...' as status;

-- Verificar se a migração já foi aplicada
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM supabase_migrations.schema_migrations 
        WHERE version = '20250715000000'
    ) THEN
        RAISE NOTICE 'Migração de segurança não aplicada. Execute primeiro: supabase db push';
    ELSE
        RAISE NOTICE '✅ Migração de segurança já aplicada';
    END IF;
END $$;

-- 2. Verificar status das correções
SELECT 'Verificando status das correções...' as status;

-- Verificar política RLS da tabela leads
SELECT 
    'leads' as tabela,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE tablename = 'leads' 
            AND policyname = 'leads_tenant_isolation'
        ) THEN '✅ Política RLS corrigida'
        ELSE '❌ Política RLS não encontrada'
    END as status_rls;

-- Verificar política RLS da tabela embeddings_conhecimento
SELECT 
    'embeddings_conhecimento' as tabela,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE tablename = 'embeddings_conhecimento' 
            AND policyname = 'embeddings_conhecimento_tenant_isolation'
        ) THEN '✅ Política RLS corrigida'
        ELSE '❌ Política RLS não encontrada'
    END as status_rls;

-- Verificar política de analytics_events
SELECT 
    'analytics_events' as tabela,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE tablename = 'analytics_events' 
            AND policyname = 'restricted_anonymous_analytics_insert'
        ) THEN '✅ Política restritiva aplicada'
        ELSE '❌ Política não encontrada'
    END as status_analytics;

-- 3. Verificar se tabelas têm campo tenant_id
SELECT 
    table_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = t.table_name 
            AND column_name = 'tenant_id'
        ) THEN '✅ Campo tenant_id presente'
        ELSE '❌ Campo tenant_id ausente'
    END as status_tenant_id
FROM (
    VALUES 
        ('leads'),
        ('embeddings_conhecimento'),
        ('profiles'),
        ('obras'),
        ('contratos')
) AS t(table_name);

-- 4. Verificar se RLS está habilitado nas tabelas críticas
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename IN ('leads', 'embeddings_conhecimento', 'profiles', 'obras', 'contratos', 'fornecedores_pf', 'fornecedores_pj')
ORDER BY tablename;

-- 5. Contar registros na tabela de auditoria
SELECT 
    'security_audit_log' as tabela,
    COUNT(*) as total_registros,
    COUNT(CASE WHEN event_type = 'security_migration_applied' THEN 1 END) as migrations_aplicadas
FROM security_audit_log;

-- 6. Verificar últimas políticas antigas removidas
SELECT 
    'Verificando políticas antigas...' as status;

-- Verificar se políticas permissivas antigas foram removidas
SELECT 
    tablename,
    policyname,
    'ATENÇÃO: Política permissiva ainda existe' as warning
FROM pg_policies 
WHERE tablename IN ('leads', 'analytics_events') 
    AND policyname IN ('Allow insert leads', 'Allow read leads', 'Allow update leads', 'Anonymous can insert analytics events');

-- 7. Relatório final
SELECT 
    'RELATÓRIO FINAL DE SEGURANÇA' as relatorio,
    (
        SELECT COUNT(*) FROM pg_policies 
        WHERE tablename = 'leads' 
        AND policyname = 'leads_tenant_isolation'
    ) as leads_protegido,
    (
        SELECT COUNT(*) FROM pg_policies 
        WHERE tablename = 'embeddings_conhecimento' 
        AND policyname = 'embeddings_conhecimento_tenant_isolation'
    ) as embeddings_protegido,
    (
        SELECT COUNT(*) FROM pg_policies 
        WHERE tablename = 'analytics_events' 
        AND policyname = 'restricted_anonymous_analytics_insert'
    ) as analytics_restrito,
    (
        SELECT COUNT(*) FROM security_audit_log 
        WHERE event_type = 'security_migration_applied'
    ) as auditoria_ativa;

-- 8. Comandos para aplicar se necessário
SELECT 'Para aplicar as correções, execute os seguintes comandos no terminal:' as instrucoes;
SELECT 'cd /mnt/d/PROJETOS/obrasai2.2' as comando_1;
SELECT 'supabase db push' as comando_2;
SELECT 'supabase functions deploy --no-verify-jwt' as comando_3;

-- 9. Verificação de conformidade final
DO $$
DECLARE
    conformidade_score INTEGER := 0;
    total_checks INTEGER := 4;
BEGIN
    -- Check 1: Política RLS leads
    IF EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'leads' AND policyname = 'leads_tenant_isolation'
    ) THEN
        conformidade_score := conformidade_score + 1;
    END IF;
    
    -- Check 2: Política RLS embeddings
    IF EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'embeddings_conhecimento' AND policyname = 'embeddings_conhecimento_tenant_isolation'
    ) THEN
        conformidade_score := conformidade_score + 1;
    END IF;
    
    -- Check 3: Política analytics restritiva
    IF EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'analytics_events' AND policyname = 'restricted_anonymous_analytics_insert'
    ) THEN
        conformidade_score := conformidade_score + 1;
    END IF;
    
    -- Check 4: Tabela de auditoria
    IF EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'security_audit_log'
    ) THEN
        conformidade_score := conformidade_score + 1;
    END IF;
    
    -- Relatório final
    RAISE NOTICE 'CONFORMIDADE DE SEGURANÇA: % de % checks aprovados (%.0f%%)', 
        conformidade_score, total_checks, (conformidade_score::FLOAT / total_checks * 100);
    
    IF conformidade_score = total_checks THEN
        RAISE NOTICE '✅ SISTEMA TOTALMENTE SEGURO - Todas as correções aplicadas com sucesso!';
    ELSE
        RAISE NOTICE '⚠️ SISTEMA PARCIALMENTE SEGURO - Aplicar correções restantes';
    END IF;
END $$;