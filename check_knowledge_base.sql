-- Verificar se existem chunks da documentação de fornecedores
-- Execute este comando para verificar o status da base de conhecimento

-- 1. Total de chunks na base
SELECT COUNT(*) as total_chunks FROM embeddings_conhecimento;

-- 2. Chunks relacionados a fornecedores
SELECT COUNT(*) as chunks_fornecedores 
FROM embeddings_conhecimento 
WHERE tipo_conteudo = 'fornecedores';

-- 3. Chunks com "fornecedor" no título
SELECT COUNT(*) as titulo_fornecedor 
FROM embeddings_conhecimento 
WHERE titulo ILIKE '%fornecedor%';

-- 4. Exemplos de títulos disponíveis (primeiros 10)
SELECT titulo, tipo_conteudo, LENGTH(conteudo) as tamanho_conteudo
FROM embeddings_conhecimento 
WHERE titulo ILIKE '%fornecedor%' OR tipo_conteudo = 'fornecedores'
LIMIT 10;

-- 5. Verificar se há embedding vetorial
SELECT COUNT(*) as com_embedding 
FROM embeddings_conhecimento 
WHERE embedding IS NOT NULL;

-- 6. Buscar chunks que contenham "cadastrar"
SELECT titulo, LEFT(conteudo, 100) as preview
FROM embeddings_conhecimento 
WHERE conteudo ILIKE '%cadastrar%' 
AND (titulo ILIKE '%fornecedor%' OR tipo_conteudo = 'fornecedores')
LIMIT 5;