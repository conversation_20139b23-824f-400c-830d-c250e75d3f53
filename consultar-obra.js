import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://anrphijuostbgbscxmzx.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU5NDc0OTcsImV4cCI6MjA2MTUyMzQ5N30.6I89FlKMWwckt7xIqt6i9HxrI0MkupzWIbKlhINblUc'

const supabase = createClient(supabaseUrl, supabaseKey)

async function consultarObra() {
  try {
    console.log('🔍 Consultando obra...')
    
    // Consultar todas as obras primeiro
    const { data: todasObras, error: todasObrasError } = await supabase
      .from('obras')
      .select('id, nome_obra, tipo_obra, area_total, created_at')
      .order('created_at', { ascending: false })
      .limit(10)

    if (todasObrasError) {
      console.error('❌ Erro ao consultar todas as obras:', todasObrasError)
      return
    }

    console.log('📋 Últimas 10 obras cadastradas:')
    todasObras.forEach(obra => {
      console.log(`- ${obra.nome_obra} (${obra.tipo_obra}) - ${obra.area_total}m² - ID: ${obra.id}`)
    })

    // Consultar obra específica
    const { data: obra, error: obraError } = await supabase
      .from('obras')
      .select('*')
      .eq('id', '897511be-db46-4ec9-95e8-19c7b24624f5')
      .maybeSingle()

    if (obraError) {
      console.error('❌ Erro ao consultar obra específica:', obraError)
      return
    }

    if (!obra) {
      console.log('❌ Obra com ID 897511be-db46-4ec9-95e8-19c7b24624f5 não encontrada')
      
      // Vamos pegar a primeira obra da lista para análise
      if (todasObras.length > 0) {
        const primeiraObra = todasObras[0]
        console.log(`\n🔄 Analisando a primeira obra disponível: ${primeiraObra.nome_obra} (${primeiraObra.id})`)
        
        const { data: obraCompleta, error: obraCompletaError } = await supabase
          .from('obras')
          .select('*')
          .eq('id', primeiraObra.id)
          .single()

        if (obraCompletaError) {
          console.error('❌ Erro ao consultar obra completa:', obraCompletaError)
          return
        }

        obra = obraCompleta
      } else {
        console.log('❌ Nenhuma obra encontrada no banco de dados')
        return
      }
    }

    console.log('\n✅ Obra encontrada:')
    console.log(JSON.stringify(obra, null, 2))

    // Consultar orçamentos vinculados à obra
    const { data: orcamentos, error: orcError } = await supabase
      .from('orcamentos_parametricos')
      .select('*')
      .eq('obra_id', obra.id)

    if (orcError) {
      console.error('❌ Erro ao consultar orçamentos:', orcError)
      return
    }

    console.log('\n📊 Orçamentos vinculados à obra:')
    console.log(`Total de orçamentos: ${orcamentos.length}`)
    
    for (const orcamento of orcamentos) {
      console.log('\n--- Orçamento ---')
      console.log(JSON.stringify(orcamento, null, 2))

      // Consultar itens do orçamento
      const { data: itens, error: itensError } = await supabase
        .from('itens_orcamento')
        .select('*')
        .eq('orcamento_id', orcamento.id)

      if (itensError) {
        console.error('❌ Erro ao consultar itens:', itensError)
        continue
      }

      console.log(`\n📋 Itens do orçamento (${itens.length} itens):`)
      
      // Resumo por categoria
      const resumoCategoria = itens.reduce((acc, item) => {
        const categoria = item.categoria
        const valor = item.quantidade_estimada * item.valor_unitario_base
        
        if (!acc[categoria]) {
          acc[categoria] = { quantidade: 0, valor: 0 }
        }
        
        acc[categoria].quantidade += 1
        acc[categoria].valor += valor
        
        return acc
      }, {})

      console.log('\n💰 Resumo por categoria:')
      Object.entries(resumoCategoria).forEach(([categoria, dados]) => {
        console.log(`${categoria}: ${dados.quantidade} itens - R$ ${dados.valor.toFixed(2)}`)
      })

      // Valor total dos itens
      const valorTotalItens = itens.reduce((total, item) => {
        return total + (item.quantidade_estimada * item.valor_unitario_base)
      }, 0)

      console.log(`\n📈 Valor total dos itens: R$ ${valorTotalItens.toFixed(2)}`)
      console.log(`💲 Custo estimado do orçamento: R$ ${orcamento.custo_estimado || 0}`)
      console.log(`🎯 Diferença: R$ ${Math.abs(valorTotalItens - (orcamento.custo_estimado || 0)).toFixed(2)}`)
    }

  } catch (error) {
    console.error('💥 Erro geral:', error)
  }
}

consultarObra()