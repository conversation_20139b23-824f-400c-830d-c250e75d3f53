// Teste de debug para capturar erro específico
const url = 'https://anrphijuostbgbscxmzx.supabase.co/functions/v1/ai-chat-handler-v2';

// Token válido do Supabase (anon key)
const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU5NDc0OTcsImV4cCI6MjA2MTUyMzQ5N30.6I89FlKMWwckt7xIqt6i9HxrI0MkupzWIbKlhINblUc';

const testData = {
  message: 'Teste de debug',
  user_id: '42df8df8-3d73-4f8f-af69-f9b59f59f59f',
  context: 'geral'
};

async function debugTest() {
  try {
    console.log('🔍 Iniciando teste de debug...');
    console.log('📤 URL:', url);
    console.log('📤 Dados:', JSON.stringify(testData, null, 2));
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${anonKey}`,
        'apikey': anonKey
      },
      body: JSON.stringify(testData)
    });
    
    console.log('📊 Status Code:', response.status);
    console.log('📊 Status Text:', response.statusText);
    console.log('📋 Response Headers:');
    for (const [key, value] of response.headers.entries()) {
      console.log(`  ${key}: ${value}`);
    }
    
    const responseText = await response.text();
    console.log('📥 Response Body (raw):', responseText);
    
    try {
      const responseJson = JSON.parse(responseText);
      console.log('📥 Response Body (JSON):', JSON.stringify(responseJson, null, 2));
    } catch (e) {
      console.log('⚠️ Response não é JSON válido');
    }
    
    if (response.ok) {
      console.log('✅ SUCESSO! Função funcionando.');
    } else {
      console.log('❌ ERRO! Status não-2xx:', response.status);
    }
    
  } catch (error) {
    console.error('❌ Erro na requisição:', error);
  }
}

debugTest();
