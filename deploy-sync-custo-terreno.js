const fs = require('fs');
const path = require('path');

// Ler o arquivo da Edge Function
const functionPath = path.join(__dirname, 'supabase', 'functions', 'sync-custo-terreno', 'index.ts');

if (!fs.existsSync(functionPath)) {
  console.error('❌ Arquivo da Edge Function não encontrado:', functionPath);
  process.exit(1);
}

const functionCode = fs.readFileSync(functionPath, 'utf8');

// Configurações do Supabase
const SUPABASE_PROJECT_ID = 'anrphijuostbgbscxmzx';
const SUPABASE_ACCESS_TOKEN = process.env.SUPABASE_ACCESS_TOKEN;

console.log('🔧 Configuração:');
console.log('  - Project ID:', SUPABASE_PROJECT_ID);
console.log('  - Token configurado:', !!SUPABASE_ACCESS_TOKEN);
console.log('  - Arquivo encontrado:', functionPath);
console.log('  - <PERSON><PERSON><PERSON> do arquivo:', functionCode.length, 'caracteres');
console.log('');

async function deployFunction() {
  try {
    console.log('🚀 Iniciando deploy da Edge Function sync-custo-terreno...');
    
    if (!SUPABASE_ACCESS_TOKEN) {
      console.log('❌ SUPABASE_ACCESS_TOKEN não configurado!');
      console.log('');
      console.log('📋 Para configurar:');
      console.log('  1. Obtenha seu Access Token em: https://supabase.com/dashboard/account/tokens');
      console.log('  2. Execute: export SUPABASE_ACCESS_TOKEN="seu_token_aqui"');
      console.log('  3. Execute novamente: node deploy-sync-custo-terreno.js');
      console.log('');
      console.log('🔄 OU use o CLI diretamente:');
      console.log('  1. npx supabase login');
      console.log('  2. npx supabase functions deploy sync-custo-terreno --project-ref anrphijuostbgbscxmzx');
      throw new Error('Token de acesso necessário');
    }
    
    // Fazer deploy via API REST
    const response = await fetch(`https://api.supabase.com/v1/projects/${SUPABASE_PROJECT_ID}/functions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ACCESS_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        slug: 'sync-custo-terreno',
        name: 'sync-custo-terreno',
        source_code: functionCode,
        verify_jwt: true,
        import_map: false
      })
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Erro no deploy: ${response.status} - ${error}`);
    }

    const result = await response.json();
    console.log('✅ Edge Function sync-custo-terreno deployada com sucesso!');
    console.log('📋 Detalhes:', {
      id: result.id,
      name: result.name,
      status: result.status,
      version: result.version
    });
    console.log('');
    console.log('🔗 URL da função:');
    console.log(`   https://anrphijuostbgbscxmzx.supabase.co/functions/v1/sync-custo-terreno`);
    console.log('');
    console.log('🎯 Próximos passos:');
    console.log('  1. Teste a função criando/editando/deletando uma despesa de terreno');
    console.log('  2. Verifique se o dashboard atualiza automaticamente');
    console.log('  3. Confirme que o problema do print foi resolvido!');
    
    return result;
  } catch (error) {
    console.error('❌ Erro no deploy:', error.message);
    throw error;
  }
}

// Executar deploy se chamado diretamente
if (require.main === module) {
  deployFunction()
    .then(() => {
      console.log('🎉 Deploy da sync-custo-terreno concluído com sucesso!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Falha no deploy:', error.message);
      process.exit(1);
    });
}

module.exports = { deployFunction };