const https = require('https');
const fs = require('fs');

console.log('🚀 Fazendo deploy da função fornecedores-chat...');

// Ler o código da função
const functionCode = fs.readFileSync('supabase/functions/fornecedores-chat/index.ts', 'utf8');
console.log('📁 Código carregado:', functionCode.length, 'caracteres');

const data = JSON.stringify({
  slug: 'fornecedores-chat',
  name: 'Chat de Fornecedores',
  verify_jwt: true,
  source: functionCode
});

const options = {
  hostname: 'api.supabase.com',
  port: 443,
  path: '/v1/projects/anrphijuostbgbscxmzx/functions',
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ********************************************',
    'Content-Type': 'application/json',
    'Content-Length': data.length
  }
};

const req = https.request(options, (res) => {
  console.log(`📊 Status: ${res.statusCode}`);
  
  let responseData = '';
  res.on('data', (d) => {
    responseData += d;
  });
  
  res.on('end', () => {
    console.log('📋 Resposta:', responseData);
    if (res.statusCode === 200 || res.statusCode === 201) {
      console.log('✅ FUNÇÃO DEPLOYADA COM SUCESSO!');
    } else {
      console.log('❌ ERRO NO DEPLOY');
    }
  });
});

req.on('error', (error) => {
  console.error('💥 ERRO:', error);
});

req.write(data);
req.end();
