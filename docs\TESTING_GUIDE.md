# 🧪 Guia de Testes - ObrasAI 2.2

**Status:** ✅ IMPLEMENTADO  
**Cobertura:** Hooks, Utilitários, Validações, Edge Functions  
**Framework:** Vitest + React Testing Library + MSW

---

## 📋 VISÃO GERAL

O sistema de testes do ObrasAI foi implementado seguindo as melhores práticas de testing moderno, com foco em:

- **Testes Unitários**: Hooks, utilitários e validações
- **Testes de Integração**: Edge Functions e fluxos completos
- **Mocking Inteligente**: MSW para interceptação de APIs
- **Cobertura Abrangente**: Casos de sucesso, erro e edge cases

---

## 🛠️ STACK DE TESTES

### **Ferramentas Principais**

- **Vitest**: Framework de testes rápido e moderno
- **React Testing Library**: Testes focados no comportamento do usuário
- **MSW (Mock Service Worker)**: Interceptação de chamadas de rede
- **Jest-DOM**: Matchers adicionais para DOM
- **Jest-Axe**: Testes de acessibilidade

### **Configuração**

```typescript
// vite.config.ts
test: {
  globals: true,
  environment: "jsdom",
  setupFiles: "./src/tests/setup.ts",
  css: true,
}
```

---

## 📁 ESTRUTURA DE TESTES

```
src/tests/
├── setup.ts                    # Configuração global
├── test-utils.tsx              # Utilitários de teste
├── mocks/
│   ├── server.ts               # Servidor MSW
│   └── handlers.ts             # Handlers de API
├── hooks/                      # Testes de hooks
│   ├── useCrudOperations.test.tsx
│   └── useFormMutation.test.tsx
├── lib/                        # Testes de utilitários
│   ├── formatters.test.ts
│   └── validations.test.ts
├── integration/                # Testes de integração
│   └── edge-functions.test.ts
└── accessibility/              # Testes de acessibilidade
    └── *.accessibility.test.tsx
```

---

## 🎯 COMANDOS DE TESTE

### **Execução Básica**

```bash
# Executar todos os testes
npm test

# Executar com interface visual
npm run test:ui

# Executar em modo watch
npm run test:watch
```

### **Testes Específicos**

```bash
# Apenas testes unitários
npm run test:unit

# Apenas testes de integração
npm run test:integration

# Testes de acessibilidade
npm run test:accessibility

# Com relatório de cobertura
npm run test:coverage
```

### **Validação de Schema**

```bash
# Validar sincronização do schema
npm run validate:schema
```

---

## 🧪 TIPOS DE TESTES IMPLEMENTADOS

### **1. Testes de Hooks**

**Arquivo:** `src/tests/hooks/useCrudOperations.test.tsx`

```typescript
describe('useCrudOperations', () => {
  it('deve carregar lista de entidades com sucesso', async () => {
    const mockData = [
      { id: '1', name: 'Teste 1', tenant_id: 'tenant-123' }
    ];
    
    mockApi.getAll = vi.fn().mockResolvedValue(mockData);
    
    const { result } = renderHook(
      () => useCrudOperations(mockApi, { resource: 'test' }),
      { wrapper: createWrapper() }
    );
    
    await waitFor(() => {
      expect(result.current.data).toEqual(mockData);
    });
  });
});
```

**Cobertura:**
- ✅ Operações CRUD (Create, Read, Update, Delete)
- ✅ Tratamento de erros
- ✅ Validação de tenant
- ✅ Invalidação de cache
- ✅ Mensagens customizadas

### **2. Testes de Utilitários**

**Arquivo:** `src/tests/lib/formatters.test.ts`

```typescript
describe('formatCurrencyBR', () => {
  it('deve formatar valores monetários corretamente', () => {
    expect(formatCurrencyBR(1000)).toBe('R$ 1.000,00');
    expect(formatCurrencyBR(1234.56)).toBe('R$ 1.234,56');
  });
});
```

**Cobertura:**
- ✅ Formatação de moeda (BRL)
- ✅ Formatação de datas
- ✅ Formatação de telefone
- ✅ Formatação de CPF/CNPJ
- ✅ Formatação de CEP
- ✅ Casos extremos e edge cases

### **3. Testes de Validação**

**Arquivo:** `src/tests/lib/validations.test.ts`

```typescript
describe('Schema de Obra', () => {
  it('deve validar obra válida', () => {
    const obraValida = {
      nome: 'Casa Residencial',
      endereco: 'Rua das Flores, 123',
      cidade: 'São Paulo',
      estado: 'SP',
      cep: '01234-567'
    };
    
    const result = obraSchema.safeParse(obraValida);
    expect(result.success).toBe(true);
  });
});
```

**Cobertura:**
- ✅ Schemas Zod para todas as entidades
- ✅ Validação de campos obrigatórios
- ✅ Validação de formatos (email, telefone, etc.)
- ✅ Validação de enums e tipos
- ✅ Casos de erro e validação

### **4. Testes de Integração**

**Arquivo:** `src/tests/integration/edge-functions.test.ts`

```typescript
describe('lead-capture', () => {
  it('deve capturar lead com sucesso', async () => {
    server.use(
      http.post(`${EDGE_FUNCTIONS_URL}/lead-capture`, () => {
        return HttpResponse.json({
          success: true,
          data: { id: 'lead-123', email: '<EMAIL>' }
        });
      })
    );
    
    const response = await callEdgeFunction('lead-capture', leadData);
    expect(response.status).toBe(200);
  });
});
```

**Cobertura:**
- ✅ Edge Functions principais
- ✅ Autenticação e autorização
- ✅ Rate limiting
- ✅ CORS e headers de segurança
- ✅ Tratamento de erros

---

## 🎭 MOCKING E MSW

### **Configuração do MSW**

```typescript
// src/mocks/server.ts
import { setupServer } from 'msw/node';
import { handlers } from './handlers';

export const server = setupServer(...handlers);
```

### **Handlers de API**

```typescript
// src/mocks/handlers.ts
export const handlers = [
  http.post('/auth/v1/signup', () => {
    return HttpResponse.json({
      id: "user-123",
      email: "<EMAIL>"
    });
  }),
  
  http.get('/rest/v1/obras', () => {
    return HttpResponse.json([
      { id: '1', nome: 'Obra Teste' }
    ]);
  })
];
```

---

## 📊 COBERTURA DE TESTES

### **Métricas Atuais**

- **Hooks**: 95% de cobertura
- **Utilitários**: 100% de cobertura  
- **Validações**: 90% de cobertura
- **Edge Functions**: 85% de cobertura

### **Relatório de Cobertura**

```bash
npm run test:coverage
```

Gera relatório em `coverage/index.html`

---

## 🚀 BOAS PRÁTICAS

### **1. Estrutura de Testes**

```typescript
describe('ComponenteOuFuncao', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Funcionalidade específica', () => {
    it('deve fazer algo específico', () => {
      // Arrange
      const input = 'valor de teste';
      
      // Act
      const result = funcaoTeste(input);
      
      // Assert
      expect(result).toBe('resultado esperado');
    });
  });
});
```

### **2. Testes de Hooks**

```typescript
const { result } = renderHook(
  () => useCustomHook(params),
  { wrapper: QueryClientWrapper }
);

await waitFor(() => {
  expect(result.current.data).toBeDefined();
});
```

### **3. Mocking de Dependências**

```typescript
vi.mock('@/services/api', () => ({
  obrasApi: {
    getAll: vi.fn(),
    create: vi.fn()
  }
}));
```

---

## 🔧 TROUBLESHOOTING

### **Problemas Comuns**

1. **Testes assíncronos falhando**
   ```typescript
   // ❌ Incorreto
   it('teste async', () => {
     asyncFunction();
     expect(result).toBe(expected);
   });
   
   // ✅ Correto
   it('teste async', async () => {
     await asyncFunction();
     expect(result).toBe(expected);
   });
   ```

2. **MSW não interceptando**
   ```typescript
   // Verificar se o servidor está iniciado
   beforeAll(() => server.listen());
   afterEach(() => server.resetHandlers());
   afterAll(() => server.close());
   ```

3. **Mocks não funcionando**
   ```typescript
   // Limpar mocks entre testes
   beforeEach(() => {
     vi.clearAllMocks();
   });
   ```

---

## 📈 PRÓXIMOS PASSOS

### **Melhorias Planejadas**

1. **Testes E2E**: Implementar com Playwright
2. **Visual Regression**: Testes de regressão visual
3. **Performance**: Testes de performance
4. **Stress Testing**: Testes de carga

### **Expansão de Cobertura**

1. **Componentes React**: Testes de UI
2. **Fluxos Completos**: Jornadas do usuário
3. **Error Boundaries**: Tratamento de erros
4. **Accessibility**: Cobertura completa WCAG

---

**Equipe ObrasAI**  
_Qualidade e Confiabilidade_
