# Documentação Detalhada: Assistente Pessoal Inteligente no ObrasAI

Este documento detalha as funcionalidades do Assistente Pessoal Inteligente do ObrasAI, um sistema inovador que oferece recomendações personalizadas, insights proativos e orientações baseadas no histórico e padrões de uso do usuário. Este material será utilizado para treinar uma IA que auxiliará os usuários com suporte personalizado e inteligente.

## 1. Visão Geral das Funcionalidades do Assistente Pessoal

O Assistente Pessoal Inteligente do ObrasAI é um diferencial competitivo que transforma a experiência do usuário através de inteligência artificial personalizada. O sistema analisa continuamente o comportamento, preferências e histórico de ações do usuário para oferecer orientações proativas e recomendações precisas. As principais funcionalidades incluem:

*   **Análise Comportamental Avançada:** Sistema que monitora e analisa padrões de uso, decisões tomadas, frequência de acesso a módulos e preferências demonstradas através das ações do usuário.
*   **Recomendações Personalizadas:** Sugestões inteligentes baseadas no histórico de obras, fornecedores utilizados, padrões de despesas e consultas SINAPI realizadas.
*   **Insights Proativos:** Identificação automática de oportunidades de otimização, alertas preventivos e sugestões de melhorias baseadas em dados históricos.
*   **Perfil Dinâmico do Usuário:** Construção automática de um perfil detalhado que evolui com o uso, incluindo preferências, especialidades, padrões de trabalho e áreas de interesse.
*   **Orientações Contextuais:** Suporte inteligente que considera o contexto atual do usuário, projeto em andamento e histórico de decisões similares.
*   **Predições Inteligentes:** Antecipação de necessidades baseada em padrões históricos, sazonalidade e tendências identificadas no comportamento do usuário.
*   **Dashboard Personalizado:** Interface adaptativa que prioriza informações e funcionalidades mais relevantes para cada usuário específico.
*   **Aprendizado Contínuo:** Sistema que melhora suas recomendações continuamente através do feedback implícito e explícito do usuário.

## 2. Como o Assistente Pessoal Funciona (Análise e Recomendações)

### Passo 1: Coleta e Análise de Dados Comportamentais

**Fontes de Dados Analisadas:**
1.  **Analytics Events (`analytics_events`):**
    - Eventos de navegação e uso de funcionalidades
    - Tempo gasto em cada módulo
    - Sequência de ações realizadas
    - Frequência de uso de diferentes recursos

2.  **Histórico de Uso de IA (`ai_usage_tracking`):**
    - Funcionalidades de IA mais utilizadas (chat, orçamento, SINAPI, contratos)
    - Padrões de consulta e frequência de uso
    - Tokens utilizados e complexidade das consultas
    - Evolução do uso ao longo do tempo

3.  **Histórico de Buscas (`historico_buscas_ia`):**
    - Tipos de consultas realizadas
    - Termos de busca mais frequentes
    - Padrões de busca SINAPI
    - Tempo de resposta e satisfação com resultados

### Passo 2: Análise de Padrões de Trabalho

**Dados de Obras e Projetos:**
- **Tipos de obras** mais frequentes (residencial, comercial, industrial)
- **Padrões de orçamento** e faixas de valores trabalhadas
- **Ciclos de projeto** e duração média das obras
- **Sazonalidade** e períodos de maior atividade

**Gestão Financeira:**
- **Padrões de despesas** por categoria e fornecedor
- **Fornecedores preferenciais** e relacionamentos comerciais
- **Análise de lucratividade** e margem de contribuição
- **Controle orçamentário** e desvios históricos

### Passo 3: Construção do Perfil Personalizado

**Categorização Automática:**
- **Especialidade Principal:** Baseada nos tipos de obra mais frequentes
- **Nível de Experiência:** Calculado pela complexidade e volume de projetos
- **Preferências Tecnológicas:** Funcionalidades de IA mais utilizadas
- **Padrões de Trabalho:** Horários, frequência e metodologia preferida

**Métricas de Personalização:**
- **Índice de Eficiência:** Baseado em tempo de conclusão vs. qualidade
- **Preferência por Automação:** Uso de funcionalidades automatizadas
- **Nível de Detalhamento:** Profundidade das análises realizadas
- **Orientação Financeira:** Foco em lucratividade vs. qualidade

### Passo 4: Geração de Recomendações Inteligentes

**Tipos de Recomendações:**

#### **Recomendações de Fornecedores:**
- "Baseado no seu histórico, o fornecedor X oferece melhor custo-benefício para materiais elétricos"
- "Você não trabalha com este fornecedor há 6 meses, considere renegociar condições"
- "Fornecedores similares ao seu padrão estão oferecendo preços 15% menores"

#### **Otimizações de Orçamento:**
- "Seus orçamentos de acabamento estão 20% acima do SINAPI, revise os coeficientes"
- "Considere usar composições SINAPI para reduzir tempo de orçamentação"
- "Baseado em obras similares, adicione 8% de contingência para imprevistos"

#### **Insights de Mercado:**
- "Preços de concreto subiram 12% no último mês, considere compras antecipadas"
- "Seus projetos residenciais têm margem 25% superior à média do mercado"
- "Temporada de alta demanda se aproxima, prepare-se para aumento de custos"

## 3. Arquitetura e Estrutura Técnica

### Stack Tecnológica
- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: shadcn/ui + Tailwind CSS
- **Estado**: TanStack Query para server state
- **Analytics**: Sistema próprio com tracking avançado
- **IA**: Edge Functions com análise de padrões
- **Backend**: Supabase (PostgreSQL + Analytics)
- **Machine Learning**: Análise de padrões comportamentais

### Estrutura de Arquivos
```
src/
├── components/assistente-pessoal/
│   ├── AssistenteDashboard.tsx       # Dashboard personalizado
│   ├── RecomendacoesCard.tsx         # Card de recomendações
│   ├── InsightsProativos.tsx         # Insights automáticos
│   └── PerfilUsuario.tsx             # Perfil dinâmico
├── hooks/
│   ├── useAssistentePessoal.ts       # Hook principal
│   ├── useRecomendacoes.ts           # Recomendações personalizadas
│   └── useInsightsProativos.ts       # Insights automáticos
├── services/
│   └── assistenteApi.ts              # API de análise comportamental
└── types/
    └── assistente.ts                 # Tipos do assistente
```

### Banco de Dados

**Tabelas de Análise Comportamental:**

#### `analytics_events` (Eventos de Uso)
- **Tracking completo**: event_type, user_id, session_id, properties
- **Análise temporal**: timestamp, created_at
- **Contexto**: page, metadata específico
- **Padrões**: Sequência de ações, tempo entre eventos

#### `ai_usage_tracking` (Uso de IA)
- **Funcionalidades**: chat_requests, budget_requests, contract_requests, sinapi_requests
- **Métricas**: total_tokens, date, user_id
- **Análise**: Padrões de uso, evolução temporal, preferências

#### `historico_buscas_ia` (Histórico de Buscas)
- **Consultas**: query_original, query_processada, tipo_busca
- **Performance**: tempo_resposta_ms, resultados_encontrados
- **Contexto**: obra_id, filtros_aplicados, metadata

#### `user_preferences` (Preferências do Usuário)
- **Configurações**: notifications, appearance, security
- **Personalização**: language, devices, custom_settings
- **Evolução**: created_at, updated_at, histórico de mudanças

**Views Especializadas:**

#### `v_perfil_usuario_completo`
- **Agregação**: Dados de múltiplas tabelas para perfil completo
- **Métricas**: Estatísticas de uso, padrões comportamentais
- **Insights**: Preferências inferidas, especialidades identificadas

#### `v_recomendacoes_personalizadas`
- **Análise**: Padrões de fornecedores, despesas, orçamentos
- **Sugestões**: Baseadas em similaridade e histórico
- **Contexto**: Obra atual, sazonalidade, tendências

## 4. Funcionalidades Principais

### 4.1 Dashboard Personalizado (`AssistenteDashboard.tsx`)

**Características:**
- **Layout Adaptativo**: Interface que se adapta ao perfil e preferências do usuário
- **Widgets Inteligentes**: Componentes que mostram informações mais relevantes
- **Ações Sugeridas**: Botões e links para ações recomendadas
- **Métricas Personalizadas**: KPIs específicos para o padrão de trabalho do usuário

**Seções Dinâmicas:**
- **Resumo Inteligente**: Visão geral adaptada ao contexto atual
- **Próximas Ações**: Sugestões baseadas em padrões e prazos
- **Alertas Personalizados**: Notificações relevantes para o usuário
- **Insights do Dia**: Descobertas baseadas em análise recente

### 4.2 Sistema de Recomendações (`RecomendacoesCard.tsx`)

**Tipos de Recomendações:**

#### **Fornecedores Inteligentes:**
- Sugestões baseadas em histórico de compras
- Análise de performance e custo-benefício
- Alertas de oportunidades de negociação
- Comparação com padrões de mercado

#### **Otimização de Processos:**
- Sugestões de automação baseadas no uso
- Recomendações de funcionalidades não exploradas
- Otimizações de fluxo de trabalho
- Integração entre módulos

#### **Insights Financeiros:**
- Análise de lucratividade por tipo de projeto
- Sugestões de precificação baseadas em histórico
- Alertas de desvios orçamentários
- Oportunidades de redução de custos

### 4.3 Insights Proativos (`InsightsProativos.tsx`)

**Análises Automáticas:**
- **Padrões Sazonais**: Identificação de tendências temporais
- **Anomalias**: Detecção de comportamentos atípicos
- **Oportunidades**: Identificação de melhorias potenciais
- **Riscos**: Alertas preventivos baseados em padrões

**Tipos de Insights:**
- **Operacionais**: Eficiência de processos e fluxos
- **Financeiros**: Lucratividade e controle de custos
- **Estratégicos**: Oportunidades de crescimento
- **Técnicos**: Otimizações de uso do sistema

## 5. APIs e Serviços

### Hook Principal (`useAssistentePessoal.ts`)

**Hooks Disponíveis:**

#### `usePerfilUsuario()`
- **Propósito**: Análise completa do perfil e comportamento do usuário
- **Dados**: Agregação de analytics, preferências, histórico de uso
- **Atualização**: Tempo real com recálculo automático
- **Cache**: Otimizado para performance com invalidação inteligente

#### `useRecomendacoes(contexto?)`
- **Propósito**: Geração de recomendações personalizadas
- **Parâmetros**: Contexto opcional (obra, módulo, ação)
- **Algoritmo**: Machine learning baseado em padrões históricos
- **Tipos**: Fornecedores, processos, otimizações, insights

#### `useInsightsProativos()`
- **Propósito**: Identificação automática de oportunidades e riscos
- **Análise**: Padrões temporais, anomalias, tendências
- **Alertas**: Notificações preventivas e sugestões proativas
- **Aprendizado**: Melhoria contínua baseada em feedback

#### `useDashboardPersonalizado()`
- **Propósito**: Configuração dinâmica da interface
- **Adaptação**: Layout baseado em preferências e uso
- **Widgets**: Componentes relevantes para o perfil do usuário
- **Performance**: Carregamento otimizado de dados prioritários

### API Service (`assistenteApi.ts`)

**Funções Principais:**

#### `analisarComportamentoUsuario(userId, periodo?)`
```typescript
interface AnaliseComportamental {
  perfil: {
    especialidade: string;
    nivel_experiencia: number;
    preferencias_tecnologicas: string[];
    padroes_trabalho: {
      horarios_ativos: string[];
      frequencia_uso: number;
      modulos_preferidos: string[];
    };
  };
  metricas: {
    eficiencia: number;
    automacao: number;
    detalhamento: number;
    orientacao_financeira: number;
  };
  tendencias: {
    crescimento_uso: number;
    evolucao_complexidade: number;
    diversificacao_atividades: number;
  };
}
```

#### `gerarRecomendacoes(userId, contexto?)`
- **Análise**: Histórico de decisões e padrões identificados
- **Algoritmo**: Similaridade com usuários de perfil similar
- **Contexto**: Situação atual e objetivos inferidos
- **Personalização**: Adaptação ao estilo e preferências

#### `identificarInsights(userId, tipo?)`
- **Tipos**: 'oportunidades', 'riscos', 'otimizacoes', 'tendencias'
- **Análise**: Dados históricos vs. padrões de mercado
- **Priorização**: Baseada em impacto potencial e relevância
- **Acionabilidade**: Insights com ações concretas sugeridas

#### `atualizarPerfilDinamico(userId, novaAcao)`
- **Aprendizado**: Incorporação de nova ação ao perfil
- **Recálculo**: Atualização de métricas e preferências
- **Evolução**: Adaptação contínua do perfil do usuário
- **Feedback**: Melhoria das recomendações futuras

## 6. Estrutura de Dados e Validações

### Schemas de Validação (Zod)

#### `perfilUsuarioSchema`
```typescript
const perfilUsuarioSchema = z.object({
  user_id: z.string().uuid(),
  especialidade: z.enum(['residencial', 'comercial', 'industrial', 'mista']),
  nivel_experiencia: z.number().min(0).max(100),
  preferencias_tecnologicas: z.array(z.string()),
  padroes_trabalho: z.object({
    horarios_ativos: z.array(z.string()),
    frequencia_uso: z.number().min(0),
    modulos_preferidos: z.array(z.string())
  }),
  metricas_personalizacao: z.object({
    eficiencia: z.number().min(0).max(100),
    automacao: z.number().min(0).max(100),
    detalhamento: z.number().min(0).max(100),
    orientacao_financeira: z.number().min(0).max(100)
  }),
  ultima_atualizacao: z.date()
});
```

#### `recomendacaoSchema`
```typescript
const recomendacaoSchema = z.object({
  id: z.string().uuid(),
  user_id: z.string().uuid(),
  tipo: z.enum(['fornecedor', 'processo', 'financeiro', 'tecnico']),
  titulo: z.string().min(5),
  descricao: z.string().min(20),
  impacto_estimado: z.number().min(0).max(100),
  confianca: z.number().min(0).max(100),
  acao_sugerida: z.string(),
  contexto: z.object({
    obra_id: z.string().uuid().optional(),
    modulo: z.string().optional(),
    categoria: z.string().optional()
  }),
  metadata: z.record(z.unknown()),
  created_at: z.date()
});
```

### Algoritmos de Personalização

**Análise de Similaridade:**
- **Clustering**: Agrupamento de usuários por padrões similares
- **Collaborative Filtering**: Recomendações baseadas em usuários similares
- **Content-Based**: Sugestões baseadas no histórico individual
- **Hybrid Approach**: Combinação de múltiplas técnicas

**Métricas de Relevância:**
- **Frequência de Uso**: Peso baseado na utilização histórica
- **Contexto Temporal**: Relevância baseada em sazonalidade
- **Feedback Implícito**: Ações do usuário como indicador de satisfação
- **Impacto Potencial**: Benefício estimado da recomendação

## 7. Integração com Outros Módulos

### 7.1 Integração com Analytics

**Coleta de Dados:**
- **Eventos de Navegação**: Páginas visitadas, tempo de permanência
- **Ações Realizadas**: Cliques, formulários preenchidos, downloads
- **Uso de Funcionalidades**: Frequência e padrões de uso
- **Performance**: Tempo de resposta e satisfação do usuário

**Análise Comportamental:**
- **Jornada do Usuário**: Mapeamento de fluxos de navegação
- **Pontos de Fricção**: Identificação de dificuldades
- **Preferências**: Inferência baseada em comportamento
- **Evolução**: Mudanças de padrão ao longo do tempo

### 7.2 Integração com Obras

**Análise de Projetos:**
- **Tipos de Obra**: Categorização automática baseada em dados
- **Padrões de Orçamento**: Análise de faixas de valores
- **Ciclos de Projeto**: Duração e fases típicas
- **Sazonalidade**: Períodos de maior atividade

**Recomendações Contextuais:**
- **Fornecedores**: Sugestões baseadas no tipo de obra
- **Materiais**: Recomendações de especificações
- **Cronograma**: Otimizações baseadas em histórico
- **Orçamento**: Ajustes baseados em projetos similares

### 7.3 Integração com Fornecedores

**Análise de Relacionamentos:**
- **Performance Histórica**: Avaliação de fornecedores utilizados
- **Padrões de Compra**: Frequência e valores por categoria
- **Negociações**: Histórico de condições obtidas
- **Satisfação**: Inferência baseada em reuso

**Recomendações Inteligentes:**
- **Novos Fornecedores**: Sugestões baseadas em perfil similar
- **Renegociações**: Alertas de oportunidades
- **Diversificação**: Sugestões para reduzir dependência
- **Otimização**: Melhorias de custo-benefício

### 7.4 Integração com SINAPI

**Análise de Consultas:**
- **Padrões de Busca**: Tipos de códigos mais consultados
- **Frequência**: Regularidade de uso do sistema
- **Contexto**: Relação com projetos em andamento
- **Eficiência**: Tempo gasto vs. resultados obtidos

**Otimizações Sugeridas:**
- **Códigos Favoritos**: Sugestões baseadas em uso frequente
- **Busca Inteligente**: Melhorias nos termos de pesquisa
- **Automação**: Integração com orçamentos automáticos
- **Alertas**: Notificações de atualizações relevantes

## 8. Orientações para Treinamento de IA

### 8.1 Cenários de Uso Comum

**Solicitação de Recomendações Gerais:**
- **Pergunta**: "O que você recomenda para melhorar minha eficiência?"
- **Resposta**: Analisar perfil do usuário, identificar gargalos, sugerir otimizações
- **Contexto**: Considerar histórico de uso e padrões identificados

**Análise de Padrões Pessoais:**
- **Pergunta**: "Como está minha performance comparada ao mês passado?"
- **Resposta**: Comparar métricas atuais vs. históricas, identificar tendências
- **Insights**: Destacar melhorias e áreas de atenção

**Sugestões Contextuais:**
- **Pergunta**: "Que fornecedor devo usar para esta obra residencial?"
- **Resposta**: Analisar histórico de fornecedores para projetos similares
- **Recomendação**: Sugerir baseado em performance e custo-benefício

**Otimizações de Processo:**
- **Pergunta**: "Como posso automatizar mais meu trabalho?"
- **Resposta**: Identificar tarefas repetitivas, sugerir funcionalidades não utilizadas
- **Orientação**: Passo a passo para implementar automações

### 8.2 Dicas para Orientação de Usuários

**Para Maximizar Personalização:**
- **Uso Consistente**: Orientar sobre importância do uso regular
- **Feedback**: Explicar como ações influenciam recomendações
- **Exploração**: Incentivar teste de novas funcionalidades
- **Configuração**: Ajustar preferências para melhor experiência

**Para Interpretação de Insights:**
- **Contexto**: Sempre explicar base dos insights gerados
- **Acionabilidade**: Focar em recomendações práticas e implementáveis
- **Priorização**: Orientar sobre quais insights são mais importantes
- **Acompanhamento**: Sugerir monitoramento de resultados

**Para Evolução do Perfil:**
- **Paciência**: Explicar que personalização melhora com o tempo
- **Diversidade**: Incentivar uso de diferentes módulos
- **Atualização**: Manter dados e preferências atualizados
- **Feedback**: Importância de avaliar recomendações recebidas

### 8.3 Troubleshooting Comum

**Problema: "As recomendações não são relevantes"**
- **Diagnóstico**: Verificar se há dados suficientes para análise
- **Solução**: Orientar sobre uso mais diversificado do sistema
- **Tempo**: Explicar que personalização melhora com mais dados

**Problema: "Não vejo insights úteis"**
- **Verificação**: Confirmar se preferências estão configuradas
- **Orientação**: Explicar tipos de insights disponíveis
- **Configuração**: Ajustar filtros e categorias de interesse

**Problema: "Sugestões repetitivas"**
- **Análise**: Verificar se usuário está explorando novas funcionalidades
- **Diversificação**: Incentivar uso de módulos diferentes
- **Feedback**: Orientar sobre como indicar satisfação/insatisfação

**Problema: "Dashboard não mostra informações relevantes"**
- **Personalização**: Verificar configurações de widgets
- **Preferências**: Ajustar prioridades e áreas de interesse
- **Contexto**: Confirmar se obra/projeto atual está selecionado

### 8.4 Boas Práticas

**Para Melhor Experiência:**
- **Uso Regular**: Acessar sistema consistentemente
- **Exploração**: Testar diferentes funcionalidades
- **Feedback**: Avaliar recomendações recebidas
- **Atualização**: Manter perfil e preferências atualizados

**Para Insights Mais Precisos:**
- **Dados Completos**: Preencher informações detalhadas
- **Categorização**: Usar tags e categorias consistentemente
- **Histórico**: Manter registro completo de atividades
- **Contexto**: Associar ações a projetos específicos

**Para Recomendações Relevantes:**
- **Diversidade**: Usar diferentes módulos do sistema
- **Consistência**: Manter padrões de nomenclatura
- **Feedback**: Indicar satisfação com sugestões
- **Evolução**: Permitir que perfil evolua naturalmente

## 9. Funcionalidades Técnicas Avançadas

### 9.1 Machine Learning e Análise Preditiva

**Algoritmos Utilizados:**
- **Clustering**: K-means para agrupamento de usuários similares
- **Collaborative Filtering**: Recomendações baseadas em similaridade
- **Time Series Analysis**: Análise de padrões temporais
- **Anomaly Detection**: Identificação de comportamentos atípicos

**Modelos Preditivos:**
- **Churn Prediction**: Identificação de risco de abandono
- **Usage Forecasting**: Previsão de uso futuro
- **Preference Evolution**: Evolução de preferências
- **Success Probability**: Probabilidade de sucesso de recomendações

### 9.2 Performance e Otimização

**Cache Inteligente:**
- **Perfil do Usuário**: Cache de 1 hora com invalidação por ação
- **Recomendações**: Cache de 30 minutos com refresh automático
- **Analytics**: Cache de 15 minutos para dados em tempo real
- **Insights**: Cache de 2 horas com recálculo sob demanda

**Otimizações de Consulta:**
- **Índices Especializados**: Otimização para queries de análise
- **Agregações Pré-calculadas**: Views materializadas para métricas
- **Particionamento**: Dados históricos particionados por período
- **Compressão**: Otimização de armazenamento para dados antigos

### 9.3 Privacidade e Segurança

**Proteção de Dados:**
- **Anonimização**: Dados sensíveis anonimizados para análise
- **Criptografia**: Dados pessoais criptografados em repouso
- **Acesso Controlado**: Políticas RLS rigorosas
- **Auditoria**: Log completo de acesso a dados pessoais

**Compliance:**
- **LGPD**: Conformidade com Lei Geral de Proteção de Dados
- **Consentimento**: Controle granular de uso de dados
- **Portabilidade**: Exportação de dados pessoais
- **Esquecimento**: Remoção completa de dados quando solicitado
