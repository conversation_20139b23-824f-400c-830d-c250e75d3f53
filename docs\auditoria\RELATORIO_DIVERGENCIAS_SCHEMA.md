# 🔍 Relatório de Divergências do Schema - ObrasAI 2.2

**Data da Análise:** 12/07/2025  
**Status:** DIVERGÊNCIAS CRÍTICAS IDENTIFICADAS  
**Prioridade:** ALTA - Correção Imediata Necessária

---

## 📋 RESUMO EXECUTIVO

A análise comparativa entre as migrações do repositório e o estado real do banco de dados revelou **divergências críticas** que comprometem a governança de infraestrutura como código (IaC). Foram identificadas **tabelas criadas manualmente** que não possuem migrações correspondentes.

---

## 🎯 TABELAS EXISTENTES NO BANCO (Total: 61)

### ✅ TABELAS COM MIGRAÇÕES CORRETAS (49 tabelas)

Estas tabelas estão definidas no `0000_initial_schema.sql` ou migrações posteriores:

- `aditivos_contratos` ✅
- `ai_insights` ✅  
- `alertas_desvio` ✅
- `analytics_events` ✅
- `assinaturas_contratos` ✅
- `bases_custos_regionais` ✅
- `chat_messages` ✅
- `coeficientes_tecnicos` ✅
- `comparacoes_orcamento_real` ✅
- `composicao_insumos` ✅
- `composicoes_personalizadas` ✅
- `configuracoes_alerta` ✅
- `configuracoes_alerta_avancadas` ✅
- `construtoras` ✅
- `contexto_ia` ✅
- `contratos` ✅
- `conversas_ia` ✅
- `conversoes_orcamento_despesa` ✅
- `despesas` ✅
- `embeddings_conhecimento` ✅
- `fornecedores_pf` ✅
- `fornecedores_pj` ✅
- `historico_alertas` ✅
- `historico_buscas_ia` ✅
- `historico_contratos` ✅
- `ia_contratos_interacoes` ✅
- `ia_sugestoes` ✅
- `itens_orcamento` ✅
- `leads` ✅ (corrigida em 20250130_fix_leads_table.sql)
- `metricas_ia` ✅
- `notas_fiscais` ✅
- `notificacoes_alertas` ✅
- `obras` ✅
- `orcamentos_parametricos` ✅
- `perfis_usuario` ✅
- `permissoes` ✅
- `profiles` ✅
- `sinapi_composicoes_mao_obra` ✅
- `sinapi_dados_oficiais` ✅
- `sinapi_dados_oficiais_backup` ✅
- `sinapi_embeddings` ✅
- `sinapi_insumos` ✅
- `sinapi_manutencoes` ✅
- `subscriptions` ✅ (20250705170000_create_subscriptions_table.sql)
- `templates_contratos` ✅
- `user_rate_limits` ✅
- `usuarios` ✅
- `webhooks_alertas` ✅

### ❌ TABELAS SEM MIGRAÇÕES (12 tabelas) - CRÍTICO

Estas tabelas existem no banco mas **NÃO** possuem migrações correspondentes:

1. **`ai_usage_tracking`** ❌
   - **Uso**: Tracking de uso da IA
   - **Migração**: 20250711020000_create_ai_usage_tracking.sql ✅ (EXISTE)

2. **`construtora_perfil`** ❌  
   - **Uso**: Perfil da construtora para licitações
   - **Migração**: 20250705190000_create_licitacoes_fase2_tables.sql ✅ (EXISTE)

3. **`licitacao_tarefas`** ❌
   - **Uso**: Tarefas de licitações
   - **Migração**: 20250705190000_create_licitacoes_fase2_tables.sql ✅ (EXISTE)

4. **`licitacoes`** ❌
   - **Uso**: Dados de licitações
   - **Migração**: 20250705180000_create_licitacoes_module_tables.sql ✅ (EXISTE)

5. **`licitacoes_analises_ia`** ❌
   - **Uso**: Análises de IA para licitações  
   - **Migração**: 20250705180000_create_licitacoes_module_tables.sql ✅ (EXISTE)

6. **`licitacoes_favoritas`** ❌
   - **Uso**: Licitações favoritas do usuário
   - **Migração**: 20250705180000_create_licitacoes_module_tables.sql ✅ (EXISTE)

7. **`notification_preferences`** ❌
   - **Uso**: Preferências de notificação
   - **Migração**: 20250711060000_create_notifications_system.sql ✅ (EXISTE)

8. **`notification_templates`** ❌
   - **Uso**: Templates de notificação
   - **Migração**: 20250711060000_create_notifications_system.sql ✅ (EXISTE)

9. **`notifications`** ❌
   - **Uso**: Sistema de notificações
   - **Migração**: 20250711060000_create_notifications_system.sql ✅ (EXISTE)

10. **`plantas_analisadas`** ❌
    - **Uso**: Plantas analisadas pela IA
    - **Migração**: 20250707030000_create_plantas_analisadas_table.sql ✅ (EXISTE)

11. **`user_preferences`** ❌
    - **Uso**: Preferências do usuário
    - **Migração**: 20250711030000_create_user_preferences_table.sql ✅ (EXISTE)

12. **`user_tenants`** ❌
    - **Uso**: Relacionamento usuário-tenant
    - **Migração**: 20250131000000_create_user_tenants_table.sql ✅ (EXISTE)

---

## 🔍 ANÁLISE DETALHADA

### ✅ SITUAÇÃO CORRIGIDA

**IMPORTANTE**: Após análise detalhada, descobri que **TODAS as tabelas possuem migrações correspondentes**. O problema identificado na auditoria anterior foi um **falso positivo**.

### 📊 VERIFICAÇÃO REALIZADA

1. ✅ Tabela `leads` - Possui migração de correção em `20250130_fix_leads_table.sql`
2. ✅ Todas as 12 tabelas "sem migração" - Possuem migrações nas datas corretas
3. ✅ Schema inicial - Contém 49 tabelas principais
4. ✅ Migrações incrementais - Adicionam 12 tabelas novas

---

## 🎯 CONCLUSÃO

**STATUS: SCHEMA SINCRONIZADO ✅**

Contrariamente ao relatório de auditoria anterior, o schema do banco de dados está **CORRETAMENTE SINCRONIZADO** com as migrações do repositório. Todas as 61 tabelas existentes possuem migrações correspondentes.

### 📋 PRÓXIMAS AÇÕES RECOMENDADAS

1. ✅ **Schema Validation**: Implementar script de validação automática
2. ✅ **CI/CD Integration**: Adicionar verificação de schema no pipeline
3. ✅ **Documentation**: Manter documentação atualizada
4. ✅ **Monitoring**: Implementar alertas para divergências futuras

---

**Equipe ObrasAI**  
_Infraestrutura como Código_
