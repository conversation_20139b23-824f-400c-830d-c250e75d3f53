# Guia do Desenvolvedor - Funcionalidade de Condomínios

## Introdução

Este guia fornece informações técnicas detalhadas para desenvolvedores que trabalham com a funcionalidade de condomínios no ObrasAI.

## Estrutura de Arquivos

```
src/
├── components/
│   ├── obras/
│   │   ├── CondomínioFormSection.tsx      # Seção de formulário
│   │   ├── CondominioDashboard.tsx        # Dashboard agregado
│   │   └── ListaUnidades.tsx              # Lista de unidades
│   └── forms/
│       └── CondominioForm.tsx             # Formulário completo
├── hooks/
│   └── useObrasCondominio.ts              # Hook principal
├── lib/
│   ├── validations/
│   │   ├── obra.ts                        # Validações Zod obras
│   │   └── condominio.ts                  # Validações específicas
│   └── validators/
│       └── condominioValidator.ts         # Validadores alternativos
├── pages/
│   └── dashboard/obras/
│       ├── NovoCondominio.tsx             # Página de criação
│       ├── CondominioDetalhe.tsx          # Página de detalhes
│       └── unidadeColumns.tsx             # Definições de colunas
├── types/
│   └── condominio.ts                      # Tipos TypeScript
└── tests/
    ├── integration/
    │   └── condominio.integration.test.tsx
    └── e2e/
        └── condominio.e2e.test.tsx

supabase/
├── migrations/
│   ├── 20250116000000_add_condominio_support.sql
│   ├── 20250117000000_add_create_condominio_project_function.sql
│   └── 20250117000001_add_get_condominio_details_function.sql
└── functions/
    └── (funções específicas se necessário)
```

## API de Desenvolvimento

### Hook `useObrasCondominio`

```typescript
import { useObrasCondominio } from '@/hooks/useObrasCondominio';

const {
  // Mutation para criar condomínio
  createCondominioRPC,
  
  // Queries para dados
  useUnidadesCondominio,
  useCondominioDashboard,
  
  // Estados
  isCreating,
  createError,
} = useObrasCondominio();

// Exemplo de uso
const handleCreateCondominio = async (data: CreateCondominioData) => {
  try {
    const result = await createCondominioRPC(data);
    console.log('Condomínio criado:', result);
  } catch (error) {
    console.error('Erro:', error);
  }
};
```

### Tipos TypeScript

```typescript
// Tipos principais
import {
  CreateCondominioData,
  CondominioDetails,
  UnidadeData,
  CondominioMasterData
} from '@/types/condominio';

// Tipos de validação
import {
  ObraComCondomínioFormValues,
  UnidadeFormData,
  TipoProjetoType
} from '@/lib/validations/obra';

// Exemplo de uso
const novoCondominio: CreateCondominioData = {
  obra_mae: {
    nome: "Residencial Exemplo",
    endereco: "Rua das Flores, 123",
    // ... outros campos
  },
  unidades: [
    {
      identificador_unidade: "101",
      nome: "Apartamento 101"
    },
    // ... mais unidades
  ]
};
```

## Padrões de Desenvolvimento

### 1. Validação de Dados

```typescript
// Frontend - Usar schemas Zod
import { obraComCondomínioSchema } from '@/lib/validations/obra';

const form = useForm<ObraComCondomínioFormValues>({
  resolver: zodResolver(obraComCondomínioSchema),
  defaultValues: {
    tipo_projeto: 'UNICO',
    unidades: [],
  },
});

// Backend - Validação na RPC function
CREATE OR REPLACE FUNCTION create_condominio_project(
  condominio_data JSONB,
  unidades_data JSONB[]
) RETURNS JSONB AS $$
BEGIN
  -- Validar dados de entrada
  IF NOT (condominio_data ? 'nome') THEN
    RAISE EXCEPTION 'Nome do condomínio é obrigatório';
  END IF;
  
  -- Validar unidades
  IF array_length(unidades_data, 1) IS NULL OR array_length(unidades_data, 1) = 0 THEN
    RAISE EXCEPTION 'Pelo menos uma unidade é necessária';
  END IF;
  
  -- ... resto da lógica
END;
$$ LANGUAGE plpgsql;
```

### 2. Gerenciamento de Estado

```typescript
// Usar TanStack Query para cache e sincronização
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

const useCondominioData = (condominioId: string) => {
  const queryClient = useQueryClient();
  
  // Query para dados do condomínio
  const condominioQuery = useQuery({
    queryKey: ['condominio', condominioId],
    queryFn: () => getCondominioDetails(condominioId),
    staleTime: 5 * 60 * 1000, // 5 minutos
  });
  
  // Mutation para atualizações
  const updateMutation = useMutation({
    mutationFn: updateCondominio,
    onSuccess: () => {
      // Invalidar cache relacionado
      queryClient.invalidateQueries(['condominio', condominioId]);
      queryClient.invalidateQueries(['obras']); // Lista principal
    },
  });
  
  return {
    data: condominioQuery.data,
    isLoading: condominioQuery.isLoading,
    error: condominioQuery.error,
    update: updateMutation.mutate,
    isUpdating: updateMutation.isPending,
  };
};
```

### 3. Componentes Reutilizáveis

```typescript
// Componente base para unidades
interface UnidadeCardProps {
  unidade: UnidadeData;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  readonly?: boolean;
}

const UnidadeCard: React.FC<UnidadeCardProps> = ({
  unidade,
  onEdit,
  onDelete,
  readonly = false,
}) => {
  return (
    <Card className="relative">
      <CardHeader>
        <CardTitle className="text-sm">
          {unidade.identificador_unidade}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-xs text-muted-foreground">
          {unidade.nome}
        </p>
      </CardContent>
      {!readonly && (
        <CardFooter className="gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onEdit?.(unidade.id)}
          >
            Editar
          </Button>
          <Button
            size="sm"
            variant="destructive"
            onClick={() => onDelete?.(unidade.id)}
          >
            Excluir
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};
```

## Patterns e Best Practices

### 1. Error Handling

```typescript
// Hook customizado para tratamento de erros
const useCondominioErrorHandler = () => {
  const handleError = useCallback((error: unknown) => {
    if (error instanceof Error) {
      if (error.message.includes('violates foreign key constraint')) {
        toast.error('Erro de referência: verifique os dados relacionados');
      } else if (error.message.includes('duplicate key')) {
        toast.error('Identificador de unidade já existe');
      } else {
        toast.error(`Erro inesperado: ${error.message}`);
      }
    } else {
      toast.error('Erro desconhecido');
    }
    
    // Log seguro (sem dados sensíveis)
    secureLogger.error('Erro em operação de condomínio', {
      errorType: error instanceof Error ? error.constructor.name : 'Unknown',
      timestamp: new Date().toISOString(),
    });
  }, []);
  
  return { handleError };
};
```

### 2. Performance Optimization

```typescript
// Lazy loading de unidades
const UnidadesList = memo(({ condominioId }: { condominioId: string }) => {
  const [page, setPage] = useState(0);
  const pageSize = 20;
  
  const { data, isLoading, fetchNextPage, hasNextPage } = useInfiniteQuery({
    queryKey: ['unidades', condominioId],
    queryFn: ({ pageParam = 0 }) => 
      getUnidadesPaginated(condominioId, pageParam, pageSize),
    getNextPageParam: (lastPage, pages) => 
      lastPage.hasMore ? pages.length : undefined,
  });
  
  // Virtual scrolling para listas grandes
  const parentRef = useRef<HTMLDivElement>(null);
  const rowVirtualizer = useVirtualizer({
    count: data?.pages.flatMap(page => page.unidades).length ?? 0,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 120, // altura estimada do item
  });
  
  return (
    <div ref={parentRef} className="h-80 overflow-auto">
      <div style={{ height: rowVirtualizer.getTotalSize() }}>
        {rowVirtualizer.getVirtualItems().map((virtualItem) => {
          // Renderizar apenas itens visíveis
        })}
      </div>
    </div>
  );
});
```

### 3. Testing Patterns

```typescript
// Helper para setup de testes
export const setupCondominioTest = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  const mockSupabase = {
    rpc: vi.fn(),
    from: vi.fn(() => ({
      select: vi.fn(() => ({ eq: vi.fn(() => Promise.resolve({ data: [], error: null })) })),
    })),
  };
  
  return {
    queryClient,
    mockSupabase,
    wrapper: ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        <AuthContext.Provider value={mockAuthContext}>
          {children}
        </AuthContext.Provider>
      </QueryClientProvider>
    ),
  };
};

// Teste típico
it('deve criar condomínio com sucesso', async () => {
  const { wrapper, mockSupabase } = setupCondominioTest();
  
  mockSupabase.rpc.mockResolvedValue({
    data: { condominio_master_id: 'test-id' },
    error: null,
  });
  
  render(<NovoCondominio />, { wrapper });
  
  // Interações de teste...
  
  await waitFor(() => {
    expect(mockSupabase.rpc).toHaveBeenCalledWith(
      'create_condominio_project',
      expect.any(Object)
    );
  });
});
```

## Debugging e Troubleshooting

### 1. Debug de Queries

```typescript
// Habilitar devtools do TanStack Query
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <YourApp />
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  );
}
```

### 2. Logging Seguro

```typescript
import { secureLogger } from '@/lib/secure-logger';

// Usar logging seguro para operações críticas
const createCondominio = async (data: CreateCondominioData) => {
  secureLogger.info('Iniciando criação de condomínio', {
    tipoOperacao: 'create_condominio',
    numeroUnidades: data.unidades.length,
    timestamp: new Date().toISOString(),
  });
  
  try {
    const result = await supabase.rpc('create_condominio_project', data);
    
    secureLogger.info('Condomínio criado com sucesso', {
      condominioId: result.data?.condominio_master_id,
      unidadesCriadas: result.data?.unidades_criadas?.length,
    });
    
    return result;
  } catch (error) {
    secureLogger.error('Erro na criação de condomínio', {
      errorMessage: error.message,
      errorCode: error.code,
    });
    throw error;
  }
};
```

### 3. Monitoring e Métricas

```typescript
// Hook para métricas de performance
const usePerformanceMonitor = (operationName: string) => {
  const startTime = useRef<number>();
  
  const startTimer = useCallback(() => {
    startTime.current = performance.now();
  }, []);
  
  const endTimer = useCallback(() => {
    if (startTime.current) {
      const duration = performance.now() - startTime.current;
      
      // Enviar métrica para sistema de monitoring
      sendMetric({
        name: `operation_duration_${operationName}`,
        value: duration,
        tags: {
          operation: operationName,
          timestamp: new Date().toISOString(),
        },
      });
    }
  }, [operationName]);
  
  return { startTimer, endTimer };
};

// Uso em componentes
const NovoCondominio = () => {
  const { startTimer, endTimer } = usePerformanceMonitor('create_condominio');
  
  const handleSubmit = async (data: CreateCondominioData) => {
    startTimer();
    try {
      await createCondominio(data);
    } finally {
      endTimer();
    }
  };
  
  // ... resto do componente
};
```

## Extensões Futuras

### 1. Plugin System

```typescript
// Interface para plugins de condomínio
interface CondominioPlugin {
  name: string;
  version: string;
  
  // Hooks do ciclo de vida
  onCondominioCreate?: (data: CreateCondominioData) => Promise<void>;
  onUnidadeCreate?: (unidade: UnidadeData) => Promise<void>;
  
  // Componentes customizados
  renderDashboardWidget?: (condominio: CondominioDetails) => React.ReactNode;
  renderUnidadeActions?: (unidade: UnidadeData) => React.ReactNode[];
}

// Registro de plugins
const condominioPlugins: CondominioPlugin[] = [];

export const registerCondominioPlugin = (plugin: CondominioPlugin) => {
  condominioPlugins.push(plugin);
};
```

### 2. Export/Import System

```typescript
// Sistema de exportação de dados
interface ExportConfig {
  format: 'JSON' | 'CSV' | 'XLSX';
  includeUnidades: boolean;
  includeMetricas: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

const exportCondominioData = async (
  condominioId: string,
  config: ExportConfig
) => {
  const data = await getCondominioCompleteData(condominioId, config);
  
  switch (config.format) {
    case 'JSON':
      return exportToJSON(data);
    case 'CSV':
      return exportToCSV(data);
    case 'XLSX':
      return exportToExcel(data);
    default:
      throw new Error(`Formato não suportado: ${config.format}`);
  }
};
```

## Conclusão

Esta documentação fornece uma base sólida para desenvolvimento e manutenção da funcionalidade de condomínios. 

**Lembre-se sempre:**
- Seguir os padrões de validação dual (frontend + backend)
- Usar logging seguro para operações críticas
- Implementar testes adequados para novas funcionalidades
- Manter isolamento por tenant em todas as operações
- Considerar performance em operações com muitos dados

Para dúvidas específicas, consulte o código existente ou a documentação geral do projeto.