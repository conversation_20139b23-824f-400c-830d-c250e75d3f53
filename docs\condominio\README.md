# Documentação - Funcionalidade de Condomínios

## Visão Geral

A funcionalidade de Condomínios no ObrasAI permite que construtores gerenciem projetos complexos de condomínios através de uma estrutura hierárquica onde um projeto de condomínio (obra-mãe) pode conter múltiplas unidades ou casas (obras-filhas).

## Arquitetura

### Estrutura Hierárquica

```
CONDOMINIO_MASTER (parent_obra_id: NULL)
├── UNIDADE_CONDOMINIO (parent_obra_id: condominio_id)
├── UNIDADE_CONDOMINIO (parent_obra_id: condominio_id)
└── UNIDADE_CONDOMINIO (parent_obra_id: condominio_id)
```

### Tipos de Projeto

- **UNICO**: Obra única tradicional (default)
- **CONDOMINIO_MASTER**: Projeto principal de condomínio
- **UNIDADE_CONDOMINIO**: Unidade individual dentro de um condomínio

## Base de Dados

### Extensões na Tabela `obras`

| Campo | Tipo | Descrição |
|-------|------|-----------|
| `parent_obra_id` | UUID (nullable) | FK para obra pai (condomínio) |
| `tipo_projeto` | ENUM | Tipo do projeto (UNICO, CONDOMINIO_MASTER, UNIDADE_CONDOMINIO) |
| `identificador_unidade` | TEXT (nullable) | Identificador único da unidade |
| `tipo_condominio` | ENUM (nullable) | VERTICAL ou HORIZONTAL |
| `numero_blocos` | INTEGER (nullable) | Número de blocos |
| `andares_por_bloco` | INTEGER (nullable) | Andares por bloco |
| `area_lote` | DECIMAL (nullable) | Área do lote |

### Funções RPC Implementadas

#### `create_condominio_project(condominio_data, unidades_data)`
Cria um condomínio com todas as suas unidades de forma atômica.

**Parâmetros:**
- `condominio_data`: JSONB com dados da obra-mãe
- `unidades_data`: Array JSONB com dados das unidades

**Retorna:**
```json
{
  "condominio_master_id": "uuid",
  "unidades_criadas": [
    {"id": "uuid", "identificador_unidade": "string"}
  ]
}
```

#### `get_condominio_details(condominio_id)`
Busca detalhes completos do condomínio incluindo unidades.

#### `get_condominio_dashboard(condominio_id)`
Retorna métricas agregadas do condomínio para dashboard.

## Frontend - Componentes

### Hook Principal: `useObrasCondominio`

```typescript
const {
  createCondominioRPC,
  useUnidadesCondominio,
  useCondominioDashboard
} = useObrasCondominio();
```

**Métodos disponíveis:**
- `createCondominioRPC(data)`: Cria condomínio via RPC
- `useUnidadesCondominio(condominioId)`: Query para buscar unidades
- `useCondominioDashboard(condominioId)`: Query para dashboard

### Componentes UI

#### `CondomínioFormSection`
Seção de formulário para configuração de unidades em condomínios.

**Props:**
```typescript
interface CondomínioFormSectionProps {
  isCondominio: boolean;
  onUnidadesChange?: (unidades: UnidadeFormData[]) => void;
}
```

#### `CondominioDashboard`
Dashboard agregado com métricas do condomínio.

#### `ListaUnidades`
Tabela de unidades com navegação e ações.

### Páginas

- **`NovoCondominio`**: Criação de novos condomínios
- **`CondominioDetalhe`**: Visualização de detalhes e dashboard
- **`ObrasLista`**: Lista principal (filtra automaticamente unidades)

## Validações

### Frontend (Zod)

```typescript
// Schema para unidades
const unidadeSchema = z.object({
  identificador_unidade: z.string().min(1, "Identificador obrigatório"),
  nome: z.string().min(1, "Nome obrigatório"),
});

// Schema para condomínio completo
const obraComCondomínioSchema = obraSchema.extend({
  tipo_projeto: TipoProjetoEnum,
  unidades: z.array(unidadeSchema).min(1, "Pelo menos uma unidade necessária"),
});
```

### Backend (SQL)

- **Foreign Key**: `parent_obra_id` referencia `obras.id`
- **Check Constraints**: Validação de tipos hierárquicos
- **Unique Constraints**: `identificador_unidade` único por condomínio

## Segurança

### Row Level Security (RLS)

Todas as políticas RLS foram estendidas para suportar condomínios:

```sql
-- Política para obras principais
CREATE POLICY "tenant_isolation_obras_main" ON obras
FOR ALL USING (
  (auth.uid() IN (
    SELECT user_id FROM profiles WHERE tenant_id = obras.tenant_id
  )) AND parent_obra_id IS NULL
);

-- Política para unidades
CREATE POLICY "tenant_isolation_obras_units" ON obras
FOR ALL USING (
  (auth.uid() IN (
    SELECT user_id FROM profiles WHERE tenant_id = obras.tenant_id
  )) AND parent_obra_id IS NOT NULL
);
```

### Isolamento por Tenant

- Todas as operações respeitam `tenant_id`
- Queries filtram automaticamente por tenant
- Validação de propriedade antes de acessos

## Performance

### Índices Otimizados

```sql
-- Índice para relacionamentos hierárquicos
CREATE INDEX idx_obras_parent_obra_id ON obras(parent_obra_id);

-- Índice composto para filtragem eficiente
CREATE INDEX idx_obras_tenant_tipo ON obras(tenant_id, tipo_projeto);

-- Índice para identificadores de unidades
CREATE INDEX idx_obras_identificador_unidade ON obras(identificador_unidade)
WHERE identificador_unidade IS NOT NULL;
```

### Estratégias de Cache

- **TanStack Query**: Cache automático de consultas
- **Invalidação Inteligente**: Invalidação baseada em relacionamentos
- **Background Refetch**: Atualização automática de dados críticos

## Fluxos de Uso

### 1. Criar Condomínio

1. Usuário acessa "Novo Condomínio"
2. Preenche dados básicos da obra-mãe
3. Configura unidades (manual ou automático)
4. Sistema valida dados (frontend + backend)
5. RPC function cria obra-mãe e unidades atomicamente
6. Redirecionamento para dashboard do condomínio

### 2. Gerenciar Condomínio

1. Dashboard mostra métricas agregadas
2. Lista todas as unidades
3. Permite navegação para unidades específicas
4. Ações em lote para múltiplas unidades

### 3. Navegação

- **Lista Principal**: Mostra apenas obras-mãe
- **Dashboard Condomínio**: Visão agregada + lista de unidades
- **Detalhes Unidade**: Visão individual + link para condomínio

## Testes

### Testes Unitários

- **RPC Functions**: Validação de lógica transacional
- **Hooks**: Comportamento de queries e mutations
- **Componentes**: Renderização e interações

### Testes de Integração

- **Fluxo Completo**: Criação → Visualização → Navegação
- **Validações**: Regras de negócio e constraints
- **Performance**: Carregamento com muitas unidades

### Testes E2E

- **Jornada do Usuário**: Fluxo completo da interface
- **Cenários de Erro**: Tratamento de falhas
- **Cross-browser**: Compatibilidade

## Troubleshooting

### Problemas Comuns

#### 1. Erro na Criação de Condomínio
```
Erro: "Violação de constraint de chave estrangeira"
```
**Solução**: Verificar se `construtora_id` existe e pertence ao tenant.

#### 2. Unidades Não Aparecem na Lista Principal
```
Comportamento esperado: Unidades são filtradas automaticamente
```
**Verificação**: Query deve usar `parent_obra_id IS NULL`.

#### 3. Performance Lenta com Muitas Unidades
```
Solução: Implementar paginação e lazy loading
```

### Logs e Monitoramento

- **Logging Seguro**: Uso de `secureLogger` para operações sensíveis
- **Métricas**: Tempo de criação e carregamento de condomínios
- **Alertas**: Falhas frequentes em operações críticas

## Roadmap Futuro

### Melhorias Planejadas

1. **Interface Aprimorada**:
   - Drag & drop para organizar unidades
   - Visualização em grid/mapa
   - Bulk actions avançadas

2. **Funcionalidades Avançadas**:
   - Templates de condomínio
   - Importação/exportação de unidades
   - Relatórios específicos

3. **Performance**:
   - Virtual scrolling para listas grandes
   - Background sync para dados
   - Cache inteligente

## Suporte

Para dúvidas ou problemas:

1. Consulte esta documentação
2. Verifique logs de erro no console
3. Execute testes para verificar funcionalidade
4. Contate a equipe de desenvolvimento

---

**Última atualização**: 16/07/2025  
**Versão**: 1.0.0  
**Autor**: Equipe ObrasAI