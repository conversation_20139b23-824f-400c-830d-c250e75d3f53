# Documentação Detalhada: Sistema de Fornecedores no ObrasAI

Este documento detalha as funcionalidades do sistema de fornecedores do ObrasAI e fornece um guia passo a passo sobre como um usuário pode gerenciar fornecedores PJ (Pessoa Jurídica) e PF (Pessoa Física), incluindo cadastro, edição, validação automática e integração com despesas. Este material será utilizado para treinar uma IA que auxiliará os usuários na utilização do sistema.

## 1. Visão Geral das Funcionalidades do Sistema de Fornecedores

O sistema de fornecedores do ObrasAI é uma ferramenta completa para gerenciar relacionamentos comerciais com fornecedores de materiais, serviços e mão de obra. O sistema oferece gestão diferenciada para pessoas jurídicas e físicas, com validações automáticas, integração com APIs externas e controle completo do histórico de relacionamento. As principais funcionalidades incluem:

*   **Gestão Dual PJ/PF:** Sistema completo para gerenciar tanto fornecedores Pessoa Jurídica quanto Pessoa Física, com formulários e validações específicas para cada tipo.
*   **Validação Automática de Documentos:** Integração com APIs externas para validação automática de CNPJ/CPF, com preenchimento automático de dados da Receita Federal.
*   **CRUD Completo:** Funcionalidades completas de criação, leitura, atualização e exclusão para ambos os tipos de fornecedores, com controle de permissões multi-tenant.
*   **Integração com Despesas:** Vinculação automática de fornecedores às despesas, permitindo rastreamento completo de gastos por fornecedor e análise de performance.
*   **Histórico de Relacionamento:** Acompanhamento completo do histórico de transações, valores pagos, frequência de compras e avaliação de performance dos fornecedores.
*   **Busca e Filtros Avançados:** Sistema de busca inteligente por nome, documento, categoria ou tipo de serviço, com filtros dinâmicos para facilitar a localização.
*   **Validação de Dados em Tempo Real:** Validação automática de CPF, CNPJ, CEP, telefones e emails durante o preenchimento dos formulários.
*   **Multi-tenancy:** Isolamento completo de dados entre diferentes empresas/usuários, garantindo segurança e privacidade das informações.

## 2. Como Acessar e Gerenciar Fornecedores (Passo a Passo)

### Passo 1: Acessar o Módulo de Fornecedores

1.  No painel de controle do ObrasAI, navegue até a seção de `Fornecedores` no menu lateral.
2.  Você será direcionado para a página principal de fornecedores, que exibe duas abas: "Fornecedores PJ" e "Fornecedores PF".
3.  A interface apresenta uma tabela com todos os fornecedores cadastrados, opções de busca e botões para adicionar novos fornecedores.

### Passo 2: Cadastrar um Novo Fornecedor

#### Para Fornecedor PJ (Pessoa Jurídica):

1.  **Acesso ao Formulário:**
    - Clique na aba "Fornecedores PJ"
    - Clique no botão "Novo Fornecedor PJ"
    - Você será direcionado para o formulário de cadastro

2.  **Preenchimento dos Dados Básicos:**
    - **CNPJ:** Digite o CNPJ no formato 00.000.000/0000-00
    - **Validação Automática:** O sistema validará automaticamente o CNPJ e preencherá dados da Receita Federal
    - **Razão Social:** Campo obrigatório, preenchido automaticamente ou manualmente
    - **Nome Fantasia:** Campo opcional para nome comercial
    - **Inscrição Estadual/Municipal:** Campos opcionais para registros fiscais

3.  **Informações de Contato:**
    - **Email:** Email principal para comunicação (validação automática de formato)
    - **Telefone Principal:** Formato (00) 00000-0000 com validação automática
    - **Telefone Secundário:** Campo opcional para contato alternativo
    - **Website:** URL do site da empresa (validação de formato)

4.  **Endereço Completo:**
    - **CEP:** Com busca automática de endereço via API
    - **Endereço, Número, Complemento:** Preenchimento automático ou manual
    - **Bairro, Cidade, Estado:** Dados preenchidos automaticamente pelo CEP

#### Para Fornecedor PF (Pessoa Física):

1.  **Acesso ao Formulário:**
    - Clique na aba "Fornecedores PF"
    - Clique no botão "Novo Fornecedor PF"
    - Acesse o formulário específico para pessoa física

2.  **Dados Pessoais:**
    - **CPF:** Formato 000.000.000-00 com validação automática
    - **Nome Completo:** Nome completo da pessoa física
    - **RG:** Documento de identidade (opcional)
    - **Data de Nascimento:** Seletor de data para idade e validações

3.  **Classificação e Contato:**
    - **Tipo de Fornecedor:** Categoria do serviço (ex: Pedreiro, Eletricista, Pintor)
    - **Email:** Email para comunicação (opcional)
    - **Telefones:** Principal e secundário com validação de formato

4.  **Endereço:** Mesmo processo do PJ com busca automática por CEP

### Passo 3: Validações Automáticas

**Durante o Preenchimento:**
- **CNPJ/CPF:** Validação de formato e dígitos verificadores
- **Busca Automática:** Consulta à Receita Federal para dados do CNPJ
- **CEP:** Busca automática de endereço via API dos Correios
- **Email/Telefone:** Validação de formato em tempo real
- **Campos Obrigatórios:** Indicação visual de campos necessários

### Passo 4: Salvar e Gerenciar

1.  **Salvar Fornecedor:**
    - Clique em "Salvar" após preencher os dados obrigatórios
    - O sistema validará todos os campos antes de salvar
    - Mensagem de sucesso será exibida após cadastro

2.  **Editar Fornecedor:**
    - Na listagem, clique no ícone de edição
    - Modifique os dados necessários
    - Salve as alterações

3.  **Excluir Fornecedor:**
    - Clique no ícone de exclusão na listagem
    - Confirme a exclusão no modal de confirmação
    - Fornecedores vinculados a despesas não podem ser excluídos

## 3. Arquitetura e Estrutura Técnica

### Stack Tecnológica
- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: shadcn/ui + Tailwind CSS
- **Estado**: TanStack Query (React Query) para server state
- **Formulários**: React Hook Form + Zod validation
- **Validações**: Zod schemas com validação em tempo real
- **Backend**: Supabase (PostgreSQL + Row Level Security)
- **APIs Externas**: Receita Federal (CNPJ), ViaCEP (endereços)

### Estrutura de Arquivos
```
src/
├── pages/dashboard/fornecedores/
│   ├── FornecedoresPJLista.tsx      # Listagem de fornecedores PJ
│   ├── FornecedoresPFLista.tsx      # Listagem de fornecedores PF
│   ├── NovoFornecedor.tsx           # Formulário de criação
│   └── EditarFornecedor.tsx         # Formulário de edição
├── hooks/
│   ├── useFornecedoresPJ.ts         # Hook para fornecedores PJ
│   ├── useFornecedoresPF.ts         # Hook para fornecedores PF
│   └── useCNPJLookup.ts             # Hook para validação CNPJ
├── services/
│   └── api.ts                       # APIs fornecedoresPJApi e fornecedoresPFApi
└── lib/validations/
    └── fornecedor.ts                # Schemas Zod para validação
```

### Banco de Dados

**Tabela Principal: `fornecedores_pj`**
- **Identificação**: id (UUID), cnpj, razao_social, nome_fantasia
- **Fiscais**: inscricao_estadual, inscricao_municipal
- **Contato**: email, telefone_principal, telefone_secundario, website
- **Endereço**: endereco, numero, complemento, bairro, cidade, estado, cep
- **Controle**: created_at, updated_at, tenant_id, usuario_id
- **Observações**: observacoes (texto livre)

**Tabela Principal: `fornecedores_pf`**
- **Identificação**: id (UUID), cpf, nome, rg, data_nascimento
- **Classificação**: tipo_fornecedor (categoria de serviço)
- **Contato**: email, telefone_principal, telefone_secundario
- **Endereço**: endereco, numero, complemento, bairro, cidade, estado, cep
- **Controle**: created_at, updated_at, tenant_id, usuario_id
- **Observações**: observacoes (texto livre)

**Relacionamentos:**
- `despesas.fornecedor_pj_id` → `fornecedores_pj.id`
- `despesas.fornecedor_pf_id` → `fornecedores_pf.id`
- `contratos.fornecedor_id` → `fornecedores_pj.id` ou `fornecedores_pf.id`
- Multi-tenancy via `tenant_id` para isolamento de dados

## 4. Funcionalidades Principais

### 4.1 Listagem de Fornecedores (`FornecedoresPJLista.tsx` / `FornecedoresPFLista.tsx`)

**Características:**
- **Interface Tabular**: Exibição organizada com colunas específicas para cada tipo
- **Busca Inteligente**: Campo de busca que filtra por nome, documento ou email
- **Ações Rápidas**: Visualizar, editar e excluir diretamente da listagem
- **Paginação**: Carregamento otimizado para grandes volumes de dados
- **Estados de Loading**: Feedback visual durante carregamento de dados

**Colunas PJ:**
- Razão Social / Nome Fantasia
- CNPJ formatado
- Email e telefone principal
- Data de cadastro
- Ações (editar, excluir)

**Colunas PF:**
- Nome completo
- CPF formatado
- Tipo de fornecedor
- Contato (email/telefone)
- Data de nascimento
- Ações (editar, excluir)

### 4.2 Formulários de Cadastro e Edição

**Validação em Tempo Real:**
- **Formatação Automática**: CPF, CNPJ, telefones e CEP formatados durante digitação
- **Validação de Documentos**: Verificação de dígitos verificadores de CPF/CNPJ
- **Busca Automática**: Preenchimento automático via APIs externas
- **Feedback Visual**: Indicadores de erro e sucesso em tempo real

**Integração com APIs:**
- **CNPJ**: Consulta automática à Receita Federal para dados da empresa
- **CEP**: Busca automática de endereço via ViaCEP
- **Validação**: Verificação de formato e existência de documentos

### 4.3 Integração com Despesas

**Vinculação Automática:**
- **Seleção de Fornecedor**: Dropdown com busca para vincular despesas
- **Histórico de Transações**: Visualização de todas as despesas por fornecedor
- **Análise de Performance**: Métricas de valor total, frequência e pontualidade
- **Relatórios**: Geração de relatórios por fornecedor ou período

## 5. APIs e Serviços

### Hook Principal (`useFornecedoresPJ.ts` / `useFornecedoresPF.ts`)

**Hooks Disponíveis:**

#### `useFornecedoresPJ()`
- **Propósito**: Gerenciamento completo de fornecedores pessoa jurídica
- **Funcionalidades**: CRUD completo com validação multi-tenant
- **Retorno**: Lista de fornecedores, mutations para criar/editar/excluir
- **Cache**: Invalidação automática após operações
- **Estados**: Loading, error, success com feedback visual

#### `useFornecedoresPF()`
- **Propósito**: Gerenciamento de fornecedores pessoa física
- **Funcionalidades**: CRUD otimizado para dados de PF
- **Validações**: CPF, data de nascimento, tipo de fornecedor
- **Integração**: Vinculação automática com despesas e contratos

#### `useCNPJLookup(cnpj)`
- **Propósito**: Validação e busca automática de dados por CNPJ
- **API Externa**: Integração com Receita Federal
- **Preenchimento**: Automático de razão social, endereço e situação
- **Cache**: Dados consultados ficam em cache por 24 horas
- **Fallback**: Sistema robusto para falhas de API externa

### API Service (`api.ts`)

**Fornecedores PJ API:**

#### `fornecedoresPJApi.getAll(tenantId)`
```typescript
interface FornecedorPJ {
  id: string;
  cnpj: string;
  razao_social: string;
  nome_fantasia?: string;
  email?: string;
  telefone_principal?: string;
  endereco?: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
}
```

#### `fornecedoresPJApi.create(data)`
- **Validação**: Schema Zod completo antes do envio
- **Sanitização**: Limpeza de dados de entrada
- **Multi-tenant**: Associação automática ao tenant do usuário
- **Auditoria**: Log completo de criação

#### `fornecedoresPJApi.update(id, data)`
- **Validação Parcial**: Apenas campos modificados
- **Versionamento**: Controle de versão para conflitos
- **Histórico**: Manutenção de log de alterações

#### `fornecedoresPJApi.delete(id)`
- **Verificação de Dependências**: Impede exclusão se vinculado a despesas
- **Soft Delete**: Marcação como inativo em vez de exclusão física
- **Auditoria**: Log completo de exclusão

**Fornecedores PF API:**

#### `fornecedoresPFApi.getAll(tenantId)`
```typescript
interface FornecedorPF {
  id: string;
  cpf: string;
  nome: string;
  tipo_fornecedor?: string;
  email?: string;
  telefone_principal?: string;
  data_nascimento?: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
}
```

#### Operações CRUD similares ao PJ com validações específicas para PF

## 6. Estrutura de Dados e Validações

### Schemas de Validação (Zod)

#### `fornecedorPJSchema`
```typescript
const fornecedorPJSchema = z.object({
  cnpj: z.string().regex(cnpjRegex, "CNPJ inválido"),
  razaoSocial: z.string().min(3, "Razão social obrigatória"),
  nomeFantasia: z.string().optional(),
  inscricaoEstadual: z.string().optional(),
  inscricaoMunicipal: z.string().optional(),
  email: z.string().email("Email inválido").optional(),
  telefonePrincipal: z.string().regex(phoneRegex).optional(),
  telefoneSecundario: z.string().regex(phoneRegex).optional(),
  website: z.string().url("URL inválida").optional(),
  endereco: z.string().optional(),
  numero: z.string().optional(),
  complemento: z.string().optional(),
  bairro: z.string().optional(),
  cidade: z.string().optional(),
  estado: z.string().optional(),
  cep: z.string().regex(cepRegex).optional(),
  observacoes: z.string().optional()
});
```

#### `fornecedorPFSchema`
```typescript
const fornecedorPFSchema = z.object({
  cpf: z.string().regex(cpfRegex, "CPF inválido"),
  nome: z.string().min(3, "Nome obrigatório"),
  rg: z.string().optional(),
  dataNascimento: z.date().optional(),
  tipoFornecedor: z.string().optional(),
  email: z.string().email("Email inválido").optional(),
  telefonePrincipal: z.string().regex(phoneRegex).optional(),
  telefoneSecundario: z.string().regex(phoneRegex).optional(),
  endereco: z.string().optional(),
  numero: z.string().optional(),
  complemento: z.string().optional(),
  bairro: z.string().optional(),
  cidade: z.string().optional(),
  estado: z.string().optional(),
  cep: z.string().regex(cepRegex).optional(),
  observacoes: z.string().optional()
});
```

### Validações em Tempo Real

**Formatação Automática:**
- **CNPJ**: 00.000.000/0000-00
- **CPF**: 000.000.000-00
- **Telefone**: (00) 00000-0000
- **CEP**: 00000-000

**Validações de Negócio:**
- **Unicidade**: CNPJ/CPF únicos por tenant
- **Dependências**: Verificação antes de exclusão
- **Integridade**: Validação de relacionamentos

## 7. Integração com Outros Módulos

### 7.1 Integração com Despesas

**Vinculação Automática:**
- **Seleção de Fornecedor**: Dropdown inteligente com busca
- **Preenchimento Automático**: Dados do fornecedor preenchidos automaticamente
- **Histórico de Compras**: Visualização de todas as despesas por fornecedor
- **Análise de Performance**: Métricas de valor, frequência e pontualidade

**Processo de Integração:**
1. **Cadastro de Despesa**: Usuário seleciona fornecedor na despesa
2. **Busca Inteligente**: Sistema oferece fornecedores relevantes
3. **Vinculação Automática**: Despesa fica vinculada ao fornecedor
4. **Atualização de Métricas**: Histórico e estatísticas atualizados
5. **Relatórios**: Dados disponíveis para análises e relatórios

### 7.2 Integração com Contratos

**Gestão Contratual:**
- **Seleção de Fornecedor**: Vinculação de contratos a fornecedores
- **Histórico Contratual**: Acompanhamento de contratos por fornecedor
- **Avaliação de Performance**: Métricas de cumprimento de prazos
- **Renovações**: Alertas para renovação de contratos

### 7.3 Integração com Notas Fiscais

**Processamento de NF:**
- **Identificação Automática**: Reconhecimento de fornecedor por CNPJ/CPF
- **Vinculação Automática**: Notas fiscais vinculadas ao fornecedor correto
- **Validação Fiscal**: Verificação de dados fiscais do fornecedor
- **Relatórios Fiscais**: Consolidação de informações por fornecedor

## 8. Orientações para Treinamento de IA

### 8.1 Cenários de Uso Comum

**Cadastro de Novo Fornecedor PJ:**
- **Pergunta**: "Como cadastrar uma empresa fornecedora?"
- **Resposta**: Orientar sobre acesso ao módulo, preenchimento do CNPJ, validação automática
- **Contexto**: Explicar benefícios da validação automática e preenchimento de dados

**Cadastro de Fornecedor PF:**
- **Pergunta**: "Como cadastrar um pedreiro como fornecedor?"
- **Resposta**: Explicar diferença entre PJ e PF, orientar sobre campos específicos
- **Orientação**: Destacar importância do tipo de fornecedor para categorização

**Busca de Fornecedores:**
- **Pergunta**: "Como encontrar um fornecedor específico?"
- **Resposta**: Explicar sistema de busca, filtros disponíveis
- **Dicas**: Orientar sobre busca por nome, documento ou tipo de serviço

**Integração com Despesas:**
- **Pergunta**: "Como vincular uma despesa a um fornecedor?"
- **Resposta**: Explicar processo de seleção no formulário de despesas
- **Benefícios**: Destacar vantagens do histórico e análise de performance

### 8.2 Dicas para Orientação de Usuários

**Para Cadastros Eficientes:**
- **Dados Completos**: Sempre preencher o máximo de informações possível
- **Validação Automática**: Aproveitar a busca automática por CNPJ/CEP
- **Categorização**: Usar tipos de fornecedor para melhor organização
- **Contatos Múltiplos**: Cadastrar telefones principal e secundário

**Para Gestão de Qualidade:**
- **Verificação Regular**: Manter dados atualizados periodicamente
- **Documentação**: Usar campo observações para informações importantes
- **Histórico**: Acompanhar performance através das despesas vinculadas
- **Avaliação**: Registrar avaliações de qualidade e pontualidade

**Para Integração Eficaz:**
- **Vinculação Consistente**: Sempre vincular despesas aos fornecedores corretos
- **Análise de Dados**: Usar relatórios para avaliar performance
- **Negociação**: Usar histórico para melhorar negociações
- **Planejamento**: Analisar sazonalidade e padrões de compra

### 8.3 Troubleshooting Comum

**Problema: "CNPJ não é encontrado na Receita Federal"**
- **Verificação**: Confirmar se CNPJ está correto e ativo
- **Alternativa**: Preencher dados manualmente se API estiver indisponível
- **Orientação**: Explicar que nem todos os CNPJs estão na base pública

**Problema: "Não consigo excluir um fornecedor"**
- **Explicação**: Fornecedores vinculados a despesas não podem ser excluídos
- **Solução**: Verificar despesas vinculadas e considerar inativação
- **Alternativa**: Editar dados em vez de excluir

**Problema: "CEP não encontra endereço"**
- **Verificação**: Confirmar se CEP está correto
- **Alternativa**: Preencher endereço manualmente
- **Orientação**: Explicar limitações da API de CEP

**Problema: "Fornecedor duplicado"**
- **Verificação**: Buscar por CNPJ/CPF antes de cadastrar
- **Prevenção**: Sistema impede duplicação por documento
- **Solução**: Editar fornecedor existente em vez de criar novo

### 8.4 Boas Práticas

**Para Organização Eficiente:**
- **Nomenclatura Consistente**: Usar padrões para nomes e categorias
- **Atualização Regular**: Manter dados de contato atualizados
- **Categorização**: Usar tipos de fornecedor para facilitar buscas
- **Documentação**: Registrar informações importantes em observações

**Para Análise de Performance:**
- **Histórico Completo**: Vincular todas as despesas aos fornecedores
- **Avaliação Periódica**: Revisar performance regularmente
- **Métricas de Qualidade**: Acompanhar pontualidade e qualidade
- **Negociação Baseada em Dados**: Usar histórico para melhorar condições

**Para Segurança e Compliance:**
- **Validação de Documentos**: Sempre verificar CPF/CNPJ
- **Dados Fiscais**: Manter inscrições estaduais/municipais atualizadas
- **Backup de Informações**: Manter cópias de documentos importantes
- **Auditoria**: Acompanhar logs de alterações para compliance

## 9. Funcionalidades Técnicas Avançadas

### 9.1 Performance e Cache

**Estratégias de Cache:**
- **Query Cache**: TanStack Query com 10 minutos de stale time
- **Validação CNPJ**: Cache de consultas por 24 horas
- **Busca CEP**: Cache de endereços por 1 hora
- **Lista de Fornecedores**: Cache otimizado com invalidação inteligente

**Otimizações de Performance:**
- **Lazy Loading**: Carregamento sob demanda de dados detalhados
- **Debounce de Busca**: Redução de requisições em campos de busca
- **Paginação Eficiente**: Carregamento incremental para grandes listas
- **Índices Otimizados**: Busca rápida por documento, nome e tenant

### 9.2 Segurança

**Controle de Acesso:**
- **Multi-tenancy**: Isolamento completo de dados por empresa
- **Row Level Security**: Políticas de segurança no banco de dados
- **Validação de Permissões**: Verificação de acesso em todas as operações
- **Auditoria Completa**: Log de todas as operações CRUD

**Validação de Dados:**
- **Sanitização de Input**: Limpeza de dados de entrada
- **Validação de Schema**: Verificação rigorosa com Zod
- **Prevenção de Injection**: Proteção contra SQL injection
- **Validação de Documentos**: Verificação de CPF/CNPJ com algoritmos oficiais

### 9.3 Responsividade e Acessibilidade

**Design Responsivo:**
- **Mobile First**: Interface otimizada para dispositivos móveis
- **Formulários Adaptativos**: Campos que se ajustam ao tamanho da tela
- **Tabelas Responsivas**: Visualização otimizada em diferentes dispositivos
- **Touch Friendly**: Elementos dimensionados para interação touch

**Acessibilidade:**
- **ARIA Labels**: Rótulos apropriados para leitores de tela
- **Navegação por Teclado**: Suporte completo para navegação sem mouse
- **Contraste Adequado**: Cores que atendem padrões WCAG
- **Feedback Sonoro**: Indicações auditivas para ações importantes

**Estados de Interface:**
- **Loading States**: Feedback visual durante operações
- **Error Boundaries**: Tratamento gracioso de erros
- **Empty States**: Orientação quando não há fornecedores cadastrados
- **Success Feedback**: Confirmação visual de operações bem-sucedidas

## 10. Chat IA de Fornecedores - Nova Funcionalidade

### 10.1 Visão Geral do Chat IA

O **Chat IA de Fornecedores** é uma nova funcionalidade revolucionária que permite aos usuários interagir com o sistema de fornecedores através de linguagem natural. Esta ferramenta utiliza inteligência artificial para facilitar a busca, comparação e gestão de fornecedores de forma intuitiva e eficiente.

**Características Principais:**
- **Interface Conversacional**: Interação natural através de chat
- **Busca Inteligente**: Encontre fornecedores usando linguagem natural
- **Estatísticas Dinâmicas**: Dados em tempo real do banco de dados
- **Integração Completa**: Acesso direto às funcionalidades de cadastro
- **Sugestões Contextuais**: Guias e dicas baseadas no contexto

### 10.2 Funcionalidades do Chat IA

#### **Busca de Fornecedores por Linguagem Natural**
- **Exemplo**: "Preciso de um eletricista em São Paulo"
- **Resultado**: Sistema identifica tipo de fornecedor e localização
- **Filtros Automáticos**: Aplicação inteligente de filtros baseados na consulta

#### **Comparação de Fornecedores**
- **Exemplo**: "Compare fornecedores de cimento na região"
- **Análise**: Comparação automática de preços, qualidade e histórico
- **Recomendações**: Sugestões baseadas em performance histórica

#### **Obtenção de Informações de Contato**
- **Exemplo**: "Qual o telefone do fornecedor X?"
- **Resposta Imediata**: Acesso rápido a dados de contato
- **Informações Completas**: Email, telefone, endereço e observações

#### **Orientação para Cadastro**
- **Exemplo**: "Como cadastrar um novo fornecedor?"
- **Guia Passo a Passo**: Instruções detalhadas para cadastro
- **Redirecionamento**: Links diretos para formulários de cadastro

### 10.3 Arquitetura Técnica do Chat

#### **Componentes Principais**

**FornecedoresChatPage.tsx:**
```typescript
interface FornecedoresChatPageProps {
  // Página principal do chat com layout responsivo
  // Integração com DashboardLayout
  // Sidebar com estatísticas e guias
}
```

**Estrutura da Página:**
- **Header**: Título, descrição e botões de ação
- **Chat Interface**: Área principal de conversação
- **Sidebar**: Estatísticas, guias e dicas

#### **Hook de Estatísticas (useFornecedoresStats)**

**Funcionalidades:**
```typescript
interface FornecedoresStats {
  totalPJ: number;           // Total de fornecedores PJ
  totalPF: number;           // Total de fornecedores PF
  totalGeral: number;        // Soma total
  categorias: CategoriaStats[]; // Categorias mais populares
  loading: boolean;          // Estado de carregamento
  error: string | null;      // Tratamento de erro
}
```

**Características Técnicas:**
- **Dados Dinâmicos**: Busca em tempo real do banco de dados
- **Multi-tenant**: Isolamento por tenant_id
- **Cache Inteligente**: Otimização de performance
- **Tratamento de Erro**: Feedback adequado para falhas

### 10.4 Interface e Experiência do Usuário

#### **Layout Responsivo**
- **Desktop**: Chat centralizado com sidebar informativa
- **Mobile**: Interface adaptativa com navegação otimizada
- **Tablet**: Layout híbrido com elementos redimensionáveis

#### **Sidebar Informativa**
**Guia Rápido:**
- Exemplos de perguntas comuns
- Sugestões de uso do chat
- Orientações contextuais

**Estatísticas em Tempo Real:**
- Total de fornecedores PJ e PF
- Categorias mais populares
- Distribuição por tipo de serviço

**Dicas de Uso:**
- Orientações para consultas eficazes
- Sugestões de linguagem natural
- Melhores práticas de busca

#### **Estados da Interface**
- **Loading**: Indicadores visuais durante carregamento
- **Empty State**: Orientação quando não há fornecedores
- **Error State**: Mensagens de erro amigáveis
- **Success**: Feedback positivo para ações bem-sucedidas

### 10.5 Integração com Sistema Existente

#### **Navegação Integrada**
- **Botão "Ver Fornecedores"**: Acesso direto às listagens
- **Botão "Novo Fornecedor"**: Redirecionamento para cadastro
- **Links Contextuais**: Navegação baseada nas consultas do chat

#### **Dados Sincronizados**
- **Estatísticas Atualizadas**: Reflexo imediato de novos cadastros
- **Categorias Dinâmicas**: Baseadas nos tipos de fornecedores reais
- **Isolamento Multi-tenant**: Dados específicos por empresa

### 10.6 Funcionalidades Avançadas

#### **Processamento de Linguagem Natural**
- **Reconhecimento de Intenção**: Identificação do que o usuário deseja
- **Extração de Entidades**: Localização, tipo de serviço, orçamento
- **Contexto Conversacional**: Manutenção do contexto durante a conversa

#### **Sugestões Inteligentes**
- **Autocompletar**: Sugestões baseadas em consultas anteriores
- **Recomendações**: Fornecedores sugeridos baseados em histórico
- **Filtros Automáticos**: Aplicação inteligente de critérios de busca

#### **Análise de Performance**
- **Métricas de Uso**: Acompanhamento de consultas mais comuns
- **Otimização**: Melhoria contínua baseada no uso
- **Feedback**: Sistema de avaliação da qualidade das respostas

### 10.7 Orientações para Treinamento de IA

#### **Cenários de Uso do Chat**

**Busca Básica de Fornecedores:**
- **Pergunta**: "Preciso de um pedreiro"
- **Resposta**: Lista de pedreiros disponíveis com contatos
- **Contexto**: Explicar como refinar a busca com localização

**Busca com Localização:**
- **Pergunta**: "Eletricista em São Paulo"
- **Resposta**: Fornecedores filtrados por localização
- **Orientação**: Sugerir critérios adicionais como experiência

**Comparação de Fornecedores:**
- **Pergunta**: "Compare fornecedores de material de construção"
- **Resposta**: Análise comparativa com métricas
- **Dicas**: Orientar sobre critérios de avaliação

**Informações Específicas:**
- **Pergunta**: "Telefone do fornecedor ABC"
- **Resposta**: Dados de contato completos
- **Contexto**: Oferecer informações adicionais relevantes

#### **Melhores Práticas para Orientação**

**Para Consultas Eficazes:**
- **Especificidade**: Encorajar detalhes sobre localização e tipo
- **Contexto**: Incluir informações sobre orçamento quando relevante
- **Linguagem Natural**: Usar termos comuns da construção civil

**Para Resultados Otimizados:**
- **Filtros Múltiplos**: Combinar localização, tipo e orçamento
- **Histórico**: Considerar performance anterior dos fornecedores
- **Avaliações**: Incluir feedback de outros usuários quando disponível

### 10.8 Arquitetura Multi-tenant Corrigida

#### **Correção Crítica Implementada**

**Problema Anterior:**
- Constraint `UNIQUE (cnpj)` impedia o mesmo fornecedor para diferentes empresas
- Constraint `UNIQUE (cpf)` limitava fornecedores PF a uma única empresa

**Solução Implementada:**
- **Nova Constraint PJ**: `UNIQUE (cnpj, tenant_id)`
- **Nova Constraint PF**: `UNIQUE (cpf, tenant_id)`
- **Benefício**: Múltiplas empresas podem cadastrar o mesmo fornecedor

#### **Impacto na Funcionalidade**
- **Flexibilidade**: Fornecedores podem atender múltiplas construtoras
- **Isolamento**: Cada empresa vê apenas seus próprios fornecedores
- **Escalabilidade**: Sistema suporta crescimento sem limitações artificiais

### 10.9 Troubleshooting do Chat IA

#### **Problemas Comuns e Soluções**

**"Chat não carrega estatísticas"**
- **Verificação**: Confirmar autenticação e tenant_id válido
- **Solução**: Recarregar página ou verificar conexão
- **Prevenção**: Sistema de retry automático implementado

**"Não encontra fornecedores específicos"**
- **Causa**: Critérios de busca muito restritivos
- **Solução**: Sugerir critérios mais amplos
- **Orientação**: Usar termos mais genéricos inicialmente

**"Estatísticas não atualizam"**
- **Verificação**: Confirmar se novos fornecedores foram salvos
- **Solução**: Cache pode levar alguns minutos para atualizar
- **Alternativa**: Recarregar página para forçar atualização

### 10.10 Roadmap e Melhorias Futuras

#### **Funcionalidades Planejadas**
- **Integração com WhatsApp**: Chat via WhatsApp Business
- **Análise Preditiva**: Sugestões baseadas em padrões de uso
- **Avaliações**: Sistema de rating para fornecedores
- **Geolocalização**: Busca por proximidade GPS

#### **Melhorias de IA**
- **Processamento Avançado**: Compreensão de contexto mais sofisticada
- **Aprendizado Contínuo**: Melhoria baseada em interações
- **Personalização**: Sugestões baseadas no histórico do usuário
- **Integração com Despesas**: Análise de custo-benefício automática
