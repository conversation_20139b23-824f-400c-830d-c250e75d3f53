# Documentação Completa do Módulo de Obras - ObrasAI

Este documento detalha todas as funcionalidades e componentes do módulo de obras do sistema ObrasAI, incluindo listagem, criação, edição, detalhamento, vendas, análises de IA e chat contextual.

## Visão Geral do Módulo

O módulo de obras é o núcleo central do ObrasAI, responsável por gerenciar todo o ciclo de vida de uma obra de construção civil, desde o cadastro inicial até a venda final, incluindo controle financeiro, análises inteligentes e insights de IA. O sistema oferece funcionalidades avançadas de:

- **Gestão Completa de Obras**: Cadastro, edição, listagem e detalhamento
- **Controle Financeiro**: Orçamento vs. realizado, despesas e análise de custos
- **Vendas e Lucratividade**: Gestão de vendas com análise de viabilidade por IA
- **Inteligência Artificial**: Chat contextual, insights automáticos e análises preditivas
- **Integração com Orçamentos**: Conexão com orçamentos paramétricos baseados em SINAPI

## Arquitetura e Estrutura Técnica

### Stack Tecnológica
- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: shadcn/ui + Tailwind CSS
- **Estado**: TanStack Query (React Query) para server state
- **Animações**: Framer Motion
- **Formulários**: React Hook Form + Zod validation
- **Backend**: Supabase (PostgreSQL + Edge Functions)
- **IA**: DeepSeek AI via Edge Functions

### Estrutura de Arquivos
```
src/
├── pages/dashboard/obras/
│   ├── ObrasListaRefactored.tsx     # Listagem principal
│   ├── NovaObraRefactored.tsx       # Criação de obras
│   ├── EditarObra.tsx               # Edição de obras
│   └── ObraDetalhe.tsx              # Detalhes e gestão completa
├── components/dashboard/obras/
│   └── VendaLucroTab.tsx            # Aba de vendas e lucratividade
├── components/ai/
│   ├── InsightsObra.tsx             # Insights de IA
│   └── InterfaceChat.tsx            # Chat contextual
├── hooks/
│   └── useObras.ts                  # Hook principal de obras
└── services/
    └── api.ts                       # API de obras (obrasApi)
```

### Banco de Dados
**Tabela Principal: `obras`**
- Campos básicos: id, nome, endereco, cidade, estado, cep
- Datas: data_inicio, data_prevista_termino
- Financeiro: orcamento (orçamento disponível)
- Responsável: responsavel_id (FK para construtores/freelancers)
- Vendas: valor_venda, data_venda, status_venda, comissao_corretor_percentual, outras_despesas_venda
- Multi-tenancy: tenant_id, usuario_id

**Tabelas Relacionadas:**
- `despesas`: Gastos reais da obra
- `orcamentos_parametricos`: Orçamentos baseados em SINAPI
- `ai_insights`: Insights gerados por IA
- `chat_messages`: Histórico de conversas com IA
- `v_obras_lucratividade`: View calculada para análise de lucratividade

## Funcionalidades Principais

### 1. Listagem de Obras (`ObrasListaRefactored.tsx`)

**Características:**
- **Interface Moderna**: Cards de estatísticas com gradientes e animações
- **DataTable Avançada**: Busca, filtros e paginação
- **Métricas em Tempo Real**: Total de obras, em andamento, orçamento total, obras atrasadas
- **Ações Rápidas**: Visualizar, editar, criar orçamento IA, excluir
- **Status Inteligente**: Cálculo automático baseado em datas (Não iniciada, Planejada, Em andamento, Atrasada)

**Colunas da Tabela:**
- Nome da obra
- Endereço completo (rua, cidade, estado)
- Orçamento (formatado em R$)
- Período (data início - data fim)
- Status com badge colorido
- Ações (visualizar, editar, orçamento IA, excluir)

**Navegação:**
- `/dashboard/obras` - Listagem principal
- `/dashboard/obras/nova` - Nova obra
- `/dashboard/obras/:id` - Detalhes da obra
- `/dashboard/obras/:id/editar` - Editar obra
- `/dashboard/orcamentos/novo?obra_id=:id` - Orçamento para obra específica

### 2. Criação de Obras (`NovaObraRefactored.tsx`)

**Formulário Completo com Validação Zod:**
- **Localização**: CEP com auto-preenchimento, endereço, cidade, estado
- **Identificação**: Nome da obra, responsável (construtor/freelancer)
- **Financeiro**: Orçamento disponível, datas de início e término
- **Integração CEP**: Hook `useCEP` para busca automática de endereço
- **Carregamento Dinâmico**: Lista de construtores/freelancers do Supabase
- **UX Avançada**: Animações, loading states, validação em tempo real

### 3. Edição de Obras (`EditarObra.tsx`)

**Funcionalidades:**
- **Pré-carregamento**: Dados existentes carregados automaticamente
- **Formulário Idêntico**: Mesma estrutura da criação com dados populados
- **Validação Consistente**: Schema Zod reutilizado
- **Atualização Otimista**: Cache invalidation automática
- **Navegação Inteligente**: Retorno para detalhes após edição

### 4. Detalhamento Completo (`ObraDetalhe.tsx`)

**Interface Tabbed com 5 Abas:**

#### Aba "Detalhes"
- **Informações Básicas**: Nome, localização completa, responsável
- **Timeline e Orçamento**: Datas de início/fim, orçamento disponível
- **Métricas em Tempo Real**:
  - **Progresso**: Baseado no cronograma (% concluído)
  - **Gastos Reais**: Total de despesas, número de gastos, % do orçamento gasto
  - **Dias Restantes**: Cálculo automático até o fim previsto
- **Orçamentos Paramétricos**: Lista de orçamentos SINAPI relacionados
- **Alertas Visuais**: Indicadores para obras atrasadas ou com estouro de orçamento

#### Aba "Despesas"
- **DataTable de Despesas**: Descrição, categoria, valor, data, status de pagamento
- **Ações por Despesa**: Editar e excluir com confirmação
- **Botão "Nova Despesa"**: Integração com módulo de despesas
- **Totalizadores**: Soma automática e percentual do orçamento

#### Aba "Vendas/Lucro" (`VendaLucroTab.tsx`)
**Formulário de Vendas:**
- **Dados da Venda**: Valor, data, status (À Venda, Em Negociação, Vendido)
- **Despesas da Venda**: Comissão do corretor (%), outras despesas
- **Validação Zod**: Schema completo para dados de venda

**Análise de Lucratividade Automática:**
- **Métricas Calculadas**: Lucro bruto, lucro líquido, margem %, ROI %
- **Comparação Visual**: Valor de venda vs. custos totais
- **Status da Venda**: Badge colorido baseado no status
- **Alertas**: Avisos quando valor de venda não está definido

**Análise de Viabilidade por IA:**
- **Geração Automática**: Análise completa via DeepSeek AI
- **Conteúdo da Análise**:
  - Resumo executivo (Viável/Não Viável/Atenção)
  - Análise financeira detalhada
  - Breakdown de custos por categoria
  - Comparação com mercado local
  - Recomendações estratégicas
  - Cenários de risco e oportunidade
- **Formatação Inteligente**: Markdown sem hashtags, negrito, itálico, listas
- **Pré-requisito**: Valor de venda deve estar definido

#### Aba "Insights de IA" (`InsightsObra.tsx`)
- **Geração de Insights**: Botão para gerar análises automáticas
- **Tipos de Insight**:
  - **COST_OPTIMIZATION**: Otimização de custos
  - **RISK_PREDICTION**: Previsão de riscos
- **Widgets Informativos**: Cards com resumos e recomendações
- **Dados Estruturados**: Detalhes, recomendações, valores estimados, probabilidades
- **Atualização Automática**: Cache invalidation e refetch

#### Aba "Chat IA" (`InterfaceChat.tsx`)
- **Chat Contextual**: IA conhece todos os dados da obra específica
- **Histórico Persistente**: Mensagens salvas no banco de dados
- **Interface Moderna**: Avatares, animações, typing indicators
- **Funcionalidades**:
  - Envio de mensagens com contexto da obra
  - Limpeza de histórico com confirmação
  - Scroll automático para mensagens recentes
  - Efeito typewriter para respostas
  - Loading states e error handling
- **Integração**: Conectado com Edge Function `ai-chat-contextual`

## APIs e Serviços

### Hook Principal: `useObras.ts`
**Funcionalidades:**
- **Multi-tenant**: Filtragem automática por tenant_id
- **CRUD Completo**: Create, Read, Update, Delete
- **Cache Inteligente**: TanStack Query com invalidation
- **Mensagens de Sucesso/Erro**: Toast notifications
- **Estados de Loading**: Para todas as operações

### API Service: `obrasApi` (api.ts)
**Métodos Disponíveis:**
- `getAll()`: Busca todas as obras do tenant
- `getById(id)`: Busca obra específica
- `create(data)`: Cria nova obra com validação
- `update(id, data)`: Atualiza obra existente
- `delete(id)`: Remove obra do sistema

**Características Técnicas:**
- **Autenticação**: Verificação automática de usuário logado
- **Tenant Isolation**: Isolamento por tenant_id
- **Data Sanitization**: Limpeza e formatação de dados
- **Error Handling**: Tratamento robusto de erros
- **Logging Seguro**: Logs sem exposição de dados sensíveis

### Edge Functions de IA
**`ai-chat-contextual`:**
- Chat contextual com dados da obra
- Integração com DeepSeek AI
- Histórico persistente
- Suporte a SINAPI, orçamentos e despesas

**`ai-generate-insights`:**
- Geração automática de insights
- Análise de riscos e otimização
- Dados estruturados para widgets

**`gerar-analise-viabilidade-venda`:**
- Análise de viabilidade de vendas
- Cálculos de lucratividade
- Recomendações estratégicas

## Fluxos de Trabalho

### Fluxo de Criação de Obra
1. **Acesso**: `/dashboard/obras` → "Nova Obra"
2. **Formulário**: Preenchimento com validação Zod
3. **CEP**: Auto-preenchimento de endereço
4. **Responsável**: Seleção de construtor/freelancer
5. **Validação**: Verificação de campos obrigatórios
6. **Criação**: Chamada para `obrasApi.create()`
7. **Redirecionamento**: Para listagem com toast de sucesso

### Fluxo de Gestão Completa
1. **Listagem**: Visualização de todas as obras
2. **Detalhamento**: Acesso a informações completas
3. **Controle Financeiro**: Acompanhamento de gastos vs. orçamento
4. **Vendas**: Definição de preço e análise de lucratividade
5. **IA**: Insights automáticos e chat contextual
6. **Orçamentos**: Integração com orçamentos paramétricos

### Fluxo de Análise de Vendas
1. **Definição de Preço**: Valor, data e status da venda
2. **Despesas de Venda**: Comissões e outros custos
3. **Cálculo Automático**: Lucro bruto, líquido, margem, ROI
4. **Análise de IA**: Viabilidade e recomendações estratégicas
5. **Tomada de Decisão**: Baseada em dados e insights

## Considerações Técnicas

### Performance e Otimização
- **React Query**: Cache inteligente com invalidation automática
- **Lazy Loading**: Carregamento sob demanda de componentes
- **Memoização**: Otimização de re-renders com React.memo
- **Debounce**: Em campos de busca e filtros
- **Paginação**: Para grandes volumes de dados

### Segurança
- **Multi-tenancy**: Isolamento completo entre tenants
- **Autenticação**: Verificação em todas as operações
- **Validação**: Zod para validação de schemas
- **Sanitização**: Limpeza de dados de entrada
- **Logs Seguros**: Sem exposição de dados sensíveis

### UX/UI
- **Design System**: shadcn/ui para consistência
- **Responsividade**: Adaptação a todos os dispositivos
- **Animações**: Framer Motion para transições suaves
- **Loading States**: Feedback visual em todas as operações
- **Error Handling**: Mensagens claras e ações de recuperação

### Integração com IA
- **Chat Contextual**: Acesso a dados da obra em tempo real
- **Insights Automáticos**: Análises preditivas e otimização
- **Análise de Viabilidade**: Cálculos financeiros inteligentes
- **Embeddings**: Documentação vetorizada para melhor contexto
- **Fallbacks**: Sistema robusto com alternativas em caso de falha

## Métricas e Analytics

### KPIs Principais
- **Total de Obras**: Quantidade total no sistema
- **Obras Ativas**: Em andamento atualmente
- **Valor Total**: Soma de todos os orçamentos
- **Lucratividade Média**: ROI médio das obras vendidas
- **Tempo Médio**: Duração média dos projetos

### Dashboards
- **Visão Geral**: Cards com métricas principais
- **Análise Temporal**: Gráficos de evolução
- **Comparativos**: Análise entre obras
- **Previsões**: Projeções baseadas em IA

## Roadmap e Melhorias Futuras

### Funcionalidades Planejadas
- **Cronograma Visual**: Timeline interativo das obras
- **Gestão de Equipes**: Atribuição e acompanhamento
- **Relatórios Avançados**: Exportação em múltiplos formatos
- **Integração ERP**: Conexão com sistemas externos
- **App Mobile**: Versão nativa para dispositivos móveis

### Otimizações Técnicas
- **PWA**: Progressive Web App
- **Offline Mode**: Funcionamento sem internet
- **Real-time**: Updates em tempo real via WebSockets
- **Microservices**: Arquitetura distribuída
- **Edge Computing**: Processamento distribuído

Este detalhamento visa auxiliar no entendimento completo da página `ObrasLista.tsx` para fins de treinamento da IA e futuras manutenções.