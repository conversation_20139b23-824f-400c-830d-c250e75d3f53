# Documentação Completa: Configurações (Settings) - ObrasAI

Este documento detalha todas as funcionalidades do módulo de configurações do ObrasAI, incluindo perfil do usuário, notificações, segurança e dispositivos. Este material será utilizado para treinar uma IA que auxiliará os usuários na utilização eficiente do sistema de configurações.

## 1. Visão Geral do Módulo de Configurações

O módulo de configurações é o centro de controle personalizado do ObrasAI, permitindo que cada usuário customize sua experiência no sistema de acordo com suas preferências e necessidades. O sistema oferece um conjunto abrangente de funcionalidades organizadas em categorias específicas para facilitar a navegação e uso.

### Características Principais:
- **Interface Intuitiva**: Design moderno com navegação lateral e conteúdo principal
- **Organização por Categorias**: Configurações divididas em seções lógicas (Perfil, Notificações, Segurança, Dispositivos)
- **Validação em Tempo Real**: Formulários com validação instantânea usando Zod
- **Feedback Visual**: Animações e indicadores de progresso para melhor UX
- **Responsividade**: Interface adaptável para desktop, tablet e mobile
- **Auto-salvamento**: Configurações salvas automaticamente com feedback ao usuário

### Localização e Acesso:
- **URL**: `/settings`
- **Menu**: Acessível via menu principal do dashboard
- **Breadcrumb**: Dashboard > Configurações
- **Componente Principal**: `src/pages/Settings.tsx`

## 2. Arquitetura e Estrutura Técnica

### Stack Tecnológica das Configurações:
- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: shadcn/ui + Tailwind CSS + Framer Motion
- **Gerenciamento de Estado**: TanStack Query para server state
- **Formulários**: React Hook Form + Zod validation
- **Backend**: Supabase (Auth + Database + Edge Functions)
- **Notificações**: Sistema próprio com múltiplos canais

### Estrutura de Arquivos:
```
src/
 pages/
    Settings.tsx                    # Página principal com navegação por tabs
 components/settings/
    ProfileFormSimple.tsx           # Formulário de perfil básico
    NotificationSettings.tsx        # Sistema completo de notificações
    SecuritySettings.tsx            # Configurações de segurança
    DeviceSettings.tsx              # Gestão de dispositivos
    AppearanceSettings.tsx          # Configurações de aparência
    LanguageSettings.tsx            # Configurações de idioma
 hooks/
    useNotifications.ts             # Hook principal de notificações
    useUserPreferences.ts           # Hook de preferências do usuário
    usePhoneReminder.ts             # Hook para lembretes de telefone
 contexts/auth/
     AuthContext.tsx                 # Contexto de autenticação
```

### Banco de Dados - Tabelas Relacionadas:
```sql
-- Perfil do usuário (Supabase Auth)
auth.users (
  id, email, phone, 
  user_metadata, app_metadata,
  created_at, updated_at
)

-- Preferências do usuário
user_preferences (
  id, user_id, notifications, appearance,
  security, devices, language, timezone,
  created_at, updated_at
)

-- Sistema de notificações
notification_preferences (
  id, user_id, enabled, quiet_hours_start,
  quiet_hours_end, timezone, preferences,
  email_digest_frequency, created_at, updated_at
)

-- Histórico de notificações
notifications (
  id, user_id, type, title, message,
  priority, context_type, context_id,
  is_read, created_at, expires_at
)
```

## 3. Seção 1: Configurações de Perfil

### 3.1 Visão Geral do Perfil

A seção de perfil permite aos usuários gerenciar suas informações pessoais e de contato essenciais para o funcionamento do sistema ObrasAI.

**Componente**: `ProfileFormSimple.tsx`
**Hook Principal**: Integração direta com `useAuth()`

### 3.2 Como Acessar e Usar as Configurações de Perfil

#### Passo 1: Acessar a Seção
1. No menu de configurações, clique na aba "Perfil"
2. A seção será exibida automaticamente como padrão
3. Visualize as informações atuais do seu perfil

#### Passo 2: Campos Disponíveis

**Informações Básicas:**
- **Nome Completo**: 
  - Campo obrigatório para identificação no sistema
  - Usado em relatórios, notificações e comunicações
  - Validação: Mínimo 2 caracteres, máximo 100
  - Exemplo: "João Silva Construções"

- **Email**:
  - Campo somente leitura (não editável)
  - Definido durante o cadastro/login
  - Usado para notificações e recuperação de senha
  - Exibido para referência: "<EMAIL>"

**Informações de Contato:**
- **Telefone**:
  - Campo crítico para comunicação
  - Formato: (XX) XXXXX-XXXX
  - Validação automática de formato brasileiro
  - Usado para notificações SMS e contato de emergência
  - **Importância**: Essencial para comunicação efetiva

### 3.3 Validação e Regras de Negócio

**Schema de Validação Zod:**
```typescript
const profileSchema = z.object({
  nome: z.string()
    .min(2, "Nome deve ter pelo menos 2 caracteres")
    .max(100, "Nome deve ter no máximo 100 caracteres"),
  telefone: z.string()
    .regex(/^\(\d{2}\) \d{4,5}-\d{4}$/, "Formato inválido")
    .optional()
});
```

**Regras Específicas:**
- Nome é obrigatório e não pode estar vazio
- Telefone é opcional mas recomendado
- Email não pode ser alterado (gerenciado pelo Supabase Auth)
- Mudanças são salvas automaticamente

### 3.4 Sistema de Lembrete de Telefone

O sistema possui um mecanismo inteligente de lembrete para usuários sem telefone cadastrado:

**Hook `usePhoneReminder`:**
- Monitora se o usuário tem telefone cadastrado
- Exibe lembretes periódicos quando telefone está ausente
- Permite dispensar o lembrete temporariamente
- Controla frequência das notificações

**Funcionalidades do Lembrete:**
- **Detecção Automática**: Verifica presença do telefone no perfil
- **Notificação Não-Intrusiva**: Toast informativo sobre importância
- **Opção de Dispensar**: Botão "Não mostrar novamente"
- **Reativação**: Lembrete retorna após 7 dias se ainda sem telefone

## 4. Seção 2: Sistema de Notificações

### 4.1 Visão Geral das Notificações

O sistema de notificações do ObrasAI é uma ferramenta avançada que permite controle granular sobre como e quando o usuário recebe alertas sobre atividades do sistema.

**Componente Principal**: `NotificationSettings.tsx`
**Hook Principal**: `useNotifications.ts`
**Edge Function**: `send-notification`

### 4.2 Configurações Gerais de Notificação

#### Passo 1: Ativar/Desativar Sistema Global
1. Use o toggle "Notificações habilitadas"
2. Quando desativado, TODAS as notificações são pausadas
3. Configurações específicas ficam inacessíveis quando desabilitado
4. Mudanças são salvas automaticamente

#### Passo 2: Configurar Horário Silencioso
**Funcionalidade**: Define período sem notificações
**Campos**:
- **Ativar horário silencioso**: Toggle principal
- **Início**: Horário de início (formato 24h) - Ex: 22:00
- **Fim**: Horário de término (formato 24h) - Ex: 08:00

**Como Funciona**:
- Durante horário silencioso, notificações são pausadas
- Sistema suporta períodos que cruzam meia-noite
- Notificações urgentes podem ter tratamento especial
- Configuração baseada no timezone do usuário

### 4.3 Configurações por Categoria

O sistema organiza notificações em 6 categorias principais:

#### Categoria: Obras
**Tipos Disponíveis**:

1. **Obra Prazo Vencendo**
   - **Descrição**: Alertas quando obras estão próximas do prazo final
   - **Canais Padrão**: In-app + Email
   - **Configuração Avançada**: Antecedência em horas (padrão: 48h)
   - **Uso**: Evita atrasos em projetos críticos

2. **Obra Orçamento Excedido**
   - **Descrição**: Avisos quando gastos excedem o orçamento planejado
   - **Canais Padrão**: In-app + Email
   - **Configuração Avançada**: Percentual de limite (padrão: 110%)
   - **Uso**: Controle financeiro preventivo

3. **Obra Status Alterado**
   - **Descrição**: Notificações sobre mudanças no status das obras
   - **Canais Padrão**: In-app
   - **Uso**: Acompanhamento de progresso

#### Categoria: Leads
**Tipos Disponíveis**:

1. **Novo Lead Capturado**
   - **Descrição**: Alertas imediatos sobre novos leads via chatbot
   - **Canais Padrão**: In-app + Email
   - **Configuração Avançada**: Notificação imediata habilitada
   - **Prioridade**: Alta (resposta rápida é crucial)

#### Categoria: Contratos
**Tipos Disponíveis**:

1. **Contrato Assinado**
   - **Descrição**: Confirmações de assinatura de contratos
   - **Canais Padrão**: In-app + Email
   - **Uso**: Confirmação de marcos importantes

2. **Contrato Vencendo**
   - **Descrição**: Lembretes de contratos próximos ao vencimento
   - **Canais Padrão**: In-app + Email
   - **Configuração Avançada**: Antecedência em dias (padrão: 30 dias)

#### Categoria: Inteligência Artificial
**Tipos Disponíveis**:

1. **IA Análise Pronta**
   - **Descrição**: Notificações quando análises de IA são concluídas
   - **Canais Padrão**: In-app
   - **Uso**: Acompanhar processamento de análises

2. **IA Orçamento Gerado**
   - **Descrição**: Avisos sobre orçamentos gerados automaticamente
   - **Canais Padrão**: In-app + Email
   - **Uso**: Revisão de orçamentos paramétricos

#### Categoria: Sistema
**Tipos Disponíveis**:

1. **SINAPI Atualizado**
   - **Descrição**: Informações sobre atualizações da base SINAPI
   - **Canais Padrão**: In-app
   - **Status Padrão**: Desabilitado (pode ser muito frequente)

2. **Sistema Manutenção**
   - **Descrição**: Avisos sobre manutenções programadas
   - **Canais Padrão**: In-app + Email
   - **Prioridade**: Alta

#### Categoria: Financeiro
**Tipos Disponíveis**:

1. **Pagamento Vencendo**
   - **Descrição**: Lembretes de pagamentos a vencer
   - **Canais Padrão**: In-app + Email
   - **Configuração Avançada**: Antecedência em dias (padrão: 7 dias)

2. **Pagamento Processado**
   - **Descrição**: Confirmações de pagamentos processados
   - **Canais Padrão**: In-app
   - **Uso**: Controle de fluxo de caixa

### 4.4 Canais de Notificação

#### Canal: In-App (No Aplicativo)
- **Descrição**: Notificações dentro da interface do ObrasAI
- **Características**: 
  - Aparecem como toasts/popups
  - Ficam no histórico de notificações
  - Realtime via Supabase
  - Sempre disponível
- **Melhor Para**: Alertas imediatos durante uso ativo

#### Canal: Email
- **Descrição**: Notificações enviadas para email cadastrado
- **Características**:
  - Template HTML personalizado
  - Suporte a digest (resumo periódico)
  - Respeita horário silencioso
  - Inclui contexto da notificação
- **Melhor Para**: Informações importantes e resumos

#### Canal: Push (Futuro)
- **Descrição**: Notificações push para dispositivos móveis
- **Status**: Em desenvolvimento
- **Características Planejadas**: FCM/APNs integration

#### Canal: SMS (Futuro)
- **Descrição**: Notificações via SMS
- **Status**: Planejado
- **Uso Pretendido**: Alertas críticos apenas

### 4.5 Como Configurar Notificações Específicas

#### Passo 1: Selecionar Categoria
1. Localize a categoria desejada (Obras, Leads, Contratos, etc.)
2. Clique para expandir se necessário

#### Passo 2: Configurar Tipo Específico
1. **Ativar/Desativar**: Use o toggle ao lado do tipo
2. **Escolher Canais**: Clique nos badges de canal desejados
   - Azul = Ativo
   - Cinza = Inativo
3. **Múltiplos Canais**: Pode selecionar combinações (ex: In-app + Email)

#### Passo 3: Testar Configuração
1. Use o botão "Teste" no cabeçalho das notificações
2. Sistema enviará notificação de teste
3. Verifique se chegou pelos canais configurados

### 4.6 Resumo por Email (Digest)

**Funcionalidade**: Agrupa múltiplas notificações em email único

**Configurações**:
- **Frequência**: Nunca / Diariamente / Semanalmente
- **Horário**: Horário específico para envio (padrão: 08:00)

**Conteúdo do Digest**:
- Resumo de notificações do período
- Agrupamento por categoria
- Links diretos para ações relevantes
- Métricas e estatísticas

### 4.7 Estados Visuais e Feedback

**Indicadores de Status**:
-  Verde: Notificação ativa
- L Vermelho: Notificação desativada
- = Cinza: Sistema global desabilitado
- = Badge: Quantidade de notificações não lidas

**Feedback de Ações**:
- Toast de sucesso ao salvar configurações
- Loading states durante operações
- Mensagens de erro específicas
- Confirmações de teste

## 5. Seção 3: Configurações de Segurança

### 5.1 Visão Geral da Segurança

A seção de segurança fornece ferramentas para proteger a conta do usuário e monitorar atividades suspeitas.

**Componente**: `SecuritySettings.tsx`
**Integração**: Supabase Auth + Sistema próprio

### 5.2 Funcionalidades de Segurança

#### Autenticação em Dois Fatores (2FA)
- **Status**: Em desenvolvimento
- **Métodos Planejados**: 
  - Aplicativo autenticador (Google Authenticator, Authy)
  - SMS como backup
  - Códigos de recuperação

#### Gerenciamento de Sessões
- **Visualização**: Lista de sessões ativas
- **Informações**: Dispositivo, localização, último acesso
- **Ações**: Encerrar sessões específicas ou todas

#### Histórico de Login
- **Registros**: Tentativas de login (sucessos e falhas)
- **Dados**: IP, dispositivo, localização, timestamp
- **Alertas**: Notificações de logins suspeitos

#### Alteração de Senha
- **Processo**: Via email de recuperação do Supabase
- **Validação**: Critérios de senha segura
- **Notificação**: Confirmação por email

### 5.3 Monitoramento e Auditoria

**Logs de Atividade**:
- Alterações em configurações críticas
- Acessos de novos dispositivos
- Modificações no perfil
- Ações administrativas

**Alertas de Segurança**:
- Login de localização não usual
- Múltiplas tentativas de acesso
- Alterações de configurações importantes
- Atividade suspeita detectada

## 6. Seção 4: Configurações de Dispositivos

### 6.1 Visão Geral dos Dispositivos

Gerencia dispositivos conectados e suas permissões de acesso ao sistema.

**Componente**: `DeviceSettings.tsx`
**Funcionalidades**: Gestão de dispositivos móveis e sessões web

### 6.2 Funcionalidades de Dispositivos

#### Lista de Dispositivos Conectados
- **Desktop/Web**: Navegadores com sessões ativas
- **Mobile**: Aplicativos móveis (futuro)
- **Informações**: Nome, tipo, último acesso, localização

#### Controle de Acesso
- **Revogar Acesso**: Remove dispositivo específico
- **Revogar Todos**: Encerra todas as sessões (exceto atual)
- **Configurar Confiança**: Marca dispositivos como confiáveis

#### Notificações de Dispositivo
- **Novo Dispositivo**: Alerta quando novo dispositivo acessa
- **Localização Suspeita**: Aviso de acesso de local não usual
- **Múltiplos Acessos**: Notificação de acessos simultâneos

### 6.3 Gerenciamento de Sessões Web

**Informações por Sessão**:
- **Browser**: Chrome, Firefox, Safari, etc.
- **Sistema Operacional**: Windows, macOS, Linux, Mobile
- **IP**: Endereço IP de origem
- **Localização**: Cidade/Estado (quando disponível)
- **Último Acesso**: Timestamp da última atividade

**Ações Disponíveis**:
- **Ver Detalhes**: Informações completas da sessão
- **Encerrar Sessão**: Remove acesso específico
- **Marcar como Confiável**: Dispositivo conhecido

## 7. Configurações Adicionais (Futuras)

### 7.1 Aparência e Tema

**Componente**: `AppearanceSettings.tsx` (Em desenvolvimento)

**Configurações Planejadas**:
- **Tema**: Claro, Escuro, Automático (baseado no sistema)
- **Cores**: Paleta de cores primárias
- **Densidade**: Compacta, Confortável, Espaçosa
- **Animações**: Ativar/desativar animações
- **Fonte**: Tamanho e família da fonte

### 7.2 Idioma e Localização

**Componente**: `LanguageSettings.tsx` (Planejado)

**Configurações Futuras**:
- **Idioma**: Português (BR), Inglês, Espanhol
- **Timezone**: Fuso horário local
- **Formato de Data**: DD/MM/AAAA, MM/DD/AAAA, AAAA-MM-DD
- **Moeda**: Real (R$), Dólar (US$), Euro (€)
- **Formato de Número**: Separadores decimais

## 8. Aspectos Técnicos Avançados

### 8.1 Hooks e Gerenciamento de Estado

#### Hook `useNotifications`
**Funcionalidades**:
- Query para buscar notificações do usuário
- Mutation para criar/atualizar/deletar notificações
- Contagem de notificações não lidas
- Gerenciamento de preferências
- Realtime subscriptions

**Métodos Principais**:
```typescript
const {
  notifications,           // Lista de notificações
  unreadCount,            // Contagem não lidas
  preferences,            // Preferências do usuário
  updatePreferences,      // Atualizar configurações
  markAsRead,            // Marcar como lida
  markAllAsRead,         // Marcar todas como lidas
  sendTestNotification   // Enviar teste
} = useNotifications();
```

#### Hook `useUserPreferences`
**Funcionalidades**:
- Gerenciamento de preferências gerais
- Cache com React Query
- Persistência automática
- Valores padrão inteligentes

### 8.2 Validação e Schemas

**Schemas Zod Principais**:
```typescript
// Perfil
const profileSchema = z.object({
  nome: z.string().min(2).max(100),
  telefone: z.string().regex(/^\(\d{2}\) \d{4,5}-\d{4}$/).optional()
});

// Notificações
const notificationPrefsSchema = z.object({
  enabled: z.boolean(),
  quiet_hours_start: z.string().optional(),
  quiet_hours_end: z.string().optional(),
  preferences: z.record(z.object({
    enabled: z.boolean(),
    channels: z.array(z.enum(['in_app', 'email', 'push', 'sms']))
  }))
});
```

### 8.3 Performance e Cache

**Estratégias de Cache**:
- **React Query**: Cache inteligente com stale-while-revalidate
- **Invalidation**: Automática após mutations
- **Background Updates**: Atualizações em background
- **Optimistic Updates**: Updates otimistas para melhor UX

**Otimizações**:
- **Lazy Loading**: Componentes carregados sob demanda
- **Memoization**: React.memo para componentes pesados
- **Debounce**: Em fields de busca e filtros
- **Virtual Scrolling**: Para listas grandes (futuro)

### 8.4 Tratamento de Erros

**Estratégias de Error Handling**:
```typescript
// Error Boundaries para componentes
class SettingsErrorBoundary extends React.Component {
  // Captura erros de renderização
}

// Try-catch em mutations
const handleSaveSettings = async () => {
  try {
    await updatePreferences(newSettings);
    toast.success('Configurações salvas!');
  } catch (error) {
    console.error('Erro ao salvar:', error);
    toast.error('Erro ao salvar configurações');
  }
};

// Fallbacks para dados não disponíveis
const displayName = user?.user_metadata?.nome || 'Usuário';
```

## 9. Fluxos de Trabalho Principais

### 9.1 Fluxo de Configuração Inicial

**Primeiro Acesso**:
1. Usuário acessa Settings pela primeira vez
2. Sistema verifica preferências existentes
3. Se não existir, cria preferências padrão
4. Exibe lembrete para completar perfil
5. Sugere configuração de notificações importantes

### 9.2 Fluxo de Atualização de Perfil

**Processo Completo**:
1. Usuário acessa aba "Perfil"
2. Sistema carrega dados atuais do Supabase Auth
3. Usuário edita campos disponíveis
4. Validação em tempo real via Zod
5. Submit automático ou manual
6. Update no Supabase Auth
7. Invalidação de cache
8. Feedback de sucesso/erro

### 9.3 Fluxo de Configuração de Notificações

**Processo Detalhado**:
1. Usuário acessa aba "Notificações"
2. Sistema carrega preferências existentes
3. Se não existir, usa preferências padrão
4. Usuário configura por categoria/tipo
5. Escolha de canais de entrega
6. Teste opcional de notificação
7. Salvamento automático via mutation
8. Update em cache local
9. Confirmação visual de salvamento

### 9.4 Fluxo de Teste de Notificação

**Processo de Teste**:
1. Usuário clica "Teste" nas notificações
2. Sistema verifica se canais estão configurados
3. Chama Edge Function `send-notification`
4. Cria notificação de teste no banco
5. Envia pelos canais configurados
6. Usuário recebe notificação
7. Feedback de sucesso/erro no sistema

## 10. Orientações para Treinamento de IA

### 10.1 Cenários de Uso Comuns

**Cenário 1: Usuário Novo sem Telefone**
- Detectar ausência de telefone no perfil
- Explicar importância para comunicação
- Orientar sobre formato brasileiro correto
- Mostrar onde adicionar o telefone
- Mencionar sistema de lembretes

**Cenário 2: Configuração de Notificações**
- Identificar necessidades específicas do usuário
- Sugerir configurações baseadas no perfil
- Explicar diferenças entre canais
- Orientar sobre horário silencioso
- Demonstrar função de teste

**Cenário 3: Problemas de Notificação**
- Verificar se notificações estão habilitadas globalmente
- Confirmar configuração por categoria
- Verificar canais selecionados
- Testar funcionalidade
- Sugerir alternativas se necessário

**Cenário 4: Atualização de Perfil**
- Orientar sobre campos obrigatórios vs opcionais
- Explicar limitações (email não editável)
- Validar formato de telefone
- Confirmar salvamento automático
- Verificar reflexo em outras partes do sistema

### 10.2 Troubleshooting Comum

**Problema: "Notificação não chegou"**
- Verificar se notificações globais estão ativas
- Confirmar configuração do tipo específico
- Verificar se canal está selecionado
- Testar com notificação de teste
- Verificar horário silencioso
- Confirmar email/telefone válidos

**Problema: "Não consigo alterar email"**
- Explicar que email é gerenciado pelo sistema de auth
- Orientar sobre processo de mudança (se disponível)
- Sugerir contato com suporte se necessário
- Confirmar que é o campo correto

**Problema: "Configurações não salvam"**
- Verificar conexão com internet
- Confirmar que campos estão válidos
- Verificar se há mensagens de erro
- Tentar recarregar página
- Verificar permissões do usuário

**Problema: "Muitas notificações"**
- Orientar sobre configuração por categoria
- Sugerir uso do horário silencioso
- Explicar opção de digest por email
- Mostrar como desabilitar tipos específicos
- Configurar canais apropriados

### 10.3 Boas Práticas para Orientação

**Configuração de Perfil**:
- Sempre preencher nome completo
- Adicionar telefone para comunicação efetiva
- Manter informações atualizadas
- Verificar dados periodicamente

**Configuração de Notificações**:
- Começar com configurações padrão
- Personalizar baseado no uso real
- Usar teste para validar configuração
- Configurar horário silencioso apropriado
- Escolher canais baseado na urgência

**Segurança**:
- Revisar dispositivos conectados regularmente
- Ativar 2FA quando disponível
- Monitorar acessos suspeitos
- Manter senha segura e atualizada

### 10.4 Linguagem e Tom de Orientação

**Diretrizes para IA**:
- Use linguagem clara e direta
- Evite jargões técnicos desnecessários
- Forneça exemplos práticos
- Confirme entendimento do usuário
- Ofereça alternativas quando aplicável
- Seja paciente com usuários menos técnicos

**Exemplos de Respostas**:
-  Verde: Notificação ativa
- L Vermelho: Notificação desativada
- = Cinza: Sistema global desabilitado
- = Badge: Quantidade de notificações não lidas

**Feedback de Ações**:
- Toast de sucesso ao salvar configurações
- Loading states durante operações
- Mensagens de erro específicas
- Confirmações de teste

## 5. Seção 3: Configurações de Segurança

### 5.1 Visão Geral da Segurança

A seção de segurança fornece ferramentas para proteger a conta do usuário e monitorar atividades suspeitas.

**Componente**: `SecuritySettings.tsx`
**Integração**: Supabase Auth + Sistema próprio

### 5.2 Funcionalidades de Segurança

#### Autenticação em Dois Fatores (2FA)
- **Status**: Em desenvolvimento
- **Métodos Planejados**: 
  - Aplicativo autenticador (Google Authenticator, Authy)
  - SMS como backup
  - Códigos de recuperação

#### Gerenciamento de Sessões
- **Visualização**: Lista de sessões ativas
- **Informações**: Dispositivo, localização, último acesso
- **Ações**: Encerrar sessões específicas ou todas

#### Histórico de Login
- **Registros**: Tentativas de login (sucessos e falhas)
- **Dados**: IP, dispositivo, localização, timestamp
- **Alertas**: Notificações de logins suspeitos

#### Alteração de Senha
- **Processo**: Via email de recuperação do Supabase
- **Validação**: Critérios de senha segura
- **Notificação**: Confirmação por email

### 5.3 Monitoramento e Auditoria

**Logs de Atividade**:
- Alterações em configurações críticas
- Acessos de novos dispositivos
- Modificações no perfil
- Ações administrativas

**Alertas de Segurança**:
- Login de localização não usual
- Múltiplas tentativas de acesso
- Alterações de configurações importantes
- Atividade suspeita detectada

## 6. Seção 4: Configurações de Dispositivos

### 6.1 Visão Geral dos Dispositivos

Gerencia dispositivos conectados e suas permissões de acesso ao sistema.

**Componente**: `DeviceSettings.tsx`
**Funcionalidades**: Gestão de dispositivos móveis e sessões web

### 6.2 Funcionalidades de Dispositivos

#### Lista de Dispositivos Conectados
- **Desktop/Web**: Navegadores com sessões ativas
- **Mobile**: Aplicativos móveis (futuro)
- **Informações**: Nome, tipo, último acesso, localização

#### Controle de Acesso
- **Revogar Acesso**: Remove dispositivo específico
- **Revogar Todos**: Encerra todas as sessões (exceto atual)
- **Configurar Confiança**: Marca dispositivos como confiáveis

#### Notificações de Dispositivo
- **Novo Dispositivo**: Alerta quando novo dispositivo acessa
- **Localização Suspeita**: Aviso de acesso de local não usual
- **Múltiplos Acessos**: Notificação de acessos simultâneos

### 6.3 Gerenciamento de Sessões Web

**Informações por Sessão**:
- **Browser**: Chrome, Firefox, Safari, etc.
- **Sistema Operacional**: Windows, macOS, Linux, Mobile
- **IP**: Endereço IP de origem
- **Localização**: Cidade/Estado (quando disponível)
- **Último Acesso**: Timestamp da última atividade

**Ações Disponíveis**:
- **Ver Detalhes**: Informações completas da sessão
- **Encerrar Sessão**: Remove acesso específico
- **Marcar como Confiável**: Dispositivo conhecido

## 7. Configurações Adicionais (Futuras)

### 7.1 Aparência e Tema

**Componente**: `AppearanceSettings.tsx` (Em desenvolvimento)

**Configurações Planejadas**:
- **Tema**: Claro, Escuro, Automático (baseado no sistema)
- **Cores**: Paleta de cores primárias
- **Densidade**: Compacta, Confortável, Espaçosa
- **Animações**: Ativar/desativar animações
- **Fonte**: Tamanho e família da fonte

### 7.2 Idioma e Localização

**Componente**: `LanguageSettings.tsx` (Planejado)

**Configurações Futuras**:
- **Idioma**: Português (BR), Inglês, Espanhol
- **Timezone**: Fuso horário local
- **Formato de Data**: DD/MM/AAAA, MM/DD/AAAA, AAAA-MM-DD
- **Moeda**: Real (R$), Dólar (US$), Euro (€)
- **Formato de Número**: Separadores decimais

## 8. Aspectos Técnicos Avançados

### 8.1 Hooks e Gerenciamento de Estado

#### Hook `useNotifications`
**Funcionalidades**:
- Query para buscar notificações do usuário
- Mutation para criar/atualizar/deletar notificações
- Contagem de notificações não lidas
- Gerenciamento de preferências
- Realtime subscriptions

**Métodos Principais**:
```typescript
const {
  notifications,           // Lista de notificações
  unreadCount,            // Contagem não lidas
  preferences,            // Preferências do usuário
  updatePreferences,      // Atualizar configurações
  markAsRead,            // Marcar como lida
  markAllAsRead,         // Marcar todas como lidas
  sendTestNotification   // Enviar teste
} = useNotifications();
```

#### Hook `useUserPreferences`
**Funcionalidades**:
- Gerenciamento de preferências gerais
- Cache com React Query
- Persistência automática
- Valores padrão inteligentes

### 8.2 Validação e Schemas

**Schemas Zod Principais**:
```typescript
// Perfil
const profileSchema = z.object({
  nome: z.string().min(2).max(100),
  telefone: z.string().regex(/^\(\d{2}\) \d{4,5}-\d{4}$/).optional()
});

// Notificações
const notificationPrefsSchema = z.object({
  enabled: z.boolean(),
  quiet_hours_start: z.string().optional(),
  quiet_hours_end: z.string().optional(),
  preferences: z.record(z.object({
    enabled: z.boolean(),
    channels: z.array(z.enum(['in_app', 'email', 'push', 'sms']))
  }))
});
```

### 8.3 Performance e Cache

**Estratégias de Cache**:
- **React Query**: Cache inteligente com stale-while-revalidate
- **Invalidation**: Automática após mutations
- **Background Updates**: Atualizações em background
- **Optimistic Updates**: Updates otimistas para melhor UX

**Otimizações**:
- **Lazy Loading**: Componentes carregados sob demanda
- **Memoization**: React.memo para componentes pesados
- **Debounce**: Em fields de busca e filtros
- **Virtual Scrolling**: Para listas grandes (futuro)

### 8.4 Tratamento de Erros

**Estratégias de Error Handling**:
```typescript
// Error Boundaries para componentes
class SettingsErrorBoundary extends React.Component {
  // Captura erros de renderização
}

// Try-catch em mutations
const handleSaveSettings = async () => {
  try {
    await updatePreferences(newSettings);
    toast.success('Configurações salvas!');
  } catch (error) {
    console.error('Erro ao salvar:', error);
    toast.error('Erro ao salvar configurações');
  }
};

// Fallbacks para dados não disponíveis
const displayName = user?.user_metadata?.nome || 'Usuário';
```

## 9. Fluxos de Trabalho Principais

### 9.1 Fluxo de Configuração Inicial

**Primeiro Acesso**:
1. Usuário acessa Settings pela primeira vez
2. Sistema verifica preferências existentes
3. Se não existir, cria preferências padrão
4. Exibe lembrete para completar perfil
5. Sugere configuração de notificações importantes

### 9.2 Fluxo de Atualização de Perfil

**Processo Completo**:
1. Usuário acessa aba "Perfil"
2. Sistema carrega dados atuais do Supabase Auth
3. Usuário edita campos disponíveis
4. Validação em tempo real via Zod
5. Submit automático ou manual
6. Update no Supabase Auth
7. Invalidação de cache
8. Feedback de sucesso/erro

### 9.3 Fluxo de Configuração de Notificações

**Processo Detalhado**:
1. Usuário acessa aba "Notificações"
2. Sistema carrega preferências existentes
3. Se não existir, usa preferências padrão
4. Usuário configura por categoria/tipo
5. Escolha de canais de entrega
6. Teste opcional de notificação
7. Salvamento automático via mutation
8. Update em cache local
9. Confirmação visual de salvamento

### 9.4 Fluxo de Teste de Notificação

**Processo de Teste**:
1. Usuário clica "Teste" nas notificações
2. Sistema verifica se canais estão configurados
3. Chama Edge Function `send-notification`
4. Cria notificação de teste no banco
5. Envia pelos canais configurados
6. Usuário recebe notificação
7. Feedback de sucesso/erro no sistema

## 10. Orientações para Treinamento de IA

### 10.1 Cenários de Uso Comuns

**Cenário 1: Usuário Novo sem Telefone**
- Detectar ausência de telefone no perfil
- Explicar importância para comunicação
- Orientar sobre formato brasileiro correto
- Mostrar onde adicionar o telefone
- Mencionar sistema de lembretes

**Cenário 2: Configuração de Notificações**
- Identificar necessidades específicas do usuário
- Sugerir configurações baseadas no perfil
- Explicar diferenças entre canais
- Orientar sobre horário silencioso
- Demonstrar função de teste

**Cenário 3: Problemas de Notificação**
- Verificar se notificações estão habilitadas globalmente
- Confirmar configuração por categoria
- Verificar canais selecionados
- Testar funcionalidade
- Sugerir alternativas se necessário

**Cenário 4: Atualização de Perfil**
- Orientar sobre campos obrigatórios vs opcionais
- Explicar limitações (email não editável)
- Validar formato de telefone
- Confirmar salvamento automático
- Verificar reflexo em outras partes do sistema

### 10.2 Troubleshooting Comum

**Problema: "Notificação não chegou"**
- Verificar se notificações globais estão ativas
- Confirmar configuração do tipo específico
- Verificar se canal está selecionado
- Testar com notificação de teste
- Verificar horário silencioso
- Confirmar email/telefone válidos

**Problema: "Não consigo alterar email"**
- Explicar que email é gerenciado pelo sistema de auth
- Orientar sobre processo de mudança (se disponível)
- Sugerir contato com suporte se necessário
- Confirmar que é o campo correto

**Problema: "Configurações não salvam"**
- Verificar conexão com internet
- Confirmar que campos estão válidos
- Verificar se há mensagens de erro
- Tentar recarregar página
- Verificar permissões do usuário

**Problema: "Muitas notificações"**
- Orientar sobre configuração por categoria
- Sugerir uso do horário silencioso
- Explicar opção de digest por email
- Mostrar como desabilitar tipos específicos
- Configurar canais apropriados

### 10.3 Boas Práticas para Orientação

**Configuração de Perfil**:
- Sempre preencher nome completo
- Adicionar telefone para comunicação efetiva
- Manter informações atualizadas
- Verificar dados periodicamente

**Configuração de Notificações**:
- Começar com configurações padrão
- Personalizar baseado no uso real
- Usar teste para validar configuração
- Configurar horário silencioso apropriado
- Escolher canais baseado na urgência

**Segurança**:
- Revisar dispositivos conectados regularmente
- Ativar 2FA quando disponível
- Monitorar acessos suspeitos
- Manter senha segura e atualizada

### 10.4 Linguagem e Tom de Orientação

**Diretrizes para IA**:
- Use linguagem clara e direta
- Evite jargões técnicos desnecessários
- Forneça exemplos práticos
- Confirme entendimento do usuário
- Ofereça alternativas quando aplicável
- Seja paciente com usuários menos técnicos

**Exemplos de Respostas**:
-  Verde: Notificação ativa
- L Vermelho: Notificação desativada
- = Cinza: Sistema global desabilitado
- = Badge: Quantidade de notificações não lidas

**Feedback de Ações**:
- Toast de sucesso ao salvar configurações
- Loading states durante operações
- Mensagens de erro específicas
- Confirmações de teste

## 5. Seção 3: Configurações de Segurança

### 5.1 Visão Geral da Segurança

A seção de segurança fornece ferramentas para proteger a conta do usuário e monitorar atividades suspeitas.

**Componente**: `SecuritySettings.tsx`
**Integração**: Supabase Auth + Sistema próprio

### 5.2 Funcionalidades de Segurança

#### Autenticação em Dois Fatores (2FA)
- **Status**: Em desenvolvimento
- **Métodos Planejados**: 
  - Aplicativo autenticador (Google Authenticator, Authy)
  - SMS como backup
  - Códigos de recuperação

#### Gerenciamento de Sessões
- **Visualização**: Lista de sessões ativas
- **Informações**: Dispositivo, localização, último acesso
- **Ações**: Encerrar sessões específicas ou todas

#### Histórico de Login
- **Registros**: Tentativas de login (sucessos e falhas)
- **Dados**: IP, dispositivo, localização, timestamp
- **Alertas**: Notificações de logins suspeitos

#### Alteração de Senha
- **Processo**: Via email de recuperação do Supabase
- **Validação**: Critérios de senha segura
- **Notificação**: Confirmação por email

### 5.3 Monitoramento e Auditoria

**Logs de Atividade**:
- Alterações em configurações críticas
- Acessos de novos dispositivos
- Modificações no perfil
- Ações administrativas

**Alertas de Segurança**:
- Login de localização não usual
- Múltiplas tentativas de acesso
- Alterações de configurações importantes
- Atividade suspeita detectada

## 6. Seção 4: Configurações de Dispositivos

### 6.1 Visão Geral dos Dispositivos

Gerencia dispositivos conectados e suas permissões de acesso ao sistema.

**Componente**: `DeviceSettings.tsx`
**Funcionalidades**: Gestão de dispositivos móveis e sessões web

### 6.2 Funcionalidades de Dispositivos

#### Lista de Dispositivos Conectados
- **Desktop/Web**: Navegadores com sessões ativas
- **Mobile**: Aplicativos móveis (futuro)
- **Informações**: Nome, tipo, último acesso, localização

#### Controle de Acesso
- **Revogar Acesso**: Remove dispositivo específico
- **Revogar Todos**: Encerra todas as sessões (exceto atual)
- **Configurar Confiança**: Marca dispositivos como confiáveis

#### Notificações de Dispositivo
- **Novo Dispositivo**: Alerta quando novo dispositivo acessa
- **Localização Suspeita**: Aviso de acesso de local não usual
- **Múltiplos Acessos**: Notificação de acessos simultâneos

### 6.3 Gerenciamento de Sessões Web

**Informações por Sessão**:
- **Browser**: Chrome, Firefox, Safari, etc.
- **Sistema Operacional**: Windows, macOS, Linux, Mobile
- **IP**: Endereço IP de origem
- **Localização**: Cidade/Estado (quando disponível)
- **Último Acesso**: Timestamp da última atividade

**Ações Disponíveis**:
- **Ver Detalhes**: Informações completas da sessão
- **Encerrar Sessão**: Remove acesso específico
- **Marcar como Confiável**: Dispositivo conhecido

## 7. Configurações Adicionais (Futuras)

### 7.1 Aparência e Tema

**Componente**: `AppearanceSettings.tsx` (Em desenvolvimento)

**Configurações Planejadas**:
- **Tema**: Claro, Escuro, Automático (baseado no sistema)
- **Cores**: Paleta de cores primárias
- **Densidade**: Compacta, Confortável, Espaçosa
- **Animações**: Ativar/desativar animações
- **Fonte**: Tamanho e família da fonte

### 7.2 Idioma e Localização

**Componente**: `LanguageSettings.tsx` (Planejado)

**Configurações Futuras**:
- **Idioma**: Português (BR), Inglês, Espanhol
- **Timezone**: Fuso horário local
- **Formato de Data**: DD/MM/AAAA, MM/DD/AAAA, AAAA-MM-DD
- **Moeda**: Real (R$), Dólar (US$), Euro (€)
- **Formato de Número**: Separadores decimais

## 8. Aspectos Técnicos Avançados

### 8.1 Hooks e Gerenciamento de Estado

#### Hook `useNotifications`
**Funcionalidades**:
- Query para buscar notificações do usuário
- Mutation para criar/atualizar/deletar notificações
- Contagem de notificações não lidas
- Gerenciamento de preferências
- Realtime subscriptions

**Métodos Principais**:
```typescript
const {
  notifications,           // Lista de notificações
  unreadCount,            // Contagem não lidas
  preferences,            // Preferências do usuário
  updatePreferences,      // Atualizar configurações
  markAsRead,            // Marcar como lida
  markAllAsRead,         // Marcar todas como lidas
  sendTestNotification   // Enviar teste
} = useNotifications();
```

#### Hook `useUserPreferences`
**Funcionalidades**:
- Gerenciamento de preferências gerais
- Cache com React Query
- Persistência automática
- Valores padrão inteligentes

### 8.2 Validação e Schemas

**Schemas Zod Principais**:
```typescript
// Perfil
const profileSchema = z.object({
  nome: z.string().min(2).max(100),
  telefone: z.string().regex(/^\(\d{2}\) \d{4,5}-\d{4}$/).optional()
});

// Notificações
const notificationPrefsSchema = z.object({
  enabled: z.boolean(),
  quiet_hours_start: z.string().optional(),
  quiet_hours_end: z.string().optional(),
  preferences: z.record(z.object({
    enabled: z.boolean(),
    channels: z.array(z.enum(['in_app', 'email', 'push', 'sms']))
  }))
});
```

### 8.3 Performance e Cache

**Estratégias de Cache**:
- **React Query**: Cache inteligente com stale-while-revalidate
- **Invalidation**: Automática após mutations
- **Background Updates**: Atualizações em background
- **Optimistic Updates**: Updates otimistas para melhor UX

**Otimizações**:
- **Lazy Loading**: Componentes carregados sob demanda
- **Memoization**: React.memo para componentes pesados
- **Debounce**: Em fields de busca e filtros
- **Virtual Scrolling**: Para listas grandes (futuro)

### 8.4 Tratamento de Erros

**Estratégias de Error Handling**:
```typescript
// Error Boundaries para componentes
class SettingsErrorBoundary extends React.Component {
  // Captura erros de renderização
}

// Try-catch em mutations
const handleSaveSettings = async () => {
  try {
    await updatePreferences(newSettings);
    toast.success('Configurações salvas!');
  } catch (error) {
    console.error('Erro ao salvar:', error);
    toast.error('Erro ao salvar configurações');
  }
};

// Fallbacks para dados não disponíveis
const displayName = user?.user_metadata?.nome || 'Usuário';
```

## 9. Fluxos de Trabalho Principais

### 9.1 Fluxo de Configuração Inicial

**Primeiro Acesso**:
1. Usuário acessa Settings pela primeira vez
2. Sistema verifica preferências existentes
3. Se não existir, cria preferências padrão
4. Exibe lembrete para completar perfil
5. Sugere configuração de notificações importantes

### 9.2 Fluxo de Atualização de Perfil

**Processo Completo**:
1. Usuário acessa aba "Perfil"
2. Sistema carrega dados atuais do Supabase Auth
3. Usuário edita campos disponíveis
4. Validação em tempo real via Zod
5. Submit automático ou manual
6. Update no Supabase Auth
7. Invalidação de cache
8. Feedback de sucesso/erro

### 9.3 Fluxo de Configuração de Notificações

**Processo Detalhado**:
1. Usuário acessa aba "Notificações"
2. Sistema carrega preferências existentes
3. Se não existir, usa preferências padrão
4. Usuário configura por categoria/tipo
5. Escolha de canais de entrega
6. Teste opcional de notificação
7. Salvamento automático via mutation
8. Update em cache local
9. Confirmação visual de salvamento

### 9.4 Fluxo de Teste de Notificação

**Processo de Teste**:
1. Usuário clica "Teste" nas notificações
2. Sistema verifica se canais estão configurados
3. Chama Edge Function `send-notification`
4. Cria notificação de teste no banco
5. Envia pelos canais configurados
6. Usuário recebe notificação
7. Feedback de sucesso/erro no sistema

## 10. Orientações para Treinamento de IA

### 10.1 Cenários de Uso Comuns

**Cenário 1: Usuário Novo sem Telefone**
- Detectar ausência de telefone no perfil
- Explicar importância para comunicação
- Orientar sobre formato brasileiro correto
- Mostrar onde adicionar o telefone
- Mencionar sistema de lembretes

**Cenário 2: Configuração de Notificações**
- Identificar necessidades específicas do usuário
- Sugerir configurações baseadas no perfil
- Explicar diferenças entre canais
- Orientar sobre horário silencioso
- Demonstrar função de teste

**Cenário 3: Problemas de Notificação**
- Verificar se notificações estão habilitadas globalmente
- Confirmar configuração por categoria
- Verificar canais selecionados
- Testar funcionalidade
- Sugerir alternativas se necessário

**Cenário 4: Atualização de Perfil**
- Orientar sobre campos obrigatórios vs opcionais
- Explicar limitações (email não editável)
- Validar formato de telefone
- Confirmar salvamento automático
- Verificar reflexo em outras partes do sistema

### 10.2 Troubleshooting Comum

**Problema: "Notificação não chegou"**
- Verificar se notificações globais estão ativas
- Confirmar configuração do tipo específico
- Verificar se canal está selecionado
- Testar com notificação de teste
- Verificar horário silencioso
- Confirmar email/telefone válidos

**Problema: "Não consigo alterar email"**
- Explicar que email é gerenciado pelo sistema de auth
- Orientar sobre processo de mudança (se disponível)
- Sugerir contato com suporte se necessário
- Confirmar que é o campo correto

**Problema: "Configurações não salvam"**
- Verificar conexão com internet
- Confirmar que campos estão válidos
- Verificar se há mensagens de erro
- Tentar recarregar página
- Verificar permissões do usuário

**Problema: "Muitas notificações"**
- Orientar sobre configuração por categoria
- Sugerir uso do horário silencioso
- Explicar opção de digest por email
- Mostrar como desabilitar tipos específicos
- Configurar canais apropriados

### 10.3 Boas Práticas para Orientação

**Configuração de Perfil**:
- Sempre preencher nome completo
- Adicionar telefone para comunicação efetiva
- Manter informações atualizadas
- Verificar dados periodicamente

**Configuração de Notificações**:
- Começar com configurações padrão
- Personalizar baseado no uso real
- Usar teste para validar configuração
- Configurar horário silencioso apropriado
- Escolher canais baseado na urgência

**Segurança**:
- Revisar dispositivos conectados regularmente
- Ativar 2FA quando disponível
- Monitorar acessos suspeitos
- Manter senha segura e atualizada

### 10.4 Linguagem e Tom de Orientação

**Diretrizes para IA**:
- Use linguagem clara e direta
- Evite jargões técnicos desnecessários
- Forneça exemplos práticos
- Confirme entendimento do usuário
- Ofereça alternativas quando aplicável
- Seja paciente com usuários menos técnicos

**Exemplos de Respostas**:
-  Verde: Notificação ativa
- L Vermelho: Notificação desativada
- = Cinza: Sistema global desabilitado
- = Badge: Quantidade de notificações não lidas

**Feedback de Ações**:
- Toast de sucesso ao salvar configurações
- Loading states durante operações
- Mensagens de erro específicas
- Confirmações de teste