# Documentação Detalhada: Sistema SINAPI no ObrasAI

Este documento detalha as funcionalidades do sistema SINAPI (Sistema Nacional de Pesquisa de Custos e Índices da Construção Civil) do ObrasAI e fornece um guia passo a passo sobre como um usuário pode consultar códigos, comparar preços e utilizar dados oficiais para orçamentos. Este material será utilizado para treinar uma IA que auxiliará os usuários na utilização do sistema.

## 1. Visão Geral das Funcionalidades do Sistema SINAPI

O sistema SINAPI do ObrasAI é uma ferramenta completa e robusta para consulta e utilização de dados oficiais do Sistema Nacional de Pesquisa de Custos e Índices da Construção Civil. O sistema integra mais de 25.000 registros oficiais e oferece funcionalidades avançadas de busca, comparação e análise. As principais funcionalidades incluem:

*   **Consulta Avançada de Códigos:** Sistema de busca inteligente que permite encontrar códigos SINAPI por número, descrição ou palavras-chave, com filtros avançados por tipo, status e período.
*   **Busca Semântica:** Tecnologia de busca vetorial que compreende o contexto e significado das consultas, oferecendo resultados mais precisos e relevantes.
*   **Integração com Orçamentos:** Utilização automática de preços SINAPI oficiais na geração de orçamentos paramétricos, garantindo precisão e conformidade com padrões nacionais.
*   **Análise de Variação:** Comparação automática entre preços praticados e valores de referência SINAPI, com indicadores visuais de variação percentual.
*   **Histórico de Manutenções:** Acompanhamento completo de alterações, inclusões e exclusões de códigos SINAPI ao longo do tempo.
*   **Dados Regionalizados:** Preços específicos por estado (GO, SP, RJ, MG, RS, PR) para maior precisão regional.
*   **Validação de Qualidade:** Sistema automático de análise de qualidade de orçamentos baseado na aderência aos dados SINAPI.
*   **Rastreabilidade Completa:** Identificação da origem de cada preço utilizado, com níveis de confiabilidade calculados dinamicamente.

## 2. Como Acessar e Usar o Sistema SINAPI (Passo a Passo)

### Passo 1: Acessar a Consulta SINAPI

1.  No painel de controle do ObrasAI, navegue até a seção de `SINAPI` no menu lateral.
2.  Você será direcionado para a página de consulta avançada SINAPI.
3.  A interface apresenta um campo de busca principal e opções de filtros avançados.

### Passo 2: Realizar uma Busca Básica

#### Busca por Código SINAPI:
1.  Digite o código numérico SINAPI no campo de busca (ex: "12345").
2.  O sistema automaticamente detecta que é um código numérico e otimiza a busca.
3.  Clique no botão "Buscar" ou pressione Enter.
4.  Os resultados mostrarão o código específico e códigos relacionados.

#### Busca por Descrição:
1.  Digite palavras-chave relacionadas ao serviço ou material (ex: "concreto", "alvenaria", "pintura").
2.  O sistema utiliza busca semântica para encontrar itens relevantes.
3.  Os resultados são ordenados por relevância e similaridade.

### Passo 3: Utilizar Filtros Avançados

Para refinar sua busca, clique em "Mostrar Filtros Avançados":

#### Filtros Disponíveis:
*   **Tipo de Item:**
    - COMPOSIÇÃO: Serviços compostos com múltiplos insumos
    - INSUMO: Materiais e equipamentos básicos
    - EQUIPAMENTO: Máquinas e ferramentas
    - MÃO DE OBRA: Serviços de mão de obra especializada

*   **Status do Item:**
    - Ativo: Códigos atualmente válidos
    - Alterado: Códigos que sofreram modificações
    - Excluído: Códigos removidos do sistema
    - Incluído: Códigos recentemente adicionados

*   **Período de Referência:**
    - Data inicial e final para filtrar por período específico
    - Útil para acompanhar alterações históricas

*   **Incluir Manutenções:**
    - Opção para incluir histórico de alterações nos resultados
    - Mostra evolução de preços ao longo do tempo

### Passo 4: Interpretar os Resultados

Cada resultado da busca apresenta:

#### Informações Básicas:
*   **Código SINAPI:** Número oficial do item
*   **Descrição:** Descrição completa do serviço ou material
*   **Tipo:** Classificação (Composição, Insumo, Equipamento, etc.)
*   **Unidade:** Unidade de medida (m², m³, kg, h, etc.)
*   **Preço:** Valor de referência atual

#### Informações Técnicas:
*   **Data de Referência:** Período de validade do preço
*   **Estado:** Região de referência do preço
*   **Status:** Situação atual do código
*   **Fonte:** Origem dos dados (SINAPI Oficial)

#### Indicadores de Qualidade:
*   **Confiabilidade:** Percentual de confiabilidade do dado
*   **Última Atualização:** Data da última modificação
*   **Histórico:** Link para ver alterações históricas

## 3. Arquitetura e Estrutura Técnica

### Stack Tecnológica
- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: shadcn/ui + Tailwind CSS
- **Estado**: TanStack Query (React Query) para server state
- **Busca**: Busca semântica com embeddings vetoriais
- **Backend**: Supabase (PostgreSQL + Edge Functions)
- **Dados**: 25.000+ registros oficiais SINAPI

### Estrutura de Arquivos
```
src/
├── components/sinapi/
│   ├── ConsultaAvancada.tsx         # Interface principal de consulta
│   ├── MonitoringSinapi.tsx         # Monitoramento e métricas
│   └── index.ts                     # Exportações centralizadas
├── hooks/
│   ├── useSinapiManutencoes.ts      # Hook principal SINAPI
│   └── useSinapiEdgeFunctions.ts    # Edge Functions SINAPI
├── services/
│   └── sinapiManutencoes.ts         # API de serviços SINAPI
└── pages/dashboard/sinapi/
    └── ConsultaSinapi.tsx           # Página principal
```

### Banco de Dados

**Tabelas Principais:**

#### `sinapi_manutencoes` (25.361 registros)
- **Campos principais**: codigo_sinapi, descricao, tipo, unidade, preco_unitario
- **Metadados**: data_referencia, estado_referencia, tipo_manutencao
- **Controle**: status, fonte_dados, created_at, updated_at
- **Busca**: Índices otimizados para busca por código e texto

#### `sinapi_insumos` (4.837 registros)
- **Dados básicos**: codigo, descricao, unidade, preco
- **Regionalização**: Preços por estado (GO, SP, RJ, MG, RS, PR)
- **Classificação**: Tipo de insumo, categoria, subcategoria

#### `sinapi_composicoes_mao_obra` (7.800 registros)
- **Composições oficiais**: Serviços compostos com múltiplos insumos
- **Coeficientes técnicos**: Quantidades e proporções de cada insumo
- **Preços calculados**: Valores baseados em insumos componentes

#### `sinapi_dados_oficiais` (31 registros)
- **Metadados**: Informações sobre versões e atualizações
- **Controle de qualidade**: Validações e verificações automáticas

**Views Especializadas:**

#### `v_coeficientes_tecnicos_sinapi`
- **Integração**: Combina dados de múltiplas tabelas SINAPI
- **Otimização**: View otimizada para cálculos de orçamento
- **Rastreabilidade**: Origem e confiabilidade de cada dado

#### `v_orcamento_analise_sinapi`
- **Análise de qualidade**: Métricas de aderência aos dados SINAPI
- **Indicadores**: Percentual de itens com preços oficiais
- **Confiabilidade**: Cálculo automático de confiabilidade geral

## 4. Funcionalidades Principais

### 4.1 Consulta Avançada (`ConsultaAvancada.tsx`)

**Características:**
- **Interface Intuitiva**: Campo de busca principal com sugestões automáticas
- **Busca Inteligente**: Detecção automática de códigos numéricos vs. texto
- **Filtros Dinâmicos**: Filtros que se adaptam aos resultados encontrados
- **Paginação Eficiente**: Carregamento otimizado para grandes volumes
- **Estados de Loading**: Feedback visual durante buscas
- **Tratamento de Erros**: Mensagens claras para problemas de conectividade

**Funcionalidades de Busca:**
- **Busca por Código**: Busca exata e aproximada por códigos SINAPI
- **Busca Textual**: Busca por palavras-chave na descrição
- **Busca Semântica**: Compreensão de contexto e sinônimos
- **Busca Combinada**: Múltiplos critérios simultaneamente

### 4.2 Integração com Orçamentos

**Processo Automático:**
1. **Seleção de Coeficientes**: Sistema busca coeficientes técnicos apropriados
2. **Busca Hierárquica de Preços**:
   - 1º: Composição SINAPI oficial (95% confiabilidade)
   - 2º: Insumo SINAPI por estado (85% confiabilidade)
   - 3º: Estimativa realista (60% confiabilidade)
3. **Cálculo de Preços**: Aplicação de coeficientes e quantidades
4. **Análise de Qualidade**: Verificação automática de aderência
5. **Geração de Relatório**: Detalhamento da origem de cada preço

**Benefícios da Integração:**
- **Precisão**: Orçamentos 97.5% mais precisos que estimativas
- **Confiabilidade**: 85% de aderência aos preços oficiais SINAPI
- **Transparência**: Rastreabilidade completa da origem dos preços
- **Conformidade**: Alinhamento com padrões nacionais de construção

### 4.3 Análise de Variação e Qualidade

**Indicadores Visuais:**
- 🟢 **Normal** (variação < 10%): Preço alinhado com referência SINAPI
- 🟡 **Moderado** (variação 10-20%): Diferença moderada, aceitável
- 🔴 **Atenção** (variação > 20%): Diferença significativa, requer revisão

**Métricas Calculadas:**
- **Percentual de Aderência**: Proporção de itens com preços SINAPI
- **Variação Média**: Diferença média entre preços praticados e SINAPI
- **Confiabilidade Geral**: Índice calculado baseado nas fontes utilizadas
- **Qualidade do Orçamento**: Classificação automática da qualidade

### 4.4 Histórico e Manutenções

**Tipos de Manutenção Rastreados:**
- **INCLUSÃO**: Novos códigos adicionados ao sistema
- **ALTERAÇÃO**: Modificações em códigos existentes
- **EXCLUSÃO**: Códigos removidos ou descontinuados
- **CORREÇÃO**: Correções de dados ou descrições

**Informações Históricas:**
- **Timeline Completa**: Histórico cronológico de todas as alterações
- **Comparação de Versões**: Visualização de mudanças entre versões
- **Impacto em Orçamentos**: Análise de como alterações afetam custos
- **Notificações**: Alertas automáticos para mudanças relevantes

## 5. APIs e Serviços

### Hook Principal (`useSinapiManutencoes.ts`)

**Hooks Disponíveis:**

#### `useSinapiBuscaUnificada(filtros)`
- **Propósito**: Busca unificada com paginação e filtros
- **Parâmetros**: Objeto com filtros opcionais (tipo, status, período)
- **Retorno**: Dados paginados, loading states, funções de controle
- **Cache**: 5 minutos de stale time, 10 minutos de garbage collection

#### `useSinapiBuscaInteligente()`
- **Propósito**: Busca inteligente com detecção automática de tipo
- **Funcionalidades**: Detecção de código numérico, busca semântica
- **Estados**: Loading, error, success com feedback visual
- **Otimizações**: Debounce automático, cache inteligente

#### `useSinapiEstatisticas(filtros)`
- **Propósito**: Métricas e estatísticas do sistema SINAPI
- **Dados**: Total de registros, distribuição por tipo, atualizações
- **Atualização**: Automática a cada 30 segundos
- **Performance**: Cache otimizado para dashboards

#### `useSinapiHistorico(codigo_sinapi)`
- **Propósito**: Histórico completo de alterações de um código
- **Dados**: Timeline de manutenções, comparação de versões
- **Filtros**: Por período, tipo de manutenção, impacto
- **Visualização**: Dados formatados para componentes de timeline

#### `useSinapiValidacao(dados)`
- **Propósito**: Validação de dados SINAPI em tempo real
- **Verificações**: Formato de código, consistência de dados
- **Feedback**: Mensagens de erro específicas e sugestões
- **Performance**: Validação assíncrona não-bloqueante

### API Service (`sinapiManutencoes.ts`)

**Funções Principais:**

#### `buscarSinapiUnificado(filtros)`
```typescript
interface FiltrosBuscaUnificada {
  termo?: string;
  tipo?: string[];
  status?: string[];
  data_inicio?: string;
  data_fim?: string;
  incluir_manutencoes?: boolean;
  limit?: number;
  offset?: number;
}
```

#### `buscarHistoricoCodigo(codigo_sinapi)`
- **Retorno**: Array de manutenções ordenadas cronologicamente
- **Dados**: Tipo de manutenção, data, descrição, impacto
- **Filtros**: Período específico, tipos de alteração

#### `validarCodigoSinapi(codigo)`
- **Verificações**: Formato, existência, status atual
- **Retorno**: Boolean + detalhes de validação
- **Performance**: Cache de validações recentes

#### `obterEstatisticasManutencoes(periodo)`
- **Métricas**: Contadores por tipo, tendências, distribuições
- **Agregações**: Por período, estado, categoria
- **Formato**: Dados prontos para gráficos e dashboards

### Edge Functions

#### `ai-calculate-budget` (v5.0.0)
- **Propósito**: Cálculo de orçamentos com dados SINAPI centralizados
- **Integração**: Views otimizadas, busca hierárquica de preços
- **Performance**: 97.5% mais preciso que versões anteriores
- **Confiabilidade**: 85% de aderência aos preços oficiais

#### `sinapi-notifications`
- **Propósito**: Sistema de notificações para alterações SINAPI
- **Tipos**: Inclusões, alterações, exclusões, correções
- **Filtros**: Por relevância, impacto, período
- **Entrega**: Real-time via WebSocket, batch via email

## 6. Estrutura de Dados e Validações

### Tabelas Principais e Relacionamentos

#### Relacionamentos Chave:
```sql
-- Relacionamento entre manutenções e insumos
sinapi_manutencoes.codigo_sinapi → sinapi_insumos.codigo

-- Integração com orçamentos
itens_orcamento.sinapi_insumo_id → sinapi_insumos.id
itens_orcamento.sinapi_composicao_id → sinapi_composicoes_mao_obra.id

-- Rastreabilidade em despesas
despesas.codigo_sinapi_referencia → sinapi_manutencoes.codigo_sinapi
```

#### Índices de Performance:
- **Busca por código**: `idx_sinapi_manutencoes_codigo`
- **Busca textual**: `idx_sinapi_manutencoes_descricao_gin`
- **Filtro por tipo**: `idx_sinapi_manutencoes_tipo_status`
- **Ordenação temporal**: `idx_sinapi_manutencoes_data_referencia`

### Schemas de Validação (Zod)

#### `sinapiItemSchema`
```typescript
const sinapiItemSchema = z.object({
  codigo_sinapi: z.string().regex(/^\d{5,6}$/, "Código deve ter 5-6 dígitos"),
  descricao: z.string().min(10, "Descrição muito curta"),
  tipo: z.enum(["COMPOSIÇÃO", "INSUMO", "EQUIPAMENTO", "MÃO DE OBRA"]),
  unidade: z.string().min(1, "Unidade obrigatória"),
  preco_unitario: z.number().positive("Preço deve ser positivo"),
  data_referencia: z.date(),
  estado_referencia: z.string().length(2, "Estado deve ter 2 caracteres")
});
```

#### `filtrosBuscaSchema`
```typescript
const filtrosBuscaSchema = z.object({
  termo: z.string().optional(),
  tipo: z.array(z.string()).optional(),
  status: z.array(z.string()).optional(),
  data_inicio: z.string().datetime().optional(),
  data_fim: z.string().datetime().optional(),
  incluir_manutencoes: z.boolean().default(true),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
});
```

## 7. Integração com Outros Módulos

### 7.1 Integração com Orçamentos

**Fluxo de Integração:**
1. **Seleção de Parâmetros**: Usuário define área, padrão, localização
2. **Busca de Coeficientes**: Sistema consulta `v_coeficientes_tecnicos_sinapi`
3. **Aplicação de Preços SINAPI**: Busca hierárquica por preços oficiais
4. **Cálculo Automático**: Aplicação de quantidades e coeficientes
5. **Análise de Qualidade**: Verificação de aderência aos dados SINAPI
6. **Geração de Relatório**: Detalhamento completo com rastreabilidade

**Benefícios da Integração:**
- **Automatização**: Eliminação de busca manual de preços
- **Precisão**: Uso de dados oficiais atualizados
- **Transparência**: Origem de cada preço claramente identificada
- **Conformidade**: Alinhamento com padrões nacionais

### 7.2 Integração com Despesas

**Funcionalidades Integradas:**
- **Busca SINAPI em Despesas**: Campo de busca integrado no formulário
- **Comparação Automática**: Indicador de variação vs. preço SINAPI
- **Preenchimento Automático**: Descrição e unidade baseadas no SINAPI
- **Análise de Variação**: Classificação visual da diferença de preços

**Processo de Integração:**
1. **Busca Durante Cadastro**: Usuário busca item SINAPI relevante
2. **Seleção de Referência**: Escolha do código SINAPI mais apropriado
3. **Preenchimento Automático**: Campos preenchidos com dados SINAPI
4. **Cálculo de Variação**: Comparação automática com preço praticado
5. **Indicador Visual**: Classificação da variação (Normal/Moderado/Atenção)

### 7.3 Integração com Contratos

**Aplicações em Contratos:**
- **Cláusulas de Reajuste**: Uso de índices SINAPI para reajustes
- **Validação de Preços**: Verificação de preços contratuais vs. SINAPI
- **Análise de Competitividade**: Comparação de propostas com referências
- **Documentação Técnica**: Inclusão de códigos SINAPI em especificações

## 8. Orientações para Treinamento de IA

### 8.1 Cenários de Uso Comum

**Consulta Básica de Códigos:**
- **Pergunta**: "Qual o preço do código SINAPI 12345?"
- **Resposta**: Buscar código específico, mostrar preço atual, unidade, descrição
- **Contexto**: Incluir data de referência e estado de origem do preço

**Busca por Descrição:**
- **Pergunta**: "Preciso do código SINAPI para concreto usinado"
- **Resposta**: Usar busca semântica, listar opções relevantes
- **Orientação**: Explicar diferenças entre tipos de concreto disponíveis

**Comparação de Preços:**
- **Pergunta**: "Meu fornecedor cobra R$ 350/m³ de concreto, está caro?"
- **Resposta**: Buscar código SINAPI correspondente, calcular variação
- **Análise**: Classificar como Normal/Moderado/Atenção com explicação

**Histórico de Alterações:**
- **Pergunta**: "Como evoluiu o preço do código 12345 nos últimos 6 meses?"
- **Resposta**: Mostrar timeline de alterações, calcular tendência
- **Insights**: Identificar padrões sazonais ou tendências de mercado

### 8.2 Dicas para Orientação de Usuários

**Para Consultas Eficientes:**
- **Códigos Específicos**: Sempre preferir busca por código quando conhecido
- **Palavras-chave Relevantes**: Usar termos técnicos específicos da construção
- **Filtros Apropriados**: Orientar uso de filtros para refinar resultados
- **Contexto Regional**: Considerar diferenças de preços por estado

**Para Interpretação de Resultados:**
- **Unidades de Medida**: Sempre verificar unidade antes de comparar preços
- **Data de Referência**: Considerar período de validade dos preços
- **Tipo de Item**: Distinguir entre composições, insumos e equipamentos
- **Confiabilidade**: Explicar níveis de confiabilidade dos dados

**Para Integração com Orçamentos:**
- **Seleção Criteriosa**: Escolher códigos SINAPI mais apropriados ao contexto
- **Verificação de Aderência**: Monitorar percentual de aderência SINAPI
- **Análise de Variações**: Investigar variações significativas (>20%)
- **Documentação**: Manter registro da origem de cada preço utilizado

### 8.3 Troubleshooting Comum

**Problema: "Não encontro o código que preciso"**
- **Solução**: Usar busca por palavras-chave relacionadas
- **Alternativa**: Verificar códigos similares ou composições relacionadas
- **Orientação**: Explicar que nem todos os serviços têm código SINAPI específico

**Problema: "Preço SINAPI muito diferente do mercado local"**
- **Explicação**: SINAPI é referência nacional, pode haver variações regionais
- **Orientação**: Considerar fatores locais (logística, concorrência, sazonalidade)
- **Sugestão**: Usar como base para negociação, não como preço absoluto

**Problema: "Código SINAPI desatualizado ou excluído"**
- **Verificação**: Consultar histórico de manutenções do código
- **Alternativa**: Buscar códigos substitutos ou similares
- **Orientação**: Explicar processo de atualização do SINAPI

**Problema: "Dificuldade para interpretar composições"**
- **Explicação**: Composições incluem múltiplos insumos e coeficientes
- **Detalhamento**: Mostrar breakdown de insumos componentes
- **Orientação**: Explicar como cada insumo contribui para o preço final

### 8.4 Boas Práticas

**Para Consultas Eficazes:**
- **Seja Específico**: Use termos técnicos precisos
- **Verifique Contexto**: Considere região, período e tipo de obra
- **Compare Múltiplas Opções**: Analise códigos similares
- **Documente Escolhas**: Registre justificativas para códigos selecionados

**Para Análise de Preços:**
- **Considere o Conjunto**: Analise aderência geral, não apenas itens isolados
- **Monitore Tendências**: Acompanhe evolução de preços ao longo do tempo
- **Valide Regionalmente**: Considere especificidades do mercado local
- **Mantenha Atualizado**: Verifique periodicamente por atualizações SINAPI

**Para Integração com Projetos:**
- **Planeje Antecipadamente**: Defina códigos SINAPI na fase de projeto
- **Mantenha Consistência**: Use mesmos códigos em orçamentos e controle
- **Documente Decisões**: Registre critérios para seleção de códigos
- **Revise Periodicamente**: Atualize códigos conforme manutenções SINAPI

## 9. Funcionalidades Técnicas Avançadas

### 9.1 Performance e Cache

**Estratégias de Cache:**
- **Query Cache**: TanStack Query com 5 minutos de stale time
- **Resultado de Buscas**: Cache de resultados frequentes por 10 minutos
- **Validações**: Cache de validações de código por 1 hora
- **Estatísticas**: Cache de métricas por 30 minutos

**Otimizações de Performance:**
- **Índices Especializados**: Busca por código, texto e filtros otimizada
- **Paginação Eficiente**: Carregamento sob demanda com offset/limit
- **Debounce de Busca**: Redução de requisições desnecessárias
- **Lazy Loading**: Carregamento progressivo de dados detalhados

### 9.2 Segurança

**Controle de Acesso:**
- **Autenticação Obrigatória**: Todas as consultas requerem login
- **Tenant Isolation**: Dados isolados por empresa/usuário
- **Rate Limiting**: Limitação de consultas por minuto
- **Auditoria**: Log completo de consultas e acessos

**Validação de Dados:**
- **Sanitização de Input**: Limpeza de dados de entrada
- **Validação de Schema**: Verificação rigorosa com Zod
- **Prevenção de Injection**: Proteção contra SQL injection
- **Validação de Permissões**: Verificação de acesso por funcionalidade

### 9.3 Responsividade

**Design Responsivo:**
- **Mobile First**: Interface otimizada para dispositivos móveis
- **Breakpoints Adaptativos**: Layout que se adapta a diferentes telas
- **Touch Friendly**: Elementos dimensionados para toque
- **Performance Mobile**: Carregamento otimizado para conexões lentas

**Acessibilidade:**
- **ARIA Labels**: Rótulos apropriados para leitores de tela
- **Navegação por Teclado**: Suporte completo para navegação sem mouse
- **Contraste Adequado**: Cores que atendem padrões de acessibilidade
- **Feedback Sonoro**: Indicações auditivas para ações importantes

**Estados de Interface:**
- **Loading States**: Feedback visual durante carregamento
- **Error Boundaries**: Tratamento gracioso de erros
- **Empty States**: Orientação quando não há resultados
- **Success Feedback**: Confirmação visual de ações bem-sucedidas
