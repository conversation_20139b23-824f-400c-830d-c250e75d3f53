# Landing Page - Melhorias de Contraste e Cores - Technical Design Document

## Architecture Overview
Este design implementa melhorias sistemáticas de contraste na landing page através de uma abordagem baseada em CSS custom properties (variáveis CSS) e classes utilitárias. A solução prioriza acessibilidade, performance e manutenibilidade.

## Component Architecture

### Color System Component
**Purpose**: Sistema centralizado de cores com validação de contraste
**Dependencies**: CSS custom properties, Tailwind CSS
**Interfaces**: Classes utilitárias, variáveis CSS

```typescript
// Color System Interface
interface ColorSystem {
  primary: {
    text: string;
    textCritical: string;
    background: string;
  };
  secondary: {
    textInfo: string;
    textAux: string;
    borders: string;
  };
  accent: {
    ctaPrimary: string;
    ctaText: string;
    success: string;
    alert: string;
  };
  backgrounds: {
    sections: string;
    cards: string;
    headerFooter: string;
  };
}
```

### Typography Hierarchy Component
**Purpose**: Hierarquia tipográfica com contraste garantido
**Dependencies**: Color System, Tailwind Typography
**Interfaces**: Classes de texto responsivas

```typescript
// Typography Interface
interface TypographyScale {
  h1: {
    size: string;
    weight: string;
    color: string;
    contrast: number;
  };
  h2: {
    size: string;
    weight: string;
    color: string;
    contrast: number;
  };
  body: {
    size: string;
    weight: string;
    color: string;
    contrast: number;
  };
  caption: {
    size: string;
    weight: string;
    color: string;
    contrast: number;
  };
}
```

## Data Models & CSS Variables

### Core Color Variables
```css
/* CSS Custom Properties - Color System */
:root {
  /* Primary Colors */
  --color-text-primary: #1a1a1a;        /* Contrast: 15.8:1 */
  --color-text-critical: #000000;       /* Contrast: 21:1 */
  --color-background-primary: #ffffff;
  
  /* Secondary Colors */
  --color-text-info: #4a4a4a;          /* Contrast: 9.7:1 */
  --color-text-aux: #666666;           /* Contrast: 6.3:1 */
  --color-borders: #e0e0e0;
  
  /* Accent Colors */
  --color-cta-primary: #ff6b35;
  --color-cta-text: #ffffff;           /* Contrast: 4.8:1 on orange */
  --color-success: #2d5a2d;            /* Contrast: 8.2:1 */
  --color-alert: #d32f2f;
  
  /* Background Colors */
  --color-bg-sections: #f8f9fa;
  --color-bg-cards: #ffffff;
  --color-bg-header-footer: #1a1a1a;
  
  /* Shadows */
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-cta: 0 4px 12px rgba(255, 107, 53, 0.3);
}
```

### Tailwind CSS Extensions
```javascript
// tailwind.config.js - Color Extensions
module.exports = {
  theme: {
    extend: {
      colors: {
        'text': {
          'primary': 'var(--color-text-primary)',
          'critical': 'var(--color-text-critical)',
          'info': 'var(--color-text-info)',
          'aux': 'var(--color-text-aux)',
        },
        'cta': {
          'primary': 'var(--color-cta-primary)',
          'text': 'var(--color-cta-text)',
        },
        'status': {
          'success': 'var(--color-success)',
          'alert': 'var(--color-alert)',
        },
        'bg': {
          'sections': 'var(--color-bg-sections)',
          'cards': 'var(--color-bg-cards)',
          'header-footer': 'var(--color-bg-header-footer)',
        }
      },
      boxShadow: {
        'card': 'var(--shadow-card)',
        'cta': 'var(--shadow-cta)',
      }
    }
  }
}
```

## Component-Specific Implementations

### Header Component
```typescript
// Header with improved contrast
const Header: React.FC = () => {
  return (
    <header className="bg-bg-header-footer">
      <nav className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link to="/" className="text-white font-bold text-xl hover:text-gray-200 transition-colors">
          <img src="/logo.svg" alt="ObrasVision" className="h-8" />
          ObrasVision
        </Link>
        <Button 
          variant="cta" 
          className="bg-cta-primary text-cta-text hover:bg-orange-600 transition-all duration-200 shadow-cta"
        >
          Cadastre-se
        </Button>
      </nav>
    </header>
  );
};
```

### Hero Section
```typescript
// Hero section with critical text contrast
const HeroSection: React.FC = () => {
  return (
    <section className="relative bg-gradient-to-br from-white to-bg-sections py-20">
      <div className="container mx-auto px-4 text-center">
        <h1 className="text-5xl md:text-6xl font-bold text-text-critical mb-6 leading-tight">
          Gestão de obras com{' '}
          <span className="text-cta-primary">IA especializada</span>
        </h1>
        <p className="text-xl text-text-info mb-8 max-w-3xl mx-auto leading-relaxed">
          Plataforma completa para construtoras: controle financeiro, contratos inteligentes, 
          orçamentos precisos e insights de IA.
        </p>
        <Button 
          size="lg" 
          className="bg-cta-primary text-cta-text hover:bg-orange-600 transform hover:scale-105 transition-all duration-200 shadow-cta px-8 py-4 text-lg font-semibold"
        >
          Conversar com IA
          <ArrowRight className="ml-2 h-5 w-5" />
        </Button>
      </div>
    </section>
  );
};
```

### Problem/Solution Cards
```typescript
// Cards with improved text hierarchy
interface CardProps {
  icon: React.ReactNode;
  badge: string;
  title: string;
  description: string;
  type: 'problem' | 'solution';
}

const FeatureCard: React.FC<CardProps> = ({ icon, badge, title, description, type }) => {
  const badgeColors = {
    problem: 'bg-red-100 text-red-800',
    solution: 'bg-green-100 text-status-success'
  };

  return (
    <div className="bg-bg-cards rounded-lg p-6 shadow-card hover:shadow-lg transition-shadow duration-200">
      <div className="flex items-center mb-4">
        <div className="text-cta-primary mr-3">{icon}</div>
        <span className={`px-3 py-1 rounded-full text-sm font-medium ${badgeColors[type]}`}>
          {badge}
        </span>
      </div>
      <h3 className="text-xl font-bold text-text-critical mb-3">{title}</h3>
      <p className="text-text-info leading-relaxed">{description}</p>
    </div>
  );
};
```

### AI Features Section
```typescript
// AI features with enhanced badges and metrics
const AIFeaturesSection: React.FC = () => {
  const metrics = [
    { value: '21', label: 'Etapas de Obra', sublabel: 'Controle detalhado' },
    { value: '150+', label: 'Insumos', sublabel: 'Categorizados no sistema' },
    { value: '24/7', label: 'Disponibilidade', sublabel: 'Assistente IA sempre ativo' }
  ];

  return (
    <section className="py-20 bg-bg-sections">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-cta-primary/10 rounded-full px-4 py-2 mb-6">
            <Cpu className="h-5 w-5 text-cta-primary mr-2" />
            <span className="text-cta-primary font-semibold">Inteligência Artificial</span>
          </div>
          <h2 className="text-4xl font-bold text-text-critical mb-6">
            <span className="text-cta-primary">IA que</span>{' '}
            <span className="text-text-critical">Revoluciona</span>{' '}
            <span className="text-cta-primary">a Construção Civil</span>
          </h2>
        </div>
        
        {/* Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {metrics.map((metric, index) => (
            <div key={index} className="text-center">
              <div className="bg-cta-primary text-cta-text rounded-lg p-4 mb-3 inline-block">
                <span className="text-2xl font-bold">{metric.value}</span>
              </div>
              <h4 className="font-bold text-text-critical mb-1">{metric.label}</h4>
              <p className="text-text-info text-sm">{metric.sublabel}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
```

### Implementation Status Badge
```typescript
// Status badge with high contrast
interface StatusBadgeProps {
  status: 'implemented' | 'coming-soon' | 'premium';
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const variants = {
    implemented: {
      bg: 'bg-green-100',
      text: 'text-status-success',
      icon: <Check className="h-4 w-4" />,
      label: 'Implementado'
    },
    'coming-soon': {
      bg: 'bg-blue-100',
      text: 'text-blue-800',
      icon: <Clock className="h-4 w-4" />,
      label: 'Em breve'
    },
    premium: {
      bg: 'bg-yellow-100',
      text: 'text-yellow-800',
      icon: <Star className="h-4 w-4" />,
      label: 'Premium'
    }
  };

  const variant = variants[status];

  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-full ${variant.bg} ${variant.text}`}>
      {variant.icon}
      <span className="ml-1 text-sm font-medium">{variant.label}</span>
    </div>
  );
};
```

## Error Handling Strategy

### Color Fallbacks
```css
/* Fallback colors for older browsers */
.text-primary {
  color: #1a1a1a; /* Fallback */
  color: var(--color-text-primary);
}

.bg-cta {
  background-color: #ff6b35; /* Fallback */
  background-color: var(--color-cta-primary);
}
```

### Contrast Validation
```typescript
// Utility function to validate contrast ratios
function validateContrast(foreground: string, background: string): boolean {
  const ratio = calculateContrastRatio(foreground, background);
  return ratio >= 4.5; // WCAG AA standard
}

// Development-only contrast checker
if (process.env.NODE_ENV === 'development') {
  const contrastChecker = {
    checkElement: (element: HTMLElement) => {
      const styles = getComputedStyle(element);
      const fg = styles.color;
      const bg = styles.backgroundColor;
      
      if (!validateContrast(fg, bg)) {
        console.warn(`Low contrast detected on element:`, element);
      }
    }
  };
}
```

## Testing Strategy

### Automated Accessibility Tests
```typescript
// Jest + Testing Library + axe-core
import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('Landing Page Accessibility', () => {
  test('should have no accessibility violations', async () => {
    const { container } = render(<LandingPage />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('should meet WCAG AA contrast requirements', async () => {
    const { container } = render(<LandingPage />);
    const results = await axe(container, {
      rules: {
        'color-contrast': { enabled: true }
      }
    });
    expect(results).toHaveNoViolations();
  });
});
```

### Visual Regression Tests
```typescript
// Playwright visual testing
import { test, expect } from '@playwright/test';

test('landing page visual regression', async ({ page }) => {
  await page.goto('http://localhost:8080');
  
  // Test different sections
  await expect(page.locator('header')).toHaveScreenshot('header-contrast.png');
  await expect(page.locator('[data-testid="hero-section"]')).toHaveScreenshot('hero-contrast.png');
  await expect(page.locator('[data-testid="features-section"]')).toHaveScreenshot('features-contrast.png');
});
```

## Performance Optimizations

### CSS Optimization
```css
/* Critical CSS - inline in head */
:root {
  --color-text-primary: #1a1a1a;
  --color-text-critical: #000000;
  --color-cta-primary: #ff6b35;
}

/* Non-critical CSS - async load */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

### Bundle Optimization
```typescript
// Lazy load non-critical color utilities
const ColorUtils = lazy(() => import('./utils/colorUtils'));

// Tree-shake unused Tailwind classes
module.exports = {
  content: [
    './src/**/*.{js,ts,jsx,tsx}',
  ],
  safelist: [
    'text-text-primary',
    'text-text-critical',
    'bg-cta-primary',
    // ... other critical classes
  ]
};
```

## Implementation Considerations

### Technology Choices
- **CSS Custom Properties**: Melhor suporte a temas e manutenibilidade
- **Tailwind CSS**: Consistência e performance com purging automático
- **TypeScript**: Type safety para props de cores

### Browser Support
- CSS Custom Properties: IE11+ (com fallbacks)
- Modern CSS: Chrome 60+, Firefox 55+, Safari 12+
- Graceful degradation para navegadores antigos

### Security Measures
- **Sanitização**: Validação de valores de cor via TypeScript
- **CSP**: Content Security Policy para prevenir injection
- **Fallbacks**: Cores seguras para todos os cenários

## Migration Strategy

### Phase 1: Setup (1 dia)
1. Configurar CSS custom properties
2. Estender Tailwind config
3. Criar utility classes

### Phase 2: Critical Elements (2 dias)
1. Header e navegação
2. Hero section e CTAs principais
3. Títulos e textos críticos

### Phase 3: Content Sections (2 dias)
1. Cards de problemas/soluções
2. Seção de features de IA
3. Badges e status indicators

### Phase 4: Polish & Testing (1 dia)
1. Footer e elementos auxiliares
2. Testes de acessibilidade
3. Validação final