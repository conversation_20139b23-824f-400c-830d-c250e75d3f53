# Documentação Detalhada: Funcionalidades de Vendas no ObrasAI

Este documento detalha as funcionalidades do módulo de vendas do ObrasAI e fornece um guia passo a passo sobre como um usuário pode gerenciar vendas, preencher informações de venda e utilizar a análise de viabilidade por IA. Este material será utilizado para treinar uma IA que auxiliará os usuários na utilização do sistema.

## 1. Visão Geral das Funcionalidades de Vendas

O módulo de vendas do ObrasAI é uma ferramenta completa para gerenciar o processo comercial das obras, desde a definição do valor de venda até a análise de viabilidade financeira. O sistema integra dados de custos reais com informações de venda para fornecer análises precisas de lucratividade e ROI. As principais funcionalidades incluem:

*   **Gestão de Status de Venda:** Controle do status da obra (À Venda, Em Negociação, Vendido) com indicadores visuais e métricas.
*   **Cálculo Automático de Lucratividade:** Sistema que calcula automaticamente lucro bruto, lucro líquido, margem de lucro e ROI baseado nos custos reais e valor de venda.
*   **Análise de Viabilidade por IA:** Ferramenta de inteligência artificial que gera análises detalhadas sobre a viabilidade da venda, incluindo recomendações estratégicas.
*   **Dashboard de Vendas:** Visão consolidada de todas as obras com métricas de performance comercial e indicadores financeiros.
*   **Integração com Custos Reais:** Utilização dos dados de despesas reais para cálculos precisos de lucratividade.
*   **Gestão de Comissões:** Controle de comissões de corretores e outras despesas relacionadas à venda.
*   **Relatórios e Métricas:** Indicadores de performance como faturamento total, número de obras vendidas e ROI médio.

## 2. Como Acessar o Módulo de Vendas

### Passo 1: Navegação Principal

1.  No painel de controle do ObrasAI, navegue até a seção de `Vendas` no menu lateral.
2.  Você será direcionado para a página de listagem de vendas (`VendasLista.tsx`).
3.  A página exibe um dashboard com métricas gerais e uma tabela com todas as obras e seus status de venda.

### Passo 2: Entendendo a Interface Principal

A página principal de vendas apresenta:

*   **Cabeçalho:** Título "Gestão de Vendas" com descrição "Acompanhe o desempenho comercial das suas obras".
*   **Métricas Gerais:** Quatro cards com indicadores principais:
    - **Total de Obras:** Número total de obras cadastradas
    - **Obras Vendidas:** Quantidade de obras com status "VENDIDO"
    - **Faturamento Total:** Soma de todos os valores de venda das obras vendidas
    - **Lucro Total:** Soma de todos os lucros líquidos das obras vendidas
*   **Tabela de Obras:** Lista detalhada com informações de cada obra e opções de ação.

## 3. Como Gerenciar uma Venda (Passo a Passo)

### Passo 1: Acessar os Detalhes de uma Obra

1.  Na tabela de vendas, localize a obra desejada.
2.  Clique no botão "Ver Detalhes" (ícone de olho) na coluna de ações.
3.  Você será direcionado para a página de detalhes da venda (`VendaDetalhe.tsx`).

### Passo 2: Preencher Informações de Venda

Na página de detalhes, você encontrará o formulário "Informações de Venda" com os seguintes campos:

#### Campos Principais:

*   **Valor de Venda (R$):** 
    - Campo numérico para definir o preço de venda do imóvel
    - Valor mínimo: R$ 0,00
    - Este valor é usado para todos os cálculos de lucratividade
    - Exemplo: 450000.00 (para R$ 450.000,00)

*   **Data da Venda:**
    - Campo de data para registrar quando a venda foi concretizada
    - Formato: DD/MM/AAAA
    - Campo opcional, usado principalmente quando status = "VENDIDO"
    - Exemplo: 15/12/2024

*   **Status da Venda:**
    - Campo obrigatório com três opções:
        - **À Venda:** Imóvel disponível no mercado (padrão)
        - **Em Negociação:** Processo de negociação em andamento
        - **Vendido:** Venda concretizada
    - Cada status possui cor e ícone específicos para identificação visual

#### Campos de Custos Adicionais:

*   **Comissão do Corretor (%):**
    - Percentual de comissão para o corretor
    - Valor entre 0% e 100%
    - Usado no cálculo do lucro líquido
    - Exemplo: 6.0 (para 6%)

*   **Outras Despesas de Venda (R$):**
    - Custos adicionais relacionados à venda
    - Exemplos: taxas cartoriais, ITBI, marketing, etc.
    - Valor mínimo: R$ 0,00
    - Usado no cálculo do lucro líquido

### Passo 3: Salvar as Informações

1.  Após preencher os campos desejados, clique no botão "Atualizar Venda".
2.  O sistema validará os dados usando o schema de validação:
    ```typescript
    const vendaSchema = z.object({
      valor_venda: z.coerce.number().min(0, "Valor deve ser positivo").optional(),
      data_venda: z.string().optional(),
      status_venda: z.enum(['A_VENDA', 'EM_NEGOCIACAO', 'VENDIDO']).default('A_VENDA'),
      comissao_corretor_percentual: z.coerce.number().min(0).max(100, "Comissão deve estar entre 0% e 100%").optional(),
      outras_despesas_venda: z.coerce.number().min(0, "Valor deve ser positivo").optional(),
    });
    ```
3.  Se os dados estiverem válidos, você receberá a mensagem "Dados de venda atualizados com sucesso!".
4.  Os cálculos de lucratividade serão atualizados automaticamente.

## 4. Sistema de Cálculo de Lucratividade

### 4.1 Métricas Calculadas Automaticamente

O sistema calcula automaticamente as seguintes métricas baseadas nos dados inseridos:

*   **Lucro Bruto:** `Valor de Venda - Custo Total Real`
*   **Lucro Líquido:** `Lucro Bruto - Comissão do Corretor - Outras Despesas`
*   **Margem de Lucro (%):** `(Lucro Líquido / Valor de Venda) × 100`
*   **ROI (%):** `(Lucro Líquido / Custo Total Real) × 100`

### 4.2 Fonte dos Dados de Custo

Os custos totais reais são obtidos da view `v_obras_lucratividade` que consolida:
- Orçamento inicial da obra
- Todas as despesas registradas no sistema
- Custos de materiais, mão de obra e serviços
- Despesas administrativas e operacionais

### 4.3 Indicadores Visuais

O sistema utiliza cores para facilitar a interpretação:
- **🟢 Verde:** Valores positivos (lucro, ROI positivo)
- **🔴 Vermelho:** Valores negativos (prejuízo, ROI negativo)
- **🟡 Amarelo:** Status "Em Negociação"
- **🔵 Azul:** Status "À Venda"

## 5. Análise de Viabilidade por Inteligência Artificial

### 5.1 Como Gerar uma Análise de IA

1.  **Pré-requisito:** Certifique-se de que o valor de venda está definido.
2.  Na seção "Análise de Viabilidade por IA", clique no botão "Gerar Análise".
3.  O sistema invocará a função Edge `analise-viabilidade-venda` do Supabase.
4.  Aguarde o processamento (geralmente 30-60 segundos).
5.  A análise será exibida na seção abaixo do botão.

### 5.2 Conteúdo da Análise de IA

A IA gera uma análise completa que inclui:

#### Seções da Análise:

*   **Resumo Executivo:**
    - Viabilidade geral da venda (Viável/Não Viável/Atenção)
    - Principais indicadores financeiros
    - Recomendação principal

*   **Análise Financeira Detalhada:**
    - Breakdown dos custos por categoria
    - Análise da margem de lucro
    - Comparação com benchmarks do mercado
    - Cálculo do ponto de equilíbrio

*   **Otimização de Custos:**
    - Identificação de possíveis economias
    - Sugestões para redução de despesas
    - Análise de eficiência operacional

*   **Recomendações Estratégicas:**
    - Melhor momento para venda
    - Estratégias de precificação
    - Considerações sobre o mercado
    - Ações para maximizar o valor

*   **Riscos e Oportunidades:**
    - Fatores de risco identificados
    - Oportunidades de melhoria
    - Cenários alternativos

### 5.3 Características Técnicas da IA

A análise é gerada por:
- **Modelo:** DeepSeek AI (configurado na função Edge)
- **Dados de Entrada:** Informações completas da obra, custos reais e dados de venda
- **Prompt Especializado:** Instruções específicas para análise de viabilidade imobiliária
- **Formatação:** Markdown sem uso de caracteres "###" para títulos (usa negrito, itálico, listas)

### 5.4 Exemplo de Prompt da IA

O sistema utiliza um prompt especializado que inclui:
```
Você é um especialista em análise de viabilidade de vendas imobiliárias...
Analise os dados fornecidos e gere um relatório detalhado sobre a viabilidade da venda...
IMPORTANTE: NUNCA usar caracteres ### (hashtags) para títulos. Use formatação em negrito, itálico, listas e outras opções de markdown.
```

## 6. Estrutura de Dados e Validações

### 6.1 Tabela de Obras (Campos de Venda)

Os dados de venda são armazenados diretamente na tabela `obras` com os seguintes campos:

```sql
ALTER TABLE public.obras
ADD COLUMN valor_venda DECIMAL(14, 2),
ADD COLUMN data_venda DATE,
ADD COLUMN status_venda VARCHAR(50) DEFAULT 'A_VENDA',
ADD COLUMN comissao_corretor_percentual DECIMAL(5, 2),
ADD COLUMN outras_despesas_venda DECIMAL(12, 2);
```

### 6.2 View de Lucratividade

A view `v_obras_lucratividade` consolida dados de:
- Informações básicas da obra
- Custos totais reais (soma de todas as despesas)
- Dados de venda
- Cálculos de lucratividade

### 6.3 Tabela de Análises de IA

As análises geradas pela IA são armazenadas na tabela `ia_analises_viabilidade`:
- `id`: Identificador único
- `obra_id`: Referência à obra
- `analise_conteudo`: Texto completo da análise
- `data_analise`: Timestamp da geração
- `parametros_entrada`: JSON com dados utilizados

### 6.4 Status de Venda Válidos

O sistema aceita apenas três status:
- `A_VENDA`: Imóvel disponível para venda
- `EM_NEGOCIACAO`: Processo de negociação em andamento
- `VENDIDO`: Venda concretizada

## 7. Funcionalidades da Tabela de Vendas

### 7.1 Colunas da Tabela

A tabela principal exibe:

*   **Nome:** Nome da obra (link para detalhes)
*   **Status:** Badge colorido com o status atual
*   **Valor de Venda:** Valor formatado em moeda brasileira
*   **Lucro Líquido:** Valor com indicador de cor (verde/vermelho)
*   **ROI:** Percentual com ícone de tendência (↗️/↘️)
*   **Data da Venda:** Data formatada (se aplicável)
*   **Ações:** Botão "Ver Detalhes"

### 7.2 Funcionalidades de Busca e Filtro

*   **Busca por Nome:** Campo de busca que filtra obras pelo nome
*   **Ordenação:** Colunas clicáveis para ordenação
*   **Paginação:** Sistema de paginação para grandes volumes
*   **Estados de Loading:** Indicadores visuais durante carregamento

### 7.3 Tratamento de Erros

O sistema possui tratamento robusto de erros:
- Mensagens de erro específicas para cada tipo de falha
- Estados de loading durante operações
- Fallbacks para dados não disponíveis
- Validação em tempo real nos formulários

## 8. Integração com Outros Módulos

### 8.1 Integração com Obras

*   **Dados Base:** Utiliza informações básicas da obra (nome, endereço, orçamento)
*   **Navegação:** Links diretos entre módulos de obras e vendas
*   **Sincronização:** Atualizações automáticas entre os módulos

### 8.2 Integração com Despesas

*   **Custos Reais:** Utiliza soma de todas as despesas para cálculo de lucratividade
*   **Categorização:** Considera diferentes tipos de despesas nos cálculos
*   **Atualização Automática:** Novos gastos atualizam automaticamente os cálculos

### 8.3 Integração com Relatórios

*   **Métricas Consolidadas:** Dados de vendas alimentam relatórios gerenciais
*   **Análises de Performance:** Indicadores de ROI e margem por período
*   **Exportação:** Possibilidade de exportar dados para análises externas

## 9. Orientações para Treinamento de IA

### 9.1 Cenários de Uso Comum

**Cenário 1: Usuário quer definir valor de venda**
- Orientar sobre pesquisa de mercado
- Explicar como o valor impacta nos cálculos
- Mostrar onde visualizar a lucratividade calculada
- Sugerir uso da análise de IA para validação

**Cenário 2: Obra com ROI negativo**
- Explicar possíveis causas (custos altos, valor baixo)
- Orientar revisão de custos e despesas
- Sugerir análise de IA para recomendações
- Mostrar opções de otimização

**Cenário 3: Análise de viabilidade**
- Explicar pré-requisitos (valor de venda definido)
- Orientar sobre interpretação dos resultados
- Mostrar como usar recomendações da IA
- Explicar limitações e contexto da análise

### 9.2 Dicas para Orientação de Usuários

*   **Precisão de Dados:** Enfatizar importância de dados precisos de custos e venda
*   **Status Adequado:** Orientar sobre uso correto dos status de venda
*   **Análise Regular:** Sugerir análises periódicas para acompanhamento
*   **Interpretação de Métricas:** Explicar significado de ROI, margem de lucro, etc.
*   **Uso da IA:** Orientar sobre quando e como usar a análise de viabilidade

### 9.3 Troubleshooting Comum

**Problema: "Defina o valor de venda antes de gerar a análise"**
- Verificar se campo "Valor de Venda" está preenchido
- Confirmar que valor é maior que zero
- Salvar dados antes de gerar análise

**Problema: "Lucro líquido negativo"**
- Revisar custos totais da obra
- Verificar se todas as despesas foram registradas corretamente
- Considerar ajuste no valor de venda
- Analisar comissões e outras despesas

**Problema: "Análise de IA não carrega"**
- Verificar conexão com internet
- Aguardar tempo de processamento (até 2 minutos)
- Verificar se valor de venda está definido
- Tentar novamente após alguns minutos

**Problema: "ROI muito baixo"**
- Analisar estrutura de custos
- Verificar se todos os gastos foram registrados
- Considerar otimizações sugeridas pela IA
- Revisar estratégia de precificação

### 9.4 Boas Práticas

*   **Atualização Regular:** Manter dados de venda sempre atualizados
*   **Análise Periódica:** Gerar análises de IA regularmente
*   **Controle de Custos:** Monitorar despesas que impactam lucratividade
*   **Documentação:** Registrar observações importantes sobre a venda
*   **Benchmarking:** Comparar métricas entre diferentes obras
*   **Planejamento:** Usar análises para planejamento de futuras obras

### 9.5 Métricas de Sucesso

Indicadores para avaliar performance de vendas:
- **ROI Médio:** Acima de 15% é considerado bom
- **Margem de Lucro:** Entre 20-30% é saudável
- **Tempo de Venda:** Acompanhar tempo médio no mercado
- **Taxa de Conversão:** Percentual de obras vendidas vs total

## 10. Funcionalidades Técnicas Avançadas

### 10.1 Sistema de Cache e Performance

*   **React Query:** Gerenciamento inteligente de cache para dados de vendas
*   **Invalidação Automática:** Cache atualizado automaticamente após mudanças
*   **Loading States:** Estados de carregamento otimizados para melhor UX
*   **Error Boundaries:** Tratamento robusto de erros em componentes

### 10.2 Validação e Segurança

*   **Zod Schemas:** Validação tipada em tempo de execução
*   **Sanitização:** Limpeza de dados de entrada
*   **Autorização:** Controle de acesso baseado em tenant
*   **Auditoria:** Log de alterações em dados críticos

### 10.3 Responsividade e Acessibilidade

*   **Design Responsivo:** Interface adaptável para mobile e desktop
*   **Acessibilidade:** Componentes compatíveis com leitores de tela
*   **Temas:** Suporte a modo claro e escuro
*   **Animações:** Transições suaves com Framer Motion

Este documento serve como base completa para o treinamento da IA, garantindo que ela possa orientar os usuários de forma precisa e eficiente sobre a gestão de vendas no ObrasAI, desde conceitos básicos até funcionalidades avançadas de análise de viabilidade por inteligência artificial.