# Variáveis de ambiente do Supabase


VITE_SUPABASE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTk0NzQ5NywiZXhwIjoyMDYxNTIzNDk3fQ.y_wdHHgs1O4KkvonDDUUGlPGr0K42cMEXVYrmB7JolE


# Supabase Configuration
VITE_SUPABASE_URL=https://anrphijuostbgbscxmzx.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU5NDc0OTcsImV4cCI6MjA2MTUyMzQ5N30.6I89FlKMWwckt7xIqt6i9HxrI0MkupzWIbKlhINblUc

# Configurações para Scripts Python (Importação SINAPI)
SUPABASE_URL=https://anrphijuostbgbscxmzx.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU5NDc0OTcsImV4cCI6MjA2MTUyMzQ5N30.6I89FlKMWwckt7xIqt6i9HxrI0MkupzWIbKlhINblUc
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTk0NzQ5NywiZXhwIjoyMDYxNTIzNDk3fQ.y_wdHHgs1O4KkvonDDUUGlPGr0K42cMEXVYrmB7JolE
SUPABASE_DB_PASSWORD=Consig+45Mira
# Configurações SINAPI
SINAPI_BATCH_SIZE=100
SINAPI_DEFAULT_MONTH=2024-12

# JWT Secret (para desenvolvimento local)
JWT_SECRET=9147f03f01c7ea78e584723d6d7c914badd8047f6383646a7777fa408c43b9b53c7c28e4bb870e05bb7d295edc798c2d2d2bcb558c95052ced806b7ad57150c8

VITE_ENCRYPTION_KEY=c903ff5752c2986b201b6eb3d640c48a27e0a6a035c104bbd407b4a965a9550e

# API Keys (externos)
DEEPSEEK_API_KEY=***********************************
OPENAI_API_KEY=********************************************************************************************************************************************************************
GEMINI_API_KEY=AIzaSyAo2UfS9yZCyEGmXHAxxfQf_r3l0kW9xVE
OPENROUTER_API=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
# Google Cloud Vision
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Security
ALLOWED_ORIGINS=http://localhost:8080,https://yourdomain.com

# Environment
NODE_ENV=development

   STRIPE_SECRET_KEY=sk_test_51RIrj2PfkzlpDql6lKKIWrCjKiZ06kvbLj3SPc05Mhd2wSdCp2E2KPoWiyuke8GXWz8zElibdPNmLkgobiUWebud00Bbzt0O52
   STRIPE_WEBHOOK_SECRET=whsec_...
   SITE_URL=https://obrasai.com.br

# LEMBRETE DE SEGURANÇA:
# 1. Copie este arquivo para .env
# 2. Substitua TODOS os valores por credenciais reais
# 3. NUNCA commit o arquivo .env
# 4. Use valores diferentes para dev/staging/prod
# 5. Gere chaves fortes e únicas 
# 6. As configurações SINAPI são para scripts Python de importação 