import requests
import os
import time

# Documento específico de fornecedores para enviar
documento_fornecedores = {
    "path": "docs/fornecedores/documentacao_fornecedores.md",
    "tipo": "fornecedores",
    "nome": "documentacao_fornecedores.md"
}

SUPABASE_URL = "https://anrphijuostbgbscxmzx.supabase.co"
EDGE_FUNCTION = "/functions/v1/gerar-embeddings-documentacao"
API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU5NDc0OTcsImV4cCI6MjA2MTUyMzQ5N30.6I89FlKMWwckt7xIqt6i9HxrI0MkupzWIbKlhINblUc"

CHUNK_SIZE = 2000  # caracteres


def split_chunks(text, size=CHUNK_SIZE):
    """Quebra o texto em chunks por parágrafo, respeitando o tamanho máximo"""
    paragraphs = text.split('\n\n')
    chunks = []
    current = ""
    for p in paragraphs:
        if len(current) + len(p) + 2 <= size:
            current += ("\n\n" if current else "") + p
        else:
            if current:
                chunks.append(current)
            current = p
    if current:
        chunks.append(current)
    return chunks


def enviar_chunks(documento):
    """Envia os chunks do documento para o Supabase"""
    print(f"Processando documento: {documento['path']}")
    
    # Verifica se o arquivo existe
    if not os.path.exists(documento["path"]):
        print(f"ERRO: Arquivo não encontrado: {documento['path']}")
        return False
    
    # Lê o conteúdo do arquivo
    try:
        with open(documento["path"], "r", encoding="utf-8") as f:
            texto = f.read()
    except Exception as e:
        print(f"ERRO ao ler arquivo: {e}")
        return False
    
    # Divide em chunks
    chunks = split_chunks(texto)
    print(f"Enviando {len(chunks)} chunks para {documento['nome']} ({documento['tipo']})...")
    
    # Envia cada chunk
    sucesso_total = True
    for i, chunk in enumerate(chunks):
        payload = {
            "documento": documento["tipo"],
            "chunks": [
                {
                    "conteudo": chunk,
                    "nome_documento": documento["nome"]
                }
            ]
        }
        
        headers = {
            "Content-Type": "application/json",
            "apikey": API_KEY,
            "Authorization": f"Bearer {API_KEY}"
        }
        
        try:
            resp = requests.post(
                SUPABASE_URL + EDGE_FUNCTION,
                json=payload,
                headers=headers,
                timeout=60
            )
            
            if resp.status_code == 200:
                print(f"✅ Chunk {i+1}/{len(chunks)} enviado com sucesso.")
            else:
                print(f"❌ ERRO no chunk {i+1}/{len(chunks)}: {resp.status_code} - {resp.text}")
                sucesso_total = False
                
        except Exception as e:
            print(f"❌ ERRO de conexão no chunk {i+1}/{len(chunks)}: {e}")
            sucesso_total = False
        
        # Pausa para evitar sobrecarga na API
        time.sleep(1)
    
    return sucesso_total


def verificar_documento_existente():
    """Verifica se o documento de fornecedores já existe no Supabase"""
    print("Verificando se o documento já existe no banco...")
    # Esta verificação seria feita via API, mas como já verificamos via MCP,
    # sabemos que o documento de fornecedores pode precisar ser atualizado
    print("Documento de fornecedores será enviado/atualizado. Prosseguindo com o envio.")
    return False


if __name__ == "__main__":
    print("=== ENVIO DE EMBEDDINGS - DOCUMENTAÇÃO DE FORNECEDORES ===" )
    print(f"Documento a ser processado: {documento_fornecedores['nome']}")
    print(f"Tipo: {documento_fornecedores['tipo']}")
    print(f"Caminho: {documento_fornecedores['path']}")
    print("="*60)

    # Verifica se já existe (opcional)
    verificar_documento_existente()

    # Envia o documento
    sucesso = enviar_chunks(documento_fornecedores)

    if sucesso:
        print("\n🎉 Processo finalizado com sucesso!")
        print("✅ Documentação de fornecedores enviada para o Supabase")
        print("✅ Embeddings gerados e armazenados")
        print("✅ Chat IA agora tem acesso às informações atualizadas")
    else:
        print("\n⚠️  Processo finalizado com erros!")
        print("❌ Alguns chunks podem não ter sido enviados corretamente")
        print("💡 Verifique os logs acima para mais detalhes")

    print("\n📊 Para verificar os dados no Supabase:")
    print("   - Acesse a tabela 'embeddings_conhecimento'")
    print("   - Filtre por titulo = 'documentacao_fornecedores.md'")
    print("   - Verifique se os chunks foram criados corretamente")
    print("\n🤖 O Chat IA de Fornecedores agora está treinado com:")
    print("   - Funcionalidades do sistema de fornecedores")
    print("   - Orientações para cadastro PJ e PF")
    print("   - Informações sobre o Chat IA")
    print("   - Troubleshooting e melhores práticas")