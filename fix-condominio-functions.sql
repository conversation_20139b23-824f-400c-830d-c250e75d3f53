-- Script para corrigir funções de condomínio
-- Execute este script diretamente no Supabase Dashboard

-- Corrigir função get_condominio_details
CREATE OR REPLACE FUNCTION public.get_condominio_details(p_obra_id uuid)
RETURNS jsonb AS $$
DECLARE
    condominio_master jsonb;
    unidades jsonb;
    result jsonb;
    v_tenant_id uuid;
BEGIN
    -- Obter o tenant_id do usuário atual
    SELECT auth.uid() INTO v_tenant_id;
    
    -- Garan<PERSON>r que o projeto solicitado é um CONDOMINIO_MASTER e pertence ao tenant
    IF NOT EXISTS (
        SELECT 1
        FROM obras
        WHERE id = p_obra_id 
        AND tipo_projeto = 'CONDOMINIO_MASTER'
        AND tenant_id = v_tenant_id
    ) THEN
        RAISE EXCEPTION 'Projeto com ID % não encontrado ou não é um condomínio master válido.', p_obra_id;
    END IF;

    -- <PERSON><PERSON> os detalhes da obra "mãe" (master)
    SELECT to_jsonb(o.*)
    INTO condominio_master
    FROM obras o
    WHERE o.id = p_obra_id
    AND o.tenant_id = v_tenant_id;

    -- <PERSON>car os detalhes das obras "filhas" (unidades)
    SELECT jsonb_agg(to_jsonb(u.*))
    INTO unidades
    FROM obras u
    WHERE u.parent_obra_id = p_obra_id
    AND u.tenant_id = v_tenant_id;

    -- Montar o resultado final
    result := jsonb_build_object(
        'master', condominio_master,
        'unidades', COALESCE(unidades, '[]'::jsonb)
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Garantir permissões
GRANT EXECUTE ON FUNCTION public.get_condominio_details(uuid) TO authenticated;

-- Corrigir função get_condominio_dashboard
CREATE OR REPLACE FUNCTION public.get_condominio_dashboard(p_obra_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result jsonb;
    v_tenant_id uuid;
BEGIN
    -- Obter o tenant_id do usuário atual
    SELECT auth.uid() INTO v_tenant_id;
    
    -- Verificar se é um CONDOMINIO_MASTER e pertence ao tenant
    IF NOT EXISTS (
        SELECT 1 FROM public.obras
        WHERE id = p_obra_id 
        AND tipo_projeto = 'CONDOMINIO_MASTER'
        AND tenant_id = v_tenant_id
    ) THEN
        RAISE EXCEPTION 'Obra não encontrada ou não é um condomínio master';
    END IF;

    -- Função para calcular progresso baseado no status
    WITH progresso_calculado AS (
        SELECT 
            o.*,
            CASE 
                WHEN o.status = 'concluida' THEN 100
                WHEN o.status = 'em_andamento' THEN 50
                WHEN o.status = 'pausada' THEN 25
                WHEN o.status = 'planejamento' THEN 10
                ELSE 0
            END as progresso_status
        FROM public.obras o
        WHERE o.parent_obra_id = p_obra_id
        AND o.tipo_projeto = 'UNIDADE_CONDOMINIO'
        AND o.tenant_id = v_tenant_id
    )
    SELECT jsonb_build_object(
        'estatisticas', jsonb_build_object(
            'total_unidades', COUNT(*),
            'progresso_medio', COALESCE(AVG(progresso_status), 0),
            'custo_total', COALESCE(SUM(COALESCE(orcamento, 0)), 0),
            'unidades_concluidas', COUNT(*) FILTER (WHERE progresso_status = 100)
        ),
        'unidades', COALESCE(jsonb_agg(
            jsonb_build_object(
                'id', id,
                'identificador_unidade', identificador_unidade,
                'nome', nome,
                'progresso', progresso_status
            )
        ), '[]'::jsonb)
    ) INTO result
    FROM progresso_calculado;

    RETURN result;
END;
$$;

-- Garantir permissões
GRANT EXECUTE ON FUNCTION public.get_condominio_dashboard(uuid) TO authenticated;