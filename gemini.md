# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

**Regra Essencial:** Sempre me comunicar em **português brasileiro**.

---

## 🎯 Visão Geral do Projeto

- **Produto:** ObrasAI é uma plataforma SaaS completa para gestão de obras na
  construção civil, com foco em automação, controle de custos e inteligência
  artificial especializada.
- **Status Atual:** O sistema principal (v2.2) está **100% implementado e
  funcional**, incluindo módulos de gestão, IA contextual, captura de leads,
  orçamento paramétrico, sistema SINAPI, assinaturas e contratos inteligentes.
- **Missão:** Revolucionar a gestão de obras no Brasil com tecnologia, IA e
  automação.

---

## 🛠️ Stack Tecnológica Principal

- **Frontend:** React 18+ com TypeScript, Vite, Tailwind CSS e shadcn/ui.
- **Gerenciamento de Estado:** TanStack Query (abstraído por hooks customizados).
- **Formulários:** React Hook Form com Zod para validação.
- **Backend & Infra:** Supabase (PostgreSQL, Auth, Storage, Edge Functions em
  Deno/TypeScript).
- **Automação:** n8n Cloud para workflows.
- **APIs de IA:** DeepSeek API, OpenAI API (para Embeddings).
- **Pagamentos:** Stripe.

---

## 🚨 Regra Crítica de Arquitetura

- **Limite de Tamanho de Arquivos:** Arquivos não devem exceder **400-500 linhas
  de código**. Arquivos maiores devem ser **obrigatoriamente refatorados** em
  módulos menores, aplicando os padrões de composição do projeto.

---

## 🏗️ Arquitetura e Estrutura

### Frontend (`src/`)

- `components/`: Componentes reutilizáveis (UI, AI, Dashboard, etc.).
- `pages/`: Páginas principais da aplicação.
- `hooks/`: Custom hooks para lógica de negócios (`useObras`, `useContratoAI`, `useCrudOperations`).
- `services/`: Comunicação com APIs externas (Supabase, IA, etc.).
- `lib/`: Utilitários, validações (`zod`) e configurações.
- `contexts/`: Contextos globais da aplicação (Auth, Theme, Loading).
- `types/`: **Sistema centralizado de tipos TypeScript**.

### Sistema de Contextos (`src/contexts/`)

- **AuthContext**: Autenticação com tenant isolation e profile management
- **LoadingContext**: Estados de loading globais com priorização de operações
- **ThemeContext**: Tema dark/light persistente
- **Padrão de Providers**: Contextos compostos hierarquicamente na aplicação

### Sistema de Tipos (`src/types/`)

- **Tipos de Domínio**: `Obra`, `Contrato`, `Fornecedor` com validação Zod
- **Tipos de API**: Responses e requests das Edge Functions
- **Utility Types**: `WithTimestamps`, `WithTenant`, `CrudOperations`
- **Database Types**: Tipos gerados automaticamente do Supabase

### Backend (`supabase/`)

- `functions/`: Mais de 30 Edge Functions para lógicas específicas, incluindo:
  - `_shared/`: Utilities compartilhados (auth-handler, logger, validation-schemas, cors)
  - `ai-chat/`, `ai-chat-contextual/`: Sistema de chat com IA
  - `contrato-ai-assistant/`: Assistente IA para contratos
  - `analise-viabilidade-venda/`: Análise de viabilidade para vendas
  - `lead-capture/`, `lead-capture-v2/`: Captura de leads
  - `fornecedores-chat/`: Chat específico para fornecedores
  - `analise-planta-ia/`: Análise de plantas com IA
  - `buscar-licitacoes/`: Busca de licitações
  - `sinapi-*`: Funções relacionadas ao SINAPI
- `migrations/`: Migrações do banco de dados PostgreSQL (Infraestrutura como Código)

### Arquitetura de Componentes UI (`src/components/ui/`)

- **Hierarquia de Layout**: `PageHeader` → `FormWrapper` → `FormSection` → Componentes específicos
- **Sistema de Cards**: `GradientCard` para estatísticas, `Card` para conteúdo geral
- **Componentes Base**: shadcn/ui como fundação, estendidos com lógica específica
- **Padrão de Composição**: Componentes complexos compostos de componentes menores
- **Notification System**: `notification-indicator.tsx` integrado com sistema de notificações

### Banco de Dados (Tabelas Principais)

- `leads`: Captura de leads do chatbot.
- `obras`: Gerenciamento das obras (inclui campos de vendas: valor_venda, data_venda, status_venda, comissao_corretor_percentual, outras_despesas_venda).
- `contratos`: Contratos inteligentes com histórico e status.
- `ia_contratos_interacoes`: Log e analytics de todas as interações com a IA de
  contratos.
- `sinapi_manutencoes`: Base de dados oficial SINAPI para manutenções e reformas.
- `embeddings_conhecimento`: Vetores de embeddings para busca semântica.
- Outras: `fornecedores_pj`, `fornecedores_pf`, `despesas`, `notas_fiscais`,
  etc.

---

## ⚙️ Comandos Essenciais

### Desenvolvimento
- **Instalar dependências:** `npm install`
- **Rodar ambiente de desenvolvimento:** `npm run dev`
- **Build para produção:** `npm run build`
- **Build desenvolvimento:** `npm run build:dev`
- **Preview da build:** `npm run preview`

### Qualidade de Código
- **Verificar qualidade do código:** `npm run lint`
- **Corrigir problemas de lint:** `npm run lint:fix`
- **Verificar imports não utilizados:** `npm run lint:unused`
- **Organizar imports:** `npm run organize:imports`
- **Verificar tipagem TypeScript:** `npm run type-check`
- **Verificar organização do código:** `npm run check:organization`

### Testes
- **Executar todos os testes:** `npm run test`
- **Testes unitários:** `npm run test:unit`
- **Testes de integração:** `npm run test:integration`
- **Testes de acessibilidade:** `npm run test:accessibility`
- **Testes com interface visual:** `npm run test:ui`
- **Testes em modo watch:** `npm run test:watch`
- **Cobertura de testes:** `npm run test:coverage`
- **Auditoria de acessibilidade:** `npm run audit:accessibility`
- **Relatório de acessibilidade:** `npm run test:accessibility:report`

### Supabase
- **Criar uma nova migração:** `supabase migration new <nome_da_migracao>`
- **Aplicar migrações no banco local:** `supabase db push`
- **Deploy Edge Functions:** `supabase functions deploy --no-verify-jwt`
- **Deploy função específica:** `supabase functions deploy <nome_da_funcao> --no-verify-jwt`
- **Logs de Edge Functions:** `supabase functions logs <nome_da_funcao>`

### Validação e Scripts
- **Validar schema:** `npm run validate:schema`
- **Validar pipeline:** `npm run validate:pipeline`
- **Validar edge functions:** `npm run validate:edge-functions`
- **Migrar edge functions:** `npm run migrate:edge-functions`

### Análise e Auditoria
- **Analisar bundle:** `npm run analyze`
- **Analisar e servir bundle:** `npm run analyze:serve`

---

## 🤖 Diretrizes para Claude Code

1.  **Analisar Antes de Agir:** Sempre analisar o código existente e as regras
    deste documento antes de propor uma solução.

2.  **Usar Padrões Existentes (Crítico):** Antes de criar lógica customizada,
    **verificar obrigatoriamente** se a funcionalidade pode ser implementada com
    os padrões existentes:
    - Para CRUDs: **usar o hook genérico `useCrudOperations`** em `src/hooks/useCrudOperations.ts`
    - Para mutações de formulário: **usar o hook `useFormMutation`** em `src/hooks/useFormMutation.ts`
    - Para estrutura de páginas: **usar os componentes `PageHeader` e `FormWrapper`** em `src/components/ui/`
    - Para cards de estatísticas: **usar o componente `GradientCard`** em `src/components/ui/GradientCard.tsx`
    - Para seções de formulário: **usar `FormSection`** em `src/components/ui/FormSection.tsx`
    - Para tratamento de erros: **usar `useErrorHandler`** em `src/hooks/useErrorHandler.ts`
    - Para loading states: **usar `LoadingContext`** em `src/contexts/LoadingContext.tsx`

3.  **Arquitetura de Hooks Avançada:** O projeto usa uma arquitetura baseada em custom hooks:
    - **Hooks de negócio**: `useObras`, `useContratos`, `useAIQuota` para funcionalidades específicas
    - **Hooks genéricos**: `useCrudOperations` abstrai operações CRUD com TanStack Query
    - **Hooks de UI**: `useFormMutation` combina React Hook Form + TanStack Query + toast
    - **Padrão de composição**: Hooks complexos são compostos de hooks menores
    - **Query Keys**: Sistema centralizado em `src/lib/queries/` para invalidação consistente

4.  **Sistema de Validação Dual:** 
    - **Frontend**: Schemas Zod em `src/lib/validations/` para validação de UI
    - **Backend**: Schemas em `supabase/functions/_shared/validation-schemas.ts` para Edge Functions
    - **Importante**: Manter sincronia entre schemas frontend e backend
    - **Sanitização**: Usar `detectMaliciousPatterns` para inputs sensíveis

5.  **Edge Functions (Backend):**
    - **Template obrigatório**: `supabase/functions/_shared/function-template.ts`
    - **Autenticação**: `auth-handler.ts` com funções `requireAuth`, `requireAuthAndTenant`
    - **Logging seguro**: `logger.ts` com sanitização automática de dados sensíveis
    - **CORS**: Headers padronizados em `cors.ts`
    - **Validação**: Schemas centralizados em `validation-schemas.ts`
    - **Quota de IA**: `ai-quota-checker.ts` para controle de uso

6.  **Segurança e RLS (Crítico):**
    - **Isolamento por tenant**: Todas as consultas devem incluir filtro `tenant_id`
    - **Políticas RLS**: Usar padrão `[tabela]_tenant_isolation` para isolamento
    - **Logging**: Nunca logar dados sensíveis (usar `secureLogger`)
    - **Validação**: Sempre validar propriedade de recursos antes de acesso

---

## 🏗️ Padrões de Desenvolvimento Específicos

### **Padrão de Hooks Compostos**
- **Hooks de negócio** (`useObras`, `useContratos`) usam `useCrudOperations` internamente
- **Hooks de UI** (`useFormMutation`) combinam React Hook Form + TanStack Query + toast
- **Hooks de efeito** (`useErrorHandler`, `useNotifications`) gerenciam side effects

### **Padrão de Componentes de Layout**
```typescript
// Estrutura padrão para páginas
<PageHeader title="Título" />
<FormWrapper>
  <FormSection title="Seção">
    {/* Conteúdo específico */}
  </FormSection>
</FormWrapper>
```

### **Padrão de Edge Functions**
- **Sempre usar** `function-template.ts` como base
- **Autenticação**: `requireAuthAndTenant` para isolamento por tenant
- **Validação**: Schema Zod + `detectMaliciousPatterns` 
- **Logging**: `secureLogger` para sanitização automática
- **Quota**: `checkAndIncrementAIUsage` para funções de IA

### **Padrão de Queries TanStack**
- **Query Keys**: Centralizadas em `src/lib/queries/`
- **Invalidação**: Usar `queryClient.invalidateQueries` após mutações
- **Optimistic Updates**: Para operações CRUD simples
- **Background Refetch**: Configurado para dados críticos

### **Padrão de Validação**
- **Frontend**: Schemas Zod em `src/lib/validations/`
- **Backend**: Schemas em `_shared/validation-schemas.ts`
- **Sincronia**: Manter schemas alinhados entre frontend e backend
- **Sanitização**: Aplicar `detectMaliciousPatterns` em inputs sensíveis

---

## ✅ Checklist de Boas Práticas de Código

1.  **Evitar Duplicação de Código (DRY):** Utilizar os hooks genéricos
    (`useCrudOperations`, `useFormMutation`) e componentes reutilizáveis
    (`FormWrapper`, `PageHeader`).
2.  **Eliminar Código Não Utilizado (Dead Code):** Remover componentes, funções e
    imports que não são usados.
3.  **Uso Consistente de TypeScript:** Proibir o uso de `any` e utilizar os tipos
    centralizados em `src/types`.
4.  **Componentes Bem Estruturados:** Manter componentes pequenos e focados
    (máximo de 400-500 linhas).
5.  **Gerenciamento de Estado Eficiente:** Usar `TanStack Query` (abstraído pelos
    hooks customizados) para estado do servidor.
6.  **Separação de Lógica e Apresentação:** Isolar a lógica de negócio em hooks e
    serviços.
7.  **Tratamento de Erros Robusto:** Usar o sistema centralizado com
    `useErrorHandler`, `ErrorBoundary` e `wrapAsync`.
8.  **Estrutura e Organização do Projeto:** Seguir a estrutura de arquivos
    definida e usar `npm run organize:imports`.
9.  **Acessibilidade (a11y):** Garantir que a aplicação seja acessível.
10. **Testes Adequados:** Seguir a estratégia de testes com `Vitest` e `MSW`.

---

## 🛡️ Checklist de Segurança (Crítico)

1.  **Proteger Chaves e Dados Sensíveis:** **Nunca** fazer commit de segredos.
    Usar variáveis de ambiente.
2.  **Não Expor APIs no Frontend:** Lógica sensível e chaves de API devem residir
    nas Edge Functions.
3.  **Validação de Dados de Entrada:** Validar **TODOS** os inputs no frontend
    (Zod) e no backend.
4.  **Autenticação e Autorização (RLS):** **RLS é obrigatório** em todas as
    tabelas para isolamento de dados.
5.  **Proteção Contra Ataques Comuns:** Usar ORM do Supabase (previne SQL
    Injection) e sanitizar dados (previne XSS).
6.  **Logging Seguro:** Usar o `secureLogger` para evitar o log de dados
    sensíveis.
7.  **Conformidade LGPD:** Implementar consentimento explícito, auditoria de acesso e
    políticas de retenção de dados.
8.  **Isolamento por Tenant:** Garantir que cada usuário veja apenas dados de seu
    próprio tenant através de filtros `tenant_id` consistentes.

---

## 🧪 Estratégia de Testes

- **Foco em Testes de Integração:** Priorizar testes que simulam o comportamento
  real do usuário com `React Testing Library`.
- **Mocking de API com MSW:** Padronizar o uso do **Mock Service Worker (MSW)**
  para interceptar chamadas de API.
- **Testes Unitários para Lógica Pura:** Cobrir com `Vitest` toda a lógica de
  negócio isolada (validadores Zod, funções utilitárias).
- **Verificação da UI:** Os testes devem verificar o resultado final na interface
  (ex: um toast de sucesso aparece).
- **Testes de Acessibilidade:** Usar `jest-axe` para testes automatizados de
  acessibilidade com relatórios detalhados.
- **Configuração de Mocks:** Mocks específicos para contextos de auth e tenant
  localizados em `src/tests/mocks/`.
- **Testes de Hooks:** Usar `@testing-library/react-hooks` para testar hooks customizados
- **Testes de Edge Functions:** Usar `deno test` para funções do Supabase
- **Setup de Testes:** Configuração em `src/tests/setup.ts` com providers necessários

---

## 📋 Regras de Configuração

### Regras de Projeto

- Arquivos de configuração importantes para o projeto:
  - `.cursor/rules/project_rules.mdc`: Regras específicas de configuração do projeto
  - `.cursor/rules/rules.md`: Regras gerais de desenvolvimento e padronização

### Configurações Técnicas

- **TypeScript Strict Mode:** Todas as verificações de tipo habilitadas para
  máxima segurança de tipos.
- **ESLint Flat Config:** Configuração moderna com plugins especializados para
  TypeScript, imports e código não utilizado.
- **Husky Git Hooks:** Verificações automáticas de qualidade antes de commits.
- **Vite Bundle Analysis:** Análise automática do bundle com visualização
  detalhada do tamanho dos módulos.

### Pipeline CI/CD

- **GitHub Actions:** Pipeline completa com 6 jobs (quality-checks, tests, build,
  security, deploy, notifications).
- **Análise de Segurança:** CodeQL integrado para detecção de vulnerabilidades.
- **Deploy Automatizado:** Vercel com ambiente de produção e staging.
- **Relatórios:** Cobertura de testes e análise de acessibilidade automatizados.