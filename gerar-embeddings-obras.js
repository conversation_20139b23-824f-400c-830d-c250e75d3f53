#!/usr/bin/env node

/**
 * Script para gerar embeddings das obras existentes
 * Execute este script após configurar as credenciais
 */

const SUPABASE_URL = 'https://anrphijuostbgbscxmzx.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTk2NzgwMzksImV4cCI6MjAzNTI1NDAzOX0.lqbXYs8e_v4UOxZFClaiEaAF0K0YBe8FKAqBjKNlwrs';

// ⚠️ CONFIGURAR SUAS CREDENCIAIS AQUI
const AUTH_TOKEN = 'SEU_TOKEN_AQUI'; // Token do usuário autenticado
const USER_ID = 'SEU_USER_ID_AQUI'; // ID do usuário

// IDs das obras encontradas
const OBRAS = [
  { id: 'c34e85ec-6bde-45ad-9ce1-8f66370fdfd8', nome: 'Esplanada' },
  { id: '53a0e632-92ce-4e50-a568-7e2d61081a86', nome: 'ODTWIN FRITSCHE FH' }
];

async function gerarEmbeddingsObra(obra) {
  console.log(`\\n🔄 Gerando embeddings para: ${obra.nome} (${obra.id})`);
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/gerar-embeddings-obra`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json',
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        obra_id: obra.id,
        tipo_conteudo: 'todos' // Gerar para obra, despesas e fornecedores
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    
    if (data.sucesso) {
      console.log(`✅ ${obra.nome}: ${data.embeddings_gerados} embeddings gerados`);
      return true;
    } else {
      console.log(`❌ ${obra.nome}: Falha - ${data.error || 'Erro desconhecido'}`);
      return false;
    }
    
  } catch (error) {
    console.error(`❌ Erro para ${obra.nome}:`, error.message);
    return false;
  }
}

async function verificarConfiguracao() {
  console.log('🔍 === VERIFICANDO CONFIGURAÇÃO ===');
  
  if (!AUTH_TOKEN || AUTH_TOKEN === 'SEU_TOKEN_AQUI') {
    console.log('❌ AUTH_TOKEN não configurado');
    console.log('\\n📋 Para configurar:');
    console.log('1. Abra o ObrasAI no navegador e faça login');
    console.log('2. Abra DevTools (F12) → Console');
    console.log('3. Execute: localStorage.getItem("supabase.auth.token")');
    console.log('4. Copie o token e cole no topo deste script\\n');
    return false;
  }
  
  if (!USER_ID || USER_ID === 'SEU_USER_ID_AQUI') {
    console.log('❌ USER_ID não configurado');
    console.log('\\n📋 Para obter USER_ID:');
    console.log('1. No DevTools, execute: JSON.parse(localStorage.getItem("supabase.auth.token")).user.id');
    console.log('2. Copie o ID e cole no topo deste script\\n');
    return false;
  }
  
  console.log('✅ Configuração OK!');
  return true;
}

async function main() {
  console.log('🚀 === GERADOR DE EMBEDDINGS - OBRASAI ===');
  
  const configOK = await verificarConfiguracao();
  if (!configOK) {
    console.log('\\n❌ Configure as credenciais antes de continuar.');
    return;
  }
  
  console.log(`\\n📊 Encontradas ${OBRAS.length} obras para processar:`);
  OBRAS.forEach(obra => {
    console.log(`   - ${obra.nome} (${obra.id})`);
  });
  
  let sucessos = 0;
  let falhas = 0;
  
  for (const obra of OBRAS) {
    const sucesso = await gerarEmbeddingsObra(obra);
    if (sucesso) {
      sucessos++;
    } else {
      falhas++;
    }
    
    // Aguardar um pouco entre as chamadas para não sobrecarregar
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\\n📈 === RESULTADO FINAL ===');
  console.log(`✅ Sucessos: ${sucessos}`);
  console.log(`❌ Falhas: ${falhas}`);
  
  if (sucessos > 0) {
    console.log('\\n🎯 Próximos passos:');
    console.log('1. Teste o chat selecionando uma obra específica');
    console.log('2. Pergunte: "Qual o valor total gasto nesta obra?"');
    console.log('3. Agora você deve receber dados específicos da obra!');
  }
}

main().catch(console.error);