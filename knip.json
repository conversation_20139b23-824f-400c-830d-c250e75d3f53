{"entry": ["src/main.tsx"], "project": ["src/**/*.ts", "src/**/*.tsx"], "ignore": ["**/*.md", "**/*.json", "**/*.cjs", "**/*.sql", "**/*.py", "**/*.ps1", "**/*.sh", "**/*.yml", "**/*.txt", "**/*.ico", "**/*.svg", "**/*.png", "**/*.jpg", "**/*.pdf", "**/*.xlsx", ".github/**", ".husky/**", ".vscode/**", "public/**", "scripts/**", "supabase/**", "docs/**", "prompt_erro_implementacoes/**"], "ignoreDependencies": ["knip"]}