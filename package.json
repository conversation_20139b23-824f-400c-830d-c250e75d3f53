{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:dev": "vite build --mode development", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "lint:unused": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --rule 'unused-imports/no-unused-imports: error'", "lint:imports": "eslint . --ext ts,tsx --config .eslintrc.imports.js", "lint:imports:fix": "eslint . --ext ts,tsx --config .eslintrc.imports.js --fix", "organize:imports": "npm run lint:imports:fix", "check:organization": "node scripts/verificar-organizacao.cjs", "validate:schema": "tsx scripts/validate-schema-sync.ts", "validate:pipeline": "bash scripts/validate-pipeline.sh", "validate:edge-functions": "deno run --allow-read scripts/validate-edge-functions.ts", "migrate:edge-functions": "deno run --allow-read --allow-write scripts/migrate-edge-functions.ts", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:unit": "vitest src/tests/hooks src/tests/lib --reporter=verbose", "test:integration": "vitest src/tests/integration --reporter=verbose", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:accessibility": "vitest src/tests/accessibility --reporter=verbose", "test:accessibility:watch": "vitest src/tests/accessibility --watch", "test:accessibility:report": "tsx src/tests/accessibility/run-accessibility-tests.ts", "audit:accessibility": "npm run test:accessibility:report", "analyze": "npm run build && echo 'Bundle analysis available at dist/stats.html'", "analyze:serve": "npm run analyze && npx serve dist", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.56.2", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "depcheck": "^1.4.7", "dompurify": "^3.2.6", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.22.0", "input-otp": "^1.2.4", "knip": "^5.61.3", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "ts-prune": "^0.10.3", "unimported": "^1.31.0", "vaul": "^0.9.3", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.9.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/crypto-js": "^4.2.2", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.10.2", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.9.0", "husky": "^9.1.7", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "msw": "^2.10.2", "postcss": "^8.5.6", "rollup-plugin-visualizer": "^6.0.3", "supabase": "^2.33.7", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vitest": "^3.2.4"}}