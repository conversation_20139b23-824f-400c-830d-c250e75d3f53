chunk-NFC5BX5N.js?v=bc1ae3ed:14032 The above error occurred in the <ListaUnidades> component:

    at ListaUnidades (http://localhost:8080/src/components/obras/ListaUnidades.tsx?t=1752973994040:33:33)
    at div
    at div
    at MotionComponent (http://localhost:8080/node_modules/.vite/deps/framer-motion.js?v=bc1ae3ed:4924:40)
    at div
    at http://localhost:8080/node_modules/.vite/deps/chunk-JLQW6FOD.js?v=bc1ae3ed:43:13
    at Presence (http://localhost:8080/node_modules/.vite/deps/chunk-ZEYZZQRY.js?v=bc1ae3ed:24:11)
    at http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=bc1ae3ed:178:13
    at _c4 (http://localhost:8080/src/components/ui/tabs.tsx:47:61)
    at div
    at http://localhost:8080/node_modules/.vite/deps/chunk-JLQW6FOD.js?v=bc1ae3ed:43:13
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-XOUBXFGS.js?v=bc1ae3ed:38:15)
    at http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=bc1ae3ed:55:7
    at div
    at MotionComponent (http://localhost:8080/node_modules/.vite/deps/framer-motion.js?v=bc1ae3ed:4924:40)
    at div
    at MotionComponent (http://localhost:8080/node_modules/.vite/deps/framer-motion.js?v=bc1ae3ed:4924:40)
    at div
    at MotionComponent (http://localhost:8080/node_modules/.vite/deps/framer-motion.js?v=bc1ae3ed:4924:40)
    at main
    at div
    at div
    at div
    at Provider (http://localhost:8080/node_modules/.vite/deps/chunk-XOUBXFGS.js?v=bc1ae3ed:38:15)
    at TooltipProvider (http://localhost:8080/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=bc1ae3ed:65:5)
    at http://localhost:8080/src/components/ui/sidebar.tsx:50:72
    at DashboardLayout (http://localhost:8080/src/components/layouts/DashboardLayout.tsx:137:28)
    at ObraDetalhe (http://localhost:8080/src/pages/dashboard/obras/ObraDetalhe.tsx?t=1752973994040:101:20)
    at ProtectedRoute (http://localhost:8080/src/contexts/auth/ProtectedRoutes.tsx:26:34)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=bc1ae3ed:4088:5)
    at Outlet (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=bc1ae3ed:4494:26)
    at AppContent
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=bc1ae3ed:4088:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=bc1ae3ed:4558:5)
    at _a (http://localhost:8080/node_modules/.vite/deps/react-helmet-async.js?v=bc1ae3ed:624:5)
    at AuthProvider (http://localhost:8080/src/contexts/auth/AuthContext.tsx:32:32)
    at LoadingProvider (http://localhost:8080/src/contexts/LoadingContext.tsx:31:35)
    at ThemeProvider (http://localhost:8080/src/providers/theme-provider.tsx:26:33)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=bc1ae3ed:2933:3)
    at App
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=bc1ae3ed:4501:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=bc1ae3ed:5247:5)

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
chunk-NFC5BX5N.js?v=bc1ae3ed:9129 Uncaught ReferenceError: useObras is not defined
    at ListaUnidades (ListaUnidades.tsx:42:26)
﻿

