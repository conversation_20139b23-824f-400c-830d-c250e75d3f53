# 🚀 IMPLEMENTAÇÃO COMPLETA: SISTEMA FREEMIUM COM TRIAL DE 7 DIAS

## 📋 CONTEXTO E SITUAÇÃO ATUAL

### 🎯 **Projeto: ObrasVision (Rebranding de ObrasAI)**

- **Stack**: React 18+ TypeScript, Vite, Tailwind CSS, shadcn/ui, Supabase, Stripe
- **Status Atual**: Sistema básico funcional com assinaturas e quotas de IA
- **Objetivo**: Implementar modelo freemium com trial automático de 7 dias

### ✅ **Infraestrutura Existente Analisada**

#### **1. Sistema de Assinaturas**

```sql
-- Tabela: subscriptions (já existe)
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  plan_type TEXT CHECK (plan_type IN ('free', 'basic', 'pro', 'enterprise')),
  status TEXT CHECK (status IN ('active', 'canceled', 'past_due', 'trialing')),
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  stripe_subscription_id TEXT,
  stripe_customer_id TEXT
);
```

#### **2. Sistema de Quotas IA**

```typescript
// Hook: useAIQuota (já existe)
const AI_QUOTAS = {
  free: {
    chat: 20, // 20 perguntas/dia
    budget: 1, // 1 orçamento/dia
    contract: 1, // 1 análise/dia
    sinapi: 15, // 15 consultas/dia
  },
  trialing: {
    chat: 20, // Mesmo que free durante trial
    budget: 1,
    contract: 1,
    sinapi: 15,
  },
};
```

#### **3. Integração Stripe**

- ✅ Edge Function: `create-checkout-session`
- ✅ Edge Function: `stripe-webhook`
- ✅ Webhook handlers básicos funcionais

#### **4. Componentes Auth Existentes**

- ✅ `RegisterForm.tsx` - Formulário de cadastro
- ✅ `Register.tsx` - Página de cadastro (com Google Auth)
- ✅ `useSubscription.ts` - Hook de assinaturas

---

## 🎯 OBJETIVOS DA IMPLEMENTAÇÃO

### **FASE 1: Remover Google Auth**

- ❌ Remover botão "Entrar com Google" da página de cadastro
- ❌ Remover link "Já tem conta? Fazer login"
- ✅ Manter apenas formulário email/senha

### **FASE 2: Trial Automático de 7 Dias**

- ✅ Criar assinatura trial automaticamente após cadastro
- ✅ Status `'trialing'` com data de expiração
- ✅ Quotas de IA ativas durante trial
- ✅ Redirecionar para dashboard (não Stripe)

### **FASE 3: Sistema de Notificação**

- ✅ Indicador visual de dias restantes
- ⚠️ Alertas nos dias 5, 2 e último dia
- 💳 Botão de upgrade para Stripe

### **FASE 4: Bloqueio Pós-Trial**

- 🚫 Bloqueio completo após expiração
- 💰 Tela de upgrade obrigatória
- 🎯 Integração com Stripe Checkout

---

## 🏗️ ARQUITETURA DETALHADA

### **FLUXO COMPLETO DO USUÁRIO**

```mermaid
graph TD
    A[Landing Page] --> B[Clica "Cadastre-se"]
    B --> C[Página de Cadastro - SEM Google Auth]
    C --> D[Preenche email/senha]
    D --> E[Confirma Email]
    E --> F[Cria Trial Automático - 7 dias]
    F --> G[Dashboard com Quotas IA]
    G --> H{Trial Expirado?}
    H -->|Não| I[Continua usando - Notificações]
    H -->|Sim| J[Bloqueio Total]
    J --> K[Tela Upgrade Obrigatória]
    K --> L[Stripe Checkout]
    L --> M[Acesso Ilimitado]
```

### **ESTRUTURA DE COMPONENTES**

```
src/
├── pages/
│   ├── Register.tsx (MODIFICAR - remover Google Auth)
│   └── TrialExpired.tsx (NOVO - tela de bloqueio)
├── components/
│   ├── auth/
│   │   └── RegisterForm.tsx (MODIFICAR - criar trial)
│   └── subscription/
│       ├── TrialIndicator.tsx (NOVO - dias restantes)
│       └── UpgradeRequired.tsx (NOVO - bloqueio)
├── hooks/
│   ├── useSubscription.ts (MODIFICAR - detectar trial)
│   └── useTrial.ts (NOVO - lógica trial)
└── lib/
    └── trial-utils.ts (NOVO - helpers)
```

---

## 💻 IMPLEMENTAÇÃO TÉCNICA COMPLETA

### **1. MODIFICAR: src/pages/Register.tsx**

```typescript
import { motion } from "framer-motion";
import { Link } from "react-router-dom";

import registerBg from "@/assets/images/footer.jpg";
import logoDarkHorizon from "@/assets/logo/logo_dark_horizon.png";
import { RegisterForm } from "@/components/auth/RegisterForm";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

const Register = () => {
  return (
    <div className="relative min-h-screen flex items-center justify-center p-4 overflow-hidden">
      {/* Imagem de fundo com overlay */}
      <img
        src={registerBg}
        alt="Background ObrasVision"
        className="absolute inset-0 w-full h-full object-cover z-0"
        style={{ filter: "blur(0px) brightness(0.6)" }}
      />
      <div className="absolute inset-0 bg-black/70 z-10" />

      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className="relative z-20 w-full max-w-md"
      >
        <Card className="border border-white/20 backdrop-blur-sm bg-white/5 shadow-2xl">
          <CardHeader className="space-y-1 text-center pb-8">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="flex justify-center mb-4"
            >
              <div className="h-16 w-full flex items-center justify-center">
                <img
                  src={logoDarkHorizon}
                  width={240}
                  height={64}
                  alt="Logo ObrasVision"
                  style={{
                    maxWidth: 240,
                    maxHeight: 64,
                    height: "auto",
                    width: "auto",
                    display: "block",
                  }}
                  draggable={false}
                />
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <CardTitle className="text-3xl font-bold text-white text-center">
                Teste Grátis por 7 Dias
              </CardTitle>
            </motion.div>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <CardDescription className="text-white/90">
                Acesso completo às funcionalidades de IA. Sem cartão de crédito.
              </CardDescription>
            </motion.div>
          </CardHeader>

          <CardContent className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <RegisterForm />
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default Register;
```

### **2. MODIFICAR: src/components/auth/RegisterForm.tsx**

```typescript
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/auth";
import { t } from "@/lib/i18n";
import type { RegisterFormValues } from "@/lib/validations/auth";
import { registerSchema } from "@/lib/validations/auth";
import { useAnalytics } from "@/services/analyticsApi";
import { supabase } from "@/integrations/supabase/client";

export const RegisterForm = () => {
  const { register } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const { trackConversion } = useAnalytics();

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const createTrialSubscription = async (userId: string) => {
    try {
      const { data, error } = await supabase.functions.invoke(
        "create-trial-subscription",
        {
          body: { userId },
        }
      );

      if (error) {
        console.error("Erro ao criar trial:", error);
        // Não falha o cadastro se não conseguir criar trial
      }

      return data;
    } catch (error) {
      console.error("Erro ao chamar função de trial:", error);
    }
  };

  const onSubmit = async (data: RegisterFormValues) => {
    try {
      setIsLoading(true);

      // 1. Registrar usuário
      const { error, data: authData } = await register(
        data.email,
        data.password,
        data.firstName,
        data.lastName
      );

      if (error) {
        toast.error(error.message || t("auth.registerError"));
        return;
      }

      // 2. Criar trial automático
      if (authData?.user?.id) {
        await createTrialSubscription(authData.user.id);
      }

      // 3. Track conversão de signup
      await trackConversion("signup", {
        user_email: data.email,
        user_name: `${data.firstName} ${data.lastName}`,
        referrer: document.referrer,
        user_agent: navigator.userAgent,
        registration_method: "email",
        trial_created: true,
      });

      toast.success(
        "🎉 Conta criada com sucesso! Seu trial de 7 dias começou. Verifique seu email para confirmar."
      );

      // 4. Redirecionar para dashboard após confirmação
      navigate("/dashboard");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : t("messages.generalError");
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-200 font-medium">
                  {t("auth.firstName")}
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("auth.enterFirstName")}
                    {...field}
                    disabled={isLoading}
                    className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                  />
                </FormControl>
                <FormMessage className="text-red-400" />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-200 font-medium">
                  {t("auth.lastName")}
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("auth.enterLastName")}
                    {...field}
                    disabled={isLoading}
                    className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                  />
                </FormControl>
                <FormMessage className="text-red-400" />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-200 font-medium">
                {t("auth.email")}
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("auth.enterEmail")}
                  {...field}
                  disabled={isLoading}
                  className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                />
              </FormControl>
              <FormMessage className="text-red-400" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-200 font-medium">
                {t("auth.password")}
              </FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder={t("auth.enterPassword")}
                  {...field}
                  disabled={isLoading}
                  className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                />
              </FormControl>
              <FormMessage className="text-red-400" />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-200 font-medium">
                {t("auth.confirmPassword")}
              </FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder={t("auth.confirmPassword")}
                  {...field}
                  disabled={isLoading}
                  className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                />
              </FormControl>
              <FormMessage className="text-red-400" />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full bg-orange-500 text-white font-bold shadow-lg border-0 hover:bg-orange-600 transition-all duration-300 transform hover:scale-[1.02] focus:ring-4 focus:ring-orange-500/40"
          disabled={isLoading}
        >
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Começar Trial Gratuito - 7 Dias
        </Button>

        <p className="text-xs text-gray-400 text-center mt-4">
          Ao cadastrar-se, você concorda com nossos Termos de Serviço
        </p>
      </form>
    </Form>
  );
};
```

### **3. CRIAR: supabase/functions/create-trial-subscription/index.ts**

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.29.0";
import { corsHeaders } from "../_shared/cors.ts";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (req.method !== "POST") {
      return new Response(JSON.stringify({ error: "Método não permitido" }), {
        status: 405,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Verificar autorização
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: "Token de autorização necessário" }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Criar cliente Supabase
    const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;

    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      global: { headers: { Authorization: authHeader } },
    });

    // Obter usuário autenticado
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: "Usuário não autenticado" }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Obter dados do corpo da requisição
    const { userId } = await req.json();

    if (!userId || userId !== user.id) {
      return new Response(JSON.stringify({ error: "ID do usuário inválido" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Verificar se já existe assinatura ativa
    const { data: existingSubscription } = await supabase
      .from("subscriptions")
      .select("*")
      .eq("user_id", userId)
      .eq("status", "active")
      .single();

    if (existingSubscription) {
      return new Response(
        JSON.stringify({
          success: true,
          message: "Usuário já possui assinatura ativa",
          subscription: existingSubscription,
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Criar assinatura trial de 7 dias
    const trialStart = new Date();
    const trialEnd = new Date();
    trialEnd.setDate(trialStart.getDate() + 7); // 7 dias a partir de hoje

    const { data: newSubscription, error: subscriptionError } = await supabase
      .from("subscriptions")
      .insert([
        {
          user_id: userId,
          plan_type: "free",
          status: "trialing",
          current_period_start: trialStart.toISOString(),
          current_period_end: trialEnd.toISOString(),
          stripe_subscription_id: null,
          stripe_customer_id: null,
        },
      ])
      .select()
      .single();

    if (subscriptionError) {
      console.error("Erro ao criar assinatura trial:", subscriptionError);
      return new Response(JSON.stringify({ error: "Erro ao criar trial" }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // Log da criação do trial
    console.log(`Trial criado para usuário ${userId}:`, {
      subscription_id: newSubscription.id,
      trial_start: trialStart.toISOString(),
      trial_end: trialEnd.toISOString(),
    });

    return new Response(
      JSON.stringify({
        success: true,
        message: "Trial de 7 dias criado com sucesso",
        subscription: newSubscription,
        trial_days: 7,
        expires_at: trialEnd.toISOString(),
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Erro na função create-trial-subscription:", error);
    return new Response(JSON.stringify({ error: "Erro interno do servidor" }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
```

### **4. CRIAR: src/components/subscription/TrialIndicator.tsx**

```typescript
import { motion } from "framer-motion";
import { Calendar, Clock, Crown, Zap } from "lucide-react";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useSubscription } from "@/hooks/useSubscription";

export const TrialIndicator = () => {
  const { subscription, currentPlan } = useSubscription();
  const navigate = useNavigate();

  const trialInfo = useMemo(() => {
    if (!subscription || subscription.status !== "trialing") return null;

    const now = new Date();
    const endDate = new Date(subscription.current_period_end);
    const startDate = new Date(subscription.current_period_start);

    const totalDays = Math.ceil(
      (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const remainingMs = endDate.getTime() - now.getTime();
    const remainingDays = Math.max(
      0,
      Math.ceil(remainingMs / (1000 * 60 * 60 * 24))
    );
    const remainingHours = Math.max(
      0,
      Math.ceil(remainingMs / (1000 * 60 * 60))
    );

    const progress = ((totalDays - remainingDays) / totalDays) * 100;
    const isExpiringSoon = remainingDays <= 2;
    const isLastDay = remainingDays <= 1;

    return {
      totalDays,
      remainingDays,
      remainingHours,
      progress,
      isExpiringSoon,
      isLastDay,
      endDate: endDate.toLocaleDateString("pt-BR"),
    };
  }, [subscription]);

  if (!trialInfo) return null;

  const handleUpgrade = () => {
    navigate("/subscription?plan=pro&source=trial");
  };

  const getStatusColor = () => {
    if (trialInfo.isLastDay) return "bg-red-500";
    if (trialInfo.isExpiringSoon) return "bg-yellow-500";
    return "bg-green-500";
  };

  const getStatusText = () => {
    if (trialInfo.isLastDay && trialInfo.remainingHours <= 24) {
      return `${trialInfo.remainingHours}h restantes`;
    }
    return `${trialInfo.remainingDays} dias restantes`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`fixed top-16 right-4 z-50 max-w-sm ${
        trialInfo.isExpiringSoon ? "animate-pulse" : ""
      }`}
    >
      <Card
        className={`border-2 ${
          trialInfo.isLastDay
            ? "border-red-500"
            : trialInfo.isExpiringSoon
            ? "border-yellow-500"
            : "border-green-500"
        } shadow-lg bg-white dark:bg-gray-900`}
      >
        <CardHeader className="pb-3">
          <div className="flex items-center gap-2">
            <div
              className={`w-3 h-3 rounded-full ${getStatusColor()} animate-pulse`}
            />
            <Badge variant="outline" className="text-xs">
              <Crown className="w-3 h-3 mr-1" />
              Trial Gratuito
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-4 h-4" />
                <span className="font-medium">{getStatusText()}</span>
              </div>
              <div className="text-xs text-gray-500">
                até {trialInfo.endDate}
              </div>
            </div>

            <Progress value={trialInfo.progress} className="h-2" />

            <div className="text-xs text-gray-600 dark:text-gray-400">
              <div className="flex items-center gap-1 mb-1">
                <Zap className="w-3 h-3" />
                Acesso completo às funcionalidades de IA
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {trialInfo.totalDays} dias de teste gratuito
              </div>
            </div>

            {trialInfo.isExpiringSoon && (
              <motion.div
                initial={{ scale: 0.95 }}
                animate={{ scale: 1 }}
                transition={{
                  repeat: Infinity,
                  repeatType: "reverse",
                  duration: 1.5,
                }}
              >
                <Button
                  onClick={handleUpgrade}
                  className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold"
                  size="sm"
                >
                  <Crown className="w-4 h-4 mr-2" />
                  Fazer Upgrade Agora
                </Button>
              </motion.div>
            )}

            {!trialInfo.isExpiringSoon && (
              <Button
                onClick={handleUpgrade}
                variant="outline"
                className="w-full"
                size="sm"
              >
                Ver Planos Premium
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};
```

### **5. CRIAR: src/components/subscription/UpgradeRequired.tsx**

```typescript
import { motion } from "framer-motion";
import { Crown, Lock, Sparkles, Zap } from "lucide-react";
import { useNavigate } from "react-router-dom";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export const UpgradeRequired = () => {
  const navigate = useNavigate();

  const plans = [
    {
      name: "Pro",
      price: "R$ 97",
      period: "/mês",
      description: "Perfeito para construtoras em crescimento",
      features: [
        "IA ilimitada para chat e orçamentos",
        "Análise de contratos com IA",
        "Sistema SINAPI completo",
        "Analytics avançados",
        "Suporte prioritário",
        "Até 50 obras simultâneas",
      ],
      highlighted: true,
      planId: "pro",
    },
    {
      name: "Enterprise",
      price: "R$ 197",
      period: "/mês",
      description: "Para grandes construtoras e empreiteiras",
      features: [
        "Tudo do plano Pro",
        "Obras ilimitadas",
        "Usuários ilimitados",
        "API completa",
        "Suporte dedicado",
        "Customizações exclusivas",
      ],
      highlighted: false,
      planId: "enterprise",
    },
  ];

  const handleSelectPlan = (planId: string) => {
    navigate(`/subscription?plan=${planId}&source=trial_expired`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl w-full"
      >
        <div className="text-center mb-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="flex justify-center mb-4"
          >
            <div className="bg-orange-500 p-4 rounded-full">
              <Lock className="w-8 h-8 text-white" />
            </div>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-3xl font-bold text-gray-900 dark:text-white mb-2"
          >
            Seu trial de 7 dias expirou
          </motion.h1>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="text-gray-600 dark:text-gray-400 text-lg"
          >
            Continue aproveitando todas as funcionalidades de IA do ObrasVision
          </motion.p>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.planId}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 + index * 0.1 }}
            >
              <Card
                className={`relative h-full ${
                  plan.highlighted
                    ? "border-2 border-orange-500 shadow-xl scale-105"
                    : "border border-gray-200 dark:border-gray-700"
                }`}
              >
                {plan.highlighted && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-orange-500 text-white px-3 py-1">
                      <Sparkles className="w-3 h-3 mr-1" />
                      Mais Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-6">
                  <CardTitle className="text-2xl font-bold">
                    {plan.name}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {plan.description}
                  </CardDescription>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-orange-500">
                      {plan.price}
                    </span>
                    <span className="text-gray-500">{plan.period}</span>
                  </div>
                </CardHeader>

                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, featureIndex) => (
                      <li
                        key={featureIndex}
                        className="flex items-center gap-3"
                      >
                        <div className="bg-green-500 rounded-full p-1">
                          <Zap className="w-3 h-3 text-white" />
                        </div>
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    onClick={() => handleSelectPlan(plan.planId)}
                    className={`w-full ${
                      plan.highlighted
                        ? "bg-orange-500 hover:bg-orange-600 text-white"
                        : "bg-gray-900 dark:bg-white dark:text-gray-900 hover:bg-gray-800 dark:hover:bg-gray-100 text-white"
                    }`}
                    size="lg"
                  >
                    <Crown className="w-4 h-4 mr-2" />
                    Escolher {plan.name}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="text-center mt-8"
        >
          <p className="text-sm text-gray-500">
            ✅ Sem compromisso • ✅ Cancele quando quiser • ✅ Suporte 24/7
          </p>
        </motion.div>
      </motion.div>
    </div>
  );
};

const Badge = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => (
  <div
    className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${className}`}
  >
    {children}
  </div>
);
```

### **6. MODIFICAR: src/hooks/useSubscription.ts**

```typescript
import { useQuery } from "@tanstack/react-query";

import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";

// Tipos de planos disponíveis
export type PlanType = "free" | "basic" | "pro" | "enterprise";

// Funcionalidades disponíveis por plano
export interface PlanFeatures {
  maxObras: number;
  maxUsuarios: number;
  aiFeatures: boolean;
  advancedAnalytics: boolean;
  apiAccess: boolean;
  prioritySupport: boolean;
  customTemplates: boolean;
  exportData: boolean;
}

// Configuração dos planos e suas funcionalidades
const PLAN_FEATURES: Record<PlanType, PlanFeatures> = {
  free: {
    maxObras: 3,
    maxUsuarios: 1,
    aiFeatures: false,
    advancedAnalytics: false,
    apiAccess: false,
    prioritySupport: false,
    customTemplates: false,
    exportData: false,
  },
  basic: {
    maxObras: 10,
    maxUsuarios: 3,
    aiFeatures: false,
    advancedAnalytics: false,
    apiAccess: false,
    prioritySupport: false,
    customTemplates: true,
    exportData: true,
  },
  pro: {
    maxObras: 50,
    maxUsuarios: 10,
    aiFeatures: true,
    advancedAnalytics: true,
    apiAccess: false,
    prioritySupport: true,
    customTemplates: true,
    exportData: true,
  },
  enterprise: {
    maxObras: -1, // Ilimitado
    maxUsuarios: -1, // Ilimitado
    aiFeatures: true,
    advancedAnalytics: true,
    apiAccess: true,
    prioritySupport: true,
    customTemplates: true,
    exportData: true,
  },
};

interface SubscriptionData {
  id: string;
  user_id: string;
  plan_type: PlanType;
  status: "active" | "canceled" | "past_due" | "trialing";
  current_period_start: string;
  current_period_end: string;
  stripe_subscription_id?: string;
  stripe_customer_id?: string;
  created_at: string;
  updated_at: string;
}

// Modo de desenvolvimento - permite testar funcionalidades premium
const DEV_MODE = import.meta.env.DEV;
const DEV_PREMIUM_PLAN: PlanType = "pro"; // Mude para 'free' se quiser testar restrições

export function useSubscription() {
  const { user } = useAuth();

  // Buscar dados da assinatura do usuário
  const {
    data: subscription,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["subscription", user?.id],
    queryFn: async () => {
      if (!user?.id) return null;

      // Em modo de desenvolvimento, usar plano premium para testes
      if (DEV_MODE) {
        console.log(`🧪 Modo DEV: Usando plano ${DEV_PREMIUM_PLAN}`);
        return {
          plan_type: DEV_PREMIUM_PLAN,
          status: "active" as const,
        };
      }

      try {
        const { data, error } = await supabase
          .from("subscriptions")
          .select("*")
          .eq("user_id", user.id)
          .in("status", ["active", "trialing"]) // Incluir trials ativos
          .order("created_at", { ascending: false })
          .limit(1)
          .single();

        if (error) {
          // Se não encontrar assinatura ativa/trial, retorna plano gratuito
          if (
            error.code === "PGRST116" ||
            error.code === "42P01" ||
            error.message?.includes('relation "subscriptions" does not exist')
          ) {
            console.log("📝 Assinatura não encontrada, usando plano gratuito");
            return {
              plan_type: "free" as PlanType,
              status: "active" as const,
            };
          }
          throw error;
        }

        return data as SubscriptionData;
      } catch (err) {
        // Em caso de erro (tabela não existe, etc), retorna plano gratuito
        console.warn(
          "⚠️ Erro ao buscar assinatura, usando plano gratuito:",
          err
        );
        return {
          plan_type: "free" as PlanType,
          status: "active" as const,
        };
      }
    },
    enabled: !!user?.id,
    retry: false, // Não tentar novamente se falhar
    staleTime: 5 * 60 * 1000, // Cache por 5 minutos
  });

  // Determinar o plano atual
  const currentPlan: PlanType = subscription?.plan_type || "free";

  // Verificar se o trial expirou
  const isTrialExpired =
    subscription?.status === "trialing" &&
    new Date(subscription.current_period_end) < new Date();

  // Obter funcionalidades do plano atual
  const planFeatures = PLAN_FEATURES[currentPlan];

  // Verificar se o usuário pode usar funcionalidades específicas
  const canUseFeature = {
    aiFeatures:
      planFeatures.aiFeatures ||
      (subscription?.status === "trialing" && !isTrialExpired),
    advancedAnalytics: planFeatures.advancedAnalytics,
    apiAccess: planFeatures.apiAccess,
    prioritySupport: planFeatures.prioritySupport,
    customTemplates: planFeatures.customTemplates,
    exportData: planFeatures.exportData,
  };

  // Verificar limites
  const limits = {
    maxObras: planFeatures.maxObras,
    maxUsuarios: planFeatures.maxUsuarios,
    hasUnlimitedObras: planFeatures.maxObras === -1,
    hasUnlimitedUsuarios: planFeatures.maxUsuarios === -1,
  };

  // URLs para upgrade
  const upgradeUrl =
    currentPlan === "free"
      ? "/subscription?plan=pro"
      : "/subscription?plan=enterprise";

  // Informações sobre o trial
  const trialInfo =
    subscription?.status === "trialing"
      ? {
          isTrialing: true,
          isExpired: isTrialExpired,
          endDate: new Date(subscription.current_period_end),
          daysRemaining: Math.max(
            0,
            Math.ceil(
              (new Date(subscription.current_period_end).getTime() -
                Date.now()) /
                (1000 * 60 * 60 * 24)
            )
          ),
        }
      : {
          isTrialing: false,
          isExpired: false,
          endDate: null,
          daysRemaining: 0,
        };

  return {
    subscription,
    currentPlan,
    planFeatures,
    canUseFeature,
    limits,
    upgradeUrl,
    isLoading,
    error,
    isPremium: ["pro", "enterprise"].includes(currentPlan),
    isEnterprise: currentPlan === "enterprise",
    // Informações do trial
    trial: trialInfo,
    // Verificação de acesso
    hasAccess:
      !isTrialExpired || ["active"].includes(subscription?.status || ""),
    // Informações para debug
    isDev: DEV_MODE,
    devPlan: DEV_MODE ? DEV_PREMIUM_PLAN : null,
  };
}
```

### **7. MODIFICAR: src/components/layouts/DashboardLayout.tsx**

```typescript
// Adicionar imports necessários
import { TrialIndicator } from "@/components/subscription/TrialIndicator";
import { UpgradeRequired } from "@/components/subscription/UpgradeRequired";
import { useSubscription } from "@/hooks/useSubscription";

// Modificar componente DashboardLayout
export const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  const { trial, hasAccess } = useSubscription();

  // Se o trial expirou e não tem acesso, mostrar tela de upgrade
  if (trial.isExpired && !hasAccess) {
    return <UpgradeRequired />;
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        {/* Sidebar existente... */}

        <main className="flex-1 flex flex-col overflow-hidden">
          <DashboardHeader />

          {/* Indicador de trial */}
          {trial.isTrialing && !trial.isExpired && <TrialIndicator />}

          <div className="flex-1 overflow-auto p-6">{children}</div>
        </main>
      </div>
    </SidebarProvider>
  );
};
```

### **8. CRIAR: src/lib/trial-utils.ts**

```typescript
import { supabase } from "@/integrations/supabase/client";

export interface TrialInfo {
  isTrialing: boolean;
  isExpired: boolean;
  daysRemaining: number;
  hoursRemaining: number;
  endDate: Date | null;
  startDate: Date | null;
}

export const getTrialInfo = (subscription: any): TrialInfo => {
  if (!subscription || subscription.status !== "trialing") {
    return {
      isTrialing: false,
      isExpired: false,
      daysRemaining: 0,
      hoursRemaining: 0,
      endDate: null,
      startDate: null,
    };
  }

  const now = new Date();
  const endDate = new Date(subscription.current_period_end);
  const startDate = new Date(subscription.current_period_start);

  const remainingMs = endDate.getTime() - now.getTime();
  const daysRemaining = Math.max(
    0,
    Math.ceil(remainingMs / (1000 * 60 * 60 * 24))
  );
  const hoursRemaining = Math.max(0, Math.ceil(remainingMs / (1000 * 60 * 60)));

  return {
    isTrialing: true,
    isExpired: remainingMs <= 0,
    daysRemaining,
    hoursRemaining,
    endDate,
    startDate,
  };
};

export const formatTimeRemaining = (trialInfo: TrialInfo): string => {
  if (!trialInfo.isTrialing) return "";

  if (trialInfo.isExpired) return "Expirado";

  if (trialInfo.daysRemaining > 1) {
    return `${trialInfo.daysRemaining} dias restantes`;
  }

  if (trialInfo.daysRemaining === 1) {
    return "Último dia do trial";
  }

  return `${trialInfo.hoursRemaining}h restantes`;
};

export const shouldShowUrgentAlert = (trialInfo: TrialInfo): boolean => {
  return trialInfo.isTrialing && trialInfo.daysRemaining <= 2;
};

export const createTrialSubscription = async (userId: string) => {
  try {
    const { data, error } = await supabase.functions.invoke(
      "create-trial-subscription",
      {
        body: { userId },
      }
    );

    if (error) throw error;

    return { success: true, data };
  } catch (error) {
    console.error("Erro ao criar trial:", error);
    return { success: false, error };
  }
};
```

### **9. CRIAR: supabase/functions/check-expired-trials/index.ts**

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.29.0";
import { corsHeaders } from "../_shared/cors.ts";

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Buscar trials expirados que ainda estão ativos
    const { data: expiredTrials, error } = await supabase
      .from("subscriptions")
      .select("*")
      .eq("status", "trialing")
      .lt("current_period_end", new Date().toISOString());

    if (error) {
      console.error("Erro ao buscar trials expirados:", error);
      return new Response(JSON.stringify({ error: "Erro ao buscar trials" }), {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    if (!expiredTrials || expiredTrials.length === 0) {
      return new Response(
        JSON.stringify({
          success: true,
          message: "Nenhum trial expirado encontrado",
          processed: 0,
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Atualizar status dos trials expirados para 'canceled'
    const expiredIds = expiredTrials.map((trial) => trial.id);

    const { error: updateError } = await supabase
      .from("subscriptions")
      .update({ status: "canceled" })
      .in("id", expiredIds);

    if (updateError) {
      console.error("Erro ao atualizar trials expirados:", updateError);
      return new Response(
        JSON.stringify({ error: "Erro ao atualizar trials" }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Log dos trials processados
    console.log(
      `Processados ${expiredTrials.length} trials expirados:`,
      expiredTrials.map((t) => ({
        user_id: t.user_id,
        expired_at: t.current_period_end,
      }))
    );

    return new Response(
      JSON.stringify({
        success: true,
        message: `${expiredTrials.length} trials expirados processados`,
        processed: expiredTrials.length,
        expired_trials: expiredTrials.map((t) => ({
          user_id: t.user_id,
          expired_at: t.current_period_end,
        })),
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Erro na função check-expired-trials:", error);
    return new Response(JSON.stringify({ error: "Erro interno do servidor" }), {
      status: 500,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
```

---

## 📊 CONFIGURAÇÃO E DEPLOY

### **1. Variáveis de Ambiente Necessárias**

```bash
# .env.local
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Supabase Dashboard -> Settings -> API
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

### **2. Deploy das Edge Functions**

```bash
# Deploy da função de trial
supabase functions deploy create-trial-subscription --no-verify-jwt

# Deploy da função de verificação
supabase functions deploy check-expired-trials --no-verify-jwt

# Configurar cron job para check-expired-trials (opcional)
# Via Supabase Dashboard -> Database -> Cron Jobs
```

### **3. Configuração do Stripe**

```typescript
// Produtos no Stripe Dashboard:
{
  "pro": {
    "price_id": "price_1234567890",
    "amount": 9700, // R$ 97,00
    "currency": "brl",
    "interval": "month"
  },
  "enterprise": {
    "price_id": "price_0987654321",
    "amount": 19700, // R$ 197,00
    "currency": "brl",
    "interval": "month"
  }
}
```

---

## 🧪 TESTES E VALIDAÇÃO

### **1. Cenários de Teste**

#### **Fluxo Completo - Trial**

1. ✅ Usuário acessa landing page
2. ✅ Clica "Cadastre-se" (sem login)
3. ✅ Preenche formulário (sem Google)
4. ✅ Recebe email de confirmação
5. ✅ Trial de 7 dias é criado automaticamente
6. ✅ Acessa dashboard com quotas ativas
7. ✅ Vê indicador de trial no header
8. ✅ Recebe notificações de expiração
9. ✅ Após 7 dias, é bloqueado
10. ✅ Tela de upgrade obrigatória
11. ✅ Stripe checkout funciona
12. ✅ Webhook atualiza assinatura

#### **Edge Cases**

- ✅ Trial já existe para usuário
- ✅ Edge function falha na criação
- ✅ Webhook do Stripe falha
- ✅ Usuário tenta burlar trial
- ✅ Quotas de IA durante trial

### **2. Validações Técnicas**

```sql
-- Verificar trials criados
SELECT * FROM subscriptions WHERE status = 'trialing';

-- Verificar trials expirados
SELECT * FROM subscriptions
WHERE status = 'trialing'
AND current_period_end < NOW();

-- Verificar uso de IA durante trial
SELECT * FROM ai_usage_tracking
WHERE user_id IN (
  SELECT user_id FROM subscriptions WHERE status = 'trialing'
);
```

---

## 🚀 ROADMAP DE IMPLEMENTAÇÃO

### **Semana 1: Base do Sistema**

- [ ] Remover Google Auth da página de cadastro
- [ ] Criar Edge Function `create-trial-subscription`
- [ ] Modificar `RegisterForm` para criar trial
- [ ] Testar fluxo básico de cadastro + trial

### **Semana 2: Interface e UX**

- [ ] Criar componente `TrialIndicator`
- [ ] Criar componente `UpgradeRequired`
- [ ] Integrar com `DashboardLayout`
- [ ] Modificar hook `useSubscription`

### **Semana 3: Automação e Deploy**

- [ ] Criar Edge Function `check-expired-trials`
- [ ] Configurar Stripe webhooks
- [ ] Deploy e testes em produção
- [ ] Monitoramento e analytics

### **Semana 4: Otimização**

- [ ] A/B test de mensagens
- [ ] Otimização de conversão
- [ ] Analytics avançados
- [ ] Suporte e documentação

---

## 📈 MÉTRICAS DE SUCESSO

### **KPIs Principais**

- **Taxa de Conversão Trial→Paid**: Meta 15-25%
- **Tempo até Conversão**: Meta 3-5 dias
- **Retenção Trial**: Meta 70%+ completam 7 dias
- **Uso de IA durante Trial**: Meta 80%+ testam IA

### **Analytics a Implementar**

```typescript
// Eventos para tracking
trackEvent("trial_started", { user_id, trial_days: 7 });
trackEvent("trial_day_remaining", { user_id, days_left });
trackEvent("trial_expired", { user_id, converted: false });
trackEvent("trial_converted", { user_id, plan_selected });
trackEvent("ai_quota_reached", { user_id, feature_type });
```

---

## 🛠️ FERRAMENTAS E RECURSOS

### **Desenvolvimento**

- **Frontend**: Vite + React + TypeScript + Tailwind
- **Backend**: Supabase Edge Functions (Deno)
- **Database**: PostgreSQL (Supabase)
- **Payments**: Stripe
- **Analytics**: Supabase Analytics + Custom Events

### **Monitoramento**

- **Logs**: Supabase Dashboard + Edge Function Logs
- **Errors**: Console.error + Sentry (opcional)
- **Performance**: Web Vitals + Lighthouse
- **Business**: Stripe Dashboard + Custom Analytics

---

## ⚠️ PONTOS DE ATENÇÃO

### **Segurança**

- ✅ RLS habilitado em todas as tabelas
- ✅ Validação de propriedade de recursos
- ✅ Sanitização de inputs
- ✅ Rate limiting nas Edge Functions

### **Performance**

- ✅ Índices otimizados para queries de trial
- ✅ Cache apropriado nos hooks React
- ✅ Lazy loading de componentes pesados
- ✅ Otimização de bundle size

### **UX/UI**

- ✅ Loading states em todas as operações
- ✅ Error handling gracioso
- ✅ Feedback visual claro
- ✅ Mobile-first design

### **Business Logic**

- ✅ Prevenção de múltiplos trials
- ✅ Handling de edge cases (Stripe failures)
- ✅ Graceful degradation
- ✅ Clear upgrade paths

---

## 🎯 CONCLUSÃO

Este documento fornece uma implementação completa do sistema freemium com trial de 7 dias para o ObrasVision. A arquitetura aproveita a infraestrutura existente (Supabase + Stripe) e adiciona as camadas necessárias para criar uma experiência de usuário fluida e conversões otimizadas.

**Next Steps**: Implementar fase por fase, testando cada componente isoladamente antes de integrar ao sistema principal.

**Success Criteria**: Taxa de conversão trial→paid de 15%+ e tempo de implementação de 2-3 semanas.
