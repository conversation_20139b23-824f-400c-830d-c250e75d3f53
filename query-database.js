const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuração do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente VITE_SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórias');
  process.exit(1);
}

// Criar cliente com service role key para bypass RLS
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function queryDatabase() {
  console.log('🔍 Verificando dados no banco de dados...\n');
  
  try {
    // 1. Verificar quantos usuários existem
    console.log('1. Verificando usuários no sistema:');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, first_name, last_name, tenant_id, created_at')
      .limit(10);
    
    if (profilesError) {
      console.error('❌ Erro ao buscar profiles:', profilesError);
    } else {
      console.log(`✅ Encontrados ${profiles.length} usuários:`);
      profiles.forEach(profile => {
        console.log(`   - ${profile.first_name || 'N/A'} ${profile.last_name || 'N/A'} (tenant: ${profile.tenant_id})`);
      });
    }
    
    // 2. Verificar user_tenants
    console.log('\n2. Verificando relação user_tenants:');
    const { data: userTenants, error: userTenantsError } = await supabase
      .from('user_tenants')
      .select('user_id, tenant_id, role, created_at')
      .limit(10);
    
    if (userTenantsError) {
      console.error('❌ Erro ao buscar user_tenants:', userTenantsError);
    } else {
      console.log(`✅ Encontrados ${userTenants.length} registros user_tenants:`);
      userTenants.forEach(ut => {
        console.log(`   - User: ${ut.user_id} | Tenant: ${ut.tenant_id} | Role: ${ut.role}`);
      });
    }
    
    // 3. Verificar obras
    console.log('\n3. Verificando obras cadastradas:');
    const { data: obras, error: obrasError } = await supabase
      .from('obras')
      .select('id, nome, usuario_id, tenant_id, created_at, orcamento, construtora_id')
      .limit(10);
    
    if (obrasError) {
      console.error('❌ Erro ao buscar obras:', obrasError);
    } else {
      console.log(`✅ Encontradas ${obras.length} obras:`);
      obras.forEach(obra => {
        console.log(`   - ${obra.nome} | Usuário: ${obra.usuario_id} | Tenant: ${obra.tenant_id} | Orçamento: R$ ${obra.orcamento}`);
      });
    }
    
    // 4. Verificar construtoras
    console.log('\n4. Verificando construtoras:');
    const { data: construtoras, error: construtorasError } = await supabase
      .from('construtoras')
      .select('id, nome, tenant_id, created_at')
      .limit(10);
    
    if (construtorasError) {
      console.error('❌ Erro ao buscar construtoras:', construtorasError);
    } else {
      console.log(`✅ Encontradas ${construtoras.length} construtoras:`);
      construtoras.forEach(construtora => {
        console.log(`   - ${construtora.nome} | Tenant: ${construtora.tenant_id}`);
      });
    }
    
    // 5. Verificar estatísticas gerais
    console.log('\n5. Estatísticas gerais:');
    
    // Contar total de registros por tabela
    const tabelas = ['profiles', 'user_tenants', 'obras', 'construtoras'];
    
    for (const tabela of tabelas) {
      const { count, error } = await supabase
        .from(tabela)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.error(`❌ Erro ao contar ${tabela}:`, error);
      } else {
        console.log(`   - ${tabela}: ${count} registros`);
      }
    }
    
    // 6. Verificar tenant_ids únicos
    console.log('\n6. Tenant IDs únicos no sistema:');
    const { data: tenantIds, error: tenantsError } = await supabase
      .from('profiles')
      .select('tenant_id')
      .not('tenant_id', 'is', null);
    
    if (tenantsError) {
      console.error('❌ Erro ao buscar tenant_ids:', tenantsError);
    } else {
      const uniqueTenants = [...new Set(tenantIds.map(p => p.tenant_id))];
      console.log(`✅ Encontrados ${uniqueTenants.length} tenant_ids únicos:`);
      uniqueTenants.forEach(tenantId => {
        console.log(`   - ${tenantId}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

// Executar a consulta
queryDatabase();