#!/usr/bin/env -S deno run --allow-read --allow-write

/**
 * 🔄 Script de Migração de Edge Functions - ObrasAI 2.2
 * 
 * Migra Edge Functions existentes para o padrão padronizado
 */

import { walk } from "https://deno.land/std@0.208.0/fs/walk.ts";
import { join, dirname } from "https://deno.land/std@0.208.0/path/mod.ts";

// Configuração das migrações
interface MigrationConfig {
  functionName: string;
  requiresAuth: boolean;
  requiresTenant: boolean;
  allowedMethods: string[];
  hasValidation: boolean;
  validationSchema?: string;
  rateLimit?: {
    maxRequests: number;
    windowMs: number;
  };
}

// Configurações específicas para cada função
const MIGRATION_CONFIGS: Record<string, MigrationConfig> = {
  'ai-chat-contextual': {
    functionName: 'ai-chat-contextual',
    requiresAuth: true,
    requiresTenant: false,
    allowedMethods: ['POST'],
    hasValidation: true,
    validationSchema: 'SCHEMAS.aiChat',
    rateLimit: { maxRequests: 50, windowMs: 60000 }
  },
  'ai-calculate-budget': {
    functionName: 'ai-calculate-budget',
    requiresAuth: true,
    requiresTenant: true,
    allowedMethods: ['POST'],
    hasValidation: true,
    validationSchema: 'SCHEMAS.orcamentoParametrico',
    rateLimit: { maxRequests: 20, windowMs: 60000 }
  },
  'analise-planta-ia': {
    functionName: 'analise-planta-ia',
    requiresAuth: true,
    requiresTenant: true,
    allowedMethods: ['POST'],
    hasValidation: true,
    validationSchema: 'SCHEMAS.analisePlanta',
    rateLimit: { maxRequests: 10, windowMs: 60000 }
  },
  'buscar-cnpj': {
    functionName: 'buscar-cnpj',
    requiresAuth: true,
    requiresTenant: false,
    allowedMethods: ['GET', 'POST'],
    hasValidation: true,
    validationSchema: 'SCHEMAS.cnpj',
    rateLimit: { maxRequests: 30, windowMs: 60000 }
  },
  'sinapi-semantic-search': {
    functionName: 'sinapi-semantic-search',
    requiresAuth: true,
    requiresTenant: false,
    allowedMethods: ['POST'],
    hasValidation: true,
    validationSchema: 'SCHEMAS.sinapiSearch',
    rateLimit: { maxRequests: 100, windowMs: 60000 }
  }
};

/**
 * Gera template de migração para uma Edge Function
 */
function generateMigrationTemplate(config: MigrationConfig): string {
  const configType = config.requiresAuth 
    ? (config.requiresTenant ? 'CRUD_FUNCTION_CONFIG' : 'AI_FUNCTION_CONFIG')
    : 'PUBLIC_FUNCTION_CONFIG';

  return `/**
 * 🚀 ${config.functionName} - Versão Padronizada
 * 
 * Edge Function migrada para o template padronizado
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Imports padronizados
import { 
  createEdgeFunction, 
  ${configType}
} from '../_shared/function-template.ts';

import { 
  createSuccessResponse, 
  createErrorResponse,
  createPaginatedResponse,
  ERROR_CODES
} from '../_shared/response-handler.ts';

import { 
  SCHEMAS 
} from '../_shared/validation-schemas.ts';

${config.requiresAuth ? `
import { 
  createServiceClient,
  createUserClient 
} from '../_shared/auth-handler.ts';
` : ''}

// TODO: Importar lógica específica da função original
// import { processOriginalLogic } from './original-logic.ts';

// Implementação da Edge Function
export default createEdgeFunction({
  name: '${config.functionName}',
  version: '2.0.0',
  ...${configType},
  requiresAuth: ${config.requiresAuth},
  requiresTenant: ${config.requiresTenant},
  allowedMethods: ${JSON.stringify(config.allowedMethods)},${config.rateLimit ? `
  rateLimit: {
    maxRequests: ${config.rateLimit.maxRequests},
    windowMs: ${config.rateLimit.windowMs}
  },` : ''}${config.hasValidation ? `
  validation: {
    bodySchema: ${config.validationSchema}
  }` : ''}
}, async ({ req, body, query, auth, tenantId, logger, requestId }) => {
  logger.info('Processing ${config.functionName} request', {
    method: req.method,
    hasAuth: !!auth,
    tenantId
  });

  try {
    // TODO: Implementar lógica específica da função
    
    ${config.allowedMethods.includes('GET') ? `
    if (req.method === 'GET') {
      // Lógica para GET
      const result = await handleGet(query, auth, tenantId, logger);
      return createSuccessResponse(result);
    }
    ` : ''}
    
    ${config.allowedMethods.includes('POST') ? `
    if (req.method === 'POST') {
      // Lógica para POST
      const result = await handlePost(body, auth, tenantId, logger);
      return createSuccessResponse(result);
    }
    ` : ''}
    
    ${config.allowedMethods.includes('PUT') ? `
    if (req.method === 'PUT') {
      // Lógica para PUT
      const result = await handlePut(body, auth, tenantId, logger);
      return createSuccessResponse(result);
    }
    ` : ''}
    
    ${config.allowedMethods.includes('DELETE') ? `
    if (req.method === 'DELETE') {
      // Lógica para DELETE
      const result = await handleDelete(query, auth, tenantId, logger);
      return createSuccessResponse(result);
    }
    ` : ''}

    return createErrorResponse(
      'Método não implementado',
      'VALIDATION_ERROR'
    );

  } catch (error) {
    logger.error('Error in ${config.functionName}', error);
    return createErrorResponse(
      'Erro interno do servidor',
      'INTERNAL_ERROR'
    );
  }
});

// TODO: Implementar handlers específicos
${config.allowedMethods.includes('GET') ? `
async function handleGet(query: any, auth: any, tenantId: string | undefined, logger: any) {
  // Implementar lógica GET
  throw new Error('GET handler not implemented');
}
` : ''}

${config.allowedMethods.includes('POST') ? `
async function handlePost(body: any, auth: any, tenantId: string | undefined, logger: any) {
  // Implementar lógica POST
  throw new Error('POST handler not implemented');
}
` : ''}

${config.allowedMethods.includes('PUT') ? `
async function handlePut(body: any, auth: any, tenantId: string | undefined, logger: any) {
  // Implementar lógica PUT
  throw new Error('PUT handler not implemented');
}
` : ''}

${config.allowedMethods.includes('DELETE') ? `
async function handleDelete(query: any, auth: any, tenantId: string | undefined, logger: any) {
  // Implementar lógica DELETE
  throw new Error('DELETE handler not implemented');
}
` : ''}`;
}

/**
 * Cria arquivo de migração para uma função
 */
async function createMigrationFile(functionName: string, config: MigrationConfig): Promise<void> {
  const migrationContent = generateMigrationTemplate(config);
  const migrationPath = `supabase/functions/${functionName}-v2/index.ts`;
  
  // Criar diretório se não existir
  const dir = dirname(migrationPath);
  try {
    await Deno.mkdir(dir, { recursive: true });
  } catch (error) {
    if (!(error instanceof Deno.errors.AlreadyExists)) {
      throw error;
    }
  }
  
  // Escrever arquivo de migração
  await Deno.writeTextFile(migrationPath, migrationContent);
  console.log(`✅ Migração criada: ${migrationPath}`);
}

/**
 * Gera relatório de migração
 */
function generateMigrationReport(): string {
  const totalFunctions = Object.keys(MIGRATION_CONFIGS).length;
  
  return `# 📋 Relatório de Migração de Edge Functions

**Data:** ${new Date().toISOString().split('T')[0]}
**Total de Funções:** ${totalFunctions}

## 🎯 Funções Migradas

${Object.entries(MIGRATION_CONFIGS).map(([name, config]) => `
### ${name}
- **Autenticação:** ${config.requiresAuth ? '✅ Obrigatória' : '❌ Opcional'}
- **Tenant:** ${config.requiresTenant ? '✅ Obrigatório' : '❌ Opcional'}
- **Métodos:** ${config.allowedMethods.join(', ')}
- **Validação:** ${config.hasValidation ? '✅ Sim' : '❌ Não'}
- **Rate Limit:** ${config.rateLimit ? `${config.rateLimit.maxRequests}/min` : '❌ Não'}
`).join('')}

## 🚀 Próximos Passos

1. **Revisar migrações geradas** em \`supabase/functions/*-v2/\`
2. **Implementar lógica específica** em cada handler
3. **Testar funcionalidades** migradas
4. **Atualizar frontend** para usar novas versões
5. **Deprecar versões antigas** após validação

## 📝 Notas

- Todas as migrações seguem o template padronizado
- Validação Zod implementada automaticamente
- Logging estruturado configurado
- Rate limiting aplicado conforme necessário
- Tratamento de erros padronizado

**Gerado automaticamente pelo script de migração**
`;
}

/**
 * Função principal
 */
async function main(): Promise<void> {
  console.log('🔄 Iniciando migração de Edge Functions...\n');

  let migratedCount = 0;

  for (const [functionName, config] of Object.entries(MIGRATION_CONFIGS)) {
    try {
      await createMigrationFile(functionName, config);
      migratedCount++;
    } catch (error) {
      console.error(`❌ Erro ao migrar ${functionName}:`, error);
    }
  }

  // Gerar relatório
  const report = generateMigrationReport();
  await Deno.writeTextFile('docs/auditoria/RELATORIO_MIGRACAO_EDGE_FUNCTIONS.md', report);

  console.log(`\n🎉 Migração concluída!`);
  console.log(`✅ ${migratedCount} funções migradas`);
  console.log(`📄 Relatório gerado: docs/auditoria/RELATORIO_MIGRACAO_EDGE_FUNCTIONS.md`);
  console.log(`\n📋 Próximos passos:`);
  console.log(`1. Revisar arquivos gerados em supabase/functions/*-v2/`);
  console.log(`2. Implementar lógica específica em cada handler`);
  console.log(`3. Testar e validar migrações`);
}

// Executar se chamado diretamente
if (import.meta.main) {
  await main();
}
