import fetch from 'node-fetch';

// URL da sua Edge Function. Certifique-se de que o project_id está correto.
const anrphijuostbgbscxmzx = 'anrphijuostbgbscxmzx';
const functionName = 'fornecedores-chat';
const supabaseUrl = `https://anrphijuostbgbscxmzx.supabase.co/functions/v1/${functionName}`;

// Token de acesso (pode ser um anon key ou um service_role key para testes)
// Idealmente, use uma variável de ambiente para isso.
const accessToken = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'; // Chave anon pública de exemplo

// Dados do corpo da requisição (payload)
const requestBody = {
  message: 'quais sao os fornecedores de material eletrico?',
  user_id: 'a47d2f07-6b0a-4bb1-a780-be550b328e0c' // ID de um usuário de teste
};

async function testEdgeFunction() {
  console.log(`Iniciando teste para: ${supabaseUrl}`);

  try {
    const response = await fetch(supabaseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        // O cabeçalho 'Origin' simula uma requisição de um navegador
        'Origin': 'http://localhost:8080'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('--- Status da Resposta ---');
    console.log(response.status, response.statusText);

    console.log('\n--- Cabeçalhos da Resposta ---');
    // Imprime todos os cabeçalhos para análise do CORS
    response.headers.forEach((value, name) => {
      console.log(`${name}: ${value}`);
    });

    console.log('\n--- Corpo da Resposta ---');
    const responseBody = await response.json();
    console.log(JSON.stringify(responseBody, null, 2));

    if (!response.ok) {
      console.error('\n*** Teste falhou! A resposta não foi bem-sucedida. ***');
    }

  } catch (error) {
    console.error('\n--- Erro na Requisição ---');
    console.error('Ocorreu um erro ao tentar se comunicar com a Edge Function:', error);
  }
}

testEdgeFunction();