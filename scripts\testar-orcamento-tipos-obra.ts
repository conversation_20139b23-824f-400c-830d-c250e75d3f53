import { createClient } from '@supabase/supabase-js'

// Configuração do Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://anrphijuostbgbscxmzx.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0YmdiYnNjeG16eCIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNzE0NDA5Mjk4LCJleHAiOjIwMjk5ODUyOTh9.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'

const supabase = createClient(supabaseUrl, supabaseKey)

interface TesteOrcamento {
  nome: string
  tipo_obra: string
  padrao_obra: string
  area_total: number
  area_construida: number
  estado: string
  cidade: string
  tipo_condominio?: "VERTICAL" | "HORIZONTAL"
  numero_blocos?: number
  andares_por_bloco?: number
  unidades_por_andar?: number
  numero_unidades?: number
  area_lote?: number
  area_construida_unidade?: number
  custoEsperadoMin: number
  custoEsperadoMax: number
}

const cenariosTeste: TesteOrcamento[] = [
  {
    nome: "Teste R1_UNIFAMILIAR 100m² NORMAL",
    tipo_obra: "R1_UNIFAMILIAR",
    padrao_obra: "NORMAL", 
    area_total: 100,
    area_construida: 100,
    estado: "SP",
    cidade: "São Paulo",
    custoEsperadoMin: 160000, // R$ 1.600/m²
    custoEsperadoMax: 200000  // R$ 2.000/m²
  },
  {
    nome: "Teste R4_MULTIFAMILIAR VERTICAL 100m² NORMAL",
    tipo_obra: "R4_MULTIFAMILIAR",
    padrao_obra: "NORMAL",
    area_total: 100,
    area_construida: 100,
    estado: "SP", 
    cidade: "São Paulo",
    tipo_condominio: "VERTICAL",
    numero_blocos: 2,
    andares_por_bloco: 5,
    unidades_por_andar: 4,
    numero_unidades: 40,
    area_construida_unidade: 100,
    custoEsperadoMin: 240000, // 45% mais caro que unifamiliar
    custoEsperadoMax: 290000
  },
  {
    nome: "Teste R4_MULTIFAMILIAR HORIZONTAL 100m² NORMAL",
    tipo_obra: "R4_MULTIFAMILIAR", 
    padrao_obra: "NORMAL",
    area_total: 100,
    area_construida: 100,
    estado: "SP",
    cidade: "São Paulo", 
    tipo_condominio: "HORIZONTAL",
    numero_blocos: 5,
    numero_unidades: 20,
    area_lote: 2000,
    area_construida_unidade: 100,
    custoEsperadoMin: 210000, // 25% mais caro que unifamiliar
    custoEsperadoMax: 250000
  }
]

async function testarCenario(cenario: TesteOrcamento): Promise<boolean> {
  console.log(`\n🧪 Testando: ${cenario.nome}`)
  console.log(`📋 Tipo: ${cenario.tipo_obra} | Padrão: ${cenario.padrao_obra} | Área: ${cenario.area_total}m²`)
  
  try {
    // Chamar Edge Function ai-calculate-budget-v11
    const { data, error } = await supabase.functions.invoke('ai-calculate-budget-v11', {
      body: {
        // Dados do formulário
        tipo_obra: cenario.tipo_obra,
        padrao_obra: cenario.padrao_obra,
        area_total: cenario.area_total,
        area_construida: cenario.area_construida,
        estado: cenario.estado,
        cidade: cenario.cidade,
        nome_orcamento: cenario.nome,
        
        // Dados de condomínio (se aplicável)
        tipo_condominio: cenario.tipo_condominio,
        numero_blocos: cenario.numero_blocos,
        andares_por_bloco: cenario.andares_por_bloco,
        unidades_por_andar: cenario.unidades_por_andar,
        numero_unidades: cenario.numero_unidades,
        area_lote: cenario.area_lote,
        area_construida_unidade: cenario.area_construida_unidade,
        
        // Forçar novo cálculo
        forcar_recalculo: true
      }
    })

    if (error) {
      console.error(`❌ Erro na Edge Function:`, error)
      return false
    }

    if (!data || !data.success) {
      console.error(`❌ Falha no cálculo:`, data?.error || 'Erro desconhecido')
      return false
    }

    const custoCalculado = data.custo_estimado
    const custoM2 = data.custo_m2
    const parametrosCalculo = data.parametros_calculo

    console.log(`💰 Custo calculado: R$ ${custoCalculado.toLocaleString('pt-BR')}`)
    console.log(`📐 Custo por m²: R$ ${custoM2.toLocaleString('pt-BR')}/m²`)
    
    // Verificar se está na faixa esperada
    const dentroFaixa = custoCalculado >= cenario.custoEsperadoMin && custoCalculado <= cenario.custoEsperadoMax
    
    if (dentroFaixa) {
      console.log(`✅ PASSOU - Custo dentro da faixa esperada (R$ ${cenario.custoEsperadoMin.toLocaleString('pt-BR')} - R$ ${cenario.custoEsperadoMax.toLocaleString('pt-BR')})`)
    } else {
      console.log(`❌ FALHOU - Custo fora da faixa esperada (R$ ${cenario.custoEsperadoMin.toLocaleString('pt-BR')} - R$ ${cenario.custoEsperadoMax.toLocaleString('pt-BR')})`)
    }

    // Verificar se parâmetros de cálculo foram salvos
    if (parametrosCalculo) {
      console.log(`📊 Parâmetros salvos:`)
      console.log(`   - Tipo obra: ${parametrosCalculo.tipo_obra}`)
      console.log(`   - Subtipo: ${parametrosCalculo.subtipo_calculo || 'N/A'}`)
      console.log(`   - Fator complexidade: ${parametrosCalculo.fator_complexidade}`)
      console.log(`   - CUB utilizado: R$ ${parametrosCalculo.cub_utilizado}/m²`)
      console.log(`   - Versão cálculo: ${parametrosCalculo.versao_calculo}`)
    }

    return dentroFaixa

  } catch (error) {
    console.error(`❌ Erro no teste:`, error)
    return false
  }
}

async function executarTestes() {
  console.log('🚀 Iniciando testes de validação dos tipos de obra\n')
  console.log('📋 Cenários a testar:')
  cenariosTeste.forEach((cenario, index) => {
    console.log(`   ${index + 1}. ${cenario.nome}`)
  })

  let testesPassaram = 0
  let totalTestes = cenariosTeste.length

  for (const cenario of cenariosTeste) {
    const passou = await testarCenario(cenario)
    if (passou) testesPassaram++
  }

  console.log(`\n📊 RESULTADO FINAL:`)
  console.log(`✅ Testes que passaram: ${testesPassaram}/${totalTestes}`)
  console.log(`❌ Testes que falharam: ${totalTestes - testesPassaram}/${totalTestes}`)
  
  if (testesPassaram === totalTestes) {
    console.log(`🎉 TODOS OS TESTES PASSARAM! Implementação validada com sucesso.`)
  } else {
    console.log(`⚠️  Alguns testes falharam. Revisar implementação.`)
  }

  return testesPassaram === totalTestes
}

// Executar testes
if (import.meta.main) {
  executarTestes()
    .then(sucesso => {
      process.exit(sucesso ? 0 : 1)
    })
    .catch(error => {
      console.error('💥 Erro fatal nos testes:', error)
      process.exit(1)
    })
}

export { executarTestes, testarCenario, cenariosTeste }
