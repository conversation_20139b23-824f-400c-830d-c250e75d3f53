// Teste detalhado da Edge Function ai-calculate-budget-v11
const SUPABASE_URL = 'https://anrphijuostbgbscxmzx.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0YmdiYnNjeG16eCIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNzE0NDA5Mjk4LCJleHAiOjIwMjk5ODUyOTh9.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'

async function testarEdgeFunctionDetalhado() {
  console.log('🔍 TESTE DETALHADO DA EDGE FUNCTION')
  console.log('=====================================\n')

  // Teste 1: Modo LEGACY (apenas orcamento_id)
  console.log('📋 TESTE 1: Modo LEGACY')
  console.log('Orçamento ID: e489f18f-119f-49bc-9c7c-66a3c225dd0a')
  
  try {
    const response1 = await fetch(`${SUPABASE_URL}/functions/v1/ai-calculate-budget-v11`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
      },
      body: JSON.stringify({
        orcamento_id: 'e489f18f-119f-49bc-9c7c-66a3c225dd0a',
        forcar_recalculo: true
      })
    })

    console.log(`Status: ${response1.status}`)
    
    if (!response1.ok) {
      const errorText = await response1.text()
      console.log(`❌ Erro HTTP: ${errorText}`)
    } else {
      const data1 = await response1.json()
      console.log('✅ Resposta recebida:')
      console.log(`   - Success: ${data1.success}`)
      console.log(`   - Custo: R$ ${data1.custo_estimado?.toLocaleString('pt-BR') || 'N/A'}`)
      console.log(`   - Custo/m²: R$ ${data1.custo_m2?.toLocaleString('pt-BR') || 'N/A'}`)
      console.log(`   - Parâmetros salvos: ${data1.parametros_calculo ? 'SIM' : 'NÃO'}`)
      
      if (data1.parametros_calculo) {
        console.log(`   - Tipo obra: ${data1.parametros_calculo.tipo_obra}`)
        console.log(`   - Fator complexidade: ${data1.parametros_calculo.fator_complexidade}`)
        console.log(`   - Versão: ${data1.parametros_calculo.versao_calculo}`)
      }
    }
  } catch (error) {
    console.log(`❌ Erro na requisição: ${error.message}`)
  }

  console.log('\n' + '='.repeat(50) + '\n')

  // Teste 2: Modo NOVO (dados completos)
  console.log('📋 TESTE 2: Modo NOVO (dados completos)')
  console.log('Simulando dados do Condomínio Villas')
  
  try {
    const response2 = await fetch(`${SUPABASE_URL}/functions/v1/ai-calculate-budget-v11`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
      },
      body: JSON.stringify({
        // Dados do formulário
        tipo_obra: 'R4_MULTIFAMILIAR',
        padrao_obra: 'NORMAL',
        area_total: 120000,
        area_construida: 120000,
        estado: 'GO',
        cidade: 'Goiânia',
        nome_orcamento: 'Teste Modo Novo - Condomínio Villas',
        
        // Dados de condomínio
        tipo_condominio: 'VERTICAL',
        numero_blocos: 20,
        andares_por_bloco: 20,
        unidades_por_andar: 4,
        numero_unidades: 1600,
        area_lote: 36000,
        area_construida_unidade: 75,
        
        // Obra vinculada
        obra_id: '06604245-e518-41ac-ae5d-67ec32338e67',
        
        // Forçar novo cálculo
        forcar_recalculo: true
      })
    })

    console.log(`Status: ${response2.status}`)
    
    if (!response2.ok) {
      const errorText = await response2.text()
      console.log(`❌ Erro HTTP: ${errorText}`)
    } else {
      const data2 = await response2.json()
      console.log('✅ Resposta recebida:')
      console.log(`   - Success: ${data2.success}`)
      console.log(`   - Custo: R$ ${data2.custo_estimado?.toLocaleString('pt-BR') || 'N/A'}`)
      console.log(`   - Custo/m²: R$ ${data2.custo_m2?.toLocaleString('pt-BR') || 'N/A'}`)
      console.log(`   - Parâmetros salvos: ${data2.parametros_calculo ? 'SIM' : 'NÃO'}`)
      
      if (data2.parametros_calculo) {
        console.log(`   - Tipo obra: ${data2.parametros_calculo.tipo_obra}`)
        console.log(`   - Subtipo: ${data2.parametros_calculo.subtipo_calculo}`)
        console.log(`   - Fator complexidade: ${data2.parametros_calculo.fator_complexidade}`)
        console.log(`   - CUB utilizado: R$ ${data2.parametros_calculo.cub_utilizado}/m²`)
        console.log(`   - Versão: ${data2.parametros_calculo.versao_calculo}`)
      }
      
      if (data2.debug) {
        console.log(`   - Modo cálculo: ${data2.debug.modo_calculo}`)
        console.log(`   - Área calculada: ${data2.debug.area_calculada}m²`)
      }
    }
  } catch (error) {
    console.log(`❌ Erro na requisição: ${error.message}`)
  }

  console.log('\n' + '='.repeat(50) + '\n')
  console.log('🏁 TESTE CONCLUÍDO')
}

testarEdgeFunctionDetalhado()
  .then(() => {
    console.log('\n✅ Todos os testes executados')
    process.exit(0)
  })
  .catch(error => {
    console.error('\n💥 Erro fatal:', error)
    process.exit(1)
  })
