// Teste simples para validar os tipos de obra
const SUPABASE_URL = 'https://anrphijuostbgbscxmzx.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0YmdiYnNjeG16eCIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNzE0NDA5Mjk4LCJleHAiOjIwMjk5ODUyOTh9.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'

async function testarEdgeFunction() {
  console.log('🧪 Testando Edge Function ai-calculate-budget-v11')
  
  const cenarios = [
    {
      nome: "R1_UNIFAMILIAR",
      dados: {
        tipo_obra: "R1_UNIFAMILIAR",
        padrao_obra: "NORMAL",
        area_total: 100,
        area_construida: 100,
        estado: "SP",
        cidade: "São Paulo",
        nome_orcamento: "Teste Unifamiliar"
      },
      esperado: { min: 160000, max: 200000 }
    },
    {
      nome: "R4_MULTIFAMILIAR VERTICAL",
      dados: {
        tipo_obra: "R4_MULTIFAMILIAR",
        padrao_obra: "NORMAL",
        area_total: 100,
        area_construida: 100,
        estado: "SP",
        cidade: "São Paulo",
        nome_orcamento: "Teste Multifamiliar Vertical",
        tipo_condominio: "VERTICAL",
        numero_blocos: 2,
        andares_por_bloco: 5,
        unidades_por_andar: 4,
        numero_unidades: 40
      },
      esperado: { min: 240000, max: 290000 }
    },
    {
      nome: "R4_MULTIFAMILIAR HORIZONTAL", 
      dados: {
        tipo_obra: "R4_MULTIFAMILIAR",
        padrao_obra: "NORMAL",
        area_total: 100,
        area_construida: 100,
        estado: "SP",
        cidade: "São Paulo",
        nome_orcamento: "Teste Multifamiliar Horizontal",
        tipo_condominio: "HORIZONTAL",
        numero_blocos: 5,
        numero_unidades: 20,
        area_lote: 2000
      },
      esperado: { min: 210000, max: 250000 }
    }
  ]

  let sucessos = 0
  
  for (const cenario of cenarios) {
    console.log(`\n📋 Testando: ${cenario.nome}`)
    
    try {
      const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-calculate-budget-v11`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        },
        body: JSON.stringify(cenario.dados)
      })

      if (!response.ok) {
        console.log(`❌ Erro HTTP: ${response.status} - ${response.statusText}`)
        continue
      }

      const data = await response.json()
      
      if (!data.success) {
        console.log(`❌ Falha no cálculo: ${data.error || 'Erro desconhecido'}`)
        continue
      }

      const custo = data.custo_estimado
      const custoM2 = data.custo_m2
      
      console.log(`💰 Custo: R$ ${custo.toLocaleString('pt-BR')}`)
      console.log(`📐 Custo/m²: R$ ${custoM2.toLocaleString('pt-BR')}`)
      
      const dentroFaixa = custo >= cenario.esperado.min && custo <= cenario.esperado.max
      
      if (dentroFaixa) {
        console.log(`✅ PASSOU - Dentro da faixa esperada`)
        sucessos++
      } else {
        console.log(`❌ FALHOU - Fora da faixa esperada (R$ ${cenario.esperado.min.toLocaleString('pt-BR')} - R$ ${cenario.esperado.max.toLocaleString('pt-BR')})`)
      }

      // Mostrar parâmetros de cálculo se disponíveis
      if (data.parametros_calculo) {
        console.log(`📊 Tipo: ${data.parametros_calculo.tipo_obra} | Fator: ${data.parametros_calculo.fator_complexidade}`)
      }

    } catch (error) {
      console.log(`❌ Erro: ${error.message}`)
    }
  }

  console.log(`\n📊 RESULTADO: ${sucessos}/${cenarios.length} testes passaram`)
  
  if (sucessos === cenarios.length) {
    console.log(`🎉 TODOS OS TESTES PASSARAM!`)
  } else {
    console.log(`⚠️ Alguns testes falharam`)
  }
  
  return sucessos === cenarios.length
}

testarEdgeFunction()
  .then(sucesso => {
    process.exit(sucesso ? 0 : 1)
  })
  .catch(error => {
    console.error('💥 Erro:', error)
    process.exit(1)
  })
