/**
 * 🔍 Script de Validação - Percentuais de Mão de Obra
 * 
 * Analisa a composição de custos da função ai-calculate-budget-v11
 * para verificar se os percentuais de mão de obra estão realistas.
 * 
 * Padrões da Construção Civil Brasileira:
 * - Mão de Obra: 25-35% do custo total
 * - Material: 50-65% do custo total  
 * - Serviços Terceirizados: 8-15% do custo total
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 VALIDAÇÃO DOS PERCENTUAIS DE MÃO DE OBRA');
console.log('==========================================\n');

// Validar múltiplas funções de orçamento
const funcoesParaValidar = [
  'supabase/functions/ai-calculate-budget-v11/index.ts',
  'supabase/functions/ai-calculate-budget/index.ts'
];

funcoesParaValidar.forEach((funcaoRelativa, index) => {
  console.log(`\n📋 VALIDANDO FUNÇÃO ${index + 1}/2: ${funcaoRelativa}`);
  console.log('='.repeat(60));
  
  const funcaoPath = path.join(process.cwd(), funcaoRelativa);
  
  if (!fs.existsSync(funcaoPath)) {
    console.log('❌ Arquivo não encontrado');
    return;
  }
  
  const conteudo = fs.readFileSync(funcaoPath, 'utf8');

  console.log('✅ Arquivo da função encontrado');
  console.log('📊 Analisando percentuais de mão de obra:\n');

  // Detectar tipo de função
  const temComposicaoEtapas = conteudo.includes('composicaoEtapas');
  const temMaoObraObrigatoria = conteudo.includes('maoObraObrigatoria');
  
  if (temComposicaoEtapas) {
    console.log('🏗️ Tipo: Função v11 (composições por etapas)');
    validarFuncaoV11(conteudo);
  } else if (temMaoObraObrigatoria) {
    console.log('🏗️ Tipo: Função v5.1 (itens obrigatórios de mão de obra)');
    validarFuncaoV51(conteudo);
  } else {
    console.log('⚠️ Tipo: Função antiga (sem garantia de percentuais)');
    console.log('❌ Esta função pode gerar percentuais irrealistas de mão de obra');
  }
});

function validarFuncaoV11(conteudo) {
  // Buscar todos os itens de mão de obra
  const regexMaoObra = /categoria:\s*['"]MAO_DE_OBRA['"],\s*percentual:\s*([\d.]+)/g;
  let matchMaoObra;
  const itensMaoObra = [];

  while ((matchMaoObra = regexMaoObra.exec(conteudo)) !== null) {
    itensMaoObra.push(parseFloat(matchMaoObra[1]));
  }

  console.log(`🔍 Encontrados ${itensMaoObra.length} itens de mão de obra`);
  console.log('📋 Percentuais individuais:', itensMaoObra.map(p => `${(p * 100).toFixed(1)}%`).join(', '));

  // Calcular o percentual médio por etapa
  const mediaPercentualMaoObra = itensMaoObra.reduce((sum, p) => sum + p, 0) / itensMaoObra.length;

  console.log(`📊 Percentual médio de mão de obra por item: ${(mediaPercentualMaoObra * 100).toFixed(1)}%`);

// Estimar percentual total considerando todas as etapas
// Como há múltiplos itens de mão de obra por etapa, precisamos somar por etapa
const etapas = [
  { nome: 'FUNDACAO', proporcao: 0.127 },
  { nome: 'ESTRUTURA', proporcao: 0.178 },
  { nome: 'ALVENARIA', proporcao: 0.101 },
  { nome: 'COBERTURA', proporcao: 0.091 },
  { nome: 'INSTALACOES_ELETRICAS', proporcao: 0.076 },
  { nome: 'INSTALACOES_HIDRAULICAS', proporcao: 0.061 },
  { nome: 'REVESTIMENTOS_INTERNOS', proporcao: 0.112 },
  { nome: 'PINTURA', proporcao: 0.041 },
  { nome: 'ACABAMENTOS', proporcao: 0.127 }
];

let totalPercentualMaoObra = 0;

etapas.forEach(etapa => {
  // Buscar percentuais de mão de obra específicos desta etapa
  const regexEtapa = new RegExp(`${etapa.nome}:[\\s\\S]*?\\]`, 'g');
  const matchEtapa = regexEtapa.exec(conteudo);
  
  if (matchEtapa) {
    const secaoEtapa = matchEtapa[0];
    const regexMaoObraEtapa = /categoria:\s*['"]MAO_DE_OBRA['"],\s*percentual:\s*([\d.]+)/g;
    let matchMaoObraEtapa;
    let percentualMaoObraEtapa = 0;
    
    while ((matchMaoObraEtapa = regexMaoObraEtapa.exec(secaoEtapa)) !== null) {
      percentualMaoObraEtapa += parseFloat(matchMaoObraEtapa[1]);
    }
    
    const contribuicaoTotal = etapa.proporcao * percentualMaoObraEtapa;
    totalPercentualMaoObra += contribuicaoTotal;
    
    console.log(`🏗️  ${etapa.nome}:`);
    console.log(`   Proporção da obra: ${(etapa.proporcao * 100).toFixed(1)}%`);
    console.log(`   Mão de obra na etapa: ${(percentualMaoObraEtapa * 100).toFixed(1)}%`);
    console.log(`   Contribuição total: ${(contribuicaoTotal * 100).toFixed(1)}%\n`);
  }
});

console.log('📈 RESUMO FINAL:');
console.log('================');
console.log(`💪 Total de Mão de Obra: ${(totalPercentualMaoObra * 100).toFixed(1)}%`);

// Verificação dos padrões
if (totalPercentualMaoObra >= 0.25 && totalPercentualMaoObra <= 0.35) {
  console.log('✅ PERCENTUAL DENTRO DO PADRÃO (25-35%)');
} else if (totalPercentualMaoObra < 0.25) {
  console.log('❌ PERCENTUAL ABAIXO DO PADRÃO (< 25%)');
  console.log('   Necessário aumentar percentuais de mão de obra');
} else {
  console.log('⚠️  PERCENTUAL ACIMA DO PADRÃO (> 35%)');
  console.log('   Verificar se não está superestimado');
}

console.log('\n🎯 COMPARAÇÃO COM PADRÕES DA CONSTRUÇÃO CIVIL:');
console.log('================================================');
console.log('Padrão Ideal:');
console.log('• Mão de Obra: 25-35%');
console.log('• Material: 50-65%');
console.log('• Serviços: 8-15%');
console.log('• Outros: 5-10%');

console.log('\n📋 STATUS DAS CORREÇÕES:');
console.log('=========================');

if (totalPercentualMaoObra >= 0.25) {
  console.log('✅ Correções implementadas com sucesso!');
  console.log('✅ Percentuais agora refletem a realidade brasileira');
  console.log('✅ Sistema gerará orçamentos mais precisos');
} else {
  console.log('❌ Correções ainda necessárias');
  console.log('❌ Percentuais ainda não refletem a realidade');
}

console.log('\n🚀 Próximos passos:');
  console.log('• Deploy da função corrigida');
  console.log('• Teste com orçamento real');
  console.log('• Validação dos resultados');
}

function validarFuncaoV51(conteudo) {
  console.log('🔍 Analisando itens obrigatórios de mão de obra...\n');
  
  // Buscar array maoObraObrigatoria
  const regexMaoObraArray = /maoObraObrigatoria\s*=\s*\[([\s\S]*?)\];/;
  const matchArray = regexMaoObraArray.exec(conteudo);
  
  if (!matchArray) {
    console.log('❌ Array maoObraObrigatoria não encontrado');
    return;
  }
  
  // Extrair itens individuais
  const arrayContent = matchArray[1];
  const regexItens = /\{\s*insumo:\s*['"]([^'"]+)['"],\s*horas:\s*area\s*\*\s*([\d.]+),\s*valor_hora:\s*([\d.]+)/g;
  
  let matchItem;
  const itensMaoObra = [];
  let horasTotais = 0;
  let valorTotalEstimado = 0;
  
  while ((matchItem = regexItens.exec(arrayContent)) !== null) {
    const item = {
      insumo: matchItem[1],
      horasPorM2: parseFloat(matchItem[2]),
      valorHora: parseFloat(matchItem[3])
    };
    itensMaoObra.push(item);
    horasTotais += item.horasPorM2;
    valorTotalEstimado += item.horasPorM2 * item.valorHora; // para 1m²
  }
  
  console.log(`🔍 Encontrados ${itensMaoObra.length} itens obrigatórios de mão de obra:`);
  itensMaoObra.forEach(item => {
    console.log(`   • ${item.insumo}: ${item.horasPorM2}h/m² × R$ ${item.valorHora}/h`);
  });
  
  console.log(`\n📊 Total de horas por m²: ${horasTotais.toFixed(2)}h`);
  console.log(`💰 Custo de mão de obra por m² (estimado): R$ ${valorTotalEstimado.toFixed(2)}`);
  
  // Estimar percentual para uma obra típica de 100m² com CUB de R$ 1800/m²
  const areaTipica = 100;
  const cubTipico = 1800;
  const custoTotalObra = areaTipica * cubTipico;
  const custoMaoObraTotal = areaTipica * valorTotalEstimado;
  const percentualMaoObra = (custoMaoObraTotal / custoTotalObra) * 100;
  
  console.log(`\n🏗️ SIMULAÇÃO (obra de ${areaTipica}m² com CUB R$ ${cubTipico}/m²):`);
  console.log(`   Custo total obra: R$ ${custoTotalObra.toLocaleString('pt-BR')}`);
  console.log(`   Custo mão de obra: R$ ${custoMaoObraTotal.toLocaleString('pt-BR')}`);
  console.log(`   Percentual mão de obra: ${percentualMaoObra.toFixed(1)}%`);
  
  // Validação
  if (percentualMaoObra >= 25 && percentualMaoObra <= 35) {
    console.log('\n✅ PERCENTUAL DENTRO DO PADRÃO (25-35%)');
  } else if (percentualMaoObra < 25) {
    console.log('\n❌ PERCENTUAL ABAIXO DO PADRÃO (< 25%)');
  } else {
    console.log('\n⚠️ PERCENTUAL ACIMA DO PADRÃO (> 35%)');
  }
}