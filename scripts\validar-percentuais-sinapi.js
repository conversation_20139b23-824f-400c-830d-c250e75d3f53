#!/usr/bin/env node

/**
 * 🧮 Script de Validação - Percentuais SINAPI de Mão de Obra
 * 
 * Valida se os percentuais de mão de obra estão dentro dos padrões
 * realistas da construção civil brasileira (25-35%).
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuração Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Simula o cálculo de mão de obra com dados SINAPI
 */
async function simularCalculoMaoObra(area = 120, estado = 'SP') {
  console.log(`🏗️ Simulando cálculo para ${area}m² em ${estado}...`);
  
  // Mapeamento de etapas e fatores técnicos
  const etapasConstrutivas = {
    'ESTRUTURA': {
      grupos: ['ESTRUTURA', 'CONCRETO', 'FUNDAÇÃO'],
      fatores: { pedreiro: 2.4, servente: 1.8 }
    },
    'ALVENARIA': {
      grupos: ['ALVENARIA', 'VEDAÇÃO'],
      fatores: { pedreiro: 1.6, servente: 1.2 }
    },
    'COBERTURA': {
      grupos: ['COBERTURA', 'TELHADO'],
      fatores: { carpinteiro: 0.8, servente: 0.6 }
    },
    'INSTALACOES': {
      grupos: ['INSTALAÇÃO', 'ELÉTRICA', 'HIDRÁULICA'],
      fatores: { eletricista: 0.9, encanador: 0.7 }
    },
    'ACABAMENTO': {
      grupos: ['ACABAMENTO', 'PINTURA', 'REVESTIMENTO'],
      fatores: { pintor: 1.1, gesseiro: 0.5, azulejista: 0.7 }
    }
  };

  let custoTotalMaoObra = 0;
  let totalItens = 0;

  // Processar cada etapa
  for (const [etapa, config] of Object.entries(etapasConstrutivas)) {
    console.log(`\n🔍 Processando etapa: ${etapa}`);
    
    // Buscar composições SINAPI
    const { data: composicoes, error } = await supabase
      .from('sinapi_composicoes_mao_obra')
      .select('codigo_composicao, descricao, grupo, preco_sem_sp, preco_com_sp')
      .ilike('grupo', `%${config.grupos[0]}%`)
      .limit(3);

    if (error) {
      console.error(`❌ Erro ao consultar ${etapa}:`, error.message);
      continue;
    }

    if (composicoes && composicoes.length > 0) {
      // Usar dados SINAPI reais
      for (const comp of composicoes) {
        const preco = estado === 'SP' ? comp.preco_com_sp : comp.preco_sem_sp;
        if (preco && preco > 0) {
          const horasM2 = estimarHorasM2(comp.descricao);
          const valorHora = (preco * 0.4) / horasM2; // 40% do preço é mão de obra
          const custoItem = horasM2 * area * valorHora;
          
          custoTotalMaoObra += custoItem;
          totalItens++;
          
          console.log(`  ✅ ${comp.codigo_composicao}: ${horasM2}h/m² × R$${valorHora.toFixed(2)}/h = R$${custoItem.toFixed(2)}`);
        }
      }
    } else {
      // Usar fallback técnico
      console.log(`  ⚠️ Usando fallback técnico para ${etapa}`);
      for (const [profissional, horasM2] of Object.entries(config.fatores)) {
        const valorHora = obterValorHoraPadrao(profissional);
        const custoItem = horasM2 * area * valorHora;
        
        custoTotalMaoObra += custoItem;
        totalItens++;
        
        console.log(`  📋 ${profissional}: ${horasM2}h/m² × R$${valorHora}/h = R$${custoItem.toFixed(2)}`);
      }
    }
  }

  return { custoTotalMaoObra, totalItens };
}

/**
 * Estima horas/m² baseado na descrição SINAPI
 */
function estimarHorasM2(descricao) {
  const desc = descricao.toLowerCase();
  
  if (desc.includes('pedreiro')) return 1.8;
  if (desc.includes('servente')) return 1.2;
  if (desc.includes('eletricista')) return 0.8;
  if (desc.includes('encanador')) return 0.7;
  if (desc.includes('carpinteiro')) return 0.6;
  if (desc.includes('pintor')) return 0.9;
  
  return 1.0; // Padrão
}

/**
 * Valores padrão por profissional
 */
function obterValorHoraPadrao(profissional) {
  const valores = {
    pedreiro: 28.0,
    servente: 20.0,
    eletricista: 38.0,
    encanador: 35.0,
    carpinteiro: 32.0,
    pintor: 25.0,
    gesseiro: 27.0,
    azulejista: 30.0
  };
  
  return valores[profissional] || 25.0;
}

/**
 * Estima custo total da obra baseado em padrões de mercado
 */
function estimarCustoTotalObra(area, padrao = 'NORMAL') {
  const custoM2 = {
    'BAIXO': 1200,
    'NORMAL': 1800,
    'ALTO': 2500,
    'LUXO': 3500
  };
  
  return area * custoM2[padrao];
}

/**
 * Função principal
 */
async function main() {
  console.log('🎯 VALIDAÇÃO DE PERCENTUAIS SINAPI - MÃO DE OBRA');
  console.log('=' .repeat(60));
  
  const cenarios = [
    { area: 80, estado: 'SP', padrao: 'NORMAL' },
    { area: 120, estado: 'RJ', padrao: 'ALTO' },
    { area: 200, estado: 'MG', padrao: 'BAIXO' }
  ];
  
  for (const cenario of cenarios) {
    console.log(`\n📊 CENÁRIO: ${cenario.area}m² - ${cenario.estado} - ${cenario.padrao}`);
    console.log('-'.repeat(50));
    
    const { custoTotalMaoObra, totalItens } = await simularCalculoMaoObra(
      cenario.area, 
      cenario.estado
    );
    
    const custoTotalObra = estimarCustoTotalObra(cenario.area, cenario.padrao);
    const percentualMaoObra = (custoTotalMaoObra / custoTotalObra) * 100;
    
    console.log(`\n📈 RESULTADO:`);
    console.log(`   Custo Mão de Obra: R$ ${custoTotalMaoObra.toFixed(2)}`);
    console.log(`   Custo Total Obra: R$ ${custoTotalObra.toFixed(2)}`);
    console.log(`   Percentual: ${percentualMaoObra.toFixed(1)}%`);
    console.log(`   Itens Processados: ${totalItens}`);
    
    // Validação
    if (percentualMaoObra >= 25 && percentualMaoObra <= 35) {
      console.log(`   ✅ APROVADO - Percentual realista!`);
    } else if (percentualMaoObra >= 20 && percentualMaoObra <= 40) {
      console.log(`   ⚠️ ACEITÁVEL - Dentro da margem aceitável`);
    } else {
      console.log(`   ❌ PROBLEMA - Percentual fora do padrão (25-35%)`);
    }
  }
  
  console.log('\n✅ Validação concluída!');
  process.exit(0);
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { simularCalculoMaoObra, estimarCustoTotalObra };