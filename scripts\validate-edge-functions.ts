#!/usr/bin/env -S deno run --allow-read

/**
 * 🔍 Validador de Edge Functions - ObrasAI 2.2
 * 
 * Valida se as Edge Functions seguem os padrões estabelecidos
 */

import { walk } from "https://deno.land/std@0.208.0/fs/walk.ts";
import { join, basename } from "https://deno.land/std@0.208.0/path/mod.ts";

// Critérios de validação
interface ValidationCriteria {
  hasStandardImports: boolean;
  usesCreateEdgeFunction: boolean;
  hasProperErrorHandling: boolean;
  hasStructuredLogging: boolean;
  hasValidation: boolean;
  hasRateLimit: boolean;
  hasProperCORS: boolean;
  hasTypeScript: boolean;
  hasDocumentation: boolean;
}

interface ValidationResult {
  functionName: string;
  path: string;
  score: number;
  maxScore: number;
  criteria: ValidationCriteria;
  issues: string[];
  recommendations: string[];
}

/**
 * Valida uma Edge Function específica
 */
async function validateEdgeFunction(filePath: string): Promise<ValidationResult> {
  const functionName = basename(filePath.replace('/index.ts', ''));
  const content = await Deno.readTextFile(filePath);
  
  const criteria: ValidationCriteria = {
    hasStandardImports: false,
    usesCreateEdgeFunction: false,
    hasProperErrorHandling: false,
    hasStructuredLogging: false,
    hasValidation: false,
    hasRateLimit: false,
    hasProperCORS: false,
    hasTypeScript: true, // Assumindo .ts
    hasDocumentation: false
  };
  
  const issues: string[] = [];
  const recommendations: string[] = [];

  // 1. Verificar imports padronizados
  if (content.includes("from '../_shared/")) {
    criteria.hasStandardImports = true;
  } else {
    issues.push('Não usa imports padronizados da pasta _shared');
    recommendations.push('Migrar para usar utilitários da pasta _shared');
  }

  // 2. Verificar uso do createEdgeFunction
  if (content.includes('createEdgeFunction')) {
    criteria.usesCreateEdgeFunction = true;
  } else {
    issues.push('Não usa o template createEdgeFunction');
    recommendations.push('Refatorar para usar createEdgeFunction template');
  }

  // 3. Verificar tratamento de erros
  if (content.includes('createErrorResponse') || content.includes('ERROR_CODES')) {
    criteria.hasProperErrorHandling = true;
  } else {
    issues.push('Não usa tratamento de erros padronizado');
    recommendations.push('Implementar createErrorResponse e ERROR_CODES');
  }

  // 4. Verificar logging estruturado
  if (content.includes('logger.') && content.includes('createFunctionLogger')) {
    criteria.hasStructuredLogging = true;
  } else {
    issues.push('Não usa logging estruturado');
    recommendations.push('Implementar logging com createFunctionLogger');
  }

  // 5. Verificar validação Zod
  if (content.includes('z.object') || content.includes('SCHEMAS.')) {
    criteria.hasValidation = true;
  } else {
    issues.push('Não usa validação Zod');
    recommendations.push('Implementar validação com schemas Zod');
  }

  // 6. Verificar rate limiting
  if (content.includes('rateLimit') || content.includes('checkRateLimit')) {
    criteria.hasRateLimit = true;
  } else {
    issues.push('Não implementa rate limiting');
    recommendations.push('Adicionar rate limiting para proteção');
  }

  // 7. Verificar CORS padronizado
  if (content.includes('getSecureCorsHeaders') || content.includes('createOptionsResponse')) {
    criteria.hasProperCORS = true;
  } else {
    issues.push('Não usa CORS padronizado');
    recommendations.push('Implementar CORS seguro com getSecureCorsHeaders');
  }

  // 8. Verificar documentação
  if (content.includes('/**') && content.includes('*/')) {
    criteria.hasDocumentation = true;
  } else {
    issues.push('Falta documentação JSDoc');
    recommendations.push('Adicionar documentação JSDoc detalhada');
  }

  // Calcular score
  const criteriaValues = Object.values(criteria);
  const score = criteriaValues.filter(Boolean).length;
  const maxScore = criteriaValues.length;

  return {
    functionName,
    path: filePath,
    score,
    maxScore,
    criteria,
    issues,
    recommendations
  };
}

/**
 * Gera relatório de validação
 */
function generateValidationReport(results: ValidationResult[]): string {
  const totalFunctions = results.length;
  const averageScore = results.reduce((sum, r) => sum + r.score, 0) / totalFunctions;
  const maxPossibleScore = results[0]?.maxScore || 0;
  const compliancePercentage = ((averageScore / maxPossibleScore) * 100).toFixed(1);

  const excellentFunctions = results.filter(r => r.score >= maxPossibleScore * 0.8);
  const goodFunctions = results.filter(r => r.score >= maxPossibleScore * 0.6 && r.score < maxPossibleScore * 0.8);
  const needsImprovementFunctions = results.filter(r => r.score < maxPossibleScore * 0.6);

  return `# 🔍 Relatório de Validação de Edge Functions

**Data:** ${new Date().toISOString().split('T')[0]}
**Total de Funções:** ${totalFunctions}
**Score Médio:** ${averageScore.toFixed(1)}/${maxPossibleScore}
**Compliance:** ${compliancePercentage}%

## 📊 Resumo Geral

### 🎯 Distribuição de Qualidade
- **Excelente (80%+):** ${excellentFunctions.length} funções
- **Boa (60-79%):** ${goodFunctions.length} funções  
- **Precisa Melhorar (<60%):** ${needsImprovementFunctions.length} funções

### 📋 Critérios de Validação
${Object.entries({
  'Imports Padronizados': results.filter(r => r.criteria.hasStandardImports).length,
  'Template createEdgeFunction': results.filter(r => r.criteria.usesCreateEdgeFunction).length,
  'Tratamento de Erros': results.filter(r => r.criteria.hasProperErrorHandling).length,
  'Logging Estruturado': results.filter(r => r.criteria.hasStructuredLogging).length,
  'Validação Zod': results.filter(r => r.criteria.hasValidation).length,
  'Rate Limiting': results.filter(r => r.criteria.hasRateLimit).length,
  'CORS Seguro': results.filter(r => r.criteria.hasProperCORS).length,
  'Documentação': results.filter(r => r.criteria.hasDocumentation).length
}).map(([criteria, count]) => `- **${criteria}:** ${count}/${totalFunctions} (${((count/totalFunctions)*100).toFixed(1)}%)`).join('\n')}

## 🏆 Funções Excelentes (80%+)

${excellentFunctions.map(f => `### ✅ ${f.functionName}
- **Score:** ${f.score}/${f.maxScore} (${((f.score/f.maxScore)*100).toFixed(1)}%)
- **Path:** \`${f.path}\`
${f.issues.length > 0 ? `- **Melhorias:** ${f.issues.join(', ')}` : '- **Status:** Totalmente conforme'}
`).join('\n')}

## ⚠️ Funções que Precisam de Melhoria

${needsImprovementFunctions.map(f => `### ❌ ${f.functionName}
- **Score:** ${f.score}/${f.maxScore} (${((f.score/f.maxScore)*100).toFixed(1)}%)
- **Path:** \`${f.path}\`
- **Issues:** ${f.issues.join(', ')}
- **Recomendações:**
${f.recommendations.map(rec => `  - ${rec}`).join('\n')}
`).join('\n')}

## 📈 Plano de Ação

### Prioridade ALTA
1. **Migrar funções não conformes** para o template padronizado
2. **Implementar validação Zod** em todas as funções
3. **Adicionar logging estruturado** onde ausente

### Prioridade MÉDIA  
1. **Implementar rate limiting** em funções públicas
2. **Padronizar CORS** em todas as funções
3. **Adicionar documentação JSDoc** completa

### Prioridade BAIXA
1. **Otimizar performance** de funções existentes
2. **Adicionar testes unitários** específicos
3. **Implementar métricas** de uso

## 🎯 Meta de Compliance

**Objetivo:** 95% de compliance até próxima sprint
**Ações necessárias:** ${needsImprovementFunctions.length} funções precisam ser refatoradas

---

**Gerado automaticamente pelo validador de Edge Functions**
`;
}

/**
 * Função principal
 */
async function main(): Promise<void> {
  console.log('🔍 Iniciando validação de Edge Functions...\n');

  const results: ValidationResult[] = [];
  const functionsPath = 'supabase/functions';

  // Buscar todas as Edge Functions
  for await (const entry of walk(functionsPath, {
    includeDirs: false,
    exts: ['.ts'],
    match: [/index\.ts$/]
  })) {
    // Pular arquivos da pasta _shared
    if (entry.path.includes('_shared')) continue;
    
    try {
      console.log(`Validando: ${entry.path}`);
      const result = await validateEdgeFunction(entry.path);
      results.push(result);
      
      const percentage = ((result.score / result.maxScore) * 100).toFixed(1);
      const status = percentage >= '80' ? '✅' : percentage >= '60' ? '⚠️' : '❌';
      console.log(`${status} ${result.functionName}: ${result.score}/${result.maxScore} (${percentage}%)`);
      
    } catch (error) {
      console.error(`❌ Erro ao validar ${entry.path}:`, error);
    }
  }

  // Gerar relatório
  const report = generateValidationReport(results);
  await Deno.writeTextFile('docs/auditoria/RELATORIO_VALIDACAO_EDGE_FUNCTIONS.md', report);

  // Estatísticas finais
  const totalFunctions = results.length;
  const averageScore = results.reduce((sum, r) => sum + r.score, 0) / totalFunctions;
  const maxScore = results[0]?.maxScore || 0;
  const compliancePercentage = ((averageScore / maxScore) * 100).toFixed(1);

  console.log(`\n📊 Validação concluída!`);
  console.log(`📄 Relatório gerado: docs/auditoria/RELATORIO_VALIDACAO_EDGE_FUNCTIONS.md`);
  console.log(`\n📈 Estatísticas:`);
  console.log(`- Total de funções: ${totalFunctions}`);
  console.log(`- Score médio: ${averageScore.toFixed(1)}/${maxScore}`);
  console.log(`- Compliance: ${compliancePercentage}%`);
  
  const needsWork = results.filter(r => r.score < maxScore * 0.8).length;
  if (needsWork > 0) {
    console.log(`\n⚠️  ${needsWork} funções precisam de melhorias`);
  } else {
    console.log(`\n🎉 Todas as funções estão em conformidade!`);
  }
}

// Executar se chamado diretamente
if (import.meta.main) {
  await main();
}
