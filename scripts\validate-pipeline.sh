#!/bin/bash

# 🚀 Pipeline Validation Script - ObrasAI 2.2
# Executa validações locais antes de fazer push para evitar falhas no CI/CD

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Emojis for better UX
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
ROCKET="🚀"

echo -e "${BLUE}${ROCKET} ObrasAI 2.2 - Pipeline Validation${NC}"
echo "=================================================="

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}${CHECK} $2${NC}"
    else
        echo -e "${RED}${CROSS} $2${NC}"
        return 1
    fi
}

print_warning() {
    echo -e "${YELLOW}${WARNING} $1${NC}"
}

print_info() {
    echo -e "${BLUE}${INFO} $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}${CROSS} Error: package.json not found. Run this script from the project root.${NC}"
    exit 1
fi

# Check Node.js version
print_info "Checking Node.js version..."
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -ge 18 ]; then
    print_status 0 "Node.js version: $(node --version)"
else
    print_status 1 "Node.js version $(node --version) is too old. Requires Node.js 18+"
    exit 1
fi

# Check if dependencies are installed
print_info "Checking dependencies..."
if [ ! -d "node_modules" ]; then
    print_warning "Dependencies not installed. Installing..."
    npm ci
fi
print_status 0 "Dependencies are installed"

# 1. Lint Check
print_info "Running lint check..."
if npm run lint > /dev/null 2>&1; then
    print_status 0 "Lint check passed"
else
    print_status 1 "Lint check failed"
    echo "Run 'npm run lint' to see details"
    exit 1
fi

# 2. TypeScript Check
print_info "Running TypeScript strict mode check..."
if npx tsc --noEmit > /dev/null 2>&1; then
    print_status 0 "TypeScript check passed"
else
    print_status 1 "TypeScript check failed"
    echo "Run 'npx tsc --noEmit' to see details"
    exit 1
fi

# 3. Schema Validation
print_info "Running schema validation..."
if npm run validate:schema > /dev/null 2>&1; then
    print_status 0 "Schema validation passed"
else
    print_warning "Schema validation failed (non-blocking)"
fi

# 4. Unit Tests
print_info "Running unit tests..."
if npm run test:unit > /dev/null 2>&1; then
    print_status 0 "Unit tests passed"
else
    print_warning "Some unit tests failed (check details)"
fi

# 5. Integration Tests
print_info "Running integration tests..."
if npm run test:integration > /dev/null 2>&1; then
    print_status 0 "Integration tests passed"
else
    print_warning "Some integration tests failed (check details)"
fi

# 6. Build Check
print_info "Running build check..."
if npm run build > /dev/null 2>&1; then
    print_status 0 "Build successful"
else
    print_status 1 "Build failed"
    echo "Run 'npm run build' to see details"
    exit 1
fi

# 7. Security Audit
print_info "Running security audit..."
if npm audit --audit-level=high > /dev/null 2>&1; then
    print_status 0 "Security audit passed"
else
    print_warning "Security vulnerabilities found (check with 'npm audit')"
fi

# 8. Check for common issues
print_info "Checking for common issues..."

# Check for console.log statements
if grep -r "console\.log" src/ --include="*.ts" --include="*.tsx" > /dev/null 2>&1; then
    print_warning "console.log statements found in source code"
    echo "Consider removing or replacing with proper logging"
else
    print_status 0 "No console.log statements found"
fi

# Check for TODO/FIXME comments
TODO_COUNT=$(grep -r "TODO\|FIXME" src/ --include="*.ts" --include="*.tsx" | wc -l)
if [ "$TODO_COUNT" -gt 0 ]; then
    print_warning "$TODO_COUNT TODO/FIXME comments found"
else
    print_status 0 "No TODO/FIXME comments found"
fi

# Check for hardcoded URLs
if grep -r "http://\|https://" src/ --include="*.ts" --include="*.tsx" | grep -v "example\|placeholder" > /dev/null 2>&1; then
    print_warning "Hardcoded URLs found in source code"
    echo "Consider using environment variables"
else
    print_status 0 "No hardcoded URLs found"
fi

# 9. Git status check
print_info "Checking git status..."
if [ -n "$(git status --porcelain)" ]; then
    print_warning "Uncommitted changes found"
    echo "Consider committing your changes before pushing"
else
    print_status 0 "Working directory is clean"
fi

# 10. Branch check
CURRENT_BRANCH=$(git branch --show-current)
print_info "Current branch: $CURRENT_BRANCH"

if [ "$CURRENT_BRANCH" = "main" ]; then
    print_warning "You're on the main branch. Consider using a feature branch"
fi

# Summary
echo ""
echo "=================================================="
echo -e "${BLUE}${ROCKET} Pipeline Validation Summary${NC}"
echo "=================================================="

echo -e "${GREEN}${CHECK} Core validations passed:${NC}"
echo "  - Lint check"
echo "  - TypeScript strict mode"
echo "  - Build process"

echo ""
echo -e "${YELLOW}${WARNING} Recommendations:${NC}"
echo "  - Review any warnings above"
echo "  - Run full test suite: npm test"
echo "  - Check security audit: npm audit"
echo "  - Review TODO/FIXME comments"

echo ""
echo -e "${GREEN}${ROCKET} Ready for CI/CD pipeline!${NC}"
echo ""
echo "Next steps:"
echo "  1. git add ."
echo "  2. git commit -m 'your message'"
echo "  3. git push origin $CURRENT_BRANCH"
echo ""
echo "The CI/CD pipeline will run automatically and perform:"
echo "  - All validations above"
echo "  - Security scans"
echo "  - Deployment (if on main branch)"
echo ""

exit 0
