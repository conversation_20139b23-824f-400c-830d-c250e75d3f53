#!/usr/bin/env tsx

/**
 * 🔍 Script de Validação de Sincronização do Schema
 * 
 * Verifica se todas as tabelas do banco de dados possuem
 * migrações correspondentes no repositório.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { createClient } from '@supabase/supabase-js';
import { readdir, readFile } from 'fs/promises';
import { join } from 'path';

// Configuração do Supabase
const supabaseUrl = process.env.SUPABASE_URL || 'https://anrphijuostbgbscxmzx.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || '';

if (!supabaseKey) {
  console.error('❌ SUPABASE_ANON_KEY não configurada');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

interface ValidationResult {
  tablesInDatabase: string[];
  tablesInMigrations: string[];
  missingMigrations: string[];
  orphanedMigrations: string[];
  isValid: boolean;
}

/**
 * Busca todas as tabelas do banco de dados
 */
async function getTablesFromDatabase(): Promise<string[]> {
  try {
    const { data, error } = await supabase.rpc('execute_sql', {
      query: `
        SELECT tablename 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT LIKE 'pg_%'
        AND tablename NOT LIKE 'information_%'
        ORDER BY tablename;
      `
    });

    if (error) throw error;
    
    return data?.map((row: any) => row.tablename) || [];
  } catch (error) {
    console.error('❌ Erro ao buscar tabelas do banco:', error);
    return [];
  }
}

/**
 * Extrai nomes de tabelas dos arquivos de migração
 */
async function getTablesFromMigrations(): Promise<string[]> {
  const migrationsDir = join(process.cwd(), 'supabase', 'migrations');
  const tables = new Set<string>();

  try {
    const files = await readdir(migrationsDir);
    const sqlFiles = files.filter(file => file.endsWith('.sql'));

    for (const file of sqlFiles) {
      const content = await readFile(join(migrationsDir, file), 'utf-8');
      
      // Regex para encontrar CREATE TABLE statements
      const createTableRegex = /CREATE TABLE(?:\s+IF NOT EXISTS)?\s+"?public"?\."?(\w+)"?/gi;
      let match;
      
      while ((match = createTableRegex.exec(content)) !== null) {
        tables.add(match[1]);
      }
    }
  } catch (error) {
    console.error('❌ Erro ao ler migrações:', error);
  }

  return Array.from(tables).sort();
}

/**
 * Valida a sincronização do schema
 */
async function validateSchemaSync(): Promise<ValidationResult> {
  console.log('🔍 Iniciando validação de sincronização do schema...\n');

  const [tablesInDatabase, tablesInMigrations] = await Promise.all([
    getTablesFromDatabase(),
    getTablesFromMigrations()
  ]);

  const databaseSet = new Set(tablesInDatabase);
  const migrationsSet = new Set(tablesInMigrations);

  const missingMigrations = tablesInDatabase.filter(table => !migrationsSet.has(table));
  const orphanedMigrations = tablesInMigrations.filter(table => !databaseSet.has(table));

  const isValid = missingMigrations.length === 0 && orphanedMigrations.length === 0;

  return {
    tablesInDatabase,
    tablesInMigrations,
    missingMigrations,
    orphanedMigrations,
    isValid
  };
}

/**
 * Exibe o relatório de validação
 */
function displayReport(result: ValidationResult): void {
  console.log('📊 RELATÓRIO DE VALIDAÇÃO DO SCHEMA');
  console.log('=' .repeat(50));
  
  console.log(`\n📋 Tabelas no Banco: ${result.tablesInDatabase.length}`);
  console.log(`📋 Tabelas nas Migrações: ${result.tablesInMigrations.length}`);
  
  if (result.isValid) {
    console.log('\n✅ SCHEMA SINCRONIZADO');
    console.log('Todas as tabelas possuem migrações correspondentes.');
  } else {
    console.log('\n❌ DIVERGÊNCIAS ENCONTRADAS');
    
    if (result.missingMigrations.length > 0) {
      console.log(`\n🚨 Tabelas sem migração (${result.missingMigrations.length}):`);
      result.missingMigrations.forEach(table => {
        console.log(`  - ${table}`);
      });
    }
    
    if (result.orphanedMigrations.length > 0) {
      console.log(`\n⚠️  Migrações órfãs (${result.orphanedMigrations.length}):`);
      result.orphanedMigrations.forEach(table => {
        console.log(`  - ${table}`);
      });
    }
  }

  console.log('\n📋 TODAS AS TABELAS DO BANCO:');
  result.tablesInDatabase.forEach((table, index) => {
    const hasMigration = result.tablesInMigrations.includes(table);
    const status = hasMigration ? '✅' : '❌';
    console.log(`  ${(index + 1).toString().padStart(2)}. ${status} ${table}`);
  });
}

/**
 * Função principal
 */
async function main(): Promise<void> {
  try {
    const result = await validateSchemaSync();
    displayReport(result);
    
    // Exit code para CI/CD
    process.exit(result.isValid ? 0 : 1);
  } catch (error) {
    console.error('❌ Erro durante validação:', error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

export { validateSchemaSync, ValidationResult };

