import { 
  Alert<PERSON><PERSON>gle,
  BarChart3, 
  Bot, 
  Crown,
  FileText, 
  MessageSquare, 
  Search, 
  Zap
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useAIQuota } from '@/hooks/useAIQuota';
import { useSubscription } from '@/hooks/useSubscription';

interface AIQuotaIndicatorProps {
  feature?: 'chat' | 'budget' | 'contract' | 'sinapi' | 'all';
  showUpgrade?: boolean;
  compact?: boolean;
}

const FEATURE_CONFIG = {
  chat: {
    icon: MessageSquare,
    title: 'Chat IA',
    description: 'Perguntas com IA contextual'
  },
  budget: {
    icon: BarChart3,
    title: 'Orçamentos IA',
    description: 'Geração automática de orçamentos'
  },
  contract: {
    icon: FileText,
    title: 'Contratos IA',
    description: 'Análise de contratos'
  },
  sinapi: {
    icon: Search,
    title: 'Busca SINAPI',
    description: 'Consultas na base SINAPI'
  }
};

export const AIQuotaIndicator = ({ 
  feature = 'all', 
  showUpgrade = true,
  compact = false 
}: AIQuotaIndicatorProps) => {
  const { quotas, isLoading, error } = useAIQuota();
  const { subscription: _subscription, canAccess } = useSubscription();

  // Se não tem acesso a IA, mostrar upgrade
  if (!canAccess('aiFeatures')) {
    return (
      <Card className="p-4 border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-900/20">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center">
            <Crown className="w-5 h-5 text-amber-600 dark:text-amber-400" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-amber-900 dark:text-amber-100">
              IA Premium
            </h3>
            <p className="text-sm text-amber-700 dark:text-amber-300">
              Funcionalidades de IA disponíveis nos planos Pro e Enterprise
            </p>
          </div>
          {showUpgrade && (
            <Button size="sm" className="bg-amber-600 hover:bg-amber-700 text-white">
              Fazer Upgrade
            </Button>
          )}
        </div>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className="p-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-slate-100 dark:bg-slate-800 rounded-lg animate-pulse" />
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded animate-pulse" />
            <div className="h-3 bg-slate-200 dark:bg-slate-700 rounded w-2/3 animate-pulse" />
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-4 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
        <div className="flex items-center gap-3">
          <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
          <span className="text-sm text-red-700 dark:text-red-300">
            Erro ao carregar quotas de IA
          </span>
        </div>
      </Card>
    );
  }

  // Renderizar quota específica
  if (feature !== 'all') {
    const quota = quotas[feature];
    const config = FEATURE_CONFIG[feature];
    const Icon = config.icon;

    // Se ilimitado, mostrar badge premium
    if (quota.limit === -1) {
      return (
        <Card className="p-4 border-emerald-200 bg-emerald-50 dark:border-emerald-800 dark:bg-emerald-900/20">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center">
              <Icon className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-emerald-900 dark:text-emerald-100">
                  {config.title}
                </h3>
                <Badge className="bg-emerald-100 text-emerald-700 border-emerald-300 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-700">
                  <Zap className="w-3 h-3 mr-1" />
                  Ilimitado
                </Badge>
              </div>
              <p className="text-sm text-emerald-700 dark:text-emerald-300">
                {config.description}
              </p>
            </div>
          </div>
        </Card>
      );
    }

    // Mostrar uso atual com barra de progresso
    const isNearLimit = quota.percentage > 80;
    const isAtLimit = quota.current >= quota.limit;

    return (
      <Card className={`p-4 ${isAtLimit ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20' : 
        isNearLimit ? 'border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-900/20' :
        'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50'}`}>
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
              isAtLimit ? 'bg-red-100 dark:bg-red-900/30' :
              isNearLimit ? 'bg-amber-100 dark:bg-amber-900/30' :
              'bg-slate-100 dark:bg-slate-800'
            }`}>
              <Icon className={`w-5 h-5 ${
                isAtLimit ? 'text-red-600 dark:text-red-400' :
                isNearLimit ? 'text-amber-600 dark:text-amber-400' :
                'text-slate-600 dark:text-slate-400'
              }`} />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-slate-900 dark:text-slate-100">
                  {config.title}
                </h3>
                <span className={`text-sm font-medium ${
                  isAtLimit ? 'text-red-600 dark:text-red-400' :
                  isNearLimit ? 'text-amber-600 dark:text-amber-400' :
                  'text-slate-600 dark:text-slate-400'
                }`}>
                  {quota.current}/{quota.limit}
                </span>
              </div>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                {config.description}
              </p>
            </div>
          </div>
          
          <div className="space-y-2">
            <Progress 
              value={quota.percentage} 
              className="h-2"
            />
            <div className="flex items-center justify-between text-xs">
              <span className={`${
                isAtLimit ? 'text-red-600 dark:text-red-400' :
                isNearLimit ? 'text-amber-600 dark:text-amber-400' :
                'text-slate-500 dark:text-slate-400'
              }`}>
                {quota.remaining} usos restantes hoje
              </span>
              {isAtLimit && showUpgrade && (
                <Button size="sm" variant="outline" className="h-6 px-2 text-xs">
                  Fazer Upgrade
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>
    );
  }

  // Renderizar todas as quotas (compact mode)
  if (compact) {
    const features = Object.entries(quotas) as [keyof typeof quotas, typeof quotas[keyof typeof quotas]][];
    const limitedFeatures = features.filter(([_, quota]) => quota.limit !== -1 && quota.limit > 0);
    
    if (limitedFeatures.length === 0) {
      return (
        <Badge className="bg-emerald-100 text-emerald-700 border-emerald-300">
          <Zap className="w-3 h-3 mr-1" />
          IA Ilimitada
        </Badge>
      );
    }

    return (
      <div className="flex items-center gap-2 flex-wrap">
        {limitedFeatures.map(([feature, quota]) => {
          const config = FEATURE_CONFIG[feature];
          const Icon = config.icon;
          const isAtLimit = quota.current >= quota.limit;
          
          return (
            <Badge 
              key={feature}
              variant={isAtLimit ? "destructive" : quota.percentage > 80 ? "secondary" : "default"}
              className="flex items-center gap-1"
            >
              <Icon className="w-3 h-3" />
              {quota.current}/{quota.limit}
            </Badge>
          );
        })}
      </div>
    );
  }

  // Renderizar grid completo de todas as quotas
  const features = Object.entries(quotas) as [keyof typeof quotas, typeof quotas[keyof typeof quotas]][];
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {features.map(([feature, _quota]) => (
        <AIQuotaIndicator 
          key={feature} 
          feature={feature} 
          showUpgrade={showUpgrade}
        />
      ))}
    </div>
  );
};

// Componente helper para mostrar apenas status geral
export const AIQuotaStatus = () => {
  const { quotas } = useAIQuota();
  const { canAccess } = useSubscription();

  if (!canAccess('aiFeatures')) {
    return (
      <Badge variant="secondary" className="bg-amber-100 text-amber-700 border-amber-300">
        <Crown className="w-3 h-3 mr-1" />
        Premium
      </Badge>
    );
  }

  const features = Object.entries(quotas) as [keyof typeof quotas, typeof quotas[keyof typeof quotas]][];
  const limitedFeatures = features.filter(([_, quota]) => quota.limit !== -1 && quota.limit > 0);
  
  if (limitedFeatures.length === 0) {
    return (
      <Badge className="bg-emerald-100 text-emerald-700 border-emerald-300">
        <Zap className="w-3 h-3 mr-1" />
        Ilimitado
      </Badge>
    );
  }

  const atLimitFeatures = limitedFeatures.filter(([_, quota]) => quota.current >= quota.limit);
  const nearLimitFeatures = limitedFeatures.filter(([_, quota]) => quota.percentage > 80);

  if (atLimitFeatures.length > 0) {
    return (
      <Badge variant="destructive">
        <AlertTriangle className="w-3 h-3 mr-1" />
        Limite atingido
      </Badge>
    );
  }

  if (nearLimitFeatures.length > 0) {
    return (
      <Badge variant="secondary" className="bg-amber-100 text-amber-700 border-amber-300">
        <AlertTriangle className="w-3 h-3 mr-1" />
        Próximo do limite
      </Badge>
    );
  }

  return (
    <Badge className="bg-blue-100 text-blue-700 border-blue-300">
      <Bot className="w-3 h-3 mr-1" />
      Disponível
    </Badge>
  );
};