import React from "react";

import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

interface Obra {
  id: string;
  nome: string;
}

interface ChatContextSidebarProps {
  topics: string[];
  obras: Obra[];
  value: string; // 'chat' | `topic:<t>` | `obra:<id>`
  onChange: (v: string) => void;
}

const _ItemButton: React.FC<{
  active: boolean;
  children: React.ReactNode;
  onClick: () => void;
}> = ({ active, children, onClick }) => (
  <button
    onClick={onClick}
    className={cn(
      "w-full text-left px-3 py-2 rounded-md text-sm hover:bg-muted/50",
      active ? "bg-orange-100 dark:bg-orange-900/30 font-medium" : ""
    )}
  >
    {children}
  </button>
);

const ChatContextSidebar: React.FC<ChatContextSidebarProps> = ({ topics, _obras, value, onChange }) => {
  // Mapeamento de tópicos para nomes amigáveis
  const topicLabels: Record<string, string> = {
    despesas: "💰 Despesas",
    obras: "🏗️ Obras",
    orcamento: "📊 Orçamento",
    contratos: "📋 Contratos",
    vendas: "💼 Vendas",
    controle_orcamentario: "📈 Controle Orçamentário",
    sinapi: "🔍 SINAPI",
    fornecedores: "👥 Fornecedores",
    assistente_pessoal: "🤖 Assistente Pessoal"
  };

  return (
    <aside className="w-full lg:w-72 h-full flex flex-col bg-gradient-to-b from-background to-background/95 border-l border-border/50">
      {/* Cabeçalho fixo */}
      <div className="flex-shrink-0 p-4 border-b border-border/30">
        <h3 className="font-semibold text-foreground flex items-center gap-2">
          📚 Tópicos de Treinamento
        </h3>
        <p className="text-xs text-muted-foreground mt-1 leading-relaxed">
          IA especializada com documentação de cada módulo
        </p>
      </div>
      
      {/* Área de scroll para tópicos */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-4 space-y-3 pb-24">
            {topics.map((t) => {
              const val = `topic:${t}`;
              const label = topicLabels[t] || t.charAt(0).toUpperCase() + t.slice(1);
              const isActive = value === val;
              
              return (
                <div key={val} className="group">
                  <button
                    onClick={() => onChange(val)}
                    className={`
                      w-full text-left p-3 rounded-lg transition-all duration-200 hover:scale-[1.02]
                      ${isActive 
                        ? "bg-gradient-to-r from-orange-100 to-orange-50 dark:from-orange-900/30 dark:to-orange-800/20 border border-orange-200 dark:border-orange-700/50 shadow-sm" 
                        : "bg-card hover:bg-accent border border-border/30 hover:border-border/60"
                      }
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`
                        w-2 h-2 rounded-full transition-colors
                        ${isActive ? "bg-orange-500" : "bg-muted-foreground/40"}
                      `} />
                      <span className={`
                        font-medium transition-colors
                        ${isActive ? "text-orange-700 dark:text-orange-300" : "text-foreground"}
                      `}>
                        {label}
                      </span>
                    </div>
                  </button>
                </div>
              );
            })}
          </div>
        </ScrollArea>
      </div>
      
      {/* Dica fixada no final */}
      <div className="flex-shrink-0 p-4 pt-0">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/20 p-4 rounded-lg border border-blue-200/50 dark:border-blue-800/30">
          <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-2 flex items-center gap-2">
            💡 Análise de Obras
          </h4>
          <p className="text-xs text-blue-600 dark:text-blue-400 leading-relaxed">
            Use o dropdown no cabeçalho do chat para acessar dados específicos de suas obras!
          </p>
        </div>
      </div>
    </aside>
  );
};

export default ChatContextSidebar;