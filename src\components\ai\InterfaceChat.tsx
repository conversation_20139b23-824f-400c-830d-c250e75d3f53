import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AnimatePresence, motion } from "framer-motion";
import {
    Building2,
    ChevronDown,
    Copy,
    Loader2,
    RefreshCw,
    Send,
    ThumbsDown,
    ThumbsUp,
    Trash2,
    User as UserIcon
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

import LogoImageDark from "@/assets/logo/logo_image_dark.png";
import LogoImageLight from "@/assets/logo/logo_image_light.png";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAuth } from "@/contexts/auth";
import { useTheme } from "@/hooks/useTheme";
import type { ChatMessage } from "@/services/aiApi";
import { aiApi } from "@/services/aiApi";
import { obrasApi } from "@/services/api";

interface InterfaceChatProps {
  obraId?: string;
  mode?: 'chat' | 'training';
  topic?: string;
  onObraChange?: (obraId: string | null) => void;
}

interface SendMessageSuccessResponse {
  resposta_bot?: string;
  result?: {
    resposta_bot?: string;
  };
}

const InterfaceChat = ({ obraId: propObraId, mode = 'chat', topic, onObraChange }: InterfaceChatProps) => {
  const { user } = useAuth();
  const { theme } = useTheme();
  const [message, setMessage] = useState("");
  const selectedObraId = propObraId || null;
  const scrollRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();
  const [lastTypedMessageId, setLastTypedMessageId] = useState<string | null>(null);
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Logo component that switches based on theme
  const LogoComponent = ({ className = "h-5 w-5" }: { className?: string }) => {
    const isDark = theme === 'dark';
    return (
      <img 
        src={isDark ? LogoImageDark : LogoImageLight} 
        alt="ObrasVision" 
        className={className}
      />
    );
  };
  
  // Carregar obras apenas para exibição do nome, se necessário
  const tenantId = user?.profile?.tenant_id;
  const { data: obras = [] } = useQuery({
    queryKey: ["obras", tenantId],
    queryFn: () => tenantId ? obrasApi.getAll(tenantId) : Promise.resolve([]),
    enabled: !!tenantId, // Só carrega se tiver tenantId válido
  });
  
  // Carregar histórico de mensagens
  const { data: messages = [], isLoading, error } = useQuery({
    queryKey: ["chat_messages", selectedObraId],
    queryFn: () => aiApi.getMessages(selectedObraId),
    refetchOnWindowFocus: false,
  });

  // Limpar histórico
  const { mutate: clearHistory, isPending: isClearingHistory } = useMutation({
    mutationFn: async () => {
      await aiApi.clearMessages(selectedObraId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["chat_messages", selectedObraId] });
      queryClient.refetchQueries({ queryKey: ["chat_messages", selectedObraId] });
      toast.success("Histórico limpo com sucesso!");
    },
    onError: (error) => {
      toast.error("Erro ao limpar histórico: " + error.message);
    },
  });

  const handleClearHistory = () => {
    if (messages.length === 0) return;
    
    if (confirm("Tem certeza que deseja apagar todo o histórico desta conversa? Esta ação não pode ser desfeita.")) {
      clearHistory();
    }
  };

  // Enviar mensagem
  const { mutate: sendMessage, isPending } = useMutation({
    mutationFn: (userMessage: string) => aiApi.sendMessage(userMessage, selectedObraId, mode, topic),
    onMutate: async (userMessage: string) => {
      // Cancelar queries em progresso
      await queryClient.cancelQueries({ queryKey: ["chat_messages", selectedObraId] });

      // Salvar snapshot do estado anterior
      const previousMessages = queryClient.getQueryData<ChatMessage[]>(["chat_messages", selectedObraId]);

      // Limpar input imediatamente
      setMessage("");

      // Iniciar estado de análise
      setIsAnalyzing(true);

      // Atualização otimista: adicionar mensagem do usuário imediatamente
      const tempUserMessage: ChatMessage = {
        id: `temp-${Date.now()}`,
        usuario_id: user?.id || 'anon',
        obra_id: selectedObraId || null,
        mensagem: userMessage,
        resposta_bot: null,
        created_at: new Date().toISOString(),
      };

      queryClient.setQueryData<ChatMessage[]>(["chat_messages", selectedObraId], (old = []) => [
        ...old,
        tempUserMessage
      ]);

      return { previousMessages, userMessage, tempUserMessage };
    },
    onSuccess: (data: SendMessageSuccessResponse, variables, context) => {
      // Atualizar com a resposta real do servidor
      const completeMessage: ChatMessage = {
        ...context!.tempUserMessage,
        id: data?.result?.id || crypto.randomUUID(),
        resposta_bot: data?.resposta_bot || data?.result?.resposta_bot || '—',
      };

      queryClient.setQueryData<ChatMessage[]>(["chat_messages", selectedObraId], (old = []) => {
        // Remover mensagem temporária e adicionar mensagem completa
        const filtered = old.filter(msg => msg.id !== context!.tempUserMessage.id);
        return [...filtered, completeMessage];
      });

      // Parar análise e iniciar typewriter se há resposta
      if (completeMessage.resposta_bot && completeMessage.resposta_bot !== '—') {
        setIsAnalyzing(false);
        setLastTypedMessageId(completeMessage.id);
      } else {
        setIsAnalyzing(false);
      }
    },
    onError: (error, variables, context) => {
      // Parar estado de análise
      setIsAnalyzing(false);

      // Reverter para estado anterior em caso de erro
      if (context?.previousMessages) {
        queryClient.setQueryData(["chat_messages", selectedObraId], context.previousMessages);
      }

      // Restaurar mensagem no input para o usuário tentar novamente
      setMessage(context?.userMessage || '');

      toast.error("Erro ao enviar mensagem: " + error.message);
    },
  });

  // Rolar para a mensagem mais recente quando novas mensagens são adicionadas
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim()) return;
    sendMessage(message.trim());
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // ✅ NOVO - Função para gerar sugestões contextuais baseadas na obra
  const getSuggestions = () => {
    // Se há uma obra selecionada, mostrar sugestões específicas da obra
    if (selectedObraId && obras.length > 0) {
      const obra = obras.find(o => o.id === selectedObraId);
      return [
        {
          icon: "📊",
          text: `Qual o status atual da obra ${obra?.nome}?`,
          description: "Informações sobre progresso e cronograma"
        },
        {
          icon: "💰",
          text: "Quanto já foi gasto nesta obra?",
          description: "Resumo financeiro e despesas"
        },
        {
          icon: "📋",
          text: "Quais materiais ainda precisam ser comprados?",
          description: "Lista de materiais pendentes"
        },
        {
          icon: "⏰",
          text: "Quando a obra deve ser concluída?",
          description: "Cronograma e prazos"
        },
        {
          icon: "🔧",
          text: "Quais são os próximos passos da obra?",
          description: "Atividades planejadas"
        },
        {
          icon: "📈",
          text: "Como está o orçamento vs realizado?",
          description: "Análise financeira comparativa"
        }
      ];
    }
    // Se estamos na página geral (/dashboard/chat) sem obra selecionada
    else if (onObraChange) {
      return [
        {
          icon: "🏗️",
          text: "Quantas obras tenho cadastradas?",
          description: "Visão geral do portfólio"
        },
        {
          icon: "💼",
          text: "Qual obra está mais atrasada?",
          description: "Análise de cronogramas"
        },
        {
          icon: "💰",
          text: "Qual o valor total investido em obras?",
          description: "Resumo financeiro geral"
        },
        {
          icon: "📊",
          text: "Como calcular um orçamento paramétrico?",
          description: "Orientações sobre orçamentação"
        },
        {
          icon: "🔍",
          text: "Quais materiais estão mais caros no SINAPI?",
          description: "Análise de preços de materiais"
        },
        {
          icon: "📋",
          text: "Como gerenciar melhor meus projetos?",
          description: "Dicas de gestão de obras"
        }
      ];
    }
    // Se estamos na aba de uma obra específica (propObraId fixo)
    else {
      const obra = obras.find(o => o.id === propObraId);
      return [
        {
          icon: "📊",
          text: `Qual o status atual da obra ${obra?.nome}?`,
          description: "Informações sobre progresso e cronograma"
        },
        {
          icon: "💰",
          text: "Quanto já foi gasto nesta obra?",
          description: "Resumo financeiro e despesas"
        },
        {
          icon: "📋",
          text: "Quais materiais ainda precisam ser comprados?",
          description: "Lista de materiais pendentes"
        },
        {
          icon: "⏰",
          text: "Quando a obra deve ser concluída?",
          description: "Cronograma e prazos"
        },
        {
          icon: "🔧",
          text: "Quais são os próximos passos da obra?",
          description: "Atividades planejadas"
        },
        {
          icon: "📈",
          text: "Como está o orçamento vs realizado?",
          description: "Análise financeira comparativa"
        }
      ];
    }
  };

  // Função para gerar sugestões de follow-up baseadas na resposta do bot (temporariamente não utilizada)
  // const getFollowUpSuggestions = (botResponse: string) => {
  //   const suggestions = [];
  //   if (botResponse.toLowerCase().includes('orçamento') || botResponse.toLowerCase().includes('custo')) {
  //     suggestions.push("Detalhe os custos", "Como economizar?");
  //   }
  //   if (botResponse.toLowerCase().includes('cronograma') || botResponse.toLowerCase().includes('prazo')) {
  //     suggestions.push("E se atrasar?", "Como acelerar?");
  //   }
  //   if (botResponse.toLowerCase().includes('material') || botResponse.toLowerCase().includes('insumo')) {
  //     suggestions.push("Fornecedores", "Alternativas");
  //   }
  //   if (botResponse.toLowerCase().includes('obra') && selectedObraId) {
  //     suggestions.push("Próximos passos", "Riscos");
  //   }
  //   const generalSuggestions = ["Explique melhor", "Dê exemplos", "E se...?"];
  //   const allSuggestions = [...suggestions, ...generalSuggestions];
  //   return allSuggestions.slice(0, 3);
  // };

  // Função para gerar sugestões rápidas na área de input (temporariamente não utilizada)
  // const getQuickSuggestions = () => {
  //   if (selectedObraId || propObraId) {
  //     return ["Status", "Gastos", "Cronograma", "Materiais"];
  //   }
  //   else {
  //     return ["Resumo", "Orçamento", "SINAPI", "Dicas"];
  //   }
  // };

  // ✅ NOVO - Função para lidar com cliques nas sugestões
  const handleSuggestionClick = (suggestionText: string) => {
    setMessage(suggestionText);
    // Simular envio da mensagem
    setTimeout(() => {
      const form = document.querySelector('form');
      if (form) {
        form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
      }
    }, 100);
  };

  // ✅ Funcionalidades Avançadas
  const handleCopyText = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success("Texto copiado!");
    }).catch(() => {
      toast.error("Erro ao copiar texto");
    });
  };

  const handleRegenerateResponse = (originalMessage: string) => {
    setIsRegenerating(true);
    toast.info("Regenerando resposta...");
    setMessage(originalMessage);
    setTimeout(() => {
      const form = document.querySelector('form');
      if (form) {
        form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
      }
      setTimeout(() => setIsRegenerating(false), 2000);
    }, 100);
  };

  const handleFeedback = (messageId: string, isPositive: boolean) => {
    toast.success(isPositive ? "Obrigado pelo feedback positivo!" : "Obrigado pelo feedback! Vamos melhorar.");
    console.log(`Feedback para mensagem ${messageId}: ${isPositive ? 'positivo' : 'negativo'}`);
  };

  // ✅ Sugestões Rápidas no Footer
  const getQuickSuggestions = () => {
    if (selectedObraId || propObraId) {
      return ["Status", "Gastos", "Cronograma", "Materiais"];
    } else {
      return ["Resumo", "Orçamento", "SINAPI", "Dicas"];
    }
  };

  // ✅ Componente Typewriter com Cursor Piscante
  const Typewriter = ({ text }: { text: string }) => {
    const [displayed, setDisplayed] = useState("");
    const [localIsTyping, setLocalIsTyping] = useState(true);

    useEffect(() => {
      if (!text) return;

      let index = 0;
      setLocalIsTyping(true);
      const timer = setInterval(() => {
        if (index < text.length) {
          setDisplayed(text.slice(0, index + 1));
          index++;
        } else {
          clearInterval(timer);
          setLocalIsTyping(false);
          // Limpar o ID da mensagem sendo digitada quando terminar
          setTimeout(() => setLastTypedMessageId(null), 500);
        }
      }, 20); // Velocidade de digitação: 20ms por caractere

      return () => clearInterval(timer);
    }, [text]);

    return (
      <span className="relative">
        {displayed}
        {/* Cursor piscante com gradiente */}
        {localIsTyping && (
          <motion.span
            animate={{ opacity: [0, 1, 0] }}
            transition={{ duration: 1, repeat: Infinity }}
            className="inline-block w-0.5 h-4 bg-gradient-to-b from-orange-500 to-orange-600 ml-0.5"
          />
        )}
      </span>
    );
  };

  // ✅ Indicador de Análise Simplificado
  const TypingIndicator = () => (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -10, scale: 0.9 }}
      transition={{ type: "spring", stiffness: 200, damping: 20 }}
      className="flex items-center gap-3 p-3 sm:p-4 rounded-2xl bg-gradient-to-br from-orange-500/10 via-orange-600/5 to-blue-500/10 border border-orange-500/20 shadow-lg backdrop-blur-sm"
    >
      <div className="flex items-center gap-3">
        <div className="flex space-x-1">
          {[0, 0.3, 0.6].map((delay, index) => (
            <motion.div
              key={index}
              animate={{
                scale: [1, 1.4, 1],
                opacity: [0.4, 1, 0.4],
                y: [0, -4, 0]
              }}
              transition={{ repeat: Infinity, duration: 1.5, delay }}
              className="w-2.5 h-2.5 bg-gradient-to-r from-orange-500 to-orange-600 rounded-full"
            />
          ))}
        </div>
        <motion.span
          className="text-sm text-muted-foreground font-medium"
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ repeat: Infinity, duration: 2 }}
        >
          <span className="hidden sm:inline">Analisando sua mensagem...</span>
          <span className="sm:hidden">Analisando...</span>
        </motion.span>
      </div>
    </motion.div>
  );

  return (
    <Card className="flex flex-col h-full max-h-full border-0 bg-gradient-to-br from-background via-background to-muted/20 shadow-2xl">
      <CardHeader className="flex-shrink-0 border-b border-gradient-to-r from-transparent via-border/50 to-transparent bg-gradient-to-r from-background/80 via-background to-background/80 backdrop-blur-md">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-4">
            <div className="h-14 w-14 flex items-center justify-center rounded-full bg-transparent">
              <LogoComponent className="h-10 w-10" />
            </div>
            <div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-foreground via-foreground to-muted-foreground bg-clip-text text-transparent">
                Assistente{" "}
                <span className="bg-gradient-to-r from-orange-500 via-orange-600 to-orange-700 bg-clip-text text-transparent">
                  ObrasVision
                </span>
              </h3>
              <p className="text-sm text-muted-foreground font-medium">
                {selectedObraId 
                  ? `Conversa sobre ${obras.find(o => o.id === selectedObraId)?.nome}`
                  : propObraId
                    ? `Conversa sobre ${obras.find(o => o.id === propObraId)?.nome}`
                    : onObraChange
                      ? messages.length > 0 
                        ? `${messages.length} mensagens • Chat geral`
                        : "Chat geral • Selecione uma obra para contexto específico"
                      : messages.length > 0 
                        ? `${messages.length} mensagens nesta conversa`
                        : "Inicie uma conversa"
                }
              </p>
            </div>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {/* Dropdown de Obras - Só aparece se onObraChange foi fornecido */}
            {onObraChange && obras.length > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <Building2 className="h-4 w-4" />
                    <span className="hidden sm:inline">
                      {selectedObraId 
                        ? obras.find(o => o.id === selectedObraId)?.nome || "Obra..." 
                        : "Todas as obras"
                      }
                    </span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-64">
                  <DropdownMenuItem 
                    onClick={() => onObraChange(null)}
                    className={selectedObraId === null ? "bg-accent" : ""}
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                      <div>
                        <div className="font-medium">Conversa Geral</div>
                        <div className="text-xs text-muted-foreground">Sem obra específica</div>
                      </div>
                    </div>
                  </DropdownMenuItem>
                  {obras.map((obra) => (
                    <DropdownMenuItem 
                      key={obra.id}
                      onClick={() => onObraChange(obra.id)}
                      className={selectedObraId === obra.id ? "bg-accent" : ""}
                    >
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <div>
                          <div className="font-medium">{obra.nome}</div>
                          <div className="text-xs text-muted-foreground">Dados específicos da obra</div>
                        </div>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
            
            {/* Botão de limpar histórico */}
            {messages.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearHistory}
                disabled={isClearingHistory}
                className="text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950"
                title="Limpar histórico da conversa"
              >
                {isClearingHistory ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 min-h-0 p-0 overflow-hidden bg-gradient-to-b from-transparent via-background/50 to-transparent">
        <ScrollArea ref={scrollRef} className="h-full w-full">
          <div className="p-6">
          {isLoading ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex justify-center items-center h-full"
            >
              <div className="text-center space-y-3">
                <Loader2 className="h-8 w-8 animate-spin mx-auto text-purple-500" />
                <p className="text-sm text-muted-foreground">Carregando conversa...</p>
              </div>
            </motion.div>
          ) : error ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex justify-center items-center h-full"
            >
              <div className="text-center space-y-3">
                <p className="text-sm text-red-500">Erro ao carregar mensagens: {error.message}</p>
                <Button 
                  onClick={() => queryClient.refetchQueries({ queryKey: ["chat_messages", selectedObraId] })}
                  variant="outline"
                  size="sm"
                >
                  Tentar novamente
                </Button>
              </div>
            </motion.div>
          ) : messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center p-8 space-y-8">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="relative"
              >
                <div className="h-24 w-24 rounded-full bg-muted/20 flex items-center justify-center shadow-lg border border-border/50">
                  <LogoComponent className="h-14 w-14" />
                </div>
                <div className="absolute -top-1 -right-1 h-6 w-6 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                  <div className="h-2 w-2 bg-white rounded-full animate-pulse"></div>
                </div>
              </motion.div>
              <motion.div
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="space-y-4"
              >
                <h3 className="text-2xl font-bold bg-gradient-to-r from-foreground via-foreground to-muted-foreground bg-clip-text text-transparent">
                  Olá! Sou seu assistente {" "}
                  <span className="bg-gradient-to-r from-orange-500 via-orange-600 to-orange-700 bg-clip-text text-transparent">
                    ObrasVision
                  </span>
                </h3>
                <p className="text-muted-foreground max-w-lg leading-relaxed">
                  {selectedObraId
                    ? `Estou aqui para ajudar com tudo sobre a obra "${obras.find(o => o.id === selectedObraId)?.nome}". Posso responder sobre orçamentos, cronogramas, materiais e muito mais!`
                    : propObraId
                      ? `Estou aqui para ajudar com tudo sobre a obra "${obras.find(o => o.id === propObraId)?.nome}". Posso responder sobre orçamentos, cronogramas, materiais e muito mais!`
                      : onObraChange
                        ? "Selecione uma obra no dropdown acima para perguntas específicas, ou faça perguntas gerais sobre construção civil, gerenciamento de obras e análise de projetos!"
                        : "Estou aqui para ajudar com suas dúvidas sobre construção civil, gerenciamento de obras, análise de projetos e muito mais!"
                  }
                </p>
                <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground/60">
                  <div className="h-1 w-1 bg-muted-foreground/40 rounded-full"></div>
                  <span>Powered by AI</span>
                  <div className="h-1 w-1 bg-muted-foreground/40 rounded-full"></div>
                  <span>Sempre aprendendo</span>
                  <div className="h-1 w-1 bg-muted-foreground/40 rounded-full"></div>
                </div>
              </motion.div>

              {/* Sugestões de Perguntas Contextuais */}
              <motion.div
                initial={{ y: 30, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="w-full max-w-2xl"
              >
                {/* Dica especial para página geral sem obra selecionada */}
                {onObraChange && !selectedObraId && (
                  <motion.div
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                    className="mb-6 p-4 rounded-xl bg-gradient-to-r from-blue-500/10 via-blue-600/5 to-purple-500/10 border border-blue-500/20"
                  >
                    <div className="text-center">
                      <p className="text-sm font-medium text-blue-600 dark:text-blue-400 mb-2">
                        🎯 Dica: Para perguntas específicas
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Use o dropdown "Selecionar Obra" acima para fazer perguntas contextuais sobre uma obra específica
                      </p>
                    </div>
                  </motion.div>
                )}

                <div className="text-center mb-4">
                  <p className="text-sm font-medium text-muted-foreground">
                    💡 {selectedObraId || propObraId ? 'Perguntas sobre esta obra:' : 'Sugestões para começar:'}
                  </p>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {getSuggestions().map((suggestion, index) => (
                    <motion.button
                      key={index}
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: 0.5 + index * 0.1, duration: 0.3 }}
                      whileHover={{ scale: 1.02, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleSuggestionClick(suggestion.text)}
                      className="p-4 text-left rounded-xl border border-border/50 bg-muted/20 hover:bg-muted/40 hover:border-border transition-all duration-200 group"
                    >
                      <div className="flex items-start gap-3">
                        <div className="text-lg group-hover:scale-110 transition-transform duration-200">
                          {suggestion.icon}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-foreground group-hover:text-foreground/90 transition-colors">
                            {suggestion.text}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {suggestion.description}
                          </p>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            </div>
          ) : (
            <div className="space-y-4 sm:space-y-6">
              <AnimatePresence>
                {messages.map((msg, index) => (
                  <motion.div
                    key={msg.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.05 }}
                    className="space-y-2"
                  >
                    {/* Mensagem do usuário */}
                    <div className="flex justify-end">
                      <div className="flex items-end gap-3 max-w-[85%] sm:max-w-[80%] lg:max-w-[75%]">
                        <div className="space-y-1">
                          <motion.div
                            className="bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 text-white p-4 rounded-2xl rounded-br-md shadow-lg border-0 relative overflow-hidden group"
                            whileHover={{ scale: 1.02, y: -2 }}
                            transition={{ duration: 0.2 }}
                          >
                            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none"></div>
                            <motion.div
                              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"
                              initial={{ x: "-100%" }}
                              animate={{ x: "100%" }}
                              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                            />
                            <p className="text-sm whitespace-pre-wrap font-medium relative z-10 mb-2">{msg.mensagem}</p>
                            
                            {/* Botão de copiar para mensagem do usuário */}
                            <div className="flex justify-end opacity-0 group-hover:opacity-100 transition-opacity duration-200 relative z-10">
                              <button
                                onClick={() => handleCopyText(msg.mensagem)}
                                className="p-1.5 rounded-md hover:bg-white/10 transition-colors duration-200"
                                title="Copiar mensagem"
                              >
                                <Copy className="h-3.5 w-3.5 text-white/70 hover:text-white transition-colors" />
                              </button>
                            </div>
                          </motion.div>
                          <p className="text-xs text-muted-foreground text-right">
                            {formatDateTime(msg.created_at)}
                          </p>
                        </div>
                        <Avatar className="h-8 w-8 sm:h-9 sm:w-9">
                          <AvatarFallback className="bg-primary/10">
                            <UserIcon className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                      </div>
                    </div>

                    {/* Resposta do bot */}
                    {msg.resposta_bot && (
                      <div className="flex justify-start">
                        <div className="flex items-end gap-3 max-w-[85%] sm:max-w-[80%] lg:max-w-[75%]">
                          <Avatar className="h-8 w-8 sm:h-9 sm:w-9">
                            <AvatarFallback className="bg-transparent border-0 p-0">
                              <LogoComponent className="h-7 w-7" />
                            </AvatarFallback>
                          </Avatar>
                          <div className="space-y-1">
                            <motion.div
                              className="bg-gradient-to-br from-muted/80 via-muted/60 to-muted/40 backdrop-blur-sm p-4 rounded-2xl rounded-bl-md shadow-lg border border-border/50 relative overflow-hidden group"
                              whileHover={{ scale: 1.01, y: -1 }}
                              transition={{ duration: 0.2 }}
                            >
                              <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 via-transparent to-blue-500/5 pointer-events-none"></div>

                              {/* Efeito shimmer - Só aparece durante digitação */}
                              {msg.id === lastTypedMessageId && (
                                <motion.div
                                  className="absolute inset-0 bg-gradient-to-r from-transparent via-orange-500/20 to-transparent"
                                  initial={{ x: "-100%" }}
                                  animate={{ x: "100%" }}
                                  transition={{ duration: 2, repeat: Infinity, repeatType: "loop", ease: "linear" }}
                                />
                              )}

                              {/* Indicador de resposta regenerada */}
                              {isRegenerating && index === messages.length - 1 && (
                                <div className="absolute top-2 right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full z-20 animate-pulse">
                                  ✨ Nova resposta
                                </div>
                              )}

                              <p className="text-sm whitespace-pre-wrap leading-relaxed relative z-10 mb-3">
                                {msg.id === lastTypedMessageId ? (
                                  <Typewriter text={msg.resposta_bot} />
                                ) : (
                                  msg.resposta_bot
                                )}
                              </p>

                              {/* Botões de ação com animações */}
                              <motion.div
                                className="flex items-center justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300 relative z-10"
                                initial={{ y: 10 }}
                                animate={{ y: 0 }}
                                transition={{ duration: 0.2 }}
                              >
                                <div className="flex items-center gap-1">
                                  {/* Reações */}
                                  <motion.button
                                    onClick={() => handleFeedback(msg.id, true)}
                                    className="p-1.5 rounded-md hover:bg-green-500/10 transition-colors duration-200 group/btn"
                                    title="Resposta útil"
                                    whileHover={{ scale: 1.1, rotate: 5 }}
                                    whileTap={{ scale: 0.9 }}
                                  >
                                    <ThumbsUp className="h-3.5 w-3.5 text-muted-foreground group-hover/btn:text-green-600 transition-colors" />
                                  </motion.button>
                                  <motion.button
                                    onClick={() => handleFeedback(msg.id, false)}
                                    className="p-1.5 rounded-md hover:bg-red-500/10 transition-colors duration-200 group/btn"
                                    title="Resposta não útil"
                                    whileHover={{ scale: 1.1, rotate: -5 }}
                                    whileTap={{ scale: 0.9 }}
                                  >
                                    <ThumbsDown className="h-3.5 w-3.5 text-muted-foreground group-hover/btn:text-red-600 transition-colors" />
                                  </motion.button>
                                </div>

                                <div className="flex items-center gap-1">
                                  {/* Copiar */}
                                  <motion.button
                                    onClick={() => handleCopyText(msg.resposta_bot)}
                                    className="p-1.5 rounded-md hover:bg-blue-500/10 transition-colors duration-200 group/btn"
                                    title="Copiar resposta"
                                    whileHover={{ scale: 1.1, y: -2 }}
                                    whileTap={{ scale: 0.9 }}
                                  >
                                    <Copy className="h-3.5 w-3.5 text-muted-foreground group-hover/btn:text-blue-600 transition-colors" />
                                  </motion.button>

                                  {/* Regenerar */}
                                  <motion.button
                                    onClick={() => handleRegenerateResponse(msg.mensagem)}
                                    disabled={isRegenerating}
                                    className="p-1.5 rounded-md hover:bg-orange-500/10 transition-colors duration-200 group/btn disabled:opacity-50"
                                    title="Regenerar resposta"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                    animate={isRegenerating ? { rotate: 360 } : { rotate: 0 }}
                                    transition={isRegenerating ? { duration: 1, repeat: Infinity, ease: "linear" } : { duration: 0.2 }}
                                  >
                                    <RefreshCw className="h-3.5 w-3.5 text-muted-foreground group-hover/btn:text-orange-600 transition-colors" />
                                  </motion.button>
                                </div>
                              </motion.div>
                            </motion.div>
                          </div>
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>
              
              {/* Indicador de análise */}
              <AnimatePresence>
                {isAnalyzing && (
                  <div className="flex justify-start">
                    <div className="flex items-end gap-3 max-w-[85%] sm:max-w-[80%] lg:max-w-[75%]">
                      <div className="space-y-1">
                        <TypingIndicator />
                      </div>
                    </div>
                  </div>
                )}
              </AnimatePresence>
            </div>
          )}
          </div>
        </ScrollArea>
      </CardContent>

      <CardFooter className="flex-shrink-0 p-4 sm:p-6 border-t border-gradient-to-r from-transparent via-border/50 to-transparent bg-gradient-to-r from-background/80 via-background to-background/80 backdrop-blur-md">
        <div className="w-full space-y-4">
          {/* Sugestões rápidas acima do input */}
          {messages.length > 0 && (
            <div className="flex flex-wrap gap-2">
              <span className="text-xs font-medium text-muted-foreground mr-2 hidden sm:inline">💡 Perguntas rápidas:</span>
              <span className="text-xs font-medium text-muted-foreground mr-2 sm:hidden">💡</span>
              {getQuickSuggestions().map((suggestion, index) => (
                <motion.button
                  key={index}
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="px-3 py-1 text-xs font-medium bg-muted/30 hover:bg-muted/60 border border-border/30 rounded-full transition-all duration-200 hover:shadow-sm"
                >
                  {suggestion}
                </motion.button>
              ))}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="flex gap-3 w-full">
            <div className="flex-1 relative">
              <Input
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder={
                  selectedObraId
                    ? `Pergunte sobre ${obras.find(o => o.id === selectedObraId)?.nome}...`
                    : "Digite sua mensagem..."
                }
                disabled={isPending}
                className="flex-1 h-12 px-4 pr-12 rounded-xl border-2 border-border/50 bg-background/80 backdrop-blur-sm focus:border-orange-500/50 focus:ring-2 focus:ring-orange-500/20 transition-all duration-200 placeholder:text-muted-foreground/60"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground/40">
                <kbd className="text-xs bg-muted/50 px-1.5 py-0.5 rounded">Enter</kbd>
              </div>
            </div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                type="submit"
                disabled={isPending || !message.trim()}
                size="icon"
                className="h-12 w-12 rounded-xl bg-gradient-to-br from-orange-500 via-orange-600 to-orange-700 hover:from-orange-600 hover:via-orange-700 hover:to-orange-800 shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed ring-2 ring-orange-500/20"
              >
                {isPending ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <Loader2 className="h-5 w-5 text-white" />
                  </motion.div>
                ) : (
                  <motion.div
                    whileHover={{ x: 2 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Send className="h-5 w-5 text-white" />
                  </motion.div>
                )}
              </Button>
            </motion.div>
          </form>
        </div>
      </CardFooter>
    </Card>
  );
};

export default InterfaceChat;
