import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON>, EyeOff, Loader2 } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { t } from "@/lib/i18n";
import type { RegisterFormValues } from "@/lib/validations/auth";
import { registerSchema } from "@/lib/validations/auth";
import { useAnalytics } from "@/services/analyticsApi";

export const RegisterForm = () => {
  const { register } = useAuth();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { trackConversion } = useAnalytics();

  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const createTrialSubscription = async (userId: string) => {
    try {
      const { data, error } = await supabase.functions.invoke(
        "create-trial-subscription",
        {
          body: { userId },
        }
      );

      if (error) {
        console.error("Erro ao criar trial:", error);
        // Não falha o cadastro se não conseguir criar trial
      }

      return data;
    } catch (error) {
      console.error("Erro ao chamar função de trial:", error);
    }
  };

  const onSubmit = async (data: RegisterFormValues) => {
    try {
      setIsLoading(true);
      
      // 1. Registrar usuário
      const { error, data: authData } = await register(
        data.email,
        data.password,
        data.firstName,
        data.lastName
      );

      if (error) {
        toast.error(error.message || t("auth.registerError"));
        return;
      }

      // 2. Criar trial automático
      if (authData?.user?.id) {
        await createTrialSubscription(authData.user.id);
      }

      // 3. Track conversão de signup
      await trackConversion("signup", {
        user_email: data.email,
        user_name: `${data.firstName} ${data.lastName}`,
        referrer: document.referrer,
        user_agent: navigator.userAgent,
        registration_method: "email",
        trial_created: true,
      });

      toast.success(
        "Conta criada com sucesso! Seu trial de 7 dias começou. Verifique seu email para confirmar."
      );

      // 4. Redirecionar para dashboard após confirmação
      navigate("/dashboard");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : t("messages.generalError");
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="firstName"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-200 font-medium">
                  {t("auth.firstName")}
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("auth.enterFirstName")}
                    {...field}
                    disabled={isLoading}
                    className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                  />
                </FormControl>
                <FormMessage className="text-red-400" />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="lastName"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-200 font-medium">
                  {t("auth.lastName")}
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("auth.enterLastName")}
                    {...field}
                    disabled={isLoading}
                    className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                  />
                </FormControl>
                <FormMessage className="text-red-400" />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium">
                {t("auth.email")}
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("auth.enterEmail")}
                  {...field}
                  disabled={isLoading}
                  className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-[#daa916] focus:ring-2 focus:ring-[#daa916]/20"
                />
              </FormControl>
              <FormMessage className="text-red-600" />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium">
                {t("auth.password")}
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder={t("auth.enterPassword")}
                    {...field}
                    disabled={isLoading}
                    className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-[#daa916] focus:ring-2 focus:ring-[#daa916]/20 pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-200 transition-colors"
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </FormControl>
              <FormMessage className="text-red-600" />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-gray-700 font-medium">
                {t("auth.confirmPassword")}
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder={t("auth.confirmPassword")}
                    {...field}
                    disabled={isLoading}
                    className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus:bg-gray-600 focus:border-[#daa916] focus:ring-2 focus:ring-[#daa916]/20 pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-200 transition-colors"
                    disabled={isLoading}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </FormControl>
              <FormMessage className="text-red-600" />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          className="w-full bg-orange-500 text-white font-bold shadow-lg border-0 hover:bg-orange-600 transition-all duration-300 transform hover:scale-[1.02] focus:ring-4 focus:ring-orange-500/40"
          disabled={isLoading}
        >
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Começar Trial Gratuito - 7 Dias
        </Button>

        <p className="text-xs text-gray-400 text-center mt-4">
          Ao cadastrar-se, você concorda com nossos Termos de Serviço
        </p>
      </form>
    </Form>
  );
};
