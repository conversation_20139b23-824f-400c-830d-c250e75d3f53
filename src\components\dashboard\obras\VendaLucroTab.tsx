import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { AlertCircle, Brain, Calculator, DollarSign, Loader2,TrendingDown, TrendingUp } from "lucide-react";
import type { FC} from "react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { FormSection,FormWrapper } from "@/components/ui/FormWrapper";
import { GradientCard } from "@/components/ui/GradientCard";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useFormMutation } from "@/hooks/useFormMutation";
import { supabase } from "@/integrations/supabase/client";
import { formatCurrencyBR } from "@/lib/i18n";
import { cn } from "@/lib/utils";

const vendaSchema = z.object({
  valor_venda: z.coerce.number().min(0, "Valor deve ser positivo").optional(),
  data_venda: z.string().optional(),
  status_venda: z.enum(['A_VENDA', 'EM_NEGOCIACAO', 'VENDIDO']).default('A_VENDA'),
  comissao_corretor_percentual: z.coerce.number().min(0).max(100, "Comissão deve estar entre 0% e 100%").optional(),
  outras_despesas_venda: z.coerce.number().min(0, "Valor deve ser positivo").optional(),
});

type VendaFormValues = z.infer<typeof vendaSchema>;

interface VendaLucroTabProps {
  obraId: string;
}

interface LucratividadeData {
  id: string;
  nome: string;
  orcamento: number;
  valor_venda: number | null;
  status_venda: string;
  custo_total_real: number | null;
  lucro_bruto: number | null;
  lucro_liquido: number | null;
  margem_lucro_percentual: number | null;
  roi_percentual: number | null;
}

const VendaLucroTab: FC<VendaLucroTabProps> = ({ obraId }) => {
  const [analysisResult, setAnalysisResult] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Query para dados da obra
  const { data: obra, refetch: refetchObra } = useQuery({
    queryKey: ["obra", obraId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("obras")
        .select("*")
        .eq("id", obraId)
        .single();

      if (error) throw error;
      return data;
    },
  });

  // Query para dados de lucratividade
  const { data: lucratividade, isLoading: _isLoadingLucratividade } = useQuery({
    queryKey: ["lucratividade", obraId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("v_obras_lucratividade")
        .select("*")
        .eq("id", obraId)
        .single();

      if (error) throw error;
      return data as LucratividadeData;
    },
  });

  // Formulário de venda
  const form = useForm<VendaFormValues>({
    resolver: zodResolver(vendaSchema),
    defaultValues: {
      valor_venda: obra?.valor_venda || undefined,
      data_venda: obra?.data_venda || "",
      status_venda: obra?.status_venda || "A_VENDA",
      comissao_corretor_percentual: obra?.comissao_corretor_percentual || undefined,
      outras_despesas_venda: obra?.outras_despesas_venda || undefined,
    },
  });

  // Mutação para atualizar dados de venda
  const updateVendaMutation = useFormMutation<{ id: string }[], VendaFormValues>({
    mutationFn: async (values: VendaFormValues) => {
      const { data, error } = await supabase
        .from("obras")
        .update({
          valor_venda: values.valor_venda,
          data_venda: values.data_venda || null,
          status_venda: values.status_venda,
          comissao_corretor_percentual: values.comissao_corretor_percentual,
          outras_despesas_venda: values.outras_despesas_venda,
        })
        .eq("id", obraId)
        .select();

      if (error) throw error;
      return data;
    },
    queryKey: ["obra", obraId],
    successMessage: "Dados de venda atualizados com sucesso!",
    errorMessage: "Erro ao atualizar dados de venda",
    onSuccessCallback: () => {
      refetchObra();
      // Invalidar cache da lista de vendas para atualizar VendasLista.tsx
      queryClient.invalidateQueries({ queryKey: ["vendas-lista"] });
      // Invalidar cache da lucratividade para atualizar a view local
      queryClient.invalidateQueries({ queryKey: ["lucratividade", obraId] });
    },
  });

  const onSubmit = (values: VendaFormValues) => {
    updateVendaMutation.mutate(values);
  };

  // Mutation para análise de IA
  const analysisMutation = useMutation({
    mutationFn: async () => {
      const { data, error } = await supabase.functions.invoke("analise-viabilidade-venda", {
        body: { obra_id: obraId },
      });

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      setAnalysisResult(data.analysis);
      toast.success("Análise de viabilidade gerada com sucesso!");
    },
    onError: (error) => {
      console.error("Erro na análise:", error);
      toast.error("Erro ao gerar análise de viabilidade");
    },
  });

  const handleGenerateAnalysis = () => {
    if (!lucratividade?.valor_venda) {
      toast.error("Defina o valor de venda antes de gerar a análise");
      return;
    }
    analysisMutation.mutate();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "VENDIDO":
        return "text-green-600 bg-green-50 border-green-200";
      case "EM_NEGOCIACAO":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      default:
        return "text-blue-600 bg-blue-50 border-blue-200";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "VENDIDO":
        return "Vendido";
      case "EM_NEGOCIACAO":
        return "Em Negociação";
      default:
        return "À Venda";
    }
  };

  return (
    <div className="space-y-6">
      {/* Formulário de Venda */}
      <FormWrapper
        form={form}
        onSubmit={onSubmit}
        title="Informações de Venda"
        description="Gerencie os dados financeiros da venda do imóvel"
        isLoading={updateVendaMutation.isPending}
        submitLabel="Atualizar Venda"
        cardVariant="blue"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormSection title="Dados da Venda" icon={<DollarSign className="h-4 w-4" />}>
            <FormField
              control={form.control}
              name="valor_venda"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Valor de Venda</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0,00"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="data_venda"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Data da Venda</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status_venda"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status da Venda</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="A_VENDA">À Venda</SelectItem>
                      <SelectItem value="EM_NEGOCIACAO">Em Negociação</SelectItem>
                      <SelectItem value="VENDIDO">Vendido</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </FormSection>

          <FormSection title="Despesas da Venda" icon={<Calculator className="h-4 w-4" />}>
            <FormField
              control={form.control}
              name="comissao_corretor_percentual"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Comissão do Corretor (%)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0,00"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="outras_despesas_venda"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Outras Despesas</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0,00"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </FormSection>
        </div>
      </FormWrapper>

      {/* Análise de Lucratividade */}
      {lucratividade && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <GradientCard
            variant="emerald"
            title="Análise de Lucratividade"
            description="Análise detalhada dos custos e lucros da obra"
            icon={<TrendingUp className="h-5 w-5" />}
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {/* Status da Venda */}
              <div className="flex items-center justify-between p-3 rounded-lg border">
                <div>
                  <p className="text-sm text-muted-foreground">Status</p>
                  <p className={cn("font-semibold text-sm px-2 py-1 rounded", getStatusColor(lucratividade.status_venda))}>
                    {getStatusLabel(lucratividade.status_venda)}
                  </p>
                </div>
              </div>

              {/* Valor de Venda */}
              <div className="flex items-center justify-between p-3 rounded-lg border">
                <div>
                  <p className="text-sm text-muted-foreground">Valor de Venda</p>
                  <p className="text-xl font-bold text-green-600">
                    {lucratividade.valor_venda ? formatCurrencyBR(lucratividade.valor_venda) : "Não definido"}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>

              {/* Custos Totais */}
              <div className="flex items-center justify-between p-3 rounded-lg border">
                <div>
                  <p className="text-sm text-muted-foreground">Custos Totais</p>
                  <p className="text-xl font-bold text-red-600">
                    {lucratividade.custo_total_real ? formatCurrencyBR(lucratividade.custo_total_real) : formatCurrencyBR(0)}
                  </p>
                </div>
                <TrendingDown className="h-8 w-8 text-red-500" />
              </div>
            </div>

            {lucratividade.valor_venda ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Lucro Bruto */}
                <div className="p-4 rounded-lg border bg-muted/30">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">Lucro Bruto</h4>
                    <TrendingUp className="h-5 w-5 text-blue-500" />
                  </div>
                  <p className={cn("text-2xl font-bold", 
                    (lucratividade.lucro_bruto || 0) >= 0 ? "text-green-600" : "text-red-600"
                  )}>
                    {lucratividade.lucro_bruto ? formatCurrencyBR(lucratividade.lucro_bruto) : formatCurrencyBR(0)}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Margem: {lucratividade.margem_lucro_percentual?.toFixed(2) || "0.00"}%
                  </p>
                </div>

                {/* Lucro Líquido */}
                <div className="p-4 rounded-lg border bg-muted/30">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold">Lucro Líquido</h4>
                    <Calculator className="h-5 w-5 text-purple-500" />
                  </div>
                  <p className={cn("text-2xl font-bold",
                    (lucratividade.lucro_liquido || 0) >= 0 ? "text-green-600" : "text-red-600"
                  )}>
                    {lucratividade.lucro_liquido ? formatCurrencyBR(lucratividade.lucro_liquido) : formatCurrencyBR(0)}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    ROI: {lucratividade.roi_percentual?.toFixed(2) || "0.00"}%
                  </p>
                </div>
              </div>
            ) : (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Defina o valor de venda para visualizar a análise de lucratividade completa.
                </AlertDescription>
              </Alert>
            )}
          </GradientCard>
        </motion.div>
      )}

      {/* Análise de IA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <GradientCard
          variant="purple"
          title="Análise de Viabilidade - IA"
          description="Obtenha insights estratégicos para otimizar sua venda"
          icon={<Brain className="h-5 w-5" />}
        >
          <div className="flex items-center justify-between mb-4">
            <p className="text-sm text-muted-foreground">
              Nossa IA especializada em mercado imobiliário analisa os dados financeiros e gera recomendações estratégicas.
            </p>
            <Button
              onClick={handleGenerateAnalysis}
              disabled={analysisMutation.isPending || !lucratividade?.valor_venda}
              className="ml-4"
              variant="outline"
            >
              {analysisMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Analisando...
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4 mr-2" />
                  Gerar Análise
                </>
              )}
            </Button>
          </div>

          {!lucratividade?.valor_venda && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Defina o valor de venda acima para ativar a análise de viabilidade com IA.
              </AlertDescription>
            </Alert>
          )}

          {analysisResult && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-purple-500" />
                  Relatório de Análise
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none">
                  <div
                    className="whitespace-pre-wrap text-sm leading-relaxed"
                    dangerouslySetInnerHTML={{
                      __html: analysisResult.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                        .replace(/\*(.*?)\*/g, '<em>$1</em>')
                        .replace(/\n\n/g, '</p><p>')
                        .replace(/^/, '<p>')
                        .replace(/$/, '</p>')
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </GradientCard>
      </motion.div>
    </div>
  );
};

export default VendaLucroTab;
