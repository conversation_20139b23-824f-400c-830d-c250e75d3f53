import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import React from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useObrasCondominio } from "@/hooks/useObrasCondominio";
import { condominioSchema } from "@/lib/validators/condominioValidator";
import type { CreateCondominioData } from "@/types/condominio";

const CondominioForm: React.FC = () => {
  const { createCondominioRPC } = useObrasCondominio();

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<CreateCondominioData>({
    resolver: zodResolver(condominioSchema),
    defaultValues: {
      obra_mae: {
        nome: "",
        endereco: "",
        cidade: "",
        estado: "",
        cep: "",
      },
      unidades: [{ identificador_unidade: "101", nome: "Apartamento 101" }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "unidades",
  });

  const mutation = useMutation({
    mutationFn: createCondominioRPC,
    onSuccess: (data) => {
      toast.success(
        `Condomínio "${data.obra_mae.nome}" criado com sucesso! ${data.unidades.length} unidades adicionadas.`
      );
      // Resetar o formulário ou redirecionar
    },
    onError: (error) => {
      toast.error(`Erro ao criar condomínio: ${error.message}`);
    },
  });

  const onSubmit = (data: CreateCondominioData) => {
    // Converter para formato esperado pela RPC
    const apiData = {
      condominio_data: data.obra_mae,
      unidades_data: data.unidades,
    };

    mutation.mutate(apiData);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Dados do Condomínio</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="obra_mae.nome">Nome do Condomínio</Label>
            <Input id="obra_mae.nome" {...register("obra_mae.nome")} />
            {errors.obra_mae?.nome && (
              <p className="text-red-500">{errors.obra_mae.nome.message}</p>
            )}
          </div>
          {/* Adicionar outros campos da obra mãe aqui */}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Unidades do Condomínio</CardTitle>
        </CardHeader>
        <CardContent>
          {fields.map((field, index) => (
            <div key={field.id} className="flex items-center space-x-4 mb-4">
              <Input
                {...register(`unidades.${index}.identificador_unidade`)}
                placeholder="Identificador"
              />
              <Input
                {...register(`unidades.${index}.nome`)}
                placeholder="Nome da Unidade"
              />
              <Button
                type="button"
                onClick={() => remove(index)}
                variant="destructive"
              >
                Remover
              </Button>
            </div>
          ))}
          <Button
            type="button"
            onClick={() => append({ identificador_unidade: "", nome: "" })}
          >
            Adicionar Unidade
          </Button>
        </CardContent>
      </Card>

      <CardFooter>
        <Button type="submit" disabled={mutation.isPending}>
          {mutation.isPending ? "Criando..." : "Criar Condomínio"}
        </Button>
      </CardFooter>
    </form>
  );
};

export default CondominioForm;
