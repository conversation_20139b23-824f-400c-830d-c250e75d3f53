import { Search, X } from "lucide-react";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

interface BarraBuscaProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export function BarraBusca({ 
  value, 
  onValueChange, 
  placeholder = "Buscar fornecedores...",
  className 
}: BarraBuscaProps) {
  const [isFocused, setIsFocused] = useState(false);

  const handleClear = () => {
    onValueChange("");
  };

  return (
    <div className={cn("relative", className)}>
      <div className="relative">
        <Search className={cn(
          "absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 transition-colors duration-200",
          isFocused ? "text-blue-500" : "text-slate-400"
        )} />
        <Input
          type="text"
          placeholder={placeholder}
          value={value}
          onChange={(e) => onValueChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          className={cn(
            "pl-10 pr-10",
            "border-slate-200 dark:border-slate-700",
            "focus:border-blue-500 dark:focus:border-blue-400",
            "focus:ring-blue-500/20 dark:focus:ring-blue-400/20",
            "bg-white dark:bg-slate-900",
            "transition-all duration-200",
            isFocused && "ring-2 ring-blue-500/20 dark:ring-blue-400/20"
          )}
        />
        {value && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className={cn(
              "absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-0",
              "hover:bg-slate-100 dark:hover:bg-slate-800",
              "text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
            )}
          >
            <X className="h-3.5 w-3.5" />
            <span className="sr-only">Limpar busca</span>
          </Button>
        )}
      </div>
      
      {/* Indicador de resultados */}
      {value && (
        <div className="absolute top-full left-0 right-0 mt-1">
          <div className="text-xs text-slate-500 dark:text-slate-400 px-1">
            Buscando por: <span className="font-medium text-slate-700 dark:text-slate-300">"{value}"</span>
          </div>
        </div>
      )}
    </div>
  );
}
