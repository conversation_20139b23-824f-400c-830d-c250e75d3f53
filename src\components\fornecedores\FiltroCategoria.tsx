import { Check, ChevronDown, Filter } from "lucide-react";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { getAllCategoriasPF, getAllCategoriasPJ } from "@/lib/utils/fornecedores";

interface FiltroCategoriaProps {
  tipo: "pj" | "pf";
  value?: string;
  onValueChange: (value: string | undefined) => void;
  placeholder?: string;
}

export function FiltroCategoria({ 
  tipo, 
  value, 
  onValueChange, 
  placeholder = "Filtrar por categoria..." 
}: FiltroCategoriaProps) {
  const [open, setOpen] = useState(false);
  
  const categorias = tipo === "pj" ? getAllCategoriasPJ() : getAllCategoriasPF();
  const selectedCategoria = categorias.find((categoria) => categoria.value === value);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-[280px] justify-between",
            "border-slate-200 dark:border-slate-700",
            "hover:bg-slate-50 dark:hover:bg-slate-800",
            "transition-colors duration-200"
          )}
        >
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-slate-500" />
            {selectedCategoria ? (
              <div className="flex items-center gap-2">
                <selectedCategoria.icon className="h-4 w-4" />
                <span className="truncate">{selectedCategoria.label}</span>
              </div>
            ) : (
              <span className="text-slate-500">{placeholder}</span>
            )}
          </div>
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[280px] p-0" align="start">
        <Command>
          <CommandInput placeholder="Buscar categoria..." className="h-9" />
          <CommandList>
            <CommandEmpty>Nenhuma categoria encontrada.</CommandEmpty>
            <CommandGroup>
              <CommandItem
                value=""
                onSelect={() => {
                  onValueChange(undefined);
                  setOpen(false);
                }}
                className="flex items-center gap-2"
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    !value ? "opacity-100" : "opacity-0"
                  )}
                />
                <Filter className="h-4 w-4 text-slate-500" />
                <span>Todas as categorias</span>
              </CommandItem>
              {categorias.map((categoria) => (
                <CommandItem
                  key={categoria.value}
                  value={categoria.value}
                  onSelect={(currentValue) => {
                    onValueChange(currentValue === value ? undefined : currentValue);
                    setOpen(false);
                  }}
                  className="flex items-center gap-2"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === categoria.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <categoria.icon className="h-4 w-4" />
                  <span>{categoria.label}</span>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
