import type { CellContext,ColumnDef, HeaderContext } from "@tanstack/react-table";
import { motion } from "framer-motion";
import { Edit, Trash2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTable } from "@/components/ui/data-table";
import { cn } from "@/lib/utils";

interface TabelaFornecedoresProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  selectedItems?: string[];
  onSelectItem?: (id: string, checked: boolean) => void;
  onSelectAll?: (checked: boolean) => void;
  className?: string;
}

export function TabelaFornecedores<T extends { id: string }>({
  data,
  columns,
  onEdit,
  onDelete,
  selectedItems = [],
  onSelectItem,
  onSelectAll,
  className
}: TabelaFornecedoresProps<T>) {
  
  // Adicionar colunas de seleção e ações se as funções forem fornecidas
  const enhancedColumns: ColumnDef<T>[] = [
    // Coluna de seleção
    ...(onSelectItem && onSelectAll ? [{
      id: "select",
      header: ({ table: _table }: HeaderContext<T, unknown>) => (
        <Checkbox
          checked={selectedItems.length === data.length && data.length > 0}
          onCheckedChange={(checked) => onSelectAll(!!checked)}
          aria-label="Selecionar todos"
          className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
        />
      ),
      cell: ({ row }: CellContext<T, unknown>) => (
        <Checkbox
          checked={selectedItems.includes(row.original.id)}
          onCheckedChange={(checked) => onSelectItem(row.original.id, !!checked)}
          aria-label="Selecionar linha"
          className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    }] : []),
    
    // Colunas originais
    ...columns,
    
    // Coluna de ações
    ...(onEdit || onDelete ? [{
      id: "actions",
      header: "Ações",
      cell: ({ row }: CellContext<T, unknown>) => (
        <div className="flex items-center gap-1">
          {onEdit && (
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(row.original.id)}
                className={cn(
                  "h-8 w-8 p-0",
                  "hover:bg-blue-50 dark:hover:bg-blue-900/20",
                  "hover:text-blue-600 dark:hover:text-blue-400",
                  "transition-all duration-200"
                )}
              >
                <Edit className="h-4 w-4" />
                <span className="sr-only">Editar</span>
              </Button>
            </motion.div>
          )}
          {onDelete && (
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(row.original.id)}
                className={cn(
                  "h-8 w-8 p-0",
                  "hover:bg-red-50 dark:hover:bg-red-900/20",
                  "hover:text-red-600 dark:hover:text-red-400",
                  "transition-all duration-200"
                )}
              >
                <Trash2 className="h-4 w-4" />
                <span className="sr-only">Excluir</span>
              </Button>
            </motion.div>
          )}
        </div>
      ),
      enableSorting: false,
    }] : [])
  ];

  return (
    <div className={cn("relative", className)}>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        className={cn(
          "rounded-lg border border-slate-200 dark:border-slate-700",
          "bg-white dark:bg-slate-900",
          "shadow-sm",
          "overflow-hidden"
        )}
      >
        <DataTable 
          columns={enhancedColumns} 
          data={data}
          className={cn(
            "[&_table]:border-collapse",
            "[&_thead]:bg-slate-50 [&_thead]:dark:bg-slate-800/50",
            "[&_th]:border-b [&_th]:border-slate-200 [&_th]:dark:border-slate-700",
            "[&_th]:px-4 [&_th]:py-3",
            "[&_th]:text-left [&_th]:font-semibold",
            "[&_th]:text-slate-700 [&_th]:dark:text-slate-300",
            "[&_td]:border-b [&_td]:border-slate-100 [&_td]:dark:border-slate-800",
            "[&_td]:px-4 [&_td]:py-3",
            "[&_tr]:transition-colors [&_tr]:duration-150",
            "[&_tbody_tr:hover]:bg-slate-50 [&_tbody_tr:hover]:dark:bg-slate-800/30",
            "[&_tbody_tr]:cursor-pointer"
          )}
        />
      </motion.div>
      
      {/* Indicador de dados vazios */}
      {data.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-12"
        >
          <div className="text-slate-500 dark:text-slate-400">
            <div className="text-lg font-medium mb-2">Nenhum fornecedor encontrado</div>
            <div className="text-sm">Tente ajustar os filtros ou adicionar novos fornecedores</div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
