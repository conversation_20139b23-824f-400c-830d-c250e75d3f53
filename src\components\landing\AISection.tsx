import { motion } from "framer-motion";
import {
  BarChart3,
  Brain,
  Calculator,
  CheckCircle,
  Database,
  MessageSquare,
  Search,
  Target,
  TrendingUp,
  Zap,
} from "lucide-react";

import aiBackground from "@/assets/images/aiimage.jpg";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";

const AISection = () => {
  const aiFeatures = [
    {
      icon: MessageSquare,
      title: "Chat Contextual Inteligente",
      description:
        "Converse com nossa IA que conhece todos os dados da sua obra. Faça perguntas sobre orçamento, despesas, fornecedores e receba insights precisos.",
    },
    {
      icon: Calculator,
      title: "Orçamento Paramétrico Automático",
      description:
        "IA analisa parâmetros da obra e gera orçamentos precisos baseados na base SINAPI e dados históricos do mercado.",
    },
    {
      icon: BarChart3,
      title: "Aná<PERSON>e Financeira Avançada",
      description:
        "Comparação automática entre orçado vs realizado, identificação de desvios e sugestões para otimização de custos.",
    },
    {
      icon: Search,
      title: "Busca Semântica SINAPI",
      description:
        'Encontre códigos e preços oficiais usando linguagem natural. Digite "concreto para laje" e encontre exatamente o que precisa.',
    },
    {
      icon: TrendingUp,
      title: "Insights Preditivos",
      description:
        "IA identifica padrões nos seus projetos e sugere melhorias baseadas em dados históricos e tendências do mercado.",
    },
    {
      icon: Target,
      title: "Recomendações Personalizadas",
      description:
        "Sugestões de fornecedores, materiais e estratégias específicas para cada tipo de obra e região.",
    },
  ];

  const stats = [
    {
      number: "SINAPI",
      label: "Base de Dados",
      description: "Preços oficiais integrados",
    },
    {
      number: "21",
      label: "Etapas de Obra",
      description: "Controle detalhado",
    },
    {
      number: "150+",
      label: "Insumos",
      description: "Categorizados no sistema",
    },
    {
      number: "24/7",
      label: "Disponibilidade",
      description: "Assistente IA sempre ativo",
    },
  ];

  return (
    <section
      id="ai-section"
      className="relative py-24 bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900"
    >
      {/* Imagem de fundo com overlay */}
      <img
        src={aiBackground}
        alt="Background IA ObrasVision"
        className="absolute inset-0 w-full h-full object-cover z-0"
        style={{ filter: "blur(0px) brightness(0.6)" }}
      />
      <div className="absolute inset-0 bg-black/70 z-10" />

      <div className="container mx-auto px-4 relative z-20">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="bg-badge-implemented-bg text-badge-implemented-text border border-slate-200 dark:border-slate-700 px-4 py-2 mb-6 font-medium badge-implemented">
            <Brain className="w-4 h-4 mr-2" />
            Inteligência Artificial
          </Badge>

          <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight heading-critical">
            <span className="text-white">IA que </span>
            <span className="bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent">
              Revoluciona
            </span>
            <span className="text-white">
              {" "}
              a Construção Civil
            </span>
          </h2>

          <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed body-info">
            Nossa inteligência artificial não é apenas um chatbot. É um
            especialista em construção civil que analisa dados reais das suas
            obras, gera insights precisos e toma decisões baseadas em normas
            técnicas e experiência do setor.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.02 }}
              className="text-center"
            >
              <Card className="p-6 bg-white/10 dark:bg-slate-800/10 border border-white/20 dark:border-slate-700/20 shadow-sm hover:shadow-md transition-all duration-300 backdrop-blur-md card-contrast">
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-white font-semibold mb-1 heading-primary">
                  {stat.label}
                </div>
                <div className="text-white/90 text-sm body-info">
                  {stat.description}
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* AI Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="grid lg:grid-cols-3 gap-8 mb-16"
        >
          {aiFeatures.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="group"
            >
              <Card className="h-full p-8 bg-white/10 dark:bg-slate-800/10 border border-white/20 dark:border-slate-700/20 shadow-sm hover:shadow-lg transition-all duration-300 backdrop-blur-md card-contrast">
                {/* Icon */}
                <div className="inline-flex p-3 rounded-xl bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/30 mb-6">
                  <feature.icon className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                </div>

                {/* Content */}
                <div>
                  <h3 className="text-xl font-bold text-white mb-4 heading-primary">
                    {feature.title}
                  </h3>
                  <p className="text-white/90 leading-relaxed body-info">
                    {feature.description}
                  </p>
                </div>

                {/* CheckCircle para indicar funcionalidade implementada */}
                <div className="mt-4 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <span className="text-sm text-status-success font-medium badge-implemented">
                    Implementado
                  </span>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Card className="inline-block p-8 bg-white/10 dark:bg-slate-800/10 border border-white/20 dark:border-slate-700/20 shadow-lg backdrop-blur-md card-contrast">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/30 rounded-xl flex items-center justify-center">
                <Zap className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <span className="text-white font-semibold text-lg heading-primary">
                Teste nossa IA agora mesmo!
              </span>
            </div>

            <p className="text-white/90 mb-6 max-w-md body-info">
              Nossa IA está integrada em todo o sistema para ajudar na gestão de
              obras, orçamentos, sistema SINAPI e todas as funcionalidades do
              ObrasVision.
            </p>

            <div className="flex items-center gap-2 text-white/70 text-sm justify-center caption-aux">
              <Database className="h-4 w-4" />
              <span>Powered by DeepSeek AI</span>
            </div>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export { AISection };
