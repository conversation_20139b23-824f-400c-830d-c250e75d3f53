import { motion } from 'framer-motion';
import { MessageSquare, PlayCircle, Zap } from 'lucide-react';

import howItWorksImage from '@/assets/images/Flux_Dev_A_highly_detailed_abstract_8K_wallpaper_A_mesmerizing_1.jpg';

const HowItWorksSection = () => {
  const steps = [
    {
      number: '1',
      icon: PlayCircle,
      title: 'Cadastre sua Obra',
      description: '5 minutos para criar o projeto e definir parâmetros básicos'
    },
    {
      number: '2', 
      icon: MessageSquare,
      title: 'IA Gera Orçamento',
      description: 'Nossa IA analisa e cria orçamento preciso baseado no SINAPI'
    },
    {
      number: '3',
      icon: Zap,
      title: '<PERSON>ere<PERSON><PERSON> e Economize',
      description: 'Controle custos em tempo real e reduza desperdícios'
    }
  ];

  return (
    <section className="relative py-24">
      {/* Background com imagem */}
      <img
        src={howItWorksImage}
        alt="Background Como Funciona"
        className="absolute inset-0 w-full h-full object-cover z-0"
        style={{ filter: "blur(0px) brightness(0.4)" }}
      />
      <div className="absolute inset-0 bg-black/60 z-10" />

      <div className="container mx-auto px-4 relative z-20">
        {/* Header Minimalista */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight text-white">
            Como Funciona
          </h2>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            3 passos simples para revolucionar sua gestão de obras
          </p>
        </motion.div>

        {/* Steps Minimalistas */}
        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              {/* Número e Ícone */}
              <div className="mb-6">
                <div className="w-20 h-20 mx-auto bg-white/10 border border-white/20 rounded-full flex items-center justify-center backdrop-blur-sm mb-4">
                  <step.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                  {step.number}
                </div>
              </div>

              {/* Conteúdo */}
              <h3 className="text-2xl font-bold text-white mb-3">
                {step.title}
              </h3>
              <p className="text-slate-300 leading-relaxed">
                {step.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export { HowItWorksSection };
