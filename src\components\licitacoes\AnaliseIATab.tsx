import { motion } from 'framer-motion'
import { 
  AlertTriangle, 
  Brain, 
  CheckCircle, 
  FileText, 
  Lightbulb,
  Star,
  Target, 
  TrendingUp,
  Zap
} from 'lucide-react'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import type { AnaliseIA } from '@/types'

interface AnaliseIATabProps {
  analise: AnaliseIA | null
  isLoading?: boolean
}

const AnaliseIATab = ({ analise, isLoading }: AnaliseIATabProps) => {
  if (isLoading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!analise) {
    return (
      <Alert>
        <Brain className="h-4 w-4" />
        <AlertDescription>
          Nenhuma análise de IA disponível. Use o botão "Analisar com IA" para gerar insights sobre esta licitação.
        </AlertDescription>
      </Alert>
    )
  }

  const getNivelComplexidadeBadge = (nivel: string) => {
    const configs = {
      'baixo': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      'medio': { color: 'bg-yellow-100 text-yellow-800', icon: TrendingUp },
      'alto': { color: 'bg-red-100 text-red-800', icon: AlertTriangle }
    }
    
    const config = configs[nivel as keyof typeof configs] || configs['medio']
    const Icon = config.icon

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {nivel.toUpperCase()}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header com resumo */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-4"
      >
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Brain className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Análise Processada</p>
                <p className="text-lg font-bold text-blue-900">IA Especializada</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Target className="w-8 h-8 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Complexidade</p>
                <div className="mt-1">{getNivelComplexidadeBadge(analise.nivel_complexidade)}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Star className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Documentos</p>
                <p className="text-lg font-bold text-green-900">{analise.documentos_necessarios?.length || 0} itens</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Resumo do Objeto */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="w-5 h-5 text-yellow-600" />
              Resumo Inteligente
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm leading-relaxed text-muted-foreground">
              {analise.resumo_objeto}
            </p>
          </CardContent>
        </Card>
      </motion.div>

      {/* Grid de informações detalhadas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Requisitos Principais */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5 text-blue-600" />
                Requisitos Principais
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {analise.requisitos_principais && Object.entries(analise.requisitos_principais).map(([categoria, requisitos]) => (
                <div key={categoria} className="space-y-2">
                  <h4 className="font-medium text-sm capitalize flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    {categoria.replace('_', ' ')}
                  </h4>
                  <ul className="space-y-1">
                    {(requisitos as string[]).map((requisito, index) => (
                      <li key={index} className="text-xs text-muted-foreground flex items-start gap-2">
                        <CheckCircle className="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" />
                        {requisito}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        {/* Documentos Necessários */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5 text-green-600" />
                Documentos Necessários
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {analise.documentos_necessarios?.map((documento, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                    <FileText className="w-4 h-4 text-gray-600" />
                    <span className="text-sm">{documento}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Informações Complementares */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Prazo de Execução */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-orange-600" />
                Prazo de Execução
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-orange-900">{analise.prazo_execucao}</p>
              <p className="text-sm text-muted-foreground mt-1">Conforme especificado no edital</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Valor Estimado pela IA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-purple-600" />
                Valor Estimado (IA)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold text-purple-900">{analise.valor_estimado_ia}</p>
              <p className="text-sm text-muted-foreground mt-1">Análise baseada no edital</p>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Observações da IA */}
      {analise.observacoes_ia && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5 text-indigo-600" />
                Observações e Insights da IA
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-200">
                <p className="text-sm leading-relaxed text-indigo-900">
                  {analise.observacoes_ia}
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}

export default AnaliseIATab