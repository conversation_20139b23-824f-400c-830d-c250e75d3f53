import { motion } from 'framer-motion'
import { 
  Alert<PERSON><PERSON>gle, 
  ArrowRight,
  Brain,
  Building2,
  CheckCircle, 
  Lightbulb,
  Target, 
  ThumbsDown,
  ThumbsUp,
  TrendingUp, 
  X} from 'lucide-react'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import type { AnaliseCompatibilidade } from '@/types'

interface CompatibilidadeTabProps {
  analiseCompatibilidade: AnaliseCompatibilidade | null
  isLoading?: boolean
}

const CompatibilidadeTab = ({ analiseCompatibilidade, isLoading }: CompatibilidadeTabProps) => {
  if (isLoading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!analiseCompatibilidade) {
    return (
      <Alert>
        <Brain className="h-4 w-4" />
        <AlertDescription>
          Análise de compatibilidade não disponível. Para gerar esta análise, você precisa:
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Configurar o perfil da sua construtora</li>
            <li>Executar a análise de IA do edital</li>
          </ul>
        </AlertDescription>
      </Alert>
    )
  }

  const getNivelBadge = (nivel: string) => {
    const configs = {
      'alto': { 
        color: 'bg-green-100 text-green-800 border-green-300', 
        icon: Target,
        gradient: 'from-green-500 to-emerald-500'
      },
      'medio': { 
        color: 'bg-yellow-100 text-yellow-800 border-yellow-300', 
        icon: TrendingUp,
        gradient: 'from-yellow-500 to-orange-500'
      },
      'baixo': { 
        color: 'bg-red-100 text-red-800 border-red-300', 
        icon: AlertTriangle,
        gradient: 'from-red-500 to-pink-500'
      }
    }
    
    const config = configs[nivel as keyof typeof configs] || configs['medio']
    const Icon = config.icon

    return { config, Icon }
  }

  const getRecomendacaoConfig = (recomendacao: string) => {
    const configs = {
      'participar': {
        color: 'text-green-600',
        bg: 'bg-green-50 border-green-200',
        icon: ThumbsUp,
        label: 'PARTICIPAR',
        description: 'Recomendamos participar desta licitação'
      },
      'avaliar': {
        color: 'text-yellow-600',
        bg: 'bg-yellow-50 border-yellow-200',
        icon: AlertTriangle,
        label: 'AVALIAR',
        description: 'Avalie cuidadosamente antes de decidir'
      },
      'nao_participar': {
        color: 'text-red-600',
        bg: 'bg-red-50 border-red-200',
        icon: ThumbsDown,
        label: 'NÃO PARTICIPAR',
        description: 'Não recomendamos participar desta licitação'
      }
    }

    return configs[recomendacao as keyof typeof configs] || configs['avaliar']
  }

  const { config: nivelConfig, Icon: NivelIcon } = getNivelBadge(analiseCompatibilidade.nivel)
  const recomendacaoConfig = getRecomendacaoConfig(analiseCompatibilidade.recomendacao)
  const RecomendacaoIcon = recomendacaoConfig.icon

  return (
    <div className="space-y-6">
      {/* Header com Score Principal */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`relative overflow-hidden rounded-2xl bg-gradient-to-r ${nivelConfig.gradient} text-white`}
      >
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative p-8">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-4 mb-4">
                <NivelIcon className="w-12 h-12" />
                <div>
                  <h2 className="text-3xl font-bold">Score: {analiseCompatibilidade.score}%</h2>
                  <p className="text-white/80">Compatibilidade {analiseCompatibilidade.nivel.toUpperCase()}</p>
                </div>
              </div>
              
              <Progress 
                value={analiseCompatibilidade.score} 
                className="w-64 h-3 bg-white/20"
              />
            </div>
            
            <div className={`p-6 rounded-xl ${recomendacaoConfig.bg.replace('bg-', 'bg-white/20 backdrop-blur-sm border-white/30')}`}>
              <div className="flex items-center gap-3">
                <RecomendacaoIcon className="w-8 h-8" />
                <div>
                  <p className="font-bold text-lg">{recomendacaoConfig.label}</p>
                  <p className="text-white/80 text-sm">{recomendacaoConfig.description}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
      </motion.div>

      {/* Grid de Análises Detalhadas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pontos Fortes */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="h-full border-green-200 bg-green-50/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-800">
                <CheckCircle className="w-5 h-5" />
                Pontos Fortes
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {analiseCompatibilidade.pontos_fortes?.map((ponto, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 + index * 0.05 }}
                  className="flex items-start gap-3 p-3 bg-green-100 rounded-lg"
                >
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-green-800">{ponto}</p>
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        {/* Pontos Fracos */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="h-full border-red-200 bg-red-50/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-800">
                <X className="w-5 h-5" />
                Pontos de Atenção
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {analiseCompatibilidade.pontos_fracos?.map((ponto, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 + index * 0.05 }}
                  className="flex items-start gap-3 p-3 bg-red-100 rounded-lg"
                >
                  <AlertTriangle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-800">{ponto}</p>
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Justificativas da Análise */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="w-5 h-5 text-indigo-600" />
              Justificativas da Análise
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analiseCompatibilidade.justificativas?.map((justificativa, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 + index * 0.1 }}
                  className="flex items-start gap-3 p-4 bg-indigo-50 rounded-lg border border-indigo-200"
                >
                  <div className="flex-shrink-0 w-6 h-6 bg-indigo-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                    {index + 1}
                  </div>
                  <p className="text-sm text-indigo-900 leading-relaxed">{justificativa}</p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Próximos Passos Recomendados */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-800">
              <Lightbulb className="w-5 h-5" />
              Próximos Passos Recomendados
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {analiseCompatibilidade.recomendacao === 'participar' && (
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-green-700">
                  <ArrowRight className="w-4 h-4" />
                  <span className="font-medium">Prosseguir com a preparação da proposta</span>
                </div>
                <div className="flex items-center gap-2 text-green-700">
                  <ArrowRight className="w-4 h-4" />
                  <span className="font-medium">Revisar todos os documentos necessários</span>
                </div>
                <div className="flex items-center gap-2 text-green-700">
                  <ArrowRight className="w-4 h-4" />
                  <span className="font-medium">Elaborar estratégia competitiva de preços</span>
                </div>
              </div>
            )}

            {analiseCompatibilidade.recomendacao === 'avaliar' && (
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-yellow-700">
                  <ArrowRight className="w-4 h-4" />
                  <span className="font-medium">Analisar detalhadamente os pontos fracos</span>
                </div>
                <div className="flex items-center gap-2 text-yellow-700">
                  <ArrowRight className="w-4 h-4" />
                  <span className="font-medium">Considerar parcerias para fortalecer a proposta</span>
                </div>
                <div className="flex items-center gap-2 text-yellow-700">
                  <ArrowRight className="w-4 h-4" />
                  <span className="font-medium">Avaliar custo-benefício da participação</span>
                </div>
              </div>
            )}

            {analiseCompatibilidade.recomendacao === 'nao_participar' && (
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-red-700">
                  <ArrowRight className="w-4 h-4" />
                  <span className="font-medium">Focar em oportunidades mais alinhadas</span>
                </div>
                <div className="flex items-center gap-2 text-red-700">
                  <ArrowRight className="w-4 h-4" />
                  <span className="font-medium">Melhorar o perfil da empresa para futuras licitações</span>
                </div>
                <div className="flex items-center gap-2 text-red-700">
                  <ArrowRight className="w-4 h-4" />
                  <span className="font-medium">Monitorar o resultado para aprendizado</span>
                </div>
              </div>
            )}

            <div className="mt-6 p-4 bg-purple-100 rounded-lg">
              <div className="flex items-start gap-3">
                <Building2 className="w-5 h-5 text-purple-600 mt-0.5" />
                <div>
                  <p className="font-medium text-purple-900 mb-1">Dica:</p>
                  <p className="text-sm text-purple-800">
                    Mantenha o perfil da sua construtora sempre atualizado para análises mais precisas. 
                    Cada licitação é uma oportunidade de aprendizado, independente do resultado.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}

export default CompatibilidadeTab