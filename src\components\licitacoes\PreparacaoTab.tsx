import { useMutation, useQueryClient } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import { 
  AlertTriangle, 
  Calendar,
  CheckCircle, 
  Clock, 
  FileText,
  Plus,
  Target,
  TrendingUp,
  Trophy,
  Users,
  Zap} from 'lucide-react'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Progress } from '@/components/ui/progress'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'

interface Tarefa {
  id: string
  descricao: string
  tipo: 'padrao' | 'ia' | 'personalizada'
  prioridade: 'baixa' | 'media' | 'alta' | 'critica'
  prazo_estimado?: number
  concluida: boolean
  data_conclusao?: string
  observacoes?: string
  ordem: number
}

interface PreparacaoTabProps {
  tarefas: Tarefa[] | undefined
  licitacaoId: string
  isLoading?: boolean
}

const PreparacaoTab = ({ tarefas, licitacaoId, isLoading }: PreparacaoTabProps) => {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  // Mutation para atualizar status da tarefa
  const { mutate: toggleTarefa } = useMutation({
    mutationFn: async ({ tarefaId, concluida }: { tarefaId: string, concluida: boolean }) => {
      const { error } = await supabase
        .from('licitacao_tarefas')
        .update({ concluida })
        .eq('id', tarefaId)

      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['licitacao-tarefas', licitacaoId] })
      toast({
        title: 'Tarefa atualizada',
        description: 'Status da tarefa foi alterado com sucesso'
      })
    }
  })

  const getPrioridadeBadge = (prioridade: string) => {
    const configs = {
      'baixa': { color: 'bg-gray-100 text-gray-800', icon: Clock },
      'media': { color: 'bg-blue-100 text-blue-800', icon: Target },
      'alta': { color: 'bg-orange-100 text-orange-800', icon: AlertTriangle },
      'critica': { color: 'bg-red-100 text-red-800', icon: Zap }
    }
    
    const config = configs[prioridade as keyof typeof configs] || configs['media']
    const Icon = config.icon

    return (
      <Badge className={`${config.color} flex items-center gap-1`} variant="outline">
        <Icon className="w-3 h-3" />
        {prioridade.toUpperCase()}
      </Badge>
    )
  }

  const getTipoBadge = (tipo: string) => {
    const configs = {
      'padrao': { color: 'bg-blue-100 text-blue-800', label: 'Padrão' },
      'ia': { color: 'bg-purple-100 text-purple-800', label: 'IA' },
      'personalizada': { color: 'bg-green-100 text-green-800', label: 'Custom' }
    }
    
    const config = configs[tipo as keyof typeof configs] || configs['padrao']

    return (
      <Badge className={config.color} variant="outline">
        {config.label}
      </Badge>
    )
  }

  const calcularEstatisticas = () => {
    if (!tarefas?.length) return { total: 0, concluidas: 0, progresso: 0, estimativa: 0 }
    
    const total = tarefas.length
    const concluidas = tarefas.filter(t => t.concluida).length
    const progresso = Math.round((concluidas / total) * 100)
    const estimativa = tarefas
      .filter(t => !t.concluida)
      .reduce((acc, t) => acc + (t.prazo_estimado || 0), 0)

    return { total, concluidas, progresso, estimativa }
  }

  const agruparTarefasPorStatus = () => {
    if (!tarefas?.length) return { pendentes: [], concluidas: [] }
    
    const pendentes = tarefas.filter(t => !t.concluida).sort((a, b) => a.ordem - b.ordem)
    const concluidas = tarefas.filter(t => t.concluida).sort((a, b) => a.ordem - b.ordem)
    
    return { pendentes, concluidas }
  }

  const stats = calcularEstatisticas()
  const { pendentes, concluidas } = agruparTarefasPorStatus()

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!tarefas?.length) {
    return (
      <Alert>
        <Plus className="h-4 w-4" />
        <AlertDescription>
          Nenhuma tarefa de preparação criada. Use o botão "Criar Checklist" para gerar automaticamente 
          uma lista de tarefas baseada no tipo de licitação.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      {/* Estatísticas de Progresso */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="grid grid-cols-1 md:grid-cols-4 gap-4"
      >
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <FileText className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-muted-foreground">Total de Tarefas</p>
                <p className="text-2xl font-bold text-blue-900">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-muted-foreground">Concluídas</p>
                <p className="text-2xl font-bold text-green-900">{stats.concluidas}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <TrendingUp className="w-8 h-8 text-purple-600" />
              <div>
                <p className="text-sm text-muted-foreground">Progresso</p>
                <p className="text-2xl font-bold text-purple-900">{stats.progresso}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-red-50 border-orange-200">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Clock className="w-8 h-8 text-orange-600" />
              <div>
                <p className="text-sm text-muted-foreground">Dias Restantes</p>
                <p className="text-2xl font-bold text-orange-900">{stats.estimativa}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Barra de Progresso */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold flex items-center gap-2">
                <Trophy className="w-5 h-5 text-yellow-600" />
                Progresso da Preparação
              </h3>
              <span className="text-sm text-muted-foreground">
                {stats.concluidas} de {stats.total} tarefas
              </span>
            </div>
            <Progress value={stats.progresso} className="h-3" />
            <p className="text-xs text-muted-foreground mt-2">
              {stats.progresso === 100 
                ? '🎉 Parabéns! Todas as tarefas foram concluídas!' 
                : `Faltam ${stats.total - stats.concluidas} tarefas para completar a preparação`
              }
            </p>
          </CardContent>
        </Card>
      </motion.div>

      {/* Lista de Tarefas Pendentes */}
      {pendentes.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-orange-600" />
                Tarefas Pendentes ({pendentes.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {pendentes.map((tarefa, index) => (
                <motion.div
                  key={tarefa.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="flex items-start gap-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Checkbox
                    checked={tarefa.concluida}
                    onCheckedChange={(checked) => 
                      toggleTarefa({ 
                        tarefaId: tarefa.id, 
                        concluida: checked as boolean 
                      })
                    }
                  />
                  
                  <div className="flex-1 space-y-2">
                    <div className="flex items-start justify-between">
                      <p className="font-medium text-sm">{tarefa.descricao}</p>
                      <div className="flex gap-2">
                        {getTipoBadge(tarefa.tipo)}
                        {getPrioridadeBadge(tarefa.prioridade)}
                      </div>
                    </div>
                    
                    {tarefa.prazo_estimado && (
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Calendar className="w-3 h-3" />
                        <span>Prazo estimado: {tarefa.prazo_estimado} dias</span>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Lista de Tarefas Concluídas */}
      {concluidas.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                Tarefas Concluídas ({concluidas.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {concluidas.map((tarefa, index) => (
                <motion.div
                  key={tarefa.id}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="flex items-start gap-3 p-4 border rounded-lg bg-green-50 border-green-200"
                >
                  <Checkbox
                    checked={tarefa.concluida}
                    onCheckedChange={(checked) => 
                      toggleTarefa({ 
                        tarefaId: tarefa.id, 
                        concluida: checked as boolean 
                      })
                    }
                  />
                  
                  <div className="flex-1 space-y-2">
                    <div className="flex items-start justify-between">
                      <p className="font-medium text-sm line-through text-muted-foreground">
                        {tarefa.descricao}
                      </p>
                      <div className="flex gap-2">
                        {getTipoBadge(tarefa.tipo)}
                        {getPrioridadeBadge(tarefa.prioridade)}
                      </div>
                    </div>
                    
                    {tarefa.data_conclusao && (
                      <div className="flex items-center gap-2 text-xs text-green-700">
                        <CheckCircle className="w-3 h-3" />
                        <span>
                          Concluída em {new Date(tarefa.data_conclusao).toLocaleDateString('pt-BR')}
                        </span>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Dicas de Preparação */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-900">
              <Users className="w-5 h-5" />
              Dicas para uma Preparação Eficiente
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-blue-800">
              <div className="flex items-start gap-2">
                <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                <span>Priorize tarefas críticas e com prazo menor</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                <span>Mantenha documentos sempre atualizados</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                <span>Revise o edital periodicamente</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                <span>Confirme prazos de entrega dos documentos</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}

export default PreparacaoTab