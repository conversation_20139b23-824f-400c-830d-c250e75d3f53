import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Building2, Trash2 } from "lucide-react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";

interface CondominioConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  type: "create" | "delete" | "deleteUnit";
  condominioName?: string;
  unidadeCount?: number;
  unitName?: string;
  isLoading?: boolean;
}

export const CondominioConfirmationDialog = ({
  isOpen,
  onClose,
  onConfirm,
  type,
  condominioName,
  unidadeCount,
  unitName,
  isLoading = false,
}: CondominioConfirmationDialogProps) => {
  const getDialogContent = () => {
    switch (type) {
      case "create":
        return {
          icon: <Building2 className="h-6 w-6 text-blue-500" />,
          title: "Confirmar Criação do Condomínio",
          description: (
            <div className="space-y-3">
              <p>
                Você está prestes a criar o condomínio{" "}
                <strong>"{condominioName}"</strong> com{" "}
                <Badge variant="secondary" className="mx-1">
                  {unidadeCount} unidades
                </Badge>
              </p>
              <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  <strong>Esta operação irá:</strong>
                </p>
                <ul className="text-sm text-blue-600 dark:text-blue-400 mt-2 space-y-1">
                  <li>• Criar 1 obra principal (condomínio master)</li>
                  <li>• Criar {unidadeCount} obras individuais (unidades)</li>
                  <li>• Configurar relacionamentos entre as obras</li>
                </ul>
              </div>
              <p className="text-sm text-muted-foreground">
                Todas as unidades serão criadas automaticamente. Você poderá
                editá-las individualmente após a criação.
              </p>
            </div>
          ),
          confirmText: "Criar Condomínio",
          confirmVariant: "default" as const,
        };

      case "delete":
        return {
          icon: <AlertTriangle className="h-6 w-6 text-red-500" />,
          title: "Excluir Condomínio",
          description: (
            <div className="space-y-3">
              <p>
                Você está prestes a excluir o condomínio{" "}
                <strong>"{condominioName}"</strong> e todas as suas{" "}
                <Badge variant="destructive" className="mx-1">
                  {unidadeCount} unidades
                </Badge>
              </p>
              <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded-lg border border-red-200 dark:border-red-800">
                <p className="text-sm text-red-700 dark:text-red-300">
                  <strong>⚠️ Esta ação é irreversível!</strong>
                </p>
                <ul className="text-sm text-red-600 dark:text-red-400 mt-2 space-y-1">
                  <li>• Todos os dados do condomínio serão perdidos</li>
                  <li>• Todas as {unidadeCount} unidades serão excluídas</li>
                  <li>• Histórico de custos e progresso será removido</li>
                </ul>
              </div>
              <p className="text-sm text-muted-foreground">
                Certifique-se de que realmente deseja excluir este condomínio.
              </p>
            </div>
          ),
          confirmText: "Sim, Excluir",
          confirmVariant: "destructive" as const,
        };

      case "deleteUnit":
        return {
          icon: <Trash2 className="h-6 w-6 text-orange-500" />,
          title: "Excluir Unidade",
          description: (
            <div className="space-y-3">
              <p>
                Você está prestes a excluir a unidade{" "}
                <strong>"{unitName}"</strong> do condomínio{" "}
                <strong>"{condominioName}"</strong>
              </p>
              <div className="bg-orange-50 dark:bg-orange-950/20 p-3 rounded-lg border border-orange-200 dark:border-orange-800">
                <p className="text-sm text-orange-700 dark:text-orange-300">
                  <strong>Esta ação irá:</strong>
                </p>
                <ul className="text-sm text-orange-600 dark:text-orange-400 mt-2 space-y-1">
                  <li>• Remover a unidade do condomínio</li>
                  <li>• Excluir todos os dados associados à unidade</li>
                  <li>• Atualizar estatísticas do condomínio</li>
                </ul>
              </div>
              <p className="text-sm text-muted-foreground">
                Esta ação não pode ser desfeita.
              </p>
            </div>
          ),
          confirmText: "Excluir Unidade",
          confirmVariant: "destructive" as const,
        };

      default:
        return {
          icon: <AlertTriangle className="h-6 w-6" />,
          title: "Confirmar Ação",
          description: "Tem certeza de que deseja continuar?",
          confirmText: "Confirmar",
          confirmVariant: "default" as const,
        };
    }
  };

  const content = getDialogContent();

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-3">
            {content.icon}
            {content.title}
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div>{content.description}</div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancelar</AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            variant={content.confirmVariant}
            disabled={isLoading}
          >
            {isLoading ? "Processando..." : content.confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
