import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>2,
  CheckCircle2,
  Clock,
  DollarSign,
  Home,
  TrendingUp,
  Zap,
} from "lucide-react";
import React, { useMemo, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { GradientCard } from "@/components/ui/GradientCard";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { useObrasCondominio } from "@/hooks/useObrasCondominio";
import { secureLogger } from "@/lib/secure-logger";
import { calcularProgressoCondominio } from "@/utils/progressoCalculator";

interface CondominioDashboardProps {
  obraId: string;
}

export const CondominioDashboard: React.FC<CondominioDashboardProps> = ({
  obraId,
}) => {
  const { useCondominioDashboard } = useObrasCondominio();
  const [showDetailed, setShowDetailed] = useState(false);

  // Usar o hook otimizado que utiliza a função RPC otimizada
  const { data, isLoading, error } = useCondominioDashboard(obraId);

  // Calcular progresso real usando a função utilitária
  const progressoReal = useMemo(() => {
    if (!data?.estatisticas) return 0;
    return calcularProgressoCondominio(data.estatisticas);
  }, [data?.estatisticas]);

  // Calcular taxa de conclusão inteligente
  const taxaConclusao = useMemo(() => {
    if (!data?.estatisticas) return 0;
    const { total_unidades, unidades_concluidas, progresso_medio } =
      data.estatisticas;

    // Se o progresso médio é >= 95%, considerar como 100% concluído
    if (progresso_medio >= 95) {
      return 100;
    }

    // Caso contrário, usar o cálculo baseado em unidades marcadas como concluídas
    return total_unidades > 0
      ? (unidades_concluidas / total_unidades) * 100
      : 0;
  }, [data?.estatisticas]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
        <Skeleton className="h-48" />
      </div>
    );
  }

  if (error) {
    secureLogger.error("Erro ao carregar dashboard do condomínio:", error);

    const errorMessage =
      error instanceof Error
        ? error.message
        : "Erro desconhecido ao carregar dashboard";

    return (
      <div className="space-y-4">
        <GradientCard
          variant="yellow"
          title="Erro no Dashboard"
          description={`Não foi possível carregar os dados: ${errorMessage}`}
          icon={<AlertCircle className="h-4 w-4 text-yellow-500" />}
        />
        <div className="text-sm text-muted-foreground">
          <p>Possíveis causas:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Obra não é um condomínio master</li>
            <li>Problemas de permissão de acesso</li>
            <li>Função do banco de dados não encontrada</li>
          </ul>
        </div>
      </div>
    );
  }

  if (!data) {
    return <div>Nenhum dado disponível</div>;
  }

  const { estatisticas } = data;

  // Calcular métricas derivadas com proteção contra NaN
  const percentualVendidas =
    estatisticas.total_unidades > 0
      ? ((estatisticas.unidades_vendidas || 0) / estatisticas.total_unidades) *
        100
      : 0;

  const percentualDisponiveis =
    estatisticas.total_unidades > 0
      ? ((estatisticas.unidades_disponiveis || 0) /
          estatisticas.total_unidades) *
        100
      : 0;

  return (
    <div className="space-y-8">
      {/* Performance Badge para grandes condomínios */}
      {estatisticas.total_unidades > 100 && (
        <div className="flex items-center gap-2">
          <Badge
            variant="secondary"
            className="bg-emerald-100 text-emerald-800"
          >
            <Zap className="h-3 w-3 mr-1" />
            Dashboard Otimizado
          </Badge>
          <span className="text-sm text-muted-foreground">
            Condomínio grande ({estatisticas.total_unidades} unidades) -
            Estatísticas agregadas carregadas rapidamente
          </span>
        </div>
      )}

      {/* Métricas principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <GradientCard
          variant="blue"
          title="Total de Unidades"
          description="Unidades no condomínio"
          icon={<Home className="h-4 w-4 text-blue-500" />}
        >
          <div className="text-3xl font-bold mt-2">
            {estatisticas.total_unidades}
          </div>
          {estatisticas.total_unidades > 500 && (
            <Badge variant="outline" className="mt-2">
              Grande Escala
            </Badge>
          )}
        </GradientCard>

        <GradientCard
          variant="emerald"
          title="Progresso Médio"
          description="Progresso geral das unidades"
          icon={<TrendingUp className="h-4 w-4 text-emerald-500" />}
        >
          <div className="text-3xl font-bold mt-2">
            {progressoReal.toFixed(1)}%
          </div>
          <Progress value={progressoReal} className="mt-2" />
          {progressoReal > 0 && (
            <div className="text-xs text-emerald-600 mt-1">
              Progresso calculado com precisão
            </div>
          )}
        </GradientCard>

        <GradientCard
          variant="purple"
          title="Capital para Construção"
          description="Investimento disponível após terreno"
          icon={<DollarSign className="h-4 w-4 text-purple-500" />}
        >
          <div className="text-2xl font-bold mt-2">
            {new Intl.NumberFormat("pt-BR", {
              style: "currency",
              currency: "BRL",
              notation:
                (estatisticas.capital_construcao || estatisticas.custo_total) >
                1000000
                  ? "compact"
                  : "standard",
              maximumFractionDigits: 0,
            }).format(
              estatisticas.capital_construcao || estatisticas.custo_total || 0
            )}
          </div>
          <div className="text-xs text-muted-foreground mt-1">
            🏗️ Capital disponível para construção
          </div>
          {estatisticas.custo_terreno && estatisticas.custo_terreno > 0 && (
            <div className="text-xs text-red-600 dark:text-red-400 mt-1">
              Terreno: -
              {new Intl.NumberFormat("pt-BR", {
                style: "currency",
                currency: "BRL",
                maximumFractionDigits: 0,
              }).format(estatisticas.custo_terreno)}
            </div>
          )}
        </GradientCard>

        <GradientCard
          variant="green"
          title="Custo Estimado da Obra"
          description="Orçamento paramétrico calculado"
          icon={<BarChart2 className="h-4 w-4 text-green-500" />}
        >
          <div className="text-2xl font-bold mt-2">
            {estatisticas.custo_parametrico ? (
              new Intl.NumberFormat("pt-BR", {
                style: "currency",
                currency: "BRL",
                notation:
                  estatisticas.custo_parametrico > 1000000
                    ? "compact"
                    : "standard",
                maximumFractionDigits: 0,
              }).format(estatisticas.custo_parametrico)
            ) : (
              <span className="text-muted-foreground text-lg">
                Não calculado
              </span>
            )}
          </div>
          <div className="text-xs text-muted-foreground mt-1">
            🏗️ Custo real estimado
          </div>
          {estatisticas.custo_parametrico &&
            (estatisticas.capital_construcao || estatisticas.custo_total) && (
              <div className="mt-2">
                <div
                  className={`text-sm font-medium ${
                    (estatisticas.capital_construcao ||
                      estatisticas.custo_total) >=
                    estatisticas.custo_parametrico
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  Margem:{" "}
                  {new Intl.NumberFormat("pt-BR", {
                    style: "currency",
                    currency: "BRL",
                    maximumFractionDigits: 0,
                  }).format(
                    (estatisticas.capital_construcao ||
                      estatisticas.custo_total) - estatisticas.custo_parametrico
                  )}
                </div>
              </div>
            )}
        </GradientCard>
      </div>

      {/* Métricas de vendas (se disponíveis) */}
      {estatisticas.unidades_vendidas !== undefined && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <GradientCard
            variant="green"
            title="Unidades Vendidas"
            description="Status de comercialização"
            icon={<CheckCircle2 className="h-4 w-4 text-green-500" />}
          >
            <div className="text-2xl font-bold mt-2">
              {estatisticas.unidades_vendidas || 0}
            </div>
            <div className="text-sm text-muted-foreground mt-1">
              {isNaN(percentualVendidas)
                ? "0.0"
                : percentualVendidas.toFixed(1)}
              % do total
            </div>
            <Progress
              value={isNaN(percentualVendidas) ? 0 : percentualVendidas}
              className="mt-2"
            />
          </GradientCard>

          <GradientCard
            variant="orange"
            title="Unidades Reservadas"
            description="Em processo de venda"
            icon={<Clock className="h-4 w-4 text-orange-500" />}
          >
            <div className="text-2xl font-bold mt-2">
              {estatisticas.unidades_reservadas || 0}
            </div>
          </GradientCard>

          <GradientCard
            variant="gray"
            title="Unidades Disponíveis"
            description="Prontas para venda"
            icon={<Home className="h-4 w-4 text-gray-500" />}
          >
            <div className="text-2xl font-bold mt-2">
              {estatisticas.unidades_disponiveis || 0}
            </div>
            <div className="text-sm text-muted-foreground mt-1">
              {isNaN(percentualDisponiveis)
                ? "0.0"
                : percentualDisponiveis.toFixed(1)}
              % disponíveis
            </div>
          </GradientCard>
        </div>
      )}

      {/* Métricas de prazo (se disponíveis) */}
      {(estatisticas.unidades_no_prazo !== undefined ||
        estatisticas.unidades_atrasadas !== undefined) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <GradientCard
            variant="green"
            title="No Prazo"
            description="Unidades dentro do cronograma"
            icon={<CheckCircle2 className="h-4 w-4 text-green-500" />}
          >
            <div className="text-2xl font-bold mt-2">
              {estatisticas.unidades_no_prazo || 0}
            </div>
          </GradientCard>

          <GradientCard
            variant="red"
            title="Atrasadas"
            description="Unidades com atraso"
            icon={<AlertCircle className="h-4 w-4 text-red-500" />}
          >
            <div className="text-2xl font-bold mt-2">
              {estatisticas.unidades_atrasadas || 0}
            </div>
            {(estatisticas.unidades_atrasadas || 0) > 0 && (
              <Badge variant="destructive" className="mt-2">
                Atenção Necessária
              </Badge>
            )}
          </GradientCard>
        </div>
      )}

      {/* Resumo executivo para grandes condomínios */}
      <GradientCard
        variant="default"
        title="Resumo Executivo"
        description="Visão geral do andamento do projeto"
      >
        <div className="mt-4 space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {estatisticas.total_unidades}
              </div>
              <div className="text-sm text-muted-foreground">
                Unidades Totais
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-emerald-600">
                {progressoReal.toFixed(0)}%
              </div>
              <div className="text-sm text-muted-foreground">
                Progresso Médio
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {new Intl.NumberFormat("pt-BR", {
                  notation: "compact",
                  maximumFractionDigits: 2,
                }).format(estatisticas.custo_total || 0)}
              </div>
              <div className="text-sm text-muted-foreground">
                Orçamento (R$)
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-600">
                {taxaConclusao.toFixed(0)}%
              </div>
              <div className="text-sm text-muted-foreground">
                Taxa Conclusão
              </div>
            </div>
          </div>

          {!showDetailed && estatisticas.total_unidades > 50 && (
            <div className="pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setShowDetailed(true)}
                className="w-full"
              >
                <BarChart2 className="h-4 w-4 mr-2" />
                Ver Detalhes por Unidade
              </Button>
              <p className="text-xs text-muted-foreground mt-2 text-center">
                Carregar progresso individual das {estatisticas.total_unidades}{" "}
                unidades
              </p>
            </div>
          )}
        </div>
      </GradientCard>

      {/* Detalhes por unidade (carregamento sob demanda) */}
      {showDetailed && (
        <GradientCard
          variant="default"
          title="Progresso Detalhado"
          description="Análise individual por unidade (carregamento otimizado)"
        >
          <div className="mt-4">
            <div className="text-center py-8">
              <Button variant="outline" onClick={() => setShowDetailed(false)}>
                Ocultar Detalhes
              </Button>
              <p className="text-sm text-muted-foreground mt-2">
                Use a aba "Unidades" para navegação completa com paginação
              </p>
            </div>
          </div>
        </GradientCard>
      )}
    </div>
  );
};
