import { AlertCircle, Info, XCircle } from "lucide-react";
import { toast } from "sonner";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface ErrorDetails {
  code?: string;
  field?: string;
  message: string;
  suggestion?: string;
}

interface CondominioError {
  type: "validation" | "network" | "business" | "system";
  title: string;
  details: ErrorDetails[];
}

// Mapeamento de erros comuns para mensagens amigáveis
const ERROR_MESSAGES: Record<string, CondominioError> = {
  CONDOMINIO_NAME_REQUIRED: {
    type: "validation",
    title: "Nome do condomínio obrigatório",
    details: [
      {
        field: "nome",
        message: "O nome do condomínio é obrigatório",
        suggestion: "Digite um nome descritivo para o condomínio (ex: Residencial Jardim das Flores)",
      },
    ],
  },
  UNITS_REQUIRED: {
    type: "validation",
    title: "Unidades não configuradas",
    details: [
      {
        field: "unidades",
        message: "É necessário configurar pelo menos uma unidade",
        suggestion: "Clique em 'Adicionar Unidade' ou 'Gerar Automaticamente' para criar as unidades",
      },
    ],
  },
  DUPLICATE_UNIT_IDENTIFIER: {
    type: "validation",
    title: "Identificadores duplicados",
    details: [
      {
        field: "identificador_unidade",
        message: "Existem unidades com identificadores duplicados",
        suggestion: "Certifique-se de que cada unidade tenha um identificador único (ex: 001, 002, A1, B2)",
      },
    ],
  },
  NETWORK_ERROR: {
    type: "network",
    title: "Erro de conexão",
    details: [
      {
        message: "Não foi possível conectar ao servidor",
        suggestion: "Verifique sua conexão com a internet e tente novamente",
      },
    ],
  },
  TENANT_LIMIT_EXCEEDED: {
    type: "business",
    title: "Limite de condomínios atingido",
    details: [
      {
        message: "Você atingiu o limite de condomínios do seu plano",
        suggestion: "Considere fazer upgrade do seu plano ou excluir condomínios não utilizados",
      },
    ],
  },
  INSUFFICIENT_PERMISSIONS: {
    type: "business",
    title: "Permissões insuficientes",
    details: [
      {
        message: "Você não tem permissão para criar condomínios",
        suggestion: "Entre em contato com o administrador do sistema para obter as permissões necessárias",
      },
    ],
  },
};

export const useCondominioErrorHandler = () => {
  const showError = (errorCode: string, customMessage?: string) => {
    const error = ERROR_MESSAGES[errorCode];
    
    if (!error) {
      // Erro genérico
      toast.error("Erro inesperado", {
        description: customMessage || "Ocorreu um erro inesperado. Tente novamente.",
      });
      return;
    }

    const description = error.details
      .map((detail) => `${detail.message}${detail.suggestion ? ` - ${detail.suggestion}` : ""}`)
      .join("\n");

    toast.error(error.title, {
      description,
      duration: 6000, // Mais tempo para ler as sugestões
    });
  };

  const showSuccess = (message: string, description?: string) => {
    toast.success(message, {
      description,
      duration: 4000,
    });
  };

  const showWarning = (message: string, description?: string) => {
    toast.warning(message, {
      description,
      duration: 5000,
    });
  };

  const showInfo = (message: string, description?: string) => {
    toast.info(message, {
      description,
      duration: 4000,
    });
  };

  return {
    showError,
    showSuccess,
    showWarning,
    showInfo,
  };
};

// Componente para exibir erros inline no formulário
interface CondominioErrorAlertProps {
  error: CondominioError;
  onDismiss?: () => void;
}

export const CondominioErrorAlert = ({ error, onDismiss }: CondominioErrorAlertProps) => {
  const getIcon = () => {
    switch (error.type) {
      case "validation":
        return <AlertCircle className="h-4 w-4" />;
      case "network":
        return <XCircle className="h-4 w-4" />;
      case "business":
        return <Info className="h-4 w-4" />;
      case "system":
        return <XCircle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getVariant = () => {
    switch (error.type) {
      case "validation":
        return "destructive" as const;
      case "network":
        return "destructive" as const;
      case "business":
        return "default" as const;
      case "system":
        return "destructive" as const;
      default:
        return "destructive" as const;
    }
  };

  return (
    <Alert variant={getVariant()} className="mb-4">
      {getIcon()}
      <AlertTitle>{error.title}</AlertTitle>
      <AlertDescription>
        <div className="space-y-2">
          {error.details.map((detail, index) => (
            <div key={index}>
              <p className="font-medium">{detail.message}</p>
              {detail.suggestion && (
                <p className="text-sm text-muted-foreground mt-1">
                  💡 {detail.suggestion}
                </p>
              )}
            </div>
          ))}
        </div>
      </AlertDescription>
    </Alert>
  );
};

// Função para mapear erros do backend para códigos conhecidos
export const mapBackendError = (error: any): string => {
  if (error?.message?.includes("nome")) {
    return "CONDOMINIO_NAME_REQUIRED";
  }
  
  if (error?.message?.includes("unidades") || error?.message?.includes("units")) {
    return "UNITS_REQUIRED";
  }
  
  if (error?.message?.includes("duplicate") || error?.message?.includes("duplicado")) {
    return "DUPLICATE_UNIT_IDENTIFIER";
  }
  
  if (error?.message?.includes("network") || error?.code === "NETWORK_ERROR") {
    return "NETWORK_ERROR";
  }
  
  if (error?.message?.includes("limit") || error?.code === "LIMIT_EXCEEDED") {
    return "TENANT_LIMIT_EXCEEDED";
  }
  
  if (error?.message?.includes("permission") || error?.code === "FORBIDDEN") {
    return "INSUFFICIENT_PERMISSIONS";
  }
  
  return "UNKNOWN_ERROR";
};
