import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import React, { useState } from "react";
import { useForm } from "react-hook-form";

import { CondominioConfirmationDialog } from "@/components/obras/CondominioConfirmationDialog";
import { mapBackendError,useCondominioErrorHandler } from "@/components/obras/CondominioErrorHandler";
import { CondomínioFormSection } from "@/components/obras/CondomínioFormSection";
import { CondominioProgressIndicator, useCondominioProgress } from "@/components/obras/CondominioProgressIndicator";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { useObrasCondominio } from "@/hooks/useObrasCondominio";
import { condominioSchema } from "@/lib/validators/condominioValidator";
import type { CreateCondominioData } from "@/types/condominio";

/**
 * Exemplo de formulário de condomínio com todas as melhorias de UX implementadas:
 * - Indicadores de progresso durante criação
 * - Confirmação antes de operações críticas
 * - Mensagens de erro melhoradas
 * - Responsividade otimizada
 * - Tooltips explicativos
 */
const CondominioFormExample: React.FC = () => {
  const { createCondominioRPC } = useObrasCondominio();
  const { showError, showSuccess } = useCondominioErrorHandler();
  const { createProgressSteps, updateStepStatus, getCompletedStepsCount } = useCondominioProgress();
  
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [progressSteps, setProgressSteps] = useState<any[]>([]);
  const [showProgress, setShowProgress] = useState(false);

  const form = useForm<CreateCondominioData>({
    resolver: zodResolver(condominioSchema),
    defaultValues: {
      master: {
        nome: "",
        endereco: "",
        cidade: "",
        estado: "",
        cep: "",
      },
      unidades: [],
    },
  });

  const mutation = useMutation({
    mutationFn: createCondominioRPC,
    onMutate: async (data) => {
      // Iniciar indicador de progresso
      const steps = createProgressSteps(data.unidades.length);
      setProgressSteps(steps);
      setShowProgress(true);
      
      // Simular progresso das etapas
      setTimeout(() => {
        setProgressSteps(prev => updateStepStatus(prev, "validation", "in-progress"));
      }, 500);
      
      setTimeout(() => {
        setProgressSteps(prev => updateStepStatus(prev, "validation", "completed"));
        setProgressSteps(prev => updateStepStatus(prev, "master", "in-progress"));
      }, 1500);
      
      setTimeout(() => {
        setProgressSteps(prev => updateStepStatus(prev, "master", "completed"));
        setProgressSteps(prev => updateStepStatus(prev, "units", "in-progress"));
      }, 2500);
    },
    onSuccess: (data) => {
      // Finalizar progresso
      setProgressSteps(prev => updateStepStatus(prev, "units", "completed"));
      setProgressSteps(prev => updateStepStatus(prev, "finalization", "in-progress"));
      
      setTimeout(() => {
        setProgressSteps(prev => updateStepStatus(prev, "finalization", "completed"));
        setShowProgress(false);
        
        showSuccess(
          "Condomínio criado com sucesso!",
          `O condomínio "${data.obra_mae.nome}" foi criado com ${data.unidades.length} unidades.`
        );
        
        // Resetar formulário
        form.reset();
      }, 1000);
    },
    onError: (error) => {
      // Marcar etapa atual como erro
      const currentStep = progressSteps.find(step => step.status === "in-progress");
      if (currentStep) {
        setProgressSteps(prev => updateStepStatus(prev, currentStep.id, "error"));
      }
      
      setTimeout(() => {
        setShowProgress(false);
        const errorCode = mapBackendError(error);
        showError(errorCode, error.message);
      }, 1000);
    },
  });

  const onSubmit = (data: CreateCondominioData) => {
    // Validações básicas
    if (!data.master.nome.trim()) {
      showError("CONDOMINIO_NAME_REQUIRED");
      return;
    }
    
    if (data.unidades.length === 0) {
      showError("UNITS_REQUIRED");
      return;
    }
    
    // Verificar identificadores duplicados
    const identifiers = data.unidades.map(u => u.identificador_unidade);
    const duplicates = identifiers.filter((item, index) => identifiers.indexOf(item) !== index);
    if (duplicates.length > 0) {
      showError("DUPLICATE_UNIT_IDENTIFIER");
      return;
    }
    
    // Mostrar confirmação
    setShowConfirmation(true);
  };

  const handleConfirmCreate = () => {
    setShowConfirmation(false);
    const formData = form.getValues();
    
    // Converter para formato esperado pela API
    const apiData = {
      condominio_data: formData.master,
      unidades_data: formData.unidades,
    };
    
    mutation.mutate(apiData);
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Criar Novo Condomínio</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Dados básicos do condomínio */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Dados do Condomínio</h3>
                {/* Aqui você adicionaria os campos básicos do condomínio */}
              </div>

              {/* Seção de unidades com melhorias de UX */}
              <CondomínioFormSection
                isCondominio={true}
                onUnidadesChange={(unidades) => {
                  form.setValue("unidades", unidades);
                }}
              />

              {/* Botões de ação */}
              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  className="w-full sm:w-auto"
                  onClick={() => form.reset()}
                >
                  Limpar Formulário
                </Button>
                <Button
                  type="submit"
                  className="w-full sm:w-auto"
                  disabled={mutation.isPending}
                >
                  {mutation.isPending ? "Criando..." : "Criar Condomínio"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Modal de confirmação */}
      <CondominioConfirmationDialog
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        onConfirm={handleConfirmCreate}
        type="create"
        condominioName={form.watch("master.nome")}
        unidadeCount={form.watch("unidades")?.length || 0}
        isLoading={mutation.isPending}
      />

      {/* Indicador de progresso */}
      <CondominioProgressIndicator
        steps={progressSteps}
        currentStep={getCompletedStepsCount(progressSteps)}
        totalSteps={progressSteps.length}
        isVisible={showProgress}
      />
    </div>
  );
};

export default CondominioFormExample;
