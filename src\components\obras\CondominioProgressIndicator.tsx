import { motion } from "framer-motion";
import { <PERSON><PERSON>ircle, Circle, Loader2 } from "lucide-react";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface ProgressStep {
  id: string;
  title: string;
  description: string;
  status: "pending" | "in-progress" | "completed" | "error";
}

interface CondominioProgressIndicatorProps {
  steps: ProgressStep[];
  currentStep: number;
  totalSteps: number;
  isVisible: boolean;
  onClose?: () => void;
}

export const CondominioProgressIndicator = ({
  steps,
  currentStep,
  totalSteps,
  isVisible,
  onClose,
}: CondominioProgressIndicatorProps) => {
  if (!isVisible) return null;

  const progressPercentage = (currentStep / totalSteps) * 100;

  const getStepIcon = (status: ProgressStep["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "in-progress":
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      case "error":
        return <Circle className="h-5 w-5 text-red-500" />;
      default:
        return <Circle className="h-5 w-5 text-muted-foreground" />;
    }
  };

  const getStepTextColor = (status: ProgressStep["status"]) => {
    switch (status) {
      case "completed":
        return "text-green-700 dark:text-green-400";
      case "in-progress":
        return "text-blue-700 dark:text-blue-400";
      case "error":
        return "text-red-700 dark:text-red-400";
      default:
        return "text-muted-foreground";
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.2 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
    >
      <Card className="w-full max-w-md mx-4">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold text-center">
            Criando Condomínio
          </CardTitle>
          <div className="space-y-2">
            <Progress value={progressPercentage} className="h-2" />
            <p className="text-sm text-muted-foreground text-center">
              {currentStep} de {totalSteps} etapas concluídas
            </p>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {steps.map((step, index) => (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start gap-3"
            >
              <div className="flex-shrink-0 mt-0.5">
                {getStepIcon(step.status)}
              </div>
              <div className="flex-1 min-w-0">
                <p className={`text-sm font-medium ${getStepTextColor(step.status)}`}>
                  {step.title}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  {step.description}
                </p>
              </div>
            </motion.div>
          ))}
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Hook para gerenciar o estado do progresso
export const useCondominioProgress = () => {
  const createProgressSteps = (totalUnidades: number): ProgressStep[] => [
    {
      id: "validation",
      title: "Validando dados",
      description: "Verificando informações do condomínio",
      status: "pending",
    },
    {
      id: "master",
      title: "Criando obra principal",
      description: "Cadastrando o condomínio master",
      status: "pending",
    },
    {
      id: "units",
      title: "Criando unidades",
      description: `Cadastrando ${totalUnidades} unidades`,
      status: "pending",
    },
    {
      id: "finalization",
      title: "Finalizando",
      description: "Configurando relacionamentos e permissões",
      status: "pending",
    },
  ];

  const updateStepStatus = (
    steps: ProgressStep[],
    stepId: string,
    status: ProgressStep["status"]
  ): ProgressStep[] => {
    return steps.map((step) =>
      step.id === stepId ? { ...step, status } : step
    );
  };

  const getCompletedStepsCount = (steps: ProgressStep[]): number => {
    return steps.filter((step) => step.status === "completed").length;
  };

  return {
    createProgressSteps,
    updateStepStatus,
    getCompletedStepsCount,
  };
};
