/**
 * Exemplo de integração do CondomínioFormSection no formulário de nova obra
 * 
 * Este arquivo demonstra como integrar o componente CondomínioFormSection
 * no formulário existente NovaObraRefactored.tsx
 */

import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { Button } from "@/components/ui/button";
import {
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { FormWrapper } from "@/components/ui/FormWrapper";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
    type ObraComCondomínioFormValues,
    obraComCondomínioSchema,
    type UnidadeFormData
} from "@/lib/validations/obra";

import { CondomínioFormSection } from "./CondomínioFormSection";

export const ExemploIntegracaoCondominio = () => {
  const [tipoProjetoSelecionado, setTipoProjetoSelecionado] = useState<"UNICO" | "CONDOMINIO_MASTER">("UNICO");

  const form = useForm<ObraComCondomínioFormValues>({
    resolver: zodResolver(obraComCondomínioSchema),
    defaultValues: {
      nome: "",
      endereco: "",
      cidade: "",
      estado: "",
      cep: "",
      orcamento: 0,
      area_total: 0,
      construtora_id: "",
      data_inicio: null,
      data_prevista_termino: null,
      tipo_projeto: "UNICO",
      unidades: [],
    },
  });

  const onSubmit = (values: ObraComCondomínioFormValues) => {
    console.log("Dados do formulário:", values);
    
    if (values.tipo_projeto === "CONDOMINIO_MASTER") {
      console.log("Criando condomínio com", values.unidades?.length, "unidades");
      // Aqui seria chamada a RPC function create_condominio_project
    } else {
      console.log("Criando obra única");
      // Aqui seria chamada a API normal de criação de obra
    }
  };

  const handleUnidadesChange = (unidades: UnidadeFormData[]) => {
    console.log("Unidades atualizadas:", unidades);
    // Callback opcional para reagir a mudanças nas unidades
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Exemplo: Nova Obra com Suporte a Condomínio</h1>
      
      <FormWrapper
        form={form}
        onSubmit={onSubmit}
        title="Informações da Obra"
        description="Preencha os dados básicos da obra que será cadastrada"
        submitLabel="Salvar Obra"
      >
        {/* Seleção do tipo de projeto */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-muted-foreground">
            Tipo de Projeto
          </h3>
          
          <FormField
            control={form.control}
            name="tipo_projeto"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Selecione o tipo de projeto</FormLabel>
                <FormControl>
                  <RadioGroup
                    value={field.value}
                    onValueChange={(value) => {
                      field.onChange(value);
                      setTipoProjetoSelecionado(value as "UNICO" | "CONDOMINIO_MASTER");
                      
                      // Limpar unidades se mudou para obra única
                      if (value === "UNICO") {
                        form.setValue("unidades", []);
                      }
                    }}
                    className="flex flex-col space-y-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="UNICO" id="unico" />
                      <label htmlFor="unico" className="text-sm font-medium">
                        Obra Única
                      </label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="CONDOMINIO_MASTER" id="condominio" />
                      <label htmlFor="condominio" className="text-sm font-medium">
                        Condomínio
                      </label>
                    </div>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Campos básicos da obra (nome, endereço, etc.) */}
        <div className="space-y-4">
          <h3 className="text-sm font-medium text-muted-foreground">
            Dados Básicos
          </h3>
          
          <FormField
            control={form.control}
            name="nome"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome da Obra</FormLabel>
                <FormControl>
                  <input
                    placeholder="Ex: Edifício Residencial Aurora"
                    {...field}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Outros campos básicos seriam adicionados aqui */}
        </div>

        {/* Seção de condomínio - renderizada condicionalmente */}
        <CondomínioFormSection
          isCondominio={tipoProjetoSelecionado === "CONDOMINIO_MASTER"}
          onUnidadesChange={handleUnidadesChange}
        />

        {/* Botão de submit customizado para mostrar diferentes ações */}
        <div className="flex justify-end pt-6">
          <Button type="submit" size="lg">
            {tipoProjetoSelecionado === "CONDOMINIO_MASTER" 
              ? "Criar Condomínio" 
              : "Criar Obra"
            }
          </Button>
        </div>
      </FormWrapper>
    </div>
  );
};

/**
 * Instruções de integração:
 * 
 * 1. No NovaObraRefactored.tsx, adicione o import:
 *    import { CondomínioFormSection } from "@/components/obras/CondomínioFormSection";
 * 
 * 2. Altere o schema de validação:
 *    import { obraComCondomínioSchema } from "@/lib/validations/obra";
 *    const form = useForm<ObraComCondomínioFormValues>({
 *      resolver: zodResolver(obraComCondomínioSchema),
 *      // ... outros campos
 *    });
 * 
 * 3. Adicione o RadioGroup para seleção do tipo de projeto antes dos campos existentes
 * 
 * 4. Adicione o CondomínioFormSection após os campos básicos:
 *    <CondomínioFormSection
 *      isCondominio={form.watch("tipo_projeto") === "CONDOMINIO_MASTER"}
 *      onUnidadesChange={(unidades) => {
 *        // Opcional: reagir a mudanças nas unidades
 *      }}
 *    />
 * 
 * 5. Modifique a função onSubmit para lidar com condomínios:
 *    const onSubmit = (values: ObraComCondomínioFormValues) => {
 *      if (values.tipo_projeto === "CONDOMINIO_MASTER") {
 *        // Chamar RPC function create_condominio_project
 *        mutateCondominio(values);
 *      } else {
 *        // Chamar API normal
 *        mutate(values);
 *      }
 *    };
 */