import { Building2, Plus, Trash2 } from "lucide-react";
import { useFieldArray, useFormContext } from "react-hook-form";

import { Button } from "@/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import type {
  ObraComCondomínioFormValues,
  UnidadeFormData,
} from "@/lib/validations/obra";

interface CondomínioFormSectionProps {
  isCondominio: boolean;
  onUnidadesChange?: (unidades: UnidadeFormData[]) => void;
}

export const CondomínioFormSection = ({
  isCondominio,
  onUnidadesChange,
}: CondomínioFormSectionProps) => {
  const form = useFormContext<ObraComCondomínioFormValues>();

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "unidades",
  });

  // Função para adicionar nova unidade
  const adicionarUnidade = () => {
    const novaUnidade: UnidadeFormData = {
      identificador_unidade: `Unidade ${fields.length + 1}`,
      nome: `Unidade ${fields.length + 1}`,
    };

    append(novaUnidade);

    // Callback para notificar mudanças - aguardar próximo tick para obter valores atualizados
    if (onUnidadesChange) {
      setTimeout(() => {
        const unidadesAtualizadas = form.getValues("unidades") || [];
        onUnidadesChange(unidadesAtualizadas);
      }, 0);
    }
  };

  // Função para remover unidade
  const removerUnidade = (index: number) => {
    remove(index);

    // Callback para notificar mudanças
    if (onUnidadesChange) {
      const unidadesAtuais = form.getValues("unidades") || [];
      const unidadesAtualizadas = unidadesAtuais.filter((_, i) => i !== index);
      onUnidadesChange(unidadesAtualizadas);
    }
  };

  // Função para gerar unidades automaticamente
  const gerarUnidadesAutomaticamente = () => {
    const numeroUnidades = prompt("Quantas unidades deseja criar?");
    const num = parseInt(numeroUnidades || "0");

    if (num > 0 && num <= 100) {
      // Limpar unidades existentes
      while (fields.length > 0) {
        remove(0);
      }

      // Adicionar novas unidades
      const novasUnidades: UnidadeFormData[] = [];
      for (let i = 1; i <= num; i++) {
        const unidade: UnidadeFormData = {
          identificador_unidade: `${i.toString().padStart(3, "0")}`,
          nome: `Unidade ${i.toString().padStart(3, "0")}`,
        };
        append(unidade);
        novasUnidades.push(unidade);
      }

      // Callback para notificar mudanças
      if (onUnidadesChange) {
        onUnidadesChange(novasUnidades);
      }
    } else if (num > 100) {
      alert("Máximo de 100 unidades permitido por condomínio.");
    }
  };

  // Não renderizar se não for condomínio
  if (!isCondominio) {
    return null;
  }

  return (
    <div className="space-y-6">
      <Separator />

      {/* Cabeçalho da seção */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
          <Building2 className="h-4 w-4" />
          Configuração do Condomínio
        </h3>
        <p className="text-sm text-muted-foreground">
          Configure as unidades que farão parte deste condomínio. Cada unidade
          será tratada como uma obra independente.
        </p>
      </div>

      {/* Controles de unidades */}
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h4 className="text-sm font-medium">Unidades do Condomínio</h4>
            <p className="text-xs text-muted-foreground">
              {fields.length}{" "}
              {fields.length === 1
                ? "unidade configurada"
                : "unidades configuradas"}
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={gerarUnidadesAutomaticamente}
              className="text-xs w-full sm:w-auto"
            >
              Gerar Automaticamente
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={adicionarUnidade}
              className="text-xs w-full sm:w-auto"
            >
              <Plus className="h-3 w-3 mr-1" />
              Adicionar Unidade
            </Button>
          </div>
        </div>

        {/* Lista de unidades */}
        {fields.length === 0 ? (
          <div className="text-center py-8 border-2 border-dashed border-muted rounded-lg">
            <Building2 className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground mb-4">
              Nenhuma unidade configurada ainda
            </p>
            <Button
              type="button"
              variant="outline"
              onClick={adicionarUnidade}
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Primeira Unidade
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {fields.map((field, index) => (
              <div
                key={field.id}
                className="p-4 border rounded-lg bg-background/50 space-y-4"
              >
                <div className="flex items-center justify-between">
                  <h5 className="text-sm font-medium">Unidade {index + 1}</h5>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removerUnidade(index)}
                    className="text-destructive hover:text-destructive hover:bg-destructive/10"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name={`unidades.${index}.identificador_unidade`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          Identificador
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">
                                  Código único para identificar a unidade dentro
                                  do condomínio. Exemplos: 001, A1, Bloco-A-01,
                                  Apt-101
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Ex: 001, A1, Bloco-A-01"
                            {...field}
                            className="bg-background focus:bg-background transition-colors"
                          />
                        </FormControl>
                        <FormDescription>
                          Código único para identificar a unidade
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`unidades.${index}.nome`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          Nome da Unidade
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">
                                  Nome descritivo da unidade que será exibido
                                  nos relatórios e listagens. Exemplos:
                                  Apartamento 101, Casa 1, Loja Térrea
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Ex: Apartamento 101, Casa 1"
                            {...field}
                            className="bg-background focus:bg-background transition-colors"
                          />
                        </FormControl>
                        <FormDescription>
                          Nome descritivo da unidade
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Validação de unidades */}
        <FormField
          control={form.control}
          name="unidades"
          render={() => (
            <FormItem>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};
