import { useQueryClient } from "@tanstack/react-query";
import type { ColumnDef } from "@tanstack/react-table";
import { Edit, Eye, Trash2 } from "lucide-react";
import { useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";

import { CondominioConfirmationDialog } from "@/components/obras/CondominioConfirmationDialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTable } from "@/components/ui/data-table";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/auth/hooks";
import { useObras } from "@/hooks/useObras";
import type { Obra } from "@/types/api";

interface ListaUnidadesProps {
  condomínioId: string;
  unidades: Obra[];
  onUnidadeSelect?: (unidadeId: string) => void;
  onBatchAction?: (action: string, selectedIds: string[]) => void;
  isLoading?: boolean;
  // 🚀 PERFORMANCE: Paginação para condomínios grandes
  pageSize?: number;
  enablePagination?: boolean;
}

export const ListaUnidades = ({
  condomínioId,
  unidades,
  onUnidadeSelect,
  onBatchAction,
  isLoading = false,
  pageSize = 50, // 🚀 PERFORMANCE: 50 unidades por página
  enablePagination = true,
}: ListaUnidadesProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // ✅ SEGUINDO PADRÕES CLAUDE.MD: Usar useObras para operações CRUD
  const { updateObra } = useObras();

  const [selectedUnidades, setSelectedUnidades] = useState<string[]>([]);
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    type: "deleteUnit";
    unitId?: string;
    unitName?: string;
    condominioName?: string;
  }>({
    isOpen: false,
    type: "deleteUnit",
  });

  // Função para navegar para detalhes da unidade
  const handleNavigateToUnidade = (unidadeId: string) => {
    if (onUnidadeSelect) {
      onUnidadeSelect(unidadeId);
    } else {
      navigate(`/dashboard/obras/${unidadeId}`);
    }
  };

  // Função para navegar para edição da unidade
  const handleEditUnidade = (unidadeId: string) => {
    navigate(`/dashboard/obras/${unidadeId}/editar`);
  };

  // Função para abrir modal de confirmação de exclusão
  const handleDeleteUnidade = (unidadeId: string, unidadeNome: string) => {
    const unidade = unidades.find((u) => u.id === unidadeId);
    setConfirmationDialog({
      isOpen: true,
      type: "deleteUnit",
      unitId: unidadeId,
      unitName: unidadeNome,
      condominioName: unidade?.nome || "Condomínio",
    });
  };

  // Função para confirmar exclusão da unidade
  const handleConfirmDeleteUnidade = async () => {
    if (confirmationDialog.unitId) {
      try {
        // Importar obrasApi dinamicamente
        const { obrasApi } = await import("@/services/api");

        // Chamar função real de exclusão
        const result = await obrasApi.deleteCondominioUnit(
          confirmationDialog.unitId
        );

        // Criar mensagem detalhada baseada no resultado
        const mensagemDetalhada = result.recalculo_realizado
          ? `Custo paramétrico recalculado automaticamente:
             • Novo custo estimado: ${new Intl.NumberFormat("pt-BR", {
               style: "currency",
               currency: "BRL",
             }).format(result.novo_custo_estimado)}
             • Custo por unidade: ${new Intl.NumberFormat("pt-BR", {
               style: "currency",
               currency: "BRL",
             }).format(result.novo_valor_por_unidade)}
             • Nova área total: ${result.nova_area_total} m²
             • Investimento preservado: ${new Intl.NumberFormat("pt-BR", {
               style: "currency",
               currency: "BRL",
             }).format(result.investimento_preservado)}
             • Margem disponível: ${new Intl.NumberFormat("pt-BR", {
               style: "currency",
               currency: "BRL",
             }).format(result.margem_disponivel)}`
          : "Valores mantidos inalterados.";

        toast({
          title: "Unidade excluída com sucesso",
          description: `A unidade "${confirmationDialog.unitName}" foi excluída. ${result.unidades_restantes} unidades restantes. ${mensagemDetalhada}`,
          variant: "default",
          duration: 8000, // Mais tempo para ler as informações
        });

        // Fechar modal
        setConfirmationDialog({ isOpen: false, type: "deleteUnit" });

        // Invalidar queries relacionadas para atualizar automaticamente
        await queryClient.invalidateQueries({
          queryKey: ["unidades-condominio"],
        });
        await queryClient.invalidateQueries({ queryKey: ["obra"] });
        await queryClient.invalidateQueries({
          queryKey: ["condominio-dashboard"],
        });
        await queryClient.invalidateQueries({ queryKey: ["orcamentos-obra"] });
      } catch (error) {
        console.error("Erro ao excluir unidade:", error);
        toast({
          title: "Erro ao excluir unidade",
          description:
            error instanceof Error ? error.message : "Erro interno do servidor",
          variant: "destructive",
        });
      }
    }
  };

  // Função para marcar unidade como concluída
  const handleMarcarConcluida = async (
    unidadeId: string,
    unidadeNome: string
  ) => {
    try {
      // Buscar dados atuais da unidade para verificar se precisa definir data_inicio
      const unidadeAtual = unidades.find((u) => u.id === unidadeId);

      const updateData: any = {
        progresso: 100, // Marcar como 100% concluída
      };

      // ✅ CORREÇÃO: SEMPRE definir data_inicio se não existir
      if (!unidadeAtual?.data_inicio) {
        updateData.data_inicio = new Date().toISOString().split("T")[0]; // Formato YYYY-MM-DD
      }

      // ✅ SEGUINDO PADRÕES CLAUDE.MD: Usar useObras.updateObra ao invés de query direta
      await updateObra.mutateAsync({
        id: unidadeId,
        data: updateData,
      });

      // ✅ FEEDBACK VISUAL ESPECÍFICO: Toast personalizado para conclusão
      toast({
        title: "Unidade marcada como concluída",
        description: `${unidadeNome} foi marcada como fisicamente concluída (100%).`,
      });

      // 🔄 CORREÇÃO: Invalidar queries específicas para atualização automática
      await queryClient.invalidateQueries({
        queryKey: ["unidades-condominio", condomínioId],
      });
      await queryClient.invalidateQueries({
        queryKey: ["obra", condomínioId],
      });
      await queryClient.invalidateQueries({
        queryKey: ["condominio-dashboard", condomínioId],
      });
    } catch (error) {
      // ✅ TRATAMENTO DE ERRO: Já é tratado pelo useCrudOperations com toast de erro
      // Erro adicional apenas se necessário para debug
      console.error("Erro específico ao marcar unidade como concluída:", error);
    }
  };

  // Função para lidar com seleção de unidades
  const handleUnidadeSelection = (unidadeId: string, isSelected: boolean) => {
    setSelectedUnidades((prev) => {
      if (isSelected) {
        return [...prev, unidadeId];
      } else {
        return prev.filter((id) => id !== unidadeId);
      }
    });
  };

  // Função para selecionar/deselecionar todas as unidades
  const handleSelectAll = (isSelected: boolean) => {
    if (isSelected) {
      setSelectedUnidades(unidades.map((u) => u.id));
    } else {
      setSelectedUnidades([]);
    }
  };

  // Função para executar ações em lote
  const handleBatchAction = (action: string) => {
    if (selectedUnidades.length === 0) {
      toast({
        title: "Nenhuma unidade selecionada",
        description:
          "Selecione pelo menos uma unidade para executar esta ação.",
        variant: "destructive",
      });
      return;
    }

    if (onBatchAction) {
      onBatchAction(action, selectedUnidades);
    } else {
      // Implementação padrão para ações em lote
      switch (action) {
        case "export":
          toast({
            title: "Exportando unidades",
            description: `Exportando ${selectedUnidades.length} unidade(s) selecionada(s).`,
          });
          break;
        case "update_status":
          toast({
            title: "Atualizando status",
            description: `Atualizando status de ${selectedUnidades.length} unidade(s).`,
          });
          break;
        default:
          toast({
            title: "Ação não implementada",
            description: `A ação "${action}" será implementada em breve.`,
          });
      }
    }

    // Limpar seleção após ação
    setSelectedUnidades([]);
  };

  // ✅ CORREÇÃO: Função para calcular status baseado em progresso e datas
  const getUnidadeStatus = (unidade: Obra) => {
    const hoje = new Date();
    const dataInicio = unidade.data_inicio
      ? new Date(unidade.data_inicio)
      : null;
    const dataFim = unidade.data_fim
      ? new Date(unidade.data_fim)
      : unidade.data_prevista_termino
      ? new Date(unidade.data_prevista_termino)
      : null;

    // ✅ CORREÇÃO: Verificar se progresso = 100% (concluída) - VERDE
    if (unidade.progresso >= 100) {
      return {
        label: "Concluída",
        variant: "outline" as const,
        className:
          "!bg-green-100 dark:!bg-green-900/30 !text-green-700 dark:!text-green-300 !border-green-200 dark:!border-green-600",
      };
    }

    if (!dataInicio)
      return { label: "Não iniciada", variant: "outline" as const };
    if (dataInicio > hoje)
      return { label: "Planejada", variant: "outline" as const };
    if (dataFim && dataFim < hoje)
      return { label: "Atrasada", variant: "destructive" as const };
    return { label: "Em andamento", variant: "default" as const };
  };

  // Função para formatar status
  const formatStatus = (unidade: Obra) => {
    const statusInfo = getUnidadeStatus(unidade);
    return (
      <Badge variant={statusInfo.variant} className={statusInfo.className}>
        {statusInfo.label}
      </Badge>
    );
  };

  // Definição das colunas da tabela
  const columns: ColumnDef<Obra>[] = useMemo(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            onCheckedChange={(value) => {
              table.toggleAllPageRowsSelected(!!value);
              handleSelectAll(!!value);
            }}
            aria-label="Selecionar todas as unidades"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={selectedUnidades.includes(row.original.id)}
            onCheckedChange={(value) => {
              row.toggleSelected(!!value);
              handleUnidadeSelection(row.original.id, !!value);
            }}
            aria-label={`Selecionar unidade ${
              row.original.identificador_unidade || row.original.nome
            }`}
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "identificador_unidade",
        header: "Identificador",
        cell: ({ row }) => (
          <div className="font-medium">
            {row.getValue("identificador_unidade") || "N/A"}
          </div>
        ),
      },
      {
        accessorKey: "nome",
        header: "Nome da Unidade",
        cell: ({ row }) => (
          <div className="max-w-[200px] truncate">{row.getValue("nome")}</div>
        ),
      },
      {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => formatStatus(row.original),
      },
      {
        accessorKey: "area_total",
        header: "Área Total (m²)",
        cell: ({ row }) => {
          const area = row.getValue("area_total") as number;
          if (!area) return "N/A";

          return (
            <div className="text-right font-medium">
              {new Intl.NumberFormat("pt-BR", {
                style: "decimal",
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }).format(area)}
            </div>
          );
        },
      },
      {
        accessorKey: "orcamento",
        header: "Orçamento Atual",
        cell: ({ row }) => {
          const orcamentoInvestimento = row.getValue("orcamento") as number;
          const orcamentoParametrico = row.original
            .valor_orcamento_parametrico as number;
          const orcamento = orcamentoInvestimento || orcamentoParametrico;

          if (!orcamento) return "N/A";

          return (
            <div className="text-right font-medium">
              {new Intl.NumberFormat("pt-BR", {
                style: "currency",
                currency: "BRL",
              }).format(orcamento)}
            </div>
          );
        },
      },
      {
        accessorKey: "data_inicio",
        header: "Data de Início",
        cell: ({ row }) => {
          const data = row.getValue("data_inicio") as string;
          if (!data) return "N/A";

          return new Date(data).toLocaleDateString("pt-BR");
        },
      },
      {
        id: "actions",
        header: "Ações",
        cell: ({ row }) => {
          const unidade = row.original;

          return (
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleNavigateToUnidade(unidade.id)}
                className="h-8 w-8 text-sky-600 dark:text-sky-400 hover:bg-sky-100 dark:hover:bg-sky-900/30"
                title="Ver detalhes"
              >
                <Eye className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleEditUnidade(unidade.id)}
                className="h-8 w-8 text-amber-600 dark:text-amber-400 hover:bg-amber-100 dark:hover:bg-amber-900/30"
                title="Editar"
              >
                <Edit className="h-4 w-4" />
              </Button>

              {unidade.status !== "concluida" && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =>
                    handleMarcarConcluida(unidade.id, unidade.nome)
                  }
                  className="h-8 w-8 text-green-600 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-900/30"
                  title="Marcar como concluída"
                >
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </Button>
              )}

              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleDeleteUnidade(unidade.id, unidade.nome)}
                className="h-8 w-8 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30"
                title="Excluir unidade"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          );
        },
        enableSorting: false,
      },
    ],
    [selectedUnidades]
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">Carregando unidades...</div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Barra de ações em lote */}
      {selectedUnidades.length > 0 && (
        <div className="flex items-center gap-2 p-4 bg-muted/50 rounded-lg">
          <span className="text-sm text-muted-foreground">
            {selectedUnidades.length} unidade(s) selecionada(s)
          </span>
          <div className="flex gap-2 ml-auto">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBatchAction("export")}
            >
              Exportar
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBatchAction("update_status")}
            >
              Atualizar Status
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedUnidades([])}
            >
              Limpar Seleção
            </Button>
          </div>
        </div>
      )}

      {/* Tabela de unidades */}
      <DataTable
        columns={columns}
        data={unidades}
        searchKey="nome"
        searchPlaceholder="Buscar por nome da unidade..."
      />

      {/* Modal de confirmação */}
      <CondominioConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={() =>
          setConfirmationDialog({ isOpen: false, type: "deleteUnit" })
        }
        onConfirm={handleConfirmDeleteUnidade}
        type={confirmationDialog.type}
        condominioName={confirmationDialog.condominioName}
        unitName={confirmationDialog.unitName}
      />
    </div>
  );
};
