import { useVirtualizer } from "@tanstack/react-virtual";
import { Edit, Eye, Loader2, MoreHorizontal, Search,Trash2 } from "lucide-react";
import { useCallback, useEffect,useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";

import { CondominioConfirmationDialog } from "@/components/obras/CondominioConfirmationDialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useObrasCondominio } from "@/hooks/useObrasCondominio";

interface ListaUnidadesVirtualizedProps {
  condominioId: string;
  onUnidadeSelect?: (unidadeId: string) => void;
  onBatchAction?: (action: string, selectedIds: string[]) => void;
}

export const ListaUnidadesVirtualized = ({
  condominioId,
  onUnidadeSelect,
  onBatchAction,
}: ListaUnidadesVirtualizedProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { useUnidadesPaginadasSimple } = useObrasCondominio();
  
  // Estados para filtros e paginação
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [selectedUnidades, setSelectedUnidades] = useState<string[]>([]);
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    type: "deleteUnit";
    unitId?: string;
    unitName?: string;
    condominioName?: string;
  }>({
    isOpen: false,
    type: "deleteUnit",
  });

  const pageSize = 50;

  // Buscar dados paginados
  const {
    data: paginatedData,
    isLoading,
    error,
    isPlaceholderData,
  } = useUnidadesPaginadasSimple(condominioId, {
    page: currentPage,
    pageSize,
    search: search || undefined,
    statusFilter: statusFilter || undefined,
  });

  const unidades = paginatedData?.data || [];
  const pagination = paginatedData?.pagination;

  // Debounce da busca
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      setCurrentPage(1); // Reset para primeira página ao buscar
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [search, statusFilter]);

  // Ref para o container da lista
  const parentRef = useRef<HTMLDivElement>(null);

  // Configurar virtualizer
  const virtualizer = useVirtualizer({
    count: unidades.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 60, // Altura estimada de cada linha
    overscan: 10, // Renderizar 10 itens adicionais fora da viewport
  });

  // Função para navegar para detalhes da unidade
  const handleNavigateToUnidade = useCallback((unidadeId: string) => {
    if (onUnidadeSelect) {
      onUnidadeSelect(unidadeId);
    } else {
      navigate(`/dashboard/obras/${unidadeId}`);
    }
  }, [onUnidadeSelect, navigate]);

  // Função para navegar para edição da unidade
  const handleEditUnidade = useCallback((unidadeId: string) => {
    navigate(`/dashboard/obras/${unidadeId}/editar`);
  }, [navigate]);

  // Função para abrir modal de confirmação de exclusão
  const handleDeleteUnidade = useCallback((unidadeId: string, unidadeNome: string) => {
    const unidade = unidades.find((u) => u.id === unidadeId);
    setConfirmationDialog({
      isOpen: true,
      type: "deleteUnit",
      unitId: unidadeId,
      unitName: unidadeNome,
      condominioName: unidade?.nome || "Condomínio",
    });
  }, [unidades]);

  // Função para confirmar exclusão da unidade
  const handleConfirmDeleteUnidade = useCallback(() => {
    if (confirmationDialog.unitId) {
      toast({
        title: "Unidade excluída",
        description: `A unidade "${confirmationDialog.unitName}" foi excluída com sucesso.`,
        variant: "default",
      });

      setConfirmationDialog({ isOpen: false, type: "deleteUnit" });
    }
  }, [confirmationDialog, toast]);

  // Função para lidar com seleção de unidades
  const handleUnidadeSelection = useCallback((unidadeId: string, isSelected: boolean) => {
    setSelectedUnidades((prev) => {
      if (isSelected) {
        return [...prev, unidadeId];
      } else {
        return prev.filter((id) => id !== unidadeId);
      }
    });
  }, []);

  // Função para selecionar/deselecionar todas as unidades da página atual
  const handleSelectAll = useCallback((isSelected: boolean) => {
    if (isSelected) {
      setSelectedUnidades(unidades.map((u) => u.id));
    } else {
      setSelectedUnidades([]);
    }
  }, [unidades]);

  // Função para executar ações em lote
  const handleBatchAction = useCallback((action: string) => {
    if (selectedUnidades.length === 0) {
      toast({
        title: "Nenhuma unidade selecionada",
        description: "Selecione pelo menos uma unidade para executar esta ação.",
        variant: "destructive",
      });
      return;
    }

    if (onBatchAction) {
      onBatchAction(action, selectedUnidades);
    } else {
      // Implementação padrão para ações em lote
      switch (action) {
        case "export":
          toast({
            title: "Exportando unidades",
            description: `Exportando ${selectedUnidades.length} unidade(s) selecionada(s).`,
          });
          break;
        case "update_status":
          toast({
            title: "Atualizando status",
            description: `Atualizando status de ${selectedUnidades.length} unidade(s).`,
          });
          break;
        default:
          toast({
            title: "Ação não implementada",
            description: `A ação "${action}" será implementada em breve.`,
          });
      }
    }

    setSelectedUnidades([]);
  }, [selectedUnidades, onBatchAction, toast]);

  // Função para formatar status
  const formatStatus = useCallback((status?: string) => {
    if (!status) return <Badge variant="outline">Não definido</Badge>;

    const statusMap: Record<
      string,
      {
        label: string;
        variant: "default" | "secondary" | "destructive" | "outline";
      }
    > = {
      planejamento: { label: "Planejamento", variant: "outline" },
      em_andamento: { label: "Em Andamento", variant: "default" },
      concluida: { label: "Concluída", variant: "secondary" },
      pausada: { label: "Pausada", variant: "destructive" },
      DISPONIVEL: { label: "Disponível", variant: "outline" },
      VENDIDA: { label: "Vendida", variant: "secondary" },
      RESERVADA: { label: "Reservada", variant: "default" },
    };

    const statusInfo = statusMap[status] || {
      label: status,
      variant: "outline" as const,
    };

    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  }, []);

  // Componente de linha virtualizada
  const VirtualizedRow = useMemo(() => {
    return ({ index }: { index: number }) => {
      const unidade = unidades[index];
      
      if (!unidade) return null;

      const isSelected = selectedUnidades.includes(unidade.id);

      return (
        <div className="flex items-center space-x-4 px-4 py-3 border-b hover:bg-muted/50 transition-colors">
          {/* Checkbox de seleção */}
          <Checkbox
            checked={isSelected}
            onCheckedChange={(checked) => 
              handleUnidadeSelection(unidade.id, !!checked)
            }
            aria-label={`Selecionar unidade ${unidade.identificador_unidade || unidade.nome}`}
          />

          {/* Identificador */}
          <div className="w-24 font-medium text-sm">
            {unidade.identificador_unidade || "N/A"}
          </div>

          {/* Nome */}
          <div className="flex-1 min-w-0">
            <div className="truncate font-medium text-sm">{unidade.nome}</div>
          </div>

          {/* Status */}
          <div className="w-32">
            {formatStatus(unidade.status_venda)}
          </div>

          {/* Área Total */}
          <div className="w-24 text-right text-sm">
            {unidade.area_total ? 
              `${new Intl.NumberFormat("pt-BR", {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2,
              }).format(unidade.area_total)}m²` : 
              "N/A"
            }
          </div>

          {/* Orçamento */}
          <div className="w-32 text-right text-sm">
            {unidade.orcamento ? 
              new Intl.NumberFormat("pt-BR", {
                style: "currency",
                currency: "BRL",
              }).format(unidade.orcamento) : 
              "N/A"
            }
          </div>

          {/* Ações */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleNavigateToUnidade(unidade.id)}
              className="h-8 w-8 text-sky-600 hover:bg-sky-100"
              title="Ver detalhes"
            >
              <Eye className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleEditUnidade(unidade.id)}
              className="h-8 w-8 text-amber-600 hover:bg-amber-100"
              title="Editar"
            >
              <Edit className="h-4 w-4" />
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-gray-600 hover:bg-gray-100"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleNavigateToUnidade(unidade.id)}>
                  <Eye className="mr-2 h-4 w-4" />
                  Ver detalhes
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleEditUnidade(unidade.id)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Editar
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => handleDeleteUnidade(unidade.id, unidade.nome)}
                  className="text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Excluir
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      );
    };
  }, [unidades, selectedUnidades, handleUnidadeSelection, handleNavigateToUnidade, handleEditUnidade, handleDeleteUnidade, formatStatus]);

  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-destructive">
          Erro ao carregar unidades: {error.message}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Controles de filtro e busca */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar por nome ou identificador..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Filtrar por status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">Todos os status</SelectItem>
            <SelectItem value="DISPONIVEL">Disponível</SelectItem>
            <SelectItem value="VENDIDA">Vendida</SelectItem>
            <SelectItem value="RESERVADA">Reservada</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Barra de ações em lote */}
      {selectedUnidades.length > 0 && (
        <div className="flex items-center gap-2 p-4 bg-muted/50 rounded-lg">
          <span className="text-sm text-muted-foreground">
            {selectedUnidades.length} unidade(s) selecionada(s)
          </span>
          <div className="flex gap-2 ml-auto">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBatchAction("export")}
            >
              Exportar
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBatchAction("update_status")}
            >
              Atualizar Status
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedUnidades([])}
            >
              Limpar Seleção
            </Button>
          </div>
        </div>
      )}

      {/* Header da tabela */}
      <div className="flex items-center space-x-4 px-4 py-2 bg-muted/20 border-b-2 font-medium text-sm">
        <Checkbox
          checked={unidades.length > 0 && selectedUnidades.length === unidades.length}
          indeterminate={selectedUnidades.length > 0 && selectedUnidades.length < unidades.length}
          onCheckedChange={handleSelectAll}
          aria-label="Selecionar todas as unidades"
        />
        <div className="w-24">Identificador</div>
        <div className="flex-1">Nome da Unidade</div>
        <div className="w-32">Status</div>
        <div className="w-24 text-right">Área Total</div>
        <div className="w-32 text-right">Orçamento</div>
        <div className="w-24 text-center">Ações</div>
      </div>

      {/* Lista virtualizada */}
      <div 
        ref={parentRef}
        className="h-[600px] overflow-auto border rounded-lg"
        style={{
          contain: 'strict',
        }}
      >
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Carregando unidades...</span>
          </div>
        )}

        {!isLoading && unidades.length === 0 && (
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">
              {search || statusFilter ? "Nenhuma unidade encontrada com os filtros aplicados." : "Nenhuma unidade encontrada."}
            </div>
          </div>
        )}

        {!isLoading && unidades.length > 0 && (
          <div
            style={{
              height: `${virtualizer.getTotalSize()}px`,
              width: '100%',
              position: 'relative',
            }}
          >
            {virtualizer.getVirtualItems().map((virtualItem) => (
              <div
                key={virtualItem.key}
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: `${virtualItem.size}px`,
                  transform: `translateY(${virtualItem.start}px)`,
                }}
              >
                <VirtualizedRow index={virtualItem.index} />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Controles de paginação */}
      {pagination && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Mostrando {((currentPage - 1) * pageSize) + 1} até {Math.min(currentPage * pageSize, pagination.total)} de {pagination.total} unidades
            {isPlaceholderData && (
              <span className="ml-2">
                <Loader2 className="h-4 w-4 animate-spin inline" />
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
              disabled={currentPage === 1 || isLoading}
            >
              Anterior
            </Button>
            
            <span className="text-sm">
              Página {currentPage} de {pagination.totalPages}
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => p + 1)}
              disabled={!pagination.hasNextPage || isLoading}
            >
              Próxima
            </Button>
          </div>
        </div>
      )}

      {/* Modal de confirmação */}
      <CondominioConfirmationDialog
        isOpen={confirmationDialog.isOpen}
        onClose={() => setConfirmationDialog({ isOpen: false, type: "deleteUnit" })}
        onConfirm={handleConfirmDeleteUnidade}
        type={confirmationDialog.type}
        condominioName={confirmationDialog.condominioName}
        unitName={confirmationDialog.unitName}
      />
    </div>
  );
};