import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';

import { CondominioDashboard } from '../CondominioDashboard';

// Mock do hook useObrasCondominio
vi.mock('@/hooks/useObrasCondominio', () => ({
  useObrasCondominio: () => ({
    useCondominioDashboard: vi.fn(() => ({
      data: {
        estatisticas: {
          total_unidades: 1600,
          unidades_concluidas: null, // Simula valor nulo que causaria NaN
          progresso_medio: NaN, // Simula progresso médio como NaN
          valor_total_vendas: 200000000,
          orcamento_total_unidades: 200000000,
          unidades_vendidas: undefined, // Simula valor undefined
          unidades_disponiveis: null, // Simula valor nulo
        }
      },
      isLoading: false,
      error: null
    }))
  })
}));

// Mock do secure logger
vi.mock('@/lib/secure-logger', () => ({
  secureLogger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn()
  }
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false }
  }
});

describe('CondominioDashboard - Proteção contra NaN', () => {
  it('deve exibir 0% ao invés de NaN% quando dados são nulos ou inválidos', () => {
    const queryClient = createTestQueryClient();
    
    render(
      <QueryClientProvider client={queryClient}>
        <CondominioDashboard obraId="test-id" />
      </QueryClientProvider>
    );

    // Verifica se não há NaN% sendo exibido
    expect(screen.queryByText(/NaN%/)).not.toBeInTheDocument();
    
    // Verifica se Taxa Conclusão mostra 0% ao invés de NaN%
    expect(screen.getByText('Taxa Conclusão')).toBeInTheDocument();
    
    // Verifica se valores padrão são exibidos corretamente
    const taxaConclusaoSection = screen.getByText('Taxa Conclusão').closest('div');
    expect(taxaConclusaoSection).toHaveTextContent('0%');
    
    // Verifica progresso médio
    expect(screen.getByText('Progresso Médio')).toBeInTheDocument();
    const progressoMedioSection = screen.getByText('Progresso Médio').closest('div');
    expect(progressoMedioSection).toHaveTextContent('0.0%');
  });

  it('deve calcular percentuais corretamente quando há dados válidos', () => {
    const queryClient = createTestQueryClient();
    
    // Recria o mock com dados válidos
    vi.mocked(require('@/hooks/useObrasCondominio').useObrasCondominio).mockReturnValue({
      useCondominioDashboard: vi.fn(() => ({
        data: {
          estatisticas: {
            total_unidades: 100,
            unidades_concluidas: 25,
            progresso_medio: 30.5,
            valor_total_vendas: 50000000,
            orcamento_total_unidades: 50000000,
            unidades_vendidas: 20,
            unidades_disponiveis: 55,
          }
        },
        isLoading: false,
        error: null
      }))
    });
    
    render(
      <QueryClientProvider client={queryClient}>
        <CondominioDashboard obraId="test-id" />
      </QueryClientProvider>
    );

    // Verifica se não há NaN% sendo exibido
    expect(screen.queryByText(/NaN%/)).not.toBeInTheDocument();
    
    // Verifica taxa de conclusão: 25/100 = 25%
    const taxaConclusaoSection = screen.getByText('Taxa Conclusão').closest('div');
    expect(taxaConclusaoSection).toHaveTextContent('25%');
    
    // Verifica progresso médio: 30.5%
    const progressoMedioSection = screen.getByText('Progresso Médio').closest('div');
    expect(progressoMedioSection).toHaveTextContent('30.5%');
  });
});