import { zodResolver } from "@hookform/resolvers/zod";
import { fireEvent, render, screen } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import { describe, expect, it, vi } from "vitest";

import {
  type ObraComCondomínioFormValues,
  obraComCondomínioSchema,
} from "@/lib/validations/obra";
import type { UnidadeCondominio } from "@/types/condominio";

import { CondomínioFormSection } from "../CondomínioFormSection";

// Mock component wrapper para fornecer contexto do formulário
const TestWrapper = ({
  isCondominio = true,
  onUnidadesChange = vi.fn(),
  defaultValues = {},
}: {
  isCondominio?: boolean;
  onUnidadesChange?: (unidades: UnidadeCondominio[]) => void;
  defaultValues?: Partial<ObraComCondomínioFormValues>;
}) => {
  const form = useForm<ObraComCondomínioFormValues>({
    resolver: zodResolver(obraComCondomínioSchema),
    defaultValues: {
      nome: "",
      endereco: "",
      cidade: "",
      estado: "",
      cep: "",
      orcamento: 0,
      area_total: 0,
      construtora_id: "",
      data_inicio: null,
      data_prevista_termino: null,
      tipo_projeto: "CONDOMINIO_MASTER",
      unidades: [],
      ...defaultValues,
    },
  });

  return (
    <FormProvider {...form}>
      <CondomínioFormSection
        isCondominio={isCondominio}
        onUnidadesChange={onUnidadesChange}
      />
    </FormProvider>
  );
};

describe("CondomínioFormSection", () => {
  it("não renderiza quando isCondominio é false", () => {
    render(<TestWrapper isCondominio={false} />);

    expect(
      screen.queryByText("Configuração do Condomínio")
    ).not.toBeInTheDocument();
  });

  it("renderiza corretamente quando isCondominio é true", () => {
    render(<TestWrapper isCondominio={true} />);

    expect(screen.getByText("Configuração do Condomínio")).toBeInTheDocument();
    expect(screen.getByText("Unidades do Condomínio")).toBeInTheDocument();
    expect(screen.getByText("0 unidades configuradas")).toBeInTheDocument();
  });

  it("exibe estado vazio quando não há unidades", () => {
    render(<TestWrapper />);

    expect(
      screen.getByText("Nenhuma unidade configurada ainda")
    ).toBeInTheDocument();
    expect(screen.getByText("Adicionar Primeira Unidade")).toBeInTheDocument();
  });

  it("adiciona nova unidade quando botão é clicado", () => {
    const onUnidadesChange = vi.fn();
    render(<TestWrapper onUnidadesChange={onUnidadesChange} />);

    const addButton = screen.getByText("Adicionar Primeira Unidade");
    fireEvent.click(addButton);

    expect(screen.getByText("1 unidade configurada")).toBeInTheDocument();
    expect(screen.getByText("Unidade 1")).toBeInTheDocument();
  });

  it("permite adicionar múltiplas unidades", () => {
    render(<TestWrapper />);

    // Adicionar primeira unidade
    fireEvent.click(screen.getByText("Adicionar Primeira Unidade"));

    // Adicionar segunda unidade
    fireEvent.click(screen.getByText("Adicionar Unidade"));

    expect(screen.getByText("2 unidades configuradas")).toBeInTheDocument();
    expect(screen.getByText("Unidade 1")).toBeInTheDocument();
    expect(screen.getByText("Unidade 2")).toBeInTheDocument();
  });

  it("remove unidade quando botão de remoção é clicado", () => {
    render(<TestWrapper />);

    // Adicionar unidade
    fireEvent.click(screen.getByText("Adicionar Primeira Unidade"));
    expect(screen.getByText("1 unidade configurada")).toBeInTheDocument();

    // Remover unidade - buscar pelo botão que contém o ícone de lixeira
    const removeButtons = screen.getAllByRole("button");
    const removeButton = removeButtons.find((button) =>
      button.className.includes("text-destructive")
    );
    expect(removeButton).toBeDefined();
    fireEvent.click(removeButton!);

    expect(screen.getByText("0 unidades configuradas")).toBeInTheDocument();
  });

  it("renderiza campos de input para cada unidade", () => {
    render(<TestWrapper />);

    // Adicionar unidade
    fireEvent.click(screen.getByText("Adicionar Primeira Unidade"));

    expect(screen.getByLabelText("Identificador")).toBeInTheDocument();
    expect(screen.getByLabelText("Nome da Unidade")).toBeInTheDocument();
    expect(screen.getAllByDisplayValue("Unidade 1")).toHaveLength(2); // Both identifier and name have same value
  });

  it("permite editar campos das unidades", () => {
    render(<TestWrapper />);

    // Adicionar unidade
    fireEvent.click(screen.getByText("Adicionar Primeira Unidade"));

    const identificadorInput = screen.getByLabelText("Identificador");
    const nomeInput = screen.getByLabelText("Nome da Unidade");

    fireEvent.change(identificadorInput, { target: { value: "A01" } });
    fireEvent.change(nomeInput, { target: { value: "Apartamento 101" } });

    expect(identificadorInput).toHaveValue("A01");
    expect(nomeInput).toHaveValue("Apartamento 101");
  });

  it("chama onUnidadesChange quando unidades são modificadas", async () => {
    const onUnidadesChange = vi.fn();
    render(<TestWrapper onUnidadesChange={onUnidadesChange} />);

    fireEvent.click(screen.getByText("Adicionar Primeira Unidade"));

    // Wait for the async callback
    await new Promise((resolve) => setTimeout(resolve, 10));

    expect(onUnidadesChange).toHaveBeenCalledWith([
      expect.objectContaining({
        identificador_unidade: "Unidade 1",
        nome: "Unidade 1",
      }),
    ]);
  });
});
