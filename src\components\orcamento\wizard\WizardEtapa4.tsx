import { <PERSON><PERSON><PERSON>, Sparkles } from 'lucide-react';
import React from 'react';
import type { UseFormReturn } from 'react-hook-form';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { PADRAO_OBRA_LABELS, type WizardCompleto } from '@/lib/validations/orcamento';

interface WizardEtapa4Props {
  form: UseFormReturn<WizardCompleto>;
}

export const WizardEtapa4: React.FC<WizardEtapa4Props> = ({ form }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-blue-600" />
          Etapa 4: <PERSON><PERSON><PERSON> da Obra e Finalizações
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Padrão da Obra */}
        <FormField
          control={form.control}
          name="padrao_obra"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Padrão da Obra *</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o padrão de acabamento" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(PADRAO_OBRA_LABELS).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                O padrão define a qualidade dos materiais e acabamentos
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Especificações */}
        <FormField
          control={form.control}
          name="especificacoes"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                Especificações Técnicas
                <span className="text-xs text-gray-500 font-normal">(Opcional)</span>
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Ex: Piso cerâmico, paredes pintadas, forro PVC, janelas de alumínio..."
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={(e) => field.onChange(e.target.value)}
                />
              </FormControl>
              <FormDescription className="text-sm text-gray-600">
                ℹ️ Campo apenas para documentação - não afeta o cálculo do orçamento
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Parâmetros de Entrada */}
        <FormField
          control={form.control}
          name="parametros_entrada"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-2">
                Informações Extras
                <span className="text-xs text-gray-500 font-normal">(Opcional)</span>
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Ex: Terreno em declive, condomínio fechado, instalações especiais..."
                  className="min-h-[80px]"
                  value={field.value || ''}
                  onChange={(e) => field.onChange(e.target.value)}
                />
              </FormControl>
              <FormDescription className="text-sm text-gray-600">
                ℹ️ Campo apenas para anotações - não afeta o cálculo do orçamento
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Informações sobre IA */}
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-950 dark:to-blue-950 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
          <div className="flex items-center gap-2 mb-2">
            <Sparkles className="h-5 w-5 text-purple-600" />
            <h4 className="font-medium text-purple-900 dark:text-purple-100">
              🤖 Cálculo Inteligente com IA
            </h4>
          </div>
          <p className="text-sm text-purple-800 dark:text-purple-200 leading-relaxed">
            Nossa IA calculará o orçamento usando: <strong>Padrão da Obra</strong> + <strong>Área</strong> + <strong>Localização</strong> + dados SINAPI atualizados.
            <br />
            <span className="text-xs opacity-75 mt-1 block">
              💡 Os campos de texto acima são opcionais e servem apenas para suas anotações.
            </span>
          </p>
        </div>

      </CardContent>
    </Card>
  );
};