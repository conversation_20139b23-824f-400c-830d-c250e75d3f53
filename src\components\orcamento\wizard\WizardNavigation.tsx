/**
 * 🧭 Componente de Navegação do Wizard
 *
 * Botões de navegação para o wizard de orçamento,
 * extraído do componente WizardOrcamento original.
 *
 * <AUTHOR> Team
 * @version 2.0.0 - Refatorado para compatibilidade
 */

import { ChevronLeft, ChevronRight, Sparkles } from "lucide-react";
import React from "react";

import { Button } from "@/components/ui/button";

// ====================================
// 🎯 TIPOS E INTERFACES
// ====================================

interface WizardNavigationProps {
  /**
   * Etapa atual do wizard
   */
  etapaAtual: number;

  /**
   * Se pode voltar para etapa anterior
   */
  podeVoltar: boolean;

  /**
   * Se pode avançar para próxima etapa
   */
  podeAvancar: boolean;

  /**
   * Se é a última etapa
   */
  isUltimaEtapa: boolean;

  /**
   * Estado de submissão
   */
  isSubmitindo: boolean;

  /**
   * Estado de carregamento/calculando
   */
  isCalculando: boolean;

  /**
   * Estado de calculando IA
   */
  calculandoIA: boolean;

  /**
   * Função para voltar à etapa anterior
   */
  onEtapaAnterior: () => void;

  /**
   * Função para avançar para próxima etapa
   */
  onProximaEtapa: () => void;

  /**
   * Função para submeter/finalizar o wizard
   */
  onSubmit: () => void;
}

// ====================================
// 🧭 COMPONENTE PRINCIPAL
// ====================================

export const WizardNavigation: React.FC<WizardNavigationProps> = ({
  etapaAtual: _etapaAtual,
  podeVoltar,
  podeAvancar,
  isUltimaEtapa,
  isSubmitindo,
  isCalculando,
  calculandoIA,
  onEtapaAnterior,
  onProximaEtapa,
  onSubmit
}) => {
  return (
    <>
      {/* Separador com gradiente */}
      <div className="relative py-4">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full h-px bg-gradient-to-r from-gray-300 dark:via-gray-600 to-transparent" />
        </div>
      </div>

      {/* Botões de Navegação */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-0 pt-4 lg:pt-6">
        {/* Botão Anterior */}
        <Button
          type="button"
          variant="outline"
          onClick={onEtapaAnterior}
          disabled={!podeVoltar || isSubmitindo || isCalculando}
          className="group flex items-center space-x-2 px-4 lg:px-6 py-2 lg:py-3 border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto"
        >
          <ChevronLeft className="h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200" />
          <span>Anterior</span>
        </Button>

        {/* Botão Próximo ou Finalizar */}
        {!isUltimaEtapa ? (
          <Button
            type="button"
            onClick={onProximaEtapa}
            disabled={!podeAvancar || isSubmitindo || isCalculando}
            className="group flex items-center space-x-2 px-4 lg:px-6 py-2 lg:py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none w-full sm:w-auto"
          >
            <span>Próxima</span>
            <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
          </Button>
        ) : (
          <Button
            type="button"
            onClick={onSubmit}
            disabled={isSubmitindo || isCalculando || calculandoIA}
            className="group flex items-center space-x-2 px-6 lg:px-8 py-2 lg:py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none w-full sm:w-auto"
          >
            {isSubmitindo || isCalculando || calculandoIA ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/30 border-t-white"></div>
                <span>
                  {isSubmitindo || isCalculando ? 'Criando...' : 'Calculando IA...'}
                </span>
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 group-hover:scale-110 transition-transform duration-200" />
                <span>Criar Orçamento</span>
              </>
            )}
          </Button>
        )}
      </div>
    </>
  );
};