import { <PERSON><PERSON><PERSON>,<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Zap } from 'lucide-react';
import { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useTheme } from '@/hooks/useTheme';
import { useUserPreferences } from '@/hooks/useUserPreferences';
import { cn } from '@/lib/utils';
import type { AppearancePreferences } from '@/types';

export function AppearanceSettings() {
  const { theme, setTheme } = useTheme();
  const { preferences, updateAppearance, isUpdating } = useUserPreferences();
  const [localPrefs, setLocalPrefs] = useState<AppearancePreferences>(
    preferences.appearance
  );

  const handleSwitchChange = (key: keyof AppearancePreferences, value: boolean) => {
    const newPrefs = { ...localPrefs, [key]: value };
    setLocalPrefs(newPrefs);
    updateAppearance({ [key]: value });
  };

  const handleSelectChange = (key: keyof AppearancePreferences, value: string) => {
    const newPrefs = { ...localPrefs, [key]: value };
    setLocalPrefs(newPrefs);
    updateAppearance({ [key]: value });
  };

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
    handleSelectChange('theme', newTheme);
  };

  const themeOptions = [
    { value: 'light', label: 'Claro', icon: Sun, description: 'Tema claro para uso diurno' },
    { value: 'dark', label: 'Escuro', icon: Moon, description: 'Tema escuro para reduzir cansaço visual' },
    { value: 'system', label: 'Sistema', icon: Monitor, description: 'Segue a configuração do sistema' },
  ];

  const colorSchemes = [
    { value: 'default', label: 'Padrão', color: 'bg-blue-500' },
    { value: 'blue', label: 'Azul', color: 'bg-blue-600' },
    { value: 'green', label: 'Verde', color: 'bg-green-600' },
    { value: 'purple', label: 'Roxo', color: 'bg-purple-600' },
  ];

  const fontSizes = [
    { value: 'small', label: 'Pequeno', description: 'Texto menor para mais conteúdo' },
    { value: 'medium', label: 'Médio', description: 'Tamanho padrão recomendado' },
    { value: 'large', label: 'Grande', description: 'Texto maior para melhor legibilidade' },
  ];

  return (
    <div className="space-y-6">
      {/* Tema */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Tema
          </CardTitle>
          <CardDescription>
            Escolha a aparência que mais combina com você.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {themeOptions.map((option) => {
              const Icon = option.icon;
              const isSelected = theme === option.value;
              
              return (
                <Button
                  key={option.value}
                  variant={isSelected ? "default" : "outline"}
                  className={cn(
                    "h-auto p-4 flex flex-col items-center gap-3 transition-all",
                    isSelected && "ring-2 ring-primary ring-offset-2"
                  )}
                  onClick={() => handleThemeChange(option.value as 'light' | 'dark' | 'system')}
                  disabled={isUpdating}
                >
                  <Icon className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-medium">{option.label}</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      {option.description}
                    </div>
                  </div>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Esquema de Cores */}
      <Card>
        <CardHeader>
          <CardTitle>Esquema de Cores</CardTitle>
          <CardDescription>
            Personalize as cores da interface.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {colorSchemes.map((scheme) => {
              const isSelected = localPrefs.color_scheme === scheme.value;
              
              return (
                <Button
                  key={scheme.value}
                  variant={isSelected ? "default" : "outline"}
                  className={cn(
                    "h-auto p-3 flex flex-col items-center gap-2",
                    isSelected && "ring-2 ring-primary ring-offset-2"
                  )}
                  onClick={() => handleSelectChange('color_scheme', scheme.value)}
                  disabled={isUpdating}
                >
                  <div className={cn("w-6 h-6 rounded-full", scheme.color)} />
                  <span className="text-sm">{scheme.label}</span>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Layout e Navegação */}
      <Card>
        <CardHeader>
          <CardTitle>Layout e Navegação</CardTitle>
          <CardDescription>
            Configure como a interface é exibida.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Sidebar Recolhida</Label>
              <p className="text-sm text-muted-foreground">
                Manter a barra lateral recolhida por padrão
              </p>
            </div>
            <Switch
              checked={localPrefs.sidebar_collapsed}
              onCheckedChange={(checked) => handleSwitchChange('sidebar_collapsed', checked)}
              disabled={isUpdating}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Modo Compacto</Label>
              <p className="text-sm text-muted-foreground">
                Reduzir espaçamentos para mostrar mais conteúdo
              </p>
            </div>
            <Switch
              checked={localPrefs.compact_mode}
              onCheckedChange={(checked) => handleSwitchChange('compact_mode', checked)}
              disabled={isUpdating}
            />
          </div>
        </CardContent>
      </Card>

      {/* Acessibilidade */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Acessibilidade
          </CardTitle>
          <CardDescription>
            Configurações para melhorar a acessibilidade.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Type className="h-4 w-4" />
              Tamanho da Fonte
            </Label>
            <Select
              value={localPrefs.font_size}
              onValueChange={(value) => handleSelectChange('font_size', value)}
              disabled={isUpdating}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {fontSizes.map((size) => (
                  <SelectItem key={size.value} value={size.value}>
                    <div className="flex flex-col">
                      <span>{size.label}</span>
                      <span className="text-xs text-muted-foreground">
                        {size.description}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Contrast className="h-4 w-4" />
                Alto Contraste
              </Label>
              <p className="text-sm text-muted-foreground">
                Aumentar o contraste para melhor visibilidade
              </p>
            </div>
            <Switch
              checked={localPrefs.high_contrast}
              onCheckedChange={(checked) => handleSwitchChange('high_contrast', checked)}
              disabled={isUpdating}
            />
          </div>
        </CardContent>
      </Card>

      {/* Animações e Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Animações e Performance
          </CardTitle>
          <CardDescription>
            Configure animações e efeitos visuais.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Animações Habilitadas</Label>
              <p className="text-sm text-muted-foreground">
                Ativar animações e transições suaves
              </p>
            </div>
            <Switch
              checked={localPrefs.animations_enabled}
              onCheckedChange={(checked) => handleSwitchChange('animations_enabled', checked)}
              disabled={isUpdating}
            />
          </div>
        </CardContent>
      </Card>

      {/* Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Prévia</CardTitle>
          <CardDescription>
            Veja como suas configurações afetam a interface.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 border rounded-lg bg-card">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <Palette className="h-4 w-4 text-primary-foreground" />
              </div>
              <div>
                <h4 className={cn(
                  "font-semibold",
                  localPrefs.font_size === 'small' && "text-sm",
                  localPrefs.font_size === 'large' && "text-lg"
                )}>
                  Exemplo de Card
                </h4>
                <p className={cn(
                  "text-muted-foreground",
                  localPrefs.font_size === 'small' && "text-xs",
                  localPrefs.font_size === 'large' && "text-base"
                )}>
                  Este é um exemplo de como o conteúdo aparecerá
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="default">Botão Primário</Button>
              <Button size="sm" variant="outline">Botão Secundário</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
