import { Clock, Globe, LogOut, Monitor, MoreHorizontal, Shield, Smartphone, Tablet } from 'lucide-react';
import { useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useUserPreferences } from '@/hooks/useUserPreferences';
import type { DevicePreferences } from '@/types';

interface DeviceSession {
  id: string;
  device_type: 'desktop' | 'mobile' | 'tablet';
  browser: 'chrome' | 'firefox' | 'safari' | 'edge' | 'other';
  os: string;
  location: string;
  last_active: string;
  is_current: boolean;
  ip_address: string;
}

// Mock data para demonstração
const mockSessions: DeviceSession[] = [
  {
    id: '1',
    device_type: 'desktop',
    browser: 'chrome',
    os: 'Windows 11',
    location: 'São Paulo, SP',
    last_active: 'Agora',
    is_current: true,
    ip_address: '*************'
  },
  {
    id: '2',
    device_type: 'mobile',
    browser: 'safari',
    os: 'iOS 17',
    location: 'São Paulo, SP',
    last_active: '2 horas atrás',
    is_current: false,
    ip_address: '*************'
  },
  {
    id: '3',
    device_type: 'tablet',
    browser: 'chrome',
    os: 'Android 14',
    location: 'Rio de Janeiro, RJ',
    last_active: '1 dia atrás',
    is_current: false,
    ip_address: '*************'
  }
];

export function DeviceSettings() {
  const { preferences, updateDevices, isUpdating } = useUserPreferences();
  const [localPrefs, setLocalPrefs] = useState<DevicePreferences>(
    preferences.devices
  );
  const [sessions] = useState<DeviceSession[]>(mockSessions);

  const handleSwitchChange = (key: keyof DevicePreferences, value: boolean) => {
    const newPrefs = { ...localPrefs, [key]: value };
    setLocalPrefs(newPrefs);
    updateDevices({ [key]: value });
  };

  const handleSelectChange = (key: keyof DevicePreferences, value: string | number) => {
    const newPrefs = { ...localPrefs, [key]: value };
    setLocalPrefs(newPrefs);
    updateDevices({ [key]: value });
  };

  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'mobile': return Smartphone;
      case 'tablet': return Tablet;
      default: return Monitor;
    }
  };

  const getBrowserIcon = (_browser: string) => {
    // Usando Globe como ícone genérico para navegadores
    // pois lucide-react não tem ícones específicos de navegadores
    return Globe;
  };

  const handleLogoutDevice = (deviceId: string) => {
    // Implementar logout do dispositivo
    console.log('Logout device:', deviceId);
  };

  const handleLogoutAllDevices = () => {
    // Implementar logout de todos os dispositivos
    console.log('Logout all devices');
  };

  return (
    <div className="space-y-6">
      {/* Configurações Gerais */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Configurações de Dispositivos
          </CardTitle>
          <CardDescription>
            Configure como seus dispositivos interagem com a conta.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Lembrar Dispositivos</Label>
              <p className="text-sm text-muted-foreground">
                Manter dispositivos confiáveis conectados por mais tempo
              </p>
            </div>
            <Switch
              checked={localPrefs.remember_devices}
              onCheckedChange={(checked) => handleSwitchChange('remember_devices', checked)}
              disabled={isUpdating}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Logout Automático por Inatividade</Label>
              <p className="text-sm text-muted-foreground">
                Desconectar automaticamente dispositivos inativos
              </p>
            </div>
            <Switch
              checked={localPrefs.auto_logout_inactive}
              onCheckedChange={(checked) => handleSwitchChange('auto_logout_inactive', checked)}
              disabled={isUpdating}
            />
          </div>

          <div className="space-y-3">
            <Label>Máximo de Sessões Simultâneas</Label>
            <Select
              value={localPrefs.max_concurrent_sessions.toString()}
              onValueChange={(value) => handleSelectChange('max_concurrent_sessions', parseInt(value))}
              disabled={isUpdating}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1 dispositivo</SelectItem>
                <SelectItem value="3">3 dispositivos</SelectItem>
                <SelectItem value="5">5 dispositivos</SelectItem>
                <SelectItem value="10">10 dispositivos</SelectItem>
                <SelectItem value="999">Ilimitado</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              Número máximo de dispositivos que podem estar conectados simultaneamente
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Notificações de Dispositivos */}
      <Card>
        <CardHeader>
          <CardTitle>Notificações por Dispositivo</CardTitle>
          <CardDescription>
            Configure notificações específicas para cada tipo de dispositivo.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Smartphone className="h-4 w-4" />
                Notificações Mobile
              </Label>
              <p className="text-sm text-muted-foreground">
                Receber notificações push em dispositivos móveis
              </p>
            </div>
            <Switch
              checked={localPrefs.mobile_notifications}
              onCheckedChange={(checked) => handleSwitchChange('mobile_notifications', checked)}
              disabled={isUpdating}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Monitor className="h-4 w-4" />
                Notificações Desktop
              </Label>
              <p className="text-sm text-muted-foreground">
                Receber notificações no navegador desktop
              </p>
            </div>
            <Switch
              checked={localPrefs.desktop_notifications}
              onCheckedChange={(checked) => handleSwitchChange('desktop_notifications', checked)}
              disabled={isUpdating}
            />
          </div>
        </CardContent>
      </Card>

      {/* Sessões Ativas */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Sessões Ativas
              </CardTitle>
              <CardDescription>
                Gerencie todos os dispositivos conectados à sua conta.
              </CardDescription>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleLogoutAllDevices}
            >
              Desconectar Todos
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {sessions.map((session) => {
            const DeviceIcon = getDeviceIcon(session.device_type);
            const BrowserIcon = getBrowserIcon(session.browser);
            
            return (
              <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <DeviceIcon className="h-5 w-5 text-muted-foreground" />
                    <BrowserIcon className="h-4 w-4 text-muted-foreground" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">
                        {session.browser.charAt(0).toUpperCase() + session.browser.slice(1)} em {session.os}
                      </h4>
                      {session.is_current && (
                        <Badge variant="secondary">Atual</Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p>{session.location} • {session.ip_address}</p>
                      <p>Última atividade: {session.last_active}</p>
                    </div>
                  </div>
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {!session.is_current && (
                      <DropdownMenuItem 
                        onClick={() => handleLogoutDevice(session.id)}
                        className="text-destructive"
                      >
                        <LogOut className="h-4 w-4 mr-2" />
                        Desconectar
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem>
                      Ver Detalhes
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            );
          })}

          {sessions.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Smartphone className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>Nenhuma sessão ativa encontrada</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Alertas de Segurança */}
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          <strong>Dica de Segurança:</strong> Revise regularmente suas sessões ativas e desconecte 
          dispositivos que você não reconhece. Se você suspeitar de atividade não autorizada, 
          altere sua senha imediatamente.
        </AlertDescription>
      </Alert>
    </div>
  );
}
