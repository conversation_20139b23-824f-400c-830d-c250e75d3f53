import { Calendar, Clock, DollarSign, Globe, Hash } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useUserPreferences } from '@/hooks/useUserPreferences';
import type { LanguagePreferences } from '@/types';

export function LanguageSettings() {
  const { preferences, updateLanguage, isUpdating } = useUserPreferences();
  const [localPrefs, setLocalPrefs] = useState<LanguagePreferences>(
    preferences.language
  );

  const handleSelectChange = (key: keyof LanguagePreferences, value: string) => {
    const newPrefs = { ...localPrefs, [key]: value };
    setLocalPrefs(newPrefs);
    updateLanguage({ [key]: value });
  };

  const languages = [
    { 
      value: 'pt-BR', 
      label: 'Português (Brasil)', 
      flag: '🇧🇷',
      description: 'Idioma padrão do sistema'
    },
    { 
      value: 'en-US', 
      label: 'English (United States)', 
      flag: '🇺🇸',
      description: 'English language support'
    },
    { 
      value: 'es-ES', 
      label: 'Español (España)', 
      flag: '🇪🇸',
      description: 'Soporte en idioma español'
    },
  ];

  const timezones = [
    { value: 'America/Sao_Paulo', label: 'São Paulo (UTC-3)', region: 'Brasil' },
    { value: 'America/Rio_Branco', label: 'Rio Branco (UTC-5)', region: 'Brasil' },
    { value: 'America/Manaus', label: 'Manaus (UTC-4)', region: 'Brasil' },
    { value: 'America/Recife', label: 'Recife (UTC-3)', region: 'Brasil' },
    { value: 'America/New_York', label: 'New York (UTC-5)', region: 'EUA' },
    { value: 'Europe/London', label: 'London (UTC+0)', region: 'Europa' },
    { value: 'Europe/Madrid', label: 'Madrid (UTC+1)', region: 'Europa' },
  ];

  const dateFormats = [
    { value: 'DD/MM/YYYY', label: 'DD/MM/AAAA', example: '31/12/2024' },
    { value: 'MM/DD/YYYY', label: 'MM/DD/AAAA', example: '12/31/2024' },
    { value: 'YYYY-MM-DD', label: 'AAAA-MM-DD', example: '2024-12-31' },
  ];

  const timeFormats = [
    { value: '24h', label: '24 horas', example: '14:30' },
    { value: '12h', label: '12 horas', example: '2:30 PM' },
  ];

  const currencies = [
    { value: 'BRL', label: 'Real Brasileiro (R$)', symbol: 'R$' },
    { value: 'USD', label: 'Dólar Americano ($)', symbol: '$' },
    { value: 'EUR', label: 'Euro (€)', symbol: '€' },
  ];

  const numberFormats = [
    { value: 'pt-BR', label: 'Brasileiro', example: '1.234.567,89' },
    { value: 'en-US', label: 'Americano', example: '1,234,567.89' },
    { value: 'es-ES', label: 'Espanhol', example: '1.234.567,89' },
  ];

  const formatPreviewValue = (value: number) => {
    try {
      return new Intl.NumberFormat(localPrefs.number_format, {
        style: 'currency',
        currency: localPrefs.currency,
      }).format(value);
    } catch {
      return `${localPrefs.currency} ${value.toLocaleString()}`;
    }
  };

  const formatDatePreview = () => {
    const now = new Date();
    const format = localPrefs.date_format;
    
    if (format === 'DD/MM/YYYY') {
      return now.toLocaleDateString('pt-BR');
    } else if (format === 'MM/DD/YYYY') {
      return now.toLocaleDateString('en-US');
    } else {
      return now.toISOString().split('T')[0];
    }
  };

  const formatTimePreview = () => {
    const now = new Date();
    if (localPrefs.time_format === '12h') {
      return now.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true 
      });
    } else {
      return now.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Idioma */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Idioma da Interface
          </CardTitle>
          <CardDescription>
            Escolha o idioma principal da aplicação.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-3">
            {languages.map((language) => {
              const isSelected = localPrefs.language === language.value;
              
              return (
                <Button
                  key={language.value}
                  variant={isSelected ? "default" : "outline"}
                  className="h-auto p-4 justify-start"
                  onClick={() => handleSelectChange('language', language.value)}
                  disabled={isUpdating}
                >
                  <div className="flex items-center gap-3 w-full">
                    <span className="text-2xl">{language.flag}</span>
                    <div className="text-left flex-1">
                      <div className="font-medium">{language.label}</div>
                      <div className="text-sm text-muted-foreground">
                        {language.description}
                      </div>
                    </div>
                    {isSelected && (
                      <Badge variant="secondary">Ativo</Badge>
                    )}
                  </div>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Fuso Horário */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Fuso Horário
          </CardTitle>
          <CardDescription>
            Configure seu fuso horário para exibição correta de datas e horários.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <Label>Fuso Horário</Label>
            <Select
              value={localPrefs.timezone}
              onValueChange={(value) => handleSelectChange('timezone', value)}
              disabled={isUpdating}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {timezones.map((tz) => (
                  <SelectItem key={tz.value} value={tz.value}>
                    <div className="flex flex-col">
                      <span>{tz.label}</span>
                      <span className="text-xs text-muted-foreground">{tz.region}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Formatos de Data e Hora */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Formatos de Data e Hora
          </CardTitle>
          <CardDescription>
            Configure como datas e horários são exibidos.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <Label>Formato de Data</Label>
              <Select
                value={localPrefs.date_format}
                onValueChange={(value) => handleSelectChange('date_format', value)}
                disabled={isUpdating}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {dateFormats.map((format) => (
                    <SelectItem key={format.value} value={format.value}>
                      <div className="flex flex-col">
                        <span>{format.label}</span>
                        <span className="text-xs text-muted-foreground">{format.example}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label>Formato de Hora</Label>
              <Select
                value={localPrefs.time_format}
                onValueChange={(value) => handleSelectChange('time_format', value)}
                disabled={isUpdating}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timeFormats.map((format) => (
                    <SelectItem key={format.value} value={format.value}>
                      <div className="flex flex-col">
                        <span>{format.label}</span>
                        <span className="text-xs text-muted-foreground">{format.example}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Moeda e Números */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Moeda e Formatação Numérica
          </CardTitle>
          <CardDescription>
            Configure como valores monetários e números são exibidos.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <Label>Moeda</Label>
              <Select
                value={localPrefs.currency}
                onValueChange={(value) => handleSelectChange('currency', value)}
                disabled={isUpdating}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((currency) => (
                    <SelectItem key={currency.value} value={currency.value}>
                      <div className="flex items-center gap-2">
                        <span className="font-mono text-sm">{currency.symbol}</span>
                        <span>{currency.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                <Hash className="h-4 w-4" />
                Formato de Números
              </Label>
              <Select
                value={localPrefs.number_format}
                onValueChange={(value) => handleSelectChange('number_format', value)}
                disabled={isUpdating}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {numberFormats.map((format) => (
                    <SelectItem key={format.value} value={format.value}>
                      <div className="flex flex-col">
                        <span>{format.label}</span>
                        <span className="text-xs text-muted-foreground">{format.example}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Prévia */}
      <Card>
        <CardHeader>
          <CardTitle>Prévia das Configurações</CardTitle>
          <CardDescription>
            Veja como suas configurações regionais afetam a exibição de dados.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-3 bg-muted/50 rounded-lg text-center">
              <Label className="text-xs text-muted-foreground">Data</Label>
              <p className="font-mono text-sm mt-1">{formatDatePreview()}</p>
            </div>
            
            <div className="p-3 bg-muted/50 rounded-lg text-center">
              <Label className="text-xs text-muted-foreground">Hora</Label>
              <p className="font-mono text-sm mt-1">{formatTimePreview()}</p>
            </div>
            
            <div className="p-3 bg-muted/50 rounded-lg text-center">
              <Label className="text-xs text-muted-foreground">Moeda</Label>
              <p className="font-mono text-sm mt-1">{formatPreviewValue(1234567.89)}</p>
            </div>
            
            <div className="p-3 bg-muted/50 rounded-lg text-center">
              <Label className="text-xs text-muted-foreground">Número</Label>
              <p className="font-mono text-sm mt-1">
                {(1234567.89).toLocaleString(localPrefs.number_format)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
