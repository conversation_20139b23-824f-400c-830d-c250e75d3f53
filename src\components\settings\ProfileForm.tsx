import { <PERSON>ert<PERSON><PERSON>cle, Mail, Phone, User } from "lucide-react";
import { useEffect } from "react";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/auth";
import { FormProvider, useValidatedFormContext } from "@/contexts/FormContext";
import { supabase } from "@/integrations/supabase/client";
import { formatPhone } from "@/lib/formatters";
import { profileSchema } from "@/lib/validations";

interface ProfileData {
  firstName: string;
  lastName: string;
  email: string;
  telefone: string;
}

function ProfileFormContent() {
  const { user } = useAuth();
  const { handleSubmit, isLoading, form } = useValidatedFormContext<ProfileData>();
  
  // Atualiza o formulário quando o usuário for carregado
  useEffect(() => {
    if (user) {
      form.setValue('firstName', user.profile?.first_name || "");
      form.setValue('lastName', user.profile?.last_name || "");
      form.setValue('email', user.email || "");
      form.setValue('telefone', user.profile?.telefone || "");
    }
  }, [user, form]);

  const hasPhone = user?.profile?.telefone;

  return (
    <div className="space-y-4">
      {/* Alert se não tiver telefone */}
      {!hasPhone && (
        <Alert className="border-orange-200 bg-orange-50">
          <AlertCircle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            <strong>Telefone necessário:</strong> Para melhor comunicação e suporte ao cliente, 
            é importante cadastrar seu número de telefone.
          </AlertDescription>
        </Alert>
      )}
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Informações Pessoais
          </CardTitle>
          <CardDescription>
            Gerencie suas informações de contato e perfil.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <Label htmlFor="firstName">Nome</Label>
              <Input
                id="firstName"
                {...form.register('firstName')}
              />
              {form.formState.errors.firstName && (
                <p className="text-sm text-red-500">{form.formState.errors.firstName.message}</p>
              )}
            </div>
            <div className="space-y-3">
              <Label htmlFor="lastName">Sobrenome</Label>
              <Input
                id="lastName"
                {...form.register('lastName')}
              />
              {form.formState.errors.lastName && (
                <p className="text-sm text-red-500">{form.formState.errors.lastName.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-3">
            <Label htmlFor="email" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Email
            </Label>
            <Input
              id="email"
              {...form.register('email')}
              disabled
              className="bg-muted"
            />
            <p className="text-sm text-muted-foreground">
              O email não pode ser alterado.
            </p>
          </div>

          <div className="space-y-3">
            <Label htmlFor="telefone" className="flex items-center gap-2">
              <Phone className="h-4 w-4" />
              Telefone
              {!hasPhone && (
                <span className="text-orange-600 text-xs font-medium">
                  (Obrigatório)
                </span>
              )}
            </Label>
            <Input
              id="telefone"
              placeholder="(00) 00000-0000"
              {...form.register('telefone', {
                onChange: (e) => {
                  const formatted = formatPhone(e.target.value);
                  form.setValue('telefone', formatted);
                }
              })}
              className={!hasPhone ? "border-orange-300 focus:border-orange-500" : ""}
            />
            {form.formState.errors.telefone && (
              <p className="text-sm text-red-500">{form.formState.errors.telefone.message}</p>
            )}
            {!hasPhone && (
              <p className="text-sm text-orange-600">
                Necessário para comunicação e suporte ao cliente.
              </p>
            )}
          </div>
        </form>
      </CardContent>
        <CardFooter>
          <Button 
            onClick={handleSubmit} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? "Salvando..." : "Salvar Alterações"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

export function ProfileForm() {
  const { user } = useAuth();
  
  const handleSaveProfile = async (data: ProfileData) => {
    if (!user?.id) {
      throw new Error('Usuário não autenticado');
    }
    
    const updateData = {
      first_name: data.firstName,
      last_name: data.lastName,
      telefone: data.telefone
    };
    
    const { error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', user.id);
    
    if (error) {
      throw error;
    }
  };
  
  const defaultValues: ProfileData = {
    firstName: "",
    lastName: "",
    email: "",
    telefone: ""
  };
  
  return (
     <FormProvider
       schema={profileSchema}
       defaultValues={defaultValues}
       onSubmit={handleSaveProfile}
       successMessage="Perfil atualizado com sucesso!"
       errorMessage="Erro ao atualizar perfil"
     >
       <ProfileFormContent />
     </FormProvider>
   );
 }
