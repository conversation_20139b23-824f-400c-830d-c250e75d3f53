import { <PERSON>ert<PERSON>ircle, CheckCircle,Mail, Phone, User } from "lucide-react";
import { useEffect,useState } from "react";
import { toast } from "sonner";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { formatPhone } from "@/lib/formatters";

interface ProfileData {
  firstName: string;
  lastName: string;
  email: string;
  telefone: string;
}

export function ProfileFormSimple() {
  const { user } = useAuth();
  const [formData, setFormData] = useState<ProfileData>({
    firstName: "",
    lastName: "",
    email: "",
    telefone: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Partial<ProfileData>>({});
  const [justUpdated, setJustUpdated] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Carrega dados do usuário quando disponível
  useEffect(() => {
    if (user) {
      const newData = {
        firstName: user.profile?.first_name || "",
        lastName: user.profile?.last_name || "",
        email: user.email || "",
        telefone: user.profile?.telefone || ""
      };
      setFormData(newData);
      setHasChanges(false);
    }
  }, [user]);

  // Effect para esconder mensagem de atualização após 5 segundos
  useEffect(() => {
    if (justUpdated) {
      const timer = setTimeout(() => {
        setJustUpdated(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [justUpdated]);

  const hasPhone = user?.profile?.telefone;

  const validateForm = (): boolean => {
    const newErrors: Partial<ProfileData> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = "Nome é obrigatório";
    } else if (formData.firstName.trim().length < 2) {
      newErrors.firstName = "Nome deve ter pelo menos 2 caracteres";
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = "Sobrenome é obrigatório";
    } else if (formData.lastName.trim().length < 2) {
      newErrors.lastName = "Sobrenome deve ter pelo menos 2 caracteres";
    }

    if (!formData.telefone.trim()) {
      newErrors.telefone = "Telefone é obrigatório";
    } else {
      const phoneRegex = /^\(\d{2}\) \d{4,5}-\d{4}$/;
      if (!phoneRegex.test(formData.telefone)) {
        newErrors.telefone = "Telefone inválido (formato: (00) 00000-0000)";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof ProfileData) => (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    
    // Aplicar formatação no telefone
    if (field === 'telefone') {
      value = formatPhone(value);
    }

    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Marcar que há mudanças
    setHasChanges(true);
    
    // Esconder mensagem de atualização se estiver visível
    if (justUpdated) {
      setJustUpdated(false);
    }

    // Limpar erro do campo quando usuário começar a digitar
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!user?.id) {
      toast.error('Usuário não autenticado');
      return;
    }

    setIsLoading(true);

    try {
      const updateData = {
        first_name: formData.firstName.trim(),
        last_name: formData.lastName.trim(),
        telefone: formData.telefone.trim()
      };

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      toast.success('Perfil atualizado com sucesso!');
      setJustUpdated(true);
      setHasChanges(false);
      
      // Limpar cache do perfil e forçar recarga dos dados do usuário
      // Isso garantirá que o hook usePhoneReminder detecte o telefone atualizado
      setTimeout(() => {
        window.location.reload();
      }, 1500); // Dar tempo para o usuário ver a mensagem de sucesso
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      toast.error('Erro ao atualizar perfil. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Alert de sucesso - dados atualizados */}
      {justUpdated && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Dados atualizados com sucesso!</strong> Suas informações pessoais foram salvas.
          </AlertDescription>
        </Alert>
      )}

      {/* Alert se não tiver telefone */}
      {!hasPhone && !justUpdated && (
        <Alert className="border-orange-200 bg-orange-50">
          <AlertCircle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            <strong>Telefone necessário:</strong> Para melhor comunicação e suporte ao cliente, 
            é importante cadastrar seu número de telefone.
          </AlertDescription>
        </Alert>
      )}
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Informações Pessoais
          </CardTitle>
          <CardDescription>
            Gerencie suas informações de contato e perfil.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="firstName">Nome</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange('firstName')}
                  placeholder="Digite seu nome"
                />
                {errors.firstName && (
                  <p className="text-sm text-red-500">{errors.firstName}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Sobrenome</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange('lastName')}
                  placeholder="Digite seu sobrenome"
                />
                {errors.lastName && (
                  <p className="text-sm text-red-500">{errors.lastName}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Email
              </Label>
              <Input
                id="email"
                value={formData.email}
                disabled
                className="bg-muted"
              />
              <p className="text-sm text-muted-foreground">
                O email não pode ser alterado.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="telefone" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Telefone
                {!hasPhone && (
                  <span className="text-orange-600 text-xs font-medium">
                    (Obrigatório)
                  </span>
                )}
              </Label>
              <Input
                id="telefone"
                placeholder="(00) 00000-0000"
                value={formData.telefone}
                onChange={handleInputChange('telefone')}
                className={!hasPhone ? "border-orange-300 focus:border-orange-500" : ""}
              />
              {errors.telefone && (
                <p className="text-sm text-red-500">{errors.telefone}</p>
              )}
              {!hasPhone && (
                <p className="text-sm text-orange-600">
                  Necessário para comunicação e suporte ao cliente.
                </p>
              )}
            </div>
          </form>
        </CardContent>
        <CardFooter>
          <Button 
            onClick={handleSubmit} 
            disabled={isLoading || !hasChanges}
            className="w-full"
          >
            {isLoading ? "Salvando..." : hasChanges ? "Salvar Alterações" : "Dados Salvos ✓"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}