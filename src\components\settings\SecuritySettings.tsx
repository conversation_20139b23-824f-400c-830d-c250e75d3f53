import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Download, <PERSON>,Key, Shield, Smartphone } from 'lucide-react';
import { useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useUserPreferences } from '@/hooks/useUserPreferences';
import type { SecurityPreferences } from '@/types';

export function SecuritySettings() {
  const { preferences, updateSecurity, isUpdating } = useUserPreferences();
  const [localPrefs, setLocalPrefs] = useState<SecurityPreferences>(
    preferences.security
  );

  const handleSwitchChange = (key: keyof SecurityPreferences, value: boolean) => {
    const newPrefs = { ...localPrefs, [key]: value };
    setLocalPrefs(newPrefs);
    updateSecurity({ [key]: value });
  };

  const handleSelectChange = (key: keyof SecurityPreferences, value: string | number) => {
    const newPrefs = { ...localPrefs, [key]: value };
    setLocalPrefs(newPrefs);
    updateSecurity({ [key]: value });
  };

  const sessionTimeoutOptions = [
    { value: 30, label: '30 minutos' },
    { value: 60, label: '1 hora' },
    { value: 120, label: '2 horas' },
    { value: 240, label: '4 horas' },
    { value: 480, label: '8 horas' },
    { value: 720, label: '12 horas' },
    { value: 1440, label: '24 horas' },
  ];

  return (
    <div className="space-y-6">
      {/* Autenticação de Dois Fatores */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Autenticação de Dois Fatores (2FA)
          </CardTitle>
          <CardDescription>
            Adicione uma camada extra de segurança à sua conta.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                Ativar 2FA
                {localPrefs.two_factor_enabled && (
                  <Badge variant="secondary" className="ml-2">
                    Ativo
                  </Badge>
                )}
              </Label>
              <p className="text-sm text-muted-foreground">
                Exigir código adicional ao fazer login
              </p>
            </div>
            <Switch
              checked={localPrefs.two_factor_enabled}
              onCheckedChange={(checked) => handleSwitchChange('two_factor_enabled', checked)}
              disabled={isUpdating}
            />
          </div>

          {!localPrefs.two_factor_enabled && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Recomendamos ativar a autenticação de dois fatores para maior segurança da sua conta.
              </AlertDescription>
            </Alert>
          )}

          {localPrefs.two_factor_enabled && (
            <div className="p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Smartphone className="h-4 w-4" />
                <span className="font-medium">Configuração do 2FA</span>
              </div>
              <p className="text-sm text-muted-foreground mb-3">
                Use um aplicativo autenticador como Google Authenticator ou Authy.
              </p>
              <Button variant="outline" size="sm">
                Configurar Aplicativo
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sessões e Timeout */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Sessões e Timeout
          </CardTitle>
          <CardDescription>
            Configure o tempo limite de sessão e segurança de login.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <Label>Timeout de Sessão</Label>
            <Select
              value={localPrefs.session_timeout.toString()}
              onValueChange={(value) => handleSelectChange('session_timeout', parseInt(value))}
              disabled={isUpdating}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {sessionTimeoutOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              Tempo de inatividade antes de ser desconectado automaticamente
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Notificações de Segurança */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notificações de Segurança
          </CardTitle>
          <CardDescription>
            Configure alertas sobre atividades de segurança na sua conta.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Notificações de Login</Label>
              <p className="text-sm text-muted-foreground">
                Receber alerta quando alguém fizer login na sua conta
              </p>
            </div>
            <Switch
              checked={localPrefs.login_notifications}
              onCheckedChange={(checked) => handleSwitchChange('login_notifications', checked)}
              disabled={isUpdating}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Alertas de Atividade Suspeita</Label>
              <p className="text-sm text-muted-foreground">
                Notificar sobre tentativas de acesso não autorizadas
              </p>
            </div>
            <Switch
              checked={localPrefs.suspicious_activity_alerts}
              onCheckedChange={(checked) => handleSwitchChange('suspicious_activity_alerts', checked)}
              disabled={isUpdating}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Notificações de Mudança de Senha</Label>
              <p className="text-sm text-muted-foreground">
                Alertar quando a senha da conta for alterada
              </p>
            </div>
            <Switch
              checked={localPrefs.password_change_notifications}
              onCheckedChange={(checked) => handleSwitchChange('password_change_notifications', checked)}
              disabled={isUpdating}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Notificações de Novo Dispositivo</Label>
              <p className="text-sm text-muted-foreground">
                Alertar quando um novo dispositivo acessar a conta
              </p>
            </div>
            <Switch
              checked={localPrefs.device_login_notifications}
              onCheckedChange={(checked) => handleSwitchChange('device_login_notifications', checked)}
              disabled={isUpdating}
            />
          </div>
        </CardContent>
      </Card>

      {/* Privacidade e Dados */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Privacidade e Dados
          </CardTitle>
          <CardDescription>
            Configure como seus dados são tratados e compartilhados.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Notificações de Exportação de Dados</Label>
              <p className="text-sm text-muted-foreground">
                Alertar quando seus dados forem exportados
              </p>
            </div>
            <Switch
              checked={localPrefs.data_export_notifications}
              onCheckedChange={(checked) => handleSwitchChange('data_export_notifications', checked)}
              disabled={isUpdating}
            />
          </div>

          <div className="pt-4 border-t">
            <div className="flex items-center gap-2 mb-3">
              <Download className="h-4 w-4" />
              <span className="font-medium">Exportar Dados</span>
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              Baixe uma cópia de todos os seus dados armazenados no sistema.
            </p>
            <Button variant="outline" size="sm">
              Solicitar Exportação
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Histórico de Segurança */}
      <Card>
        <CardHeader>
          <CardTitle>Histórico de Segurança</CardTitle>
          <CardDescription>
            Visualize atividades recentes relacionadas à segurança.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium">Login bem-sucedido</p>
                  <p className="text-xs text-muted-foreground">Hoje às 14:30 - Chrome no Windows</p>
                </div>
              </div>
              <Badge variant="secondary">Atual</Badge>
            </div>

            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium">Senha alterada</p>
                  <p className="text-xs text-muted-foreground">Ontem às 09:15</p>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium">Login bem-sucedido</p>
                  <p className="text-xs text-muted-foreground">2 dias atrás - Safari no iPhone</p>
                </div>
              </div>
            </div>

            <Button variant="ghost" size="sm" className="w-full mt-3">
              Ver Histórico Completo
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
