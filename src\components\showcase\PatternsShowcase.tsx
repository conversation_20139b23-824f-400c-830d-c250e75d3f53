import React from 'react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const PatternsShowcase: React.FC = () => {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Padrões de Design</h1>
        <p className="text-muted-foreground">
          Showcase dos componentes e padrões de design utilizados no sistema ObrasAI
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Componentes UI</CardTitle>
            <CardDescription>
              Biblioteca de componentes baseada em shadcn/ui
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Componentes reutilizáveis para construção da interface do usuário.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Padrões de Layout</CardTitle>
            <CardDescription>
              Estruturas de layout consistentes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Layouts padronizados para diferentes seções do sistema.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Temas</CardTitle>
            <CardDescription>
              Sistema de temas claro/escuro
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Suporte completo a temas com alternância dinâmica.
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Em Desenvolvimento</CardTitle>
            <CardDescription>
              Esta página será expandida com exemplos práticos dos componentes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Futuramente, esta página conterá exemplos interativos de todos os componentes
              e padrões utilizados no sistema ObrasAI, facilitando o desenvolvimento e
              manutenção da interface.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PatternsShowcase;
