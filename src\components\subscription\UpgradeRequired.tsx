import { motion } from "framer-motion";
import { Crown, <PERSON>, Sparkles, Zap } from "lucide-react";
import { useNavigate } from "react-router-dom";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export const UpgradeRequired = () => {
  const navigate = useNavigate();

  const plans = [
    {
      name: "Pro",
      price: "R$ 97",
      period: "/mês",
      description: "Perfeito para construtoras em crescimento",
      features: [
        "IA ilimitada para chat e orçamentos",
        "Análise de contratos com IA",
        "Sistema SINAPI completo",
        "Analytics avançados",
        "Suporte prioritário",
        "Até 50 obras simultâneas",
      ],
      highlighted: true,
      planId: "pro",
    },
    {
      name: "Enterprise",
      price: "R$ 197",
      period: "/mês",
      description: "Para grandes construtoras e empreiteiras",
      features: [
        "Tudo do plano Pro",
        "Obras ilimitadas",
        "Usuários ilimitados",
        "API completa",
        "Suporte dedicado",
        "Customizações exclusivas",
      ],
      highlighted: false,
      planId: "enterprise",
    },
  ];

  const handleSelectPlan = (planId: string) => {
    navigate(`/subscription?plan=${planId}&source=trial_expired`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl w-full"
      >
        <div className="text-center mb-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="flex justify-center mb-4"
          >
            <div className="bg-orange-500 p-4 rounded-full">
              <Lock className="w-8 h-8 text-white" />
            </div>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-3xl font-bold text-gray-900 dark:text-white mb-2"
          >
            Seu trial de 7 dias expirou
          </motion.h1>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="text-gray-600 dark:text-gray-400 text-lg"
          >
            Continue aproveitando todas as funcionalidades de IA do ObrasVision
          </motion.p>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.planId}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 + index * 0.1 }}
            >
              <Card
                className={`relative h-full ${
                  plan.highlighted
                    ? "border-2 border-orange-500 shadow-xl scale-105"
                    : "border border-gray-200 dark:border-gray-700"
                }`}
              >
                {plan.highlighted && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-orange-500 text-white px-3 py-1">
                      <Sparkles className="w-3 h-3 mr-1" />
                      Mais Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-6">
                  <CardTitle className="text-2xl font-bold">
                    {plan.name}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {plan.description}
                  </CardDescription>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-orange-500">
                      {plan.price}
                    </span>
                    <span className="text-gray-500">{plan.period}</span>
                  </div>
                </CardHeader>

                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, featureIndex) => (
                      <li
                        key={featureIndex}
                        className="flex items-center gap-3"
                      >
                        <div className="bg-green-500 rounded-full p-1">
                          <Zap className="w-3 h-3 text-white" />
                        </div>
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    onClick={() => handleSelectPlan(plan.planId)}
                    className={`w-full ${
                      plan.highlighted
                        ? "bg-orange-500 hover:bg-orange-600 text-white"
                        : "bg-gray-900 dark:bg-white dark:text-gray-900 hover:bg-gray-800 dark:hover:bg-gray-100 text-white"
                    }`}
                    size="lg"
                  >
                    <Crown className="w-4 h-4 mr-2" />
                    Escolher {plan.name}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="text-center mt-8"
        >
          <p className="text-sm text-gray-500">
            ✅ Sem compromisso • ✅ Cancele quando quiser • ✅ Suporte 24/7
          </p>
        </motion.div>
      </motion.div>
    </div>
  );
};