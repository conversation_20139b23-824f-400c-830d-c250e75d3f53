// ============================================================================
// COMPONENTE: NotificationIndicator - INDICADOR DE NOTIFICAÇÕES
// ============================================================================
// Componente para exibir notificações no header do dashboard
// Integração: useNotifications + Realtime + Dropdown menu
// ============================================================================

import { formatDistanceToNow } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  AlertCircle,
  AlertTriangle,
  Bell,
  BellRing,
  Check,
  CheckCheck,
  Info,
  Settings,
  X} from "lucide-react";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import type { Notification, NotificationPriority, NotificationType } from "@/hooks/useNotifications";
import { useNotifications } from "@/hooks/useNotifications";
import { cn } from "@/lib/utils";

// ============================================================================
// CONFIGURAÇÕES DE TIPOS
// ============================================================================

const priorityIcons: Record<NotificationPriority, React.ComponentType<{ className?: string }>> = {
  'low': Info,
  'medium': AlertCircle,
  'high': AlertTriangle,
  'urgent': AlertTriangle
};

const priorityColors: Record<NotificationPriority, string> = {
  'low': 'text-blue-500',
  'medium': 'text-yellow-500',
  'high': 'text-orange-500',
  'urgent': 'text-red-500'
};

const typeEmojis: Record<NotificationType, string> = {
  'obra_prazo_vencendo': '⏰',
  'obra_orcamento_excedido': '💰',
  'obra_status_alterado': '📊',
  'novo_lead_capturado': '🎯',
  'contrato_assinado': '📝',
  'contrato_vencendo': '📅',
  'ia_analise_pronta': '🤖',
  'ia_orcamento_gerado': '💡',
  'sinapi_atualizado': '📋',
  'sistema_manutencao': '🔧',
  'pagamento_vencendo': '💳',
  'pagamento_processado': '✅'
};

// ============================================================================
// COMPONENTE INDIVIDUAL DE NOTIFICAÇÃO
// ============================================================================

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
}

function NotificationItem({ notification, onMarkAsRead, onDelete }: NotificationItemProps) {
  const PriorityIcon = priorityIcons[notification.priority];
  const emoji = typeEmojis[notification.type];
  
  const formattedTime = formatDistanceToNow(new Date(notification.created_at), {
    addSuffix: true,
    locale: ptBR
  });

  return (
    <div className={cn(
      "p-3 rounded-lg border transition-all",
      notification.is_read 
        ? "bg-muted/30 border-border/50" 
        : "bg-background border-border shadow-sm"
    )}>
      <div className="flex items-start gap-3">
        {/* Emoji e ícone de prioridade */}
        <div className="flex-shrink-0 relative">
          <span className="text-lg">{emoji}</span>
          <PriorityIcon className={cn(
            "h-3 w-3 absolute -top-1 -right-1",
            priorityColors[notification.priority]
          )} />
        </div>

        {/* Conteúdo */}
        <div className="flex-1 space-y-1">
          <div className="flex items-start justify-between">
            <h4 className={cn(
              "text-sm font-medium leading-tight",
              notification.is_read ? "text-muted-foreground" : "text-foreground"
            )}>
              {notification.title}
            </h4>
            <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">
              {formattedTime}
            </span>
          </div>
          
          <p className={cn(
            "text-xs leading-relaxed",
            notification.is_read ? "text-muted-foreground/80" : "text-muted-foreground"
          )}>
            {notification.message}
          </p>

          {/* Badges para contexto */}
          {notification.context_type && (
            <div className="flex items-center gap-1 mt-2">
              <Badge variant="outline" className="text-xs">
                {notification.context_type}
              </Badge>
              {notification.priority === 'urgent' && (
                <Badge variant="destructive" className="text-xs">
                  Urgente
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Ações */}
        <div className="flex items-center gap-1">
          {!notification.is_read && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:bg-accent"
              onClick={() => onMarkAsRead(notification.id)}
              title="Marcar como lida"
            >
              <Check className="h-3 w-3" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive"
            onClick={() => onDelete(notification.id)}
            title="Remover notificação"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export function NotificationIndicator() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    isMarkingAsRead: _isMarkingAsRead,
    isMarkingAllAsRead,
    isDeletingNotification: _isDeletingNotification
  } = useNotifications();

  const [isOpen, setIsOpen] = useState(false);

  // Limitar notificações exibidas (últimas 20)
  const recentNotifications = notifications.slice(0, 20);
  const hasNotifications = recentNotifications.length > 0;

  const handleMarkAsRead = async (id: string) => {
    try {
      await markAsRead(id);
    } catch (error) {
      console.error('Erro ao marcar como lida:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteNotification(id);
    } catch (error) {
      console.error('Erro ao deletar notificação:', error);
    }
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="relative h-9 w-9 p-0"
          aria-label={`Notificações${unreadCount > 0 ? ` (${unreadCount} não lidas)` : ''}`}
        >
          {unreadCount > 0 ? (
            <BellRing className="h-4 w-4" />
          ) : (
            <Bell className="h-4 w-4" />
          )}
          
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs font-bold animate-pulse"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="end"
        side="bottom"
        className="w-80 p-0"
        sideOffset={8}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="space-y-1">
            <h3 className="font-semibold text-sm">Notificações</h3>
            {unreadCount > 0 && (
              <p className="text-xs text-muted-foreground">
                {unreadCount} não {unreadCount === 1 ? 'lida' : 'lidas'}
              </p>
            )}
          </div>
          
          <div className="flex items-center gap-1">
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={handleMarkAllAsRead}
                disabled={isMarkingAllAsRead}
              >
                <CheckCheck className="h-3 w-3 mr-1" />
                Marcar todas
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0"
              onClick={() => setIsOpen(false)}
            >
              <Settings className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {/* Lista de notificações */}
        {hasNotifications ? (
          <ScrollArea className="max-h-96">
            <div className="p-2 space-y-2">
              {recentNotifications.map((notification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={handleMarkAsRead}
                  onDelete={handleDelete}
                />
              ))}
            </div>
          </ScrollArea>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 px-4 text-center">
            <Bell className="h-8 w-8 text-muted-foreground/50 mb-2" />
            <p className="text-sm font-medium text-muted-foreground mb-1">
              Nenhuma notificação
            </p>
            <p className="text-xs text-muted-foreground">
              Você receberá notificações sobre obras, leads e contratos aqui
            </p>
          </div>
        )}

        {/* Footer */}
        {hasNotifications && (
          <>
            <Separator />
            <div className="p-2">
              <Button
                variant="ghost"
                size="sm"
                className="w-full h-8 text-xs"
                onClick={() => {
                  setIsOpen(false);
                  // Navegar para página de configurações
                  window.location.href = '/dashboard/configuracoes';
                }}
              >
                <Settings className="h-3 w-3 mr-2" />
                Configurar notificações
              </Button>
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// ============================================================================
// COMPONENTE SIMPLIFICADO PARA MOBILE
// ============================================================================

export function NotificationIndicatorMobile() {
  const { unreadCount } = useNotifications();

  return (
    <Button
      variant="ghost"
      size="sm"
      className="relative h-9 w-9 p-0"
      asChild
    >
      <a href="/dashboard/notificacoes" aria-label={`${unreadCount} notificações não lidas`}>
        {unreadCount > 0 ? (
          <BellRing className="h-4 w-4" />
        ) : (
          <Bell className="h-4 w-4" />
        )}
        
        {unreadCount > 0 && (
          <Badge
            variant="destructive"
            className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs font-bold"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </a>
    </Button>
  );
}