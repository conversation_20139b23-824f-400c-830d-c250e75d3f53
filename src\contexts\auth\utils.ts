import type { User } from "@supabase/supabase-js";

import { supabase } from "@/integrations/supabase/client";

import type { Subscription } from "./types";

// Cache simples para dados de perfil - usando unknown ao invés de any
const profileCache = new Map<string, unknown>();

// Função para limpar cache quando necessário
export const clearProfileCache = (userId?: string) => {
  if (userId) {
    profileCache.delete(userId);
  } else {
    profileCache.clear();
  }
};

// Fetch user profile data with RLS recursion handling
export const fetchUserProfile = async (userId: string, currentUser: User) => {
  // Verificar cache primeiro
  if (profileCache.has(userId)) {
    const cachedProfile = profileCache.get(userId);
    return { ...currentUser, profile: cachedProfile };
  }

  const { data: profile, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", userId)
    .single();

  if (error) {
    console.error("Error fetching profile for user:", userId, error);
    // Em caso de erro, retorna o usuário sem perfil, mas não quebra a aplicação.
    // O erro será logado, e o contexto de autenticação pode decidir como lidar com um usuário sem perfil.
    return { ...currentUser, profile: null };
  }

  if (profile) {
    profileCache.set(userId, profile);
    
    // ✅ DETECTAR NOVOS USUÁRIOS E CRIAR TRIAL AUTOMÁTICO
    // Verificar se é um usuário novo que ainda não tem subscription
    try {
      const { data: existingSubscription } = await supabase
        .from('subscriptions')
        .select('id')
        .eq('user_id', userId)
        .single();

      // Se não existe subscription, criar trial automático
      if (!existingSubscription) {
        const userCreatedAt = new Date(currentUser.created_at);
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000); // 5 minutos atrás
        
        // Se o usuário foi criado recentemente (últimos 5 minutos), 
        // provavelmente é novo (via OAuth ou registro)
        if (userCreatedAt > fiveMinutesAgo) {
          console.log('🚀 Criando trial automático para novo usuário:', userId);
          
          const trialEndDate = new Date();
          trialEndDate.setDate(trialEndDate.getDate() + 14); // 14 dias de trial
          
          const { error: subscriptionError } = await supabase
            .from('subscriptions')
            .insert({
              user_id: userId,
              plan_type: 'pro', // Durante trial, acesso às funcionalidades Pro
              status: 'trialing',
              current_period_start: new Date().toISOString(),
              current_period_end: trialEndDate.toISOString(),
              stripe_customer_id: null,
              stripe_subscription_id: null
            });

          if (subscriptionError) {
            console.error("❌ Falha ao criar trial automático:", subscriptionError);
          } else {
            console.log("✅ Trial automático criado com sucesso!", { 
              userId,
              trialEndDate: trialEndDate.toISOString()
            });
          }
        }
      }
    } catch (trialError) {
      console.error("❌ Exceção ao criar trial automático:", trialError);
      // Não falhar o carregamento do perfil por causa disso
    }

    return {
      ...currentUser,
      profile,
    };
  }

  // Se não encontrar perfil, retorna o usuário sem ele.
  return { ...currentUser, profile: null };
};

// Função auxiliar para validar tenant_id - usando unknown ao invés de any
export const validateTenantId = (tenantId: unknown): string | null => {
  // Se for null ou undefined, retorna null
  if (!tenantId) return null;

  // Se for string válida, retorna limpa
  if (typeof tenantId === "string" && tenantId.trim().length > 0) {
    return tenantId.trim();
  }

  // Se for objeto, tenta converter para string se tiver propriedades válidas
  if (typeof tenantId === "object" && tenantId !== null) {
    // Se for um objeto com id, usa o id
    const objWithId = tenantId as { id?: unknown };
    if ("id" in tenantId && typeof objWithId.id === "string") {
      return objWithId.id.trim();
    }

    // Se for um objeto que pode ser serializado para UUID
    const str = tenantId.toString();
    if (str !== "[object Object]" && str.length > 0) {
      return str.trim();
    }
  }

  // Se for qualquer outro tipo, tenta converter para string
  if (tenantId.toString && typeof tenantId.toString === "function") {
    const str = tenantId.toString();
    if (str !== "[object Object]" && str.length > 0) {
      return str.trim();
    }
  }

  // Se nada funcionar, retorna null
  console.warn("❌ tenant_id inválido:", tenantId);
  return null;
};

// Fetch user subscription data
export const fetchUserSubscription = async (
  userId: string,
): Promise<Subscription | null> => {
  try {
    const { data, error } = await supabase
      .from("subscriptions")
      .select("id, status, product_id, price_id, current_period_end")
      .eq("user_id", userId)
      .eq("status", "active")
      .limit(1);

    if (error) {
      // Se a tabela não existir ou não tiver políticas RLS configuradas
      if (error.code === "PGRST301" || error.code === "42P01") {
        return null;
      }

      throw error;
    }

    // Retornar o primeiro item se existir, garantindo que tenha a estrutura correta
    const rawSubscription = data?.[0] as Record<string, unknown>;

    if (!rawSubscription) {
      return null;
    }

    // Mapear os dados para o tipo Subscription esperado
    const subscription: Subscription = {
      id: rawSubscription.id as string,
      status: (rawSubscription.status as string) || "inactive",
      product_id: rawSubscription.product_id as string | undefined,
      price_id: rawSubscription.price_id as string | undefined,
      current_period_end: rawSubscription.current_period_end
        ? new Date(rawSubscription.current_period_end as string)
        : undefined,
    };

    return subscription;
  } catch (_error) {
    return null;
  }
};
