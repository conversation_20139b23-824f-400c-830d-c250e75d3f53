import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { useObrasCondominio } from '../useObrasCondominio';

// Mock dependencies
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    rpc: vi.fn(),
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          eq: vi.fn(() => ({
            eq: vi.fn(() => ({
              order: vi.fn(() => ({
                data: [],
                error: null,
              })),
              single: vi.fn(() => ({
                data: null,
                error: null,
              })),
            })),
          })),
        })),
      })),
    })),
  },
}));

vi.mock('@/lib/secure-logger', () => ({
  secureLogger: {
    error: vi.fn(),
    info: vi.fn(),
  },
}));

vi.mock('../useTenantValidation', () => ({
  useTenantValidation: () => ({
    validTenantId: 'test-tenant-id',
  }),
}));

vi.mock('../useObras', () => ({
  useObras: () => ({
    obras: [],
    isLoading: false,
    error: null,
    createObra: vi.fn(),
    updateObra: vi.fn(),
    deleteObra: vi.fn(),
    getObraById: vi.fn(),
    tenantId: 'test-tenant-id',
    hasValidTenant: true,
  }),
}));

vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useObrasCondominio', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with base obras functionality', () => {
    const { result } = renderHook(() => useObrasCondominio(), {
      wrapper: createWrapper(),
    });

    expect(result.current.obras).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.validTenantId).toBe('test-tenant-id');
  });

  it('should provide condominio-specific methods', () => {
    const { result } = renderHook(() => useObrasCondominio(), {
      wrapper: createWrapper(),
    });

    expect(typeof result.current.createCondominio).toBe('function');
    expect(typeof result.current.createCondominioAsync).toBe('function');
    expect(typeof result.current.useUnidadesCondominio).toBe('function');
    expect(typeof result.current.useCondominioDashboard).toBe('function');
    expect(typeof result.current.getUnidadesCondominio).toBe('function');
    expect(typeof result.current.getCondominioDashboard).toBe('function');
    expect(typeof result.current.invalidateCondominioQueries).toBe('function');
  });

  it('should have correct initial state for condominio operations', () => {
    const { result } = renderHook(() => useObrasCondominio(), {
      wrapper: createWrapper(),
    });

    expect(result.current.isCreatingCondominio).toBe(false);
    expect(result.current.createCondominioError).toBe(null);
  });

  it('should provide hook factory functions', () => {
    const { result } = renderHook(() => useObrasCondominio(), {
      wrapper: createWrapper(),
    });

    expect(typeof result.current.useUnidadesCondominio).toBe('function');
    expect(typeof result.current.useCondominioDashboard).toBe('function');
  });
});