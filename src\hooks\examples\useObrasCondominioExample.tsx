/**
 * Exemplo de uso do hook useObrasCondominio
 *
 * Este arquivo demonstra como usar o hook customizado para gerenciar
 * obras de condomínio com todas as funcionalidades específicas.
 */

import type { QueryResult } from "@tanstack/react-query";
import React from "react";

import type { CreateCondominioRequestValues } from "@/lib/validations/condominio";
import type {
  CondominioDashboard,
  UnidadeCondominio,
} from "@/types/condominio";
import type { Obra } from "@/types/obra";

import { useObrasCondominio } from "../useObrasCondominio";

// Exemplo de componente que usa o hook
export const CondominioManager: React.FC = () => {
  const {
    // Funcionalidades herdadas do useObras
    obras,
    isLoading,
    error,

    // Funcionalidades específicas de condomínio
    createCondominio,
    isCreatingCondominio,
    createCondominioError,
    useUnidadesCondominio,
    useCondominioDashboard,
  } = useObrasCondominio();

  // Exemplo de dados para criar um condomínio
  const handleCreateCondominio = () => {
    const condominioData: CreateCondominioRequestValues = {
      condominio_data: {
        nome: "Residencial Exemplo",
        endereco: "Rua das Flores, 123",
        cidade: "São Paulo",
        estado: "SP",
        cep: "01234-567",
        construtora_id: "construtora-uuid-here",
        orcamento: 2000000,
        area_total: 5000,
        data_inicio: new Date(),
        data_prevista_termino: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 ano
      },
      unidades_data: [
        {
          identificador_unidade: "A101",
          nome: "Apartamento 101 - Bloco A",
          area_total: 80,
          orcamento: 300000,
        },
        {
          identificador_unidade: "A102",
          nome: "Apartamento 102 - Bloco A",
          area_total: 85,
          orcamento: 320000,
        },
        {
          identificador_unidade: "B101",
          nome: "Apartamento 101 - Bloco B",
          area_total: 90,
          orcamento: 350000,
        },
      ],
    };

    createCondominio(condominioData);
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Gerenciador de Condomínios</h1>

      {/* Seção de criação */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Criar Novo Condomínio</h2>
        <button
          onClick={handleCreateCondominio}
          disabled={isCreatingCondominio}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {isCreatingCondominio ? "Criando..." : "Criar Condomínio Exemplo"}
        </button>
        {createCondominioError && (
          <p className="text-red-500 mt-2">
            Erro: {createCondominioError.message}
          </p>
        )}
      </div>

      {/* Lista de obras */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Obras Cadastradas</h2>
        {isLoading ? (
          <p>Carregando obras...</p>
        ) : error ? (
          <p className="text-red-500">
            Erro ao carregar obras: {error.message}
          </p>
        ) : (
          <div className="grid gap-4">
            {obras?.map((obra) => (
              <ObraCard
                key={obra.id}
                obra={obra}
                useUnidadesCondominio={useUnidadesCondominio}
                useCondominioDashboard={useCondominioDashboard}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Componente para exibir cada obra
interface ObraCardProps {
  obra: Obra;
  useUnidadesCondominio: (
    condominioId: string,
    enabled?: boolean
  ) => QueryResult<UnidadeCondominio[]>;
  useCondominioDashboard: (
    condominioId: string,
    enabled?: boolean
  ) => QueryResult<CondominioDashboard>;
}

const ObraCard: React.FC<ObraCardProps> = ({
  obra,
  useUnidadesCondominio,
  useCondominioDashboard,
}) => {
  const isCondominio = obra.tipo_projeto === "CONDOMINIO_MASTER";

  // Usar hooks condicionalmente apenas para condomínios
  const unidadesQuery = useUnidadesCondominio(obra.id, isCondominio);
  const dashboardQuery = useCondominioDashboard(obra.id, isCondominio);

  return (
    <div className="border rounded-lg p-4 bg-white shadow">
      <div className="flex justify-between items-start mb-2">
        <h3 className="text-lg font-semibold">{obra.nome}</h3>
        <span
          className={`px-2 py-1 rounded text-sm ${
            obra.tipo_projeto === "CONDOMINIO_MASTER"
              ? "bg-blue-100 text-blue-800"
              : obra.tipo_projeto === "UNIDADE_CONDOMINIO"
              ? "bg-green-100 text-green-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {obra.tipo_projeto === "CONDOMINIO_MASTER" && "Condomínio"}
          {obra.tipo_projeto === "UNIDADE_CONDOMINIO" && "Unidade"}
          {obra.tipo_projeto === "UNICO" && "Obra Única"}
        </span>
      </div>

      <p className="text-gray-600 mb-2">{obra.endereco}</p>
      <p className="text-sm text-gray-500 mb-4">
        Status: {obra.status} | Orçamento: R${" "}
        {obra.orcamento_total?.toLocaleString()}
      </p>

      {/* Informações específicas para condomínios */}
      {isCondominio && (
        <div className="mt-4 p-3 bg-gray-50 rounded">
          <h4 className="font-medium mb-2">Dashboard do Condomínio</h4>
          {dashboardQuery.isLoading ? (
            <p className="text-sm text-gray-500">Carregando dashboard...</p>
          ) : dashboardQuery.error ? (
            <p className="text-sm text-red-500">Erro ao carregar dashboard</p>
          ) : dashboardQuery.data ? (
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Total de Unidades:</span>
                <span className="ml-2">
                  {dashboardQuery.data.estatisticas.total_unidades}
                </span>
              </div>
              <div>
                <span className="font-medium">Progresso Médio:</span>
                <span className="ml-2">
                  {dashboardQuery.data.estatisticas.progresso_medio}%
                </span>
              </div>
              <div>
                <span className="font-medium">Concluídas:</span>
                <span className="ml-2">
                  {dashboardQuery.data.estatisticas.unidades_concluidas}
                </span>
              </div>
              <div>
                <span className="font-medium">Em Andamento:</span>
                <span className="ml-2">
                  {dashboardQuery.data.estatisticas.unidades_em_andamento}
                </span>
              </div>
            </div>
          ) : null}

          {/* Lista de unidades */}
          <div className="mt-3">
            <h5 className="font-medium mb-2">Unidades:</h5>
            {unidadesQuery.isLoading ? (
              <p className="text-sm text-gray-500">Carregando unidades...</p>
            ) : unidadesQuery.error ? (
              <p className="text-sm text-red-500">Erro ao carregar unidades</p>
            ) : unidadesQuery.data ? (
              <div className="space-y-1">
                {unidadesQuery.data.map((unidade: UnidadeCondominio) => (
                  <div
                    key={unidade.id}
                    className="text-sm flex justify-between"
                  >
                    <span>
                      {unidade.identificador_unidade} - {unidade.nome}
                    </span>
                    <span className="text-gray-500">{unidade.status}</span>
                  </div>
                ))}
              </div>
            ) : null}
          </div>
        </div>
      )}

      {/* Informações para unidades de condomínio */}
      {obra.tipo_projeto === "UNIDADE_CONDOMINIO" && (
        <div className="mt-4 p-3 bg-green-50 rounded">
          <p className="text-sm">
            <span className="font-medium">Identificador:</span>{" "}
            {obra.identificador_unidade}
          </p>
          <p className="text-sm">
            <span className="font-medium">Condomínio:</span>{" "}
            {obra.parent_obra_id}
          </p>
        </div>
      )}
    </div>
  );
};

export default CondominioManager;
