import { useCEPOperation } from "./useAsyncOperation";

interface CEPData {
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localidade: string;
  uf: string;
  ibge: string;
  gia: string;
  ddd: string;
  siafi: string;
}

interface CEPResponse {
  valido: boolean;
  dados?: CEPData;
  formatado?: string;
  erro?: string;
  tipo?: string;
}

export const useCEP = () => {
  const asyncOperation = useCEPOperation();

  /**
   * Busca informações de endereço pelo CEP
   * @param cep - CEP a ser consultado (pode estar formatado ou não)
   * @returns Dados do endereço ou null em caso de erro
   */
  const buscarCEP = async (cep: string): Promise<CEPData | null> => {
    if (!cep || cep.length < 8) {
      throw new Error("CEP deve ter pelo menos 8 dígitos");
    }

    return await asyncOperation.execute(async () => {
      const cepLimpo = cep.replace(/\D/g, "");

      // Usa API pública do ViaCEP
      const response = await fetch(
        `https://viacep.com.br/ws/${cepLimpo}/json/`
      );

      if (!response.ok) {
        throw new Error("Erro ao consultar CEP. Tente novamente.");
      }

      const data = await response.json();

      if (data.erro) {
        throw new Error("CEP não encontrado");
      }

      // Converte para o formato esperado
      return {
        cep: data.cep,
        logradouro: data.logradouro,
        bairro: data.bairro,
        localidade: data.localidade,
        uf: data.uf,
        complemento: data.complemento,
        ibge: data.ibge,
        gia: data.gia,
        ddd: data.ddd,
        siafi: data.siafi,
      };
    });
  };

  /**
   * Formata CEP para o padrão 00000-000
   * @param cep - CEP sem formatação
   * @returns CEP formatado
   */
  const formatarCEP = (cep: string): string => {
    const cepLimpo = cep.replace(/\D/g, "");
    if (cepLimpo.length === 8) {
      return cepLimpo.replace(/(\d{5})(\d{3})/, "$1-$2");
    }
    return cep;
  };

  /**
   * Remove formatação do CEP
   * @param cep - CEP formatado
   * @returns CEP apenas com números
   */
  const limparCEP = (cep: string): string => {
    return cep.replace(/\D/g, "");
  };

  return {
    buscarCEP,
    formatarCEP,
    limparCEP,
    isLoading: asyncOperation.isLoading,
    error: asyncOperation.error,
    data: asyncOperation.data,
    reset: asyncOperation.reset,
  };
};
