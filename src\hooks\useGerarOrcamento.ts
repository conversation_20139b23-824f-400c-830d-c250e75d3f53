import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

export interface GerarOrcamentoResponse {
  success: boolean;
  message: string;
  dados: {
    obra_id: string;
    obra_nome: string;
    area_total: number;
    cub_utilizado: number;
    valor_orcamento_parametrico: number;
    valor_formatado: string;
  };
}

export const useGerarOrcamento = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (obra_id: string): Promise<GerarOrcamentoResponse> => {
      console.log("🔍 Iniciando geração de orçamento para obra:", obra_id);
      
      // Verificar se há uma sessão ativa
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      console.log("🔐 Estado da sessão:", {
        hasSession: !!session,
        hasAccessToken: !!session?.access_token,
        userId: session?.user?.id,
        sessionError: sessionError
      });
      
      if (!session?.access_token) {
        console.error("❌ Usuário não autenticado - sem access_token");
        throw new Error("Usuário não autenticado");
      }

      console.log("📡 Chamando Edge Function com token...");
      
      // Incluir explicitamente o header Authorization
      const { data, error } = await supabase.functions.invoke("gerar-orcamento-parametrico", {
        body: { obra_id },
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      console.log("📨 Resposta da Edge Function:", { data, error });

      if (error) {
        console.error("❌ Error calling gerar-orcamento-parametrico:", error);
        throw new Error(error.message || "Erro ao gerar orçamento paramétrico");
      }

      if (!data?.success) {
        console.error("❌ Edge Function retornou erro:", data?.error);
        throw new Error(data?.error || "Erro ao gerar orçamento paramétrico");
      }

      console.log("✅ Orçamento gerado com sucesso:", data);
      return data;
    },
    onError: (error) => {
      console.error("GerarOrcamento error:", error);
      toast({
        title: "Erro ao gerar orçamento",
        description: error.message || "Erro ao gerar orçamento paramétrico. Tente novamente.",
        variant: "destructive",
      });
    },
    onSuccess: (data) => {
      console.log("✅ Orçamento gerado com sucesso - dados recebidos:", data);
      
      toast({
        title: "Orçamento gerado com sucesso",
        description: `Orçamento paramétrico: ${data.dados.valor_formatado}`,
      });
      
      // Invalidar e refetch agressivo para garantir atualização
      const invalidatePromises = [
        queryClient.invalidateQueries({ queryKey: ['obras'] }),
        queryClient.invalidateQueries({ queryKey: ['obra', data.dados.obra_id] }),
        queryClient.invalidateQueries({ queryKey: ['orcamentos'] }),
        queryClient.invalidateQueries({ queryKey: ['orcamentos-parametricos'] }),
      ];

      // Aguardar invalidações e forçar refetch
      Promise.all(invalidatePromises).then(() => {
        // Refetch imediato e forçado
        queryClient.refetchQueries({ 
          queryKey: ['obras'],
          type: 'active'
        });
        
        // Aguardar um pouco e refetch novamente para garantir
        setTimeout(() => {
          queryClient.refetchQueries({ queryKey: ['obras'] });
          console.log("🔄 Refetch secundário executado");
        }, 500);
      });
      
      console.log("🔄 Invalidações e refetch iniciados");
    },
  });

  return {
    gerarOrcamento: mutation.mutate,
    isLoading: mutation.isPending,
    error: mutation.error,
    data: mutation.data,
    isSuccess: mutation.isSuccess,
  };
};