import { obrasApi } from '@/services/api';

import { useCrudOperations } from './useCrudOperations';

/**
 * Hook customizado para gerenciamento de obras multi-tenant.
 * <PERSON>ca, cria, edita e deleta obras do tenant logado.
 * 
 * Refatorado para usar useCrudOperations - elimina duplicação de código.
 */
export const useObras = () => {
  const obrasApiCrud = {
    getAll: obrasApi.getAll,  // ✅ FILTRADO: Apenas obras-mãe e condomínios
    getById: obrasApi.getById,  // ✅ IMPLEMENTADO CORRETAMENTE
    create: obrasApi.create,
    update: obrasApi.update,
    delete: obrasApi.delete,
  };

  const {
    data: obras,
    isLoading,
    error,
    createMutation: createObra,
    updateMutation: updateObra,
    deleteMutation: deleteObra,
    validTenantId: tenantId,
  } = useCrudOperations(obrasApiCrud, {
    resource: 'obras',
    messages: {
      createSuccess: 'Obra criada com sucesso!',
      updateSuccess: 'Obra atualizada com sucesso!',
      deleteSuccess: 'Obra excluída com sucesso!',
      createError: 'Erro ao criar obra',
      updateError: 'Erro ao atualizar obra',
      deleteError: 'Erro ao excluir obra',
    },
  });

  // Debug removido - logs limpos

  // 🛡️ SEGURANÇA: Função helper para buscar obra por ID com tenant
  const getObraById = async (obraId: string) => {
    if (!tenantId) {
      throw new Error("Tenant não autenticado");
    }
    return await obrasApi.getById(obraId, tenantId);
  };

  // 🏗️ CONDOMÍNIO: Função para buscar todas as obras incluindo unidades
  const getAllObrasIncludingUnits = async () => {
    if (!tenantId) {
      throw new Error("Tenant não autenticado");
    }
    return await obrasApi.getAllIncludingUnits(tenantId);
  };

  return {
    obras,  // ✅ FILTRADO: Apenas obras-mãe e condomínios
    isLoading,
    error,
    createObra,
    updateObra,
    deleteObra,
    getObraById,  // ✅ NOVA FUNÇÃO SEGURA
    getAllObrasIncludingUnits,  // ✅ FUNÇÃO PARA BUSCAR TODAS INCLUINDO UNIDADES
    // Informações úteis para debug
    tenantId,
    hasValidTenant: !!tenantId,
  };
};