import {
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

import { supabase } from "@/integrations/supabase/client";
import { queryKeys } from "@/lib/query-keys";
import { secureLogger } from "@/lib/secure-logger";
import type { CreateCondominioRequestValues } from "@/lib/validations/condominio";
import type { CondomínioDashboardResponse } from "@/types/api";
import type { Database } from "@/types/supabase";

import { useObras } from "./useObras";
import { useTenantValidation } from "./useTenantValidation";

// Tipos para as respostas da API
type Obra = Database["public"]["Tables"]["obras"]["Row"];

interface CreateCondominioResponse {
  obra_mae: Obra;
  unidades: Obra[];
  success: boolean;
  message: string;
}

interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface PaginatedUnidadesResponse {
  data: Obra[];
  pagination: PaginationInfo;
}

interface GetUnidadesPaginadasParams {
  condominioId: string;
  page?: number;
  pageSize?: number;
  search?: string;
  statusFilter?: string;
}

/**
 * Hook customizado para gerenciamento de obras de condomínio.
 * Estende funcionalidades do useObras com métodos específicos para condomínios.
 *
 * Funcionalidades:
 * - Criação transacional de condomínios com múltiplas unidades
 * - Busca de unidades de um condomínio específico
 * - Dashboard agregado com estatísticas do condomínio
 * - Integração com TanStack Query para cache e invalidação
 */
export const useObrasCondominio = () => {
  const { validTenantId } = useTenantValidation();
  const queryClient = useQueryClient();

  // Herdar funcionalidades básicas do useObras
  const obrasBase = useObras();

  /**
   * Função para criar condomínio usando RPC function
   */
  const createCondominioRPC = async (
    data: CreateCondominioRequestValues
  ): Promise<CreateCondominioResponse> => {
    if (!validTenantId) {
      throw new Error("Tenant ID não encontrado");
    }

    try {
      // Chamar a RPC function create_condominio_project
      const { data: result, error } = await supabase.rpc(
        "create_condominio_project",
        {
          condominio_data: {
            ...data.condominio_data,
            tenant_id: validTenantId,
          },
          unidades_data: data.unidades_data,
        }
      );

      if (error) {
        secureLogger.error("Failed to create condominio via RPC", error, {
          tenantId: validTenantId,
        });
        throw error;
      }

      secureLogger.info("Condominio created successfully via RPC", {
        condominioId: result?.obra_mae?.id,
        totalUnidades: result?.unidades?.length || 0,
        tenantId: validTenantId,
      });

      return result;
    } catch (error) {
      secureLogger.error("Error in createCondominioRPC", error, {
        tenantId: validTenantId,
      });
      throw error;
    }
  };

  /**
   * Buscar unidades de um condomínio específico (versão legada - carrega todas)
   */
  const getUnidadesCondominio = async (
    condominioId: string
  ): Promise<Obra[]> => {
    if (!validTenantId) {
      throw new Error("Tenant ID não encontrado");
    }

    try {
      const { data, error } = await supabase
        .from("obras")
        .select("*")
        .eq("parent_obra_id", condominioId)
        .eq("tenant_id", validTenantId)
        .eq("tipo_projeto", "UNIDADE_CONDOMINIO")
        .order("identificador_unidade", { ascending: true });

      if (error) {
        secureLogger.error("Failed to fetch unidades do condominio", error, {
          condominioId,
          tenantId: validTenantId,
        });
        throw error;
      }

      return data || [];
    } catch (error) {
      secureLogger.error("Error in getUnidadesCondominio", error, {
        condominioId,
        tenantId: validTenantId,
      });
      throw error;
    }
  };

  /**
   * Buscar unidades paginadas usando Edge Function com fallback para RPC
   */
  const getUnidadesPaginadas = async (
    params: GetUnidadesPaginadasParams
  ): Promise<PaginatedUnidadesResponse> => {
    if (!validTenantId) {
      throw new Error("Tenant ID não encontrado");
    }

    try {
      // Tentar Edge Function primeiro
      const { data, error } = await supabase.functions.invoke(
        "get-condominio-units-paginated",
        {
          body: {
            condominioId: params.condominioId,
            page: params.page || 1,
            pageSize: params.pageSize || 50,
            search: params.search || null,
            statusFilter: params.statusFilter || null,
          },
        }
      );

      if (!error && data && !data.error) {
        secureLogger.info("Unidades paginadas carregadas via Edge Function", {
          condominioId: params.condominioId,
          page: data.pagination.page,
          total: data.pagination.total,
          tenantId: validTenantId,
        });
        return data as PaginatedUnidadesResponse;
      }

      // Fallback para RPC function
      secureLogger.warn("Edge Function falhou, usando RPC fallback", {
        edgeError: error,
        condominioId: params.condominioId,
      });

      const { data: rpcData, error: rpcError } = await supabase.rpc(
        "get_unidades_paginadas",
        {
          p_condominio_id: params.condominioId,
          p_page: params.page || 1,
          p_page_size: params.pageSize || 50,
          p_search: params.search || null,
          p_status_filter: params.statusFilter || null,
        }
      );

      if (rpcError) {
        secureLogger.error("RPC fallback também falhou", rpcError, {
          condominioId: params.condominioId,
          tenantId: validTenantId,
        });
        throw rpcError;
      }

      secureLogger.info("Unidades paginadas carregadas via RPC fallback", {
        condominioId: params.condominioId,
        page: rpcData.pagination.page,
        total: rpcData.pagination.total,
        tenantId: validTenantId,
      });

      return rpcData as PaginatedUnidadesResponse;
    } catch (error) {
      secureLogger.error("Error in getUnidadesPaginadas", error, {
        condominioId: params.condominioId,
        tenantId: validTenantId,
      });
      throw error;
    }
  };

  /**
   * Buscar dados agregados do dashboard do condomínio (otimizada)
   */
  const getCondominioDashboard = async (
    condominioId: string
  ): Promise<CondomínioDashboardResponse> => {
    if (!validTenantId) {
      throw new Error("Tenant ID não encontrado");
    }

    secureLogger.info("Tentando buscar dashboard do condomínio otimizado", {
      condominioId,
      tenantId: validTenantId,
    });

    try {
      // Usar função RPC otimizada primeiro
      const { data, error } = await supabase.rpc(
        "get_condominio_dashboard_optimized",
        {
          p_condominio_id: condominioId,
          p_tenant_id: validTenantId,
        }
      );

      if (error) {
        secureLogger.warn(
          "Erro na função RPC otimizada, tentando função original:",
          {
            error: error.message,
            code: error.code,
          }
        );

        // Fallback para função original
        const { data: fallbackData, error: fallbackError } = await supabase.rpc(
          "get_condominio_dashboard",
          {
            p_obra_id: condominioId,
          }
        );

        if (fallbackError) {
          throw fallbackError;
        }

        return fallbackData as CondomínioDashboardResponse;
      }

      if (data?.error) {
        throw new Error(data.error);
      }

      secureLogger.info(
        "Dashboard do condomínio carregado com sucesso via RPC otimizada",
        {
          condominioId,
          dataReceived: !!data,
          tenantId: validTenantId,
        }
      );

      // Mapear dados da função otimizada para formato esperado
      const mappedData: CondomínioDashboardResponse = {
        estatisticas: {
          total_unidades: data.estatisticas.total_unidades,
          progresso_medio: data.estatisticas.progresso_medio,
          custo_total:
            data.estatisticas.custo_total || data.condominio.orcamento, // Investimento total
          custo_terreno:
            data.estatisticas.custo_terreno || data.condominio.custo_terreno, // Custo do terreno
          capital_construcao: data.estatisticas.capital_construcao, // Capital para construção
          custo_parametrico: data.condominio.valor_orcamento_parametrico, // Custo estimado da obra
          unidades_concluidas: data.estatisticas.unidades_concluidas || 0,
        },
        unidades: [], // Dashboard otimizado não retorna unidades, apenas estatísticas
      };

      return mappedData;
    } catch (_rpcError) {
      // Fallback: buscar dados usando queries diretas
      secureLogger.info("Usando fallback para buscar dashboard do condomínio", {
        condominioId,
        tenantId: validTenantId,
      });

      // Verificar se a obra é um condomínio master
      const { data: masterData, error: masterError } = await supabase
        .from("obras")
        .select("*")
        .eq("id", condominioId)
        .eq("tipo_projeto", "CONDOMINIO_MASTER")
        .eq("tenant_id", validTenantId)
        .single();

      if (masterError || !masterData) {
        throw new Error("Obra não encontrada ou não é um condomínio master");
      }

      // Buscar unidades do condomínio
      const { data: unidadesData, error: unidadesError } = await supabase
        .from("obras")
        .select(
          "id, identificador_unidade, nome, orcamento, data_inicio, data_prevista_termino"
        )
        .eq("parent_obra_id", condominioId)
        .eq("tipo_projeto", "UNIDADE_CONDOMINIO")
        .eq("tenant_id", validTenantId);

      if (unidadesError) {
        throw unidadesError;
      }

      const unidades = unidadesData || [];

      // Buscar despesas do condomínio master para cálculo híbrido
      const { data: despesasMaster } = await supabase
        .from("despesas")
        .select("custo")
        .eq("obra_id", condominioId)
        .eq("tenant_id", validTenantId);

      const totalGastoMaster =
        despesasMaster?.reduce((sum, d) => sum + (d.custo || 0), 0) || 0;
      const orcamentoMaster = masterData.orcamento || 0;
      const progressoFinanceiroMaster =
        orcamentoMaster > 0 ? (totalGastoMaster / orcamentoMaster) * 100 : 0;

      // Calcular progresso híbrido baseado em status, datas E despesas do master
      const getProgressoHibrido = (
        dataInicio: string | null,
        dataPrevista: string | null,
        status?: string,
        orcamentoUnidade?: number
      ) => {
        const hoje = new Date();
        const inicio = dataInicio ? new Date(dataInicio) : null;
        const fim = dataPrevista ? new Date(dataPrevista) : null;

        // 1. Progresso por status (peso 40%)
        let progressoStatus = 0;
        if (status === "concluida") progressoStatus = 100;
        else if (status === "em_andamento") progressoStatus = 60;
        else if (status === "pausada") progressoStatus = 30;
        else if (status === "planejamento") progressoStatus = 10;

        // 2. Progresso por datas (peso 30%)
        let progressoDatas = 0;
        if (!inicio) progressoDatas = 0;
        else if (inicio > hoje) progressoDatas = 5;
        else if (fim && fim < hoje) progressoDatas = 100;
        else if (inicio && fim) {
          const duracaoTotal = fim.getTime() - inicio.getTime();
          const tempoDecorrido = hoje.getTime() - inicio.getTime();
          progressoDatas = Math.max(
            0,
            Math.min(100, (tempoDecorrido / duracaoTotal) * 100)
          );
        } else progressoDatas = 50;

        // 3. Progresso financeiro do master (peso 30%)
        // Considera que o progresso financeiro do master se aplica proporcionalmente às unidades
        const progressoFinanceiro = Math.min(progressoFinanceiroMaster, 100);

        // Calcular progresso híbrido ponderado
        const progressoHibrido =
          progressoStatus * 0.4 +
          progressoDatas * 0.3 +
          progressoFinanceiro * 0.3;

        return Math.round(Math.max(0, Math.min(100, progressoHibrido)));
      };

      // Calcular estatísticas
      const totalUnidades = unidades.length;
      const progressos = unidades.map((u) =>
        getProgressoHibrido(
          u.data_inicio,
          u.data_prevista_termino,
          u.status,
          u.orcamento
        )
      );
      const progressoMedio =
        totalUnidades > 0
          ? progressos.reduce((acc, p) => acc + p, 0) / totalUnidades
          : 0;
      // Usar orçamento do condomínio master em vez da soma das unidades
      const custoTotal = masterData.orcamento || 0;
      const unidadesConcluidas = progressos.filter((p) => p >= 95).length; // Considerar 95%+ como concluída

      const result: CondomínioDashboardResponse = {
        estatisticas: {
          total_unidades: totalUnidades,
          progresso_medio: progressoMedio,
          custo_total: custoTotal, // Investimento disponível
          custo_parametrico: masterData.valor_orcamento_parametrico, // Custo estimado da obra
          unidades_concluidas: unidadesConcluidas,
        },
        unidades: unidades.map((u, index) => ({
          id: u.id,
          identificador_unidade: u.identificador_unidade,
          nome: u.nome,
          progresso: progressos[index],
        })),
      };

      secureLogger.info(
        "Dashboard do condomínio carregado com sucesso via fallback",
        {
          condominioId,
          totalUnidades,
          tenantId: validTenantId,
        }
      );

      return result;
    }
  };

  // Query para buscar unidades de um condomínio (versão legada)
  const useUnidadesCondominio = (condominioId: string, enabled = true) => {
    return useQuery({
      queryKey: queryKeys.obrasUnidades(condominioId, validTenantId || ""),
      queryFn: () => getUnidadesCondominio(condominioId),
      enabled: enabled && !!validTenantId && !!condominioId,
      staleTime: 5 * 60 * 1000, // 5 minutos
      gcTime: 10 * 60 * 1000, // 10 minutos
    });
  };

  // Hook com useInfiniteQuery para paginação de unidades (otimizado para grandes condomínios)
  const useUnidadesPaginadas = (
    condominioId: string,
    options: {
      pageSize?: number;
      search?: string;
      statusFilter?: string;
      enabled?: boolean;
    } = {}
  ) => {
    const { pageSize = 50, search, statusFilter, enabled = true } = options;

    return useInfiniteQuery({
      queryKey: [
        "unidades-paginadas",
        condominioId,
        validTenantId,
        pageSize,
        search,
        statusFilter,
      ],
      queryFn: ({ pageParam = 1 }) =>
        getUnidadesPaginadas({
          condominioId,
          page: pageParam,
          pageSize,
          search,
          statusFilter,
        }),
      enabled: enabled && !!validTenantId && !!condominioId,
      getNextPageParam: (lastPage) => {
        return lastPage.pagination.hasNextPage
          ? lastPage.pagination.page + 1
          : undefined;
      },
      initialPageParam: 1,
      staleTime: 5 * 60 * 1000, // 5 minutos
      gcTime: 10 * 60 * 1000, // 10 minutos
    });
  };

  // Hook simples para buscar unidades paginadas (uma página por vez)
  const useUnidadesPaginadasSimple = (
    condominioId: string,
    options: {
      page?: number;
      pageSize?: number;
      search?: string;
      statusFilter?: string;
      enabled?: boolean;
    } = {}
  ) => {
    const {
      page = 1,
      pageSize = 50,
      search,
      statusFilter,
      enabled = true,
    } = options;

    return useQuery({
      queryKey: [
        "unidades-paginadas-simple",
        condominioId,
        validTenantId,
        page,
        pageSize,
        search,
        statusFilter,
      ],
      queryFn: () =>
        getUnidadesPaginadas({
          condominioId,
          page,
          pageSize,
          search,
          statusFilter,
        }),
      enabled: enabled && !!validTenantId && !!condominioId,
      staleTime: 5 * 60 * 1000, // 5 minutos
      gcTime: 10 * 60 * 1000, // 10 minutos
    });
  };

  // Query para dashboard do condomínio
  const useCondominioDashboard = (condominioId: string, enabled = true) => {
    return useQuery({
      queryKey: queryKeys.condominioDashboard(
        condominioId,
        validTenantId || ""
      ),
      queryFn: () => getCondominioDashboard(condominioId),
      enabled: enabled && !!validTenantId && !!condominioId,
      staleTime: 2 * 60 * 1000, // 2 minutos
      gcTime: 5 * 60 * 1000, // 5 minutos
    });
  };

  // Mutation para criar condomínio
  const createCondominioMutation = useMutation({
    mutationFn: createCondominioRPC,
    onSuccess: (data) => {
      toast.success(
        `Condomínio "${data.obra_mae.nome}" criado com sucesso! ${data.unidades.length} unidades adicionadas.`
      );

      // Invalidar queries relacionadas
      if (validTenantId) {
        queryClient.invalidateQueries({
          queryKey: queryKeys.obras(validTenantId),
        });
        queryClient.invalidateQueries({
          queryKey: queryKeys.metricas(validTenantId),
        });
      }
    },
    onError: (error) => {
      console.error("Error creating condominio:", error);
      toast.error("Erro ao criar condomínio. Tente novamente.");
    },
  });

  // Função para invalidar queries específicas de condomínio
  const invalidateCondominioQueries = (condominioId?: string) => {
    if (!validTenantId) return;

    if (condominioId) {
      // Invalidar queries específicas do condomínio
      queryClient.invalidateQueries({
        queryKey: queryKeys.obrasUnidades(condominioId, validTenantId),
      });
      queryClient.invalidateQueries({
        queryKey: queryKeys.condominioDashboard(condominioId, validTenantId),
      });
    }

    // Invalidar lista geral de obras
    queryClient.invalidateQueries({
      queryKey: queryKeys.obras(validTenantId),
    });
  };

  return {
    // Herdar funcionalidades básicas
    ...obrasBase,

    // Funcionalidades específicas de condomínio
    createCondominio: createCondominioMutation.mutate,
    createCondominioAsync: createCondominioMutation.mutateAsync,
    isCreatingCondominio: createCondominioMutation.isPending,
    createCondominioError: createCondominioMutation.error,

    // Hooks para queries específicas
    useUnidadesCondominio,
    useCondominioDashboard,

    // Hooks otimizados para grandes condomínios
    useUnidadesPaginadas,
    useUnidadesPaginadasSimple,

    // Funções utilitárias
    getUnidadesCondominio,
    getUnidadesPaginadas,
    getCondominioDashboard,
    invalidateCondominioQueries,

    // Invalidar cache do dashboard
    invalidateDashboardCache: (condominioId: string) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.condominioDashboard(condominioId, validTenantId),
      });
    },

    // Informações do tenant
    validTenantId,
  };
};
