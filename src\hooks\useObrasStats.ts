/**
 * 🏗️ Hook para Estatísticas de Obras - ObrasAI
 * 
 * Hook personalizado para buscar estatísticas reais das obras
 * Substitui dados hardcoded por dados dinâmicos do banco
 */

import { useCallback, useEffect, useState } from 'react';

import { useAuth } from '@/contexts/auth/hooks';
import { useTenantValidation } from '@/hooks/useTenantValidation';
import { supabase } from '@/integrations/supabase/client';

interface CategoriaStats {
  name: string;
  count: number;
  status: string;
}

interface ObrasStats {
  totalObras: number;
  obrasEmAndamento: number;
  obrasConcluidas: number;
  obrasParadas: number;
  valorTotalInvestido: number;
  categorias: CategoriaStats[];
  loading: boolean;
  error: string | null;
}

export const useObrasStats = (): ObrasStats => {
  const { user } = useAuth();
  const { tenantId } = useTenantValidation();
  const [stats, setStats] = useState<ObrasStats>({
    totalObras: 0,
    obrasEmAndamento: 0,
    obrasConcluidas: 0,
    obrasParadas: 0,
    valorTotalInvestido: 0,
    categorias: [],
    loading: true,
    error: null,
  });

  const fetchStats = useCallback(async () => {
      if (!user || !tenantId) {
        setStats(prev => ({ ...prev, loading: false, error: 'Usuário não autenticado' }));
        return;
      }

      try {
        setStats(prev => ({ ...prev, loading: true, error: null }));

        // Buscar todas as obras do tenant
        const { data: obras, error: obrasError } = await supabase
          .from('obras')
          .select('*')
          .eq('tenant_id', tenantId);

        if (obrasError) {
          throw new Error(`Erro ao buscar obras: ${obrasError.message}`);
        }

        if (!obras) {
          setStats(prev => ({ ...prev, loading: false }));
          return;
        }

        // Calcular estatísticas
        const totalObras = obras.length;
        const valorTotalInvestido = obras.reduce((sum, obra) => {
          const valor = obra.orcamento_total || obra.orcamento || 0;
          return sum + Number(valor);
        }, 0);

        // Categorizar obras por status
        const obrasEmAndamento = obras.filter(obra => {
          if (!obra.data_inicio) return false;
          if (obra.data_fim) return false;
          if (obra.data_prevista_termino) {
            return new Date(obra.data_prevista_termino) > new Date();
          }
          return true;
        }).length;

        const obrasConcluidas = obras.filter(obra => {
          return obra.data_fim || (obra.status && obra.status.toLowerCase().includes('conclu'));
        }).length;

        const obrasParadas = obras.filter(obra => {
          return obra.status && (
            obra.status.toLowerCase().includes('parad') ||
            obra.status.toLowerCase().includes('suspend') ||
            obra.status.toLowerCase().includes('cancel')
          );
        }).length;

        // Agrupar por status para categorias
        const statusCount: { [key: string]: number } = {};
        obras.forEach(obra => {
          const status = obra.status || 'Não definido';
          statusCount[status] = (statusCount[status] || 0) + 1;
        });

        const categorias: CategoriaStats[] = Object.entries(statusCount)
          .map(([status, count]) => ({
            name: status,
            count,
            status
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5); // Top 5 status

        setStats({
          totalObras,
          obrasEmAndamento,
          obrasConcluidas,
          obrasParadas,
          valorTotalInvestido,
          categorias,
          loading: false,
          error: null,
        });

      } catch (error) {
        console.error('Erro ao buscar estatísticas de obras:', error);
        setStats(prev => ({
          ...prev,
          loading: false,
          error: error instanceof Error ? error.message : 'Erro desconhecido'
        }));
      }
    }, [user, tenantId]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return stats;
};
