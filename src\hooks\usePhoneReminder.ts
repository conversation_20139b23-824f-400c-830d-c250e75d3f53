import { Phone } from 'lucide-react';
import { createElement,useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

import { useAuth } from '@/contexts/auth/hooks';

interface PhoneReminderData {
  lastDate: string;
  count: number;
}

const STORAGE_KEY = 'phone_reminder_data';
const MAX_REMINDERS_PER_DAY = 3;
const REMINDER_INTERVALS = [
  5 * 60 * 1000,    // 5 minutos após login
  2 * 60 * 60 * 1000, // 2 horas após login
  6 * 60 * 60 * 1000, // 6 horas após login
];

/**
 * Hook para gerenciar lembretes de telefone
 * Mostra toasts para usuários sem telefone cadastrado
 */
export function usePhoneReminder() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [hasShownReminder, setHasShownReminder] = useState(false);

  const hasPhone = Boolean(user?.profile?.telefone?.trim());
  const shouldShowReminder = user && !hasPhone && !hasShownReminder;

  const showPhoneReminderToast = () => {
    toast('Telefone necessário para comunicação', {
      description: 'Para melhor suporte ao cliente, cadastre seu telefone nas configurações.',
      icon: createElement(Phone, { className: "h-4 w-4 text-orange-600" }),
      action: {
        label: 'Configurar',
        onClick: () => navigate('/settings'),
      },
      duration: 8000,
      className: 'border-orange-200',
    });
  };

  const getReminderData = (): PhoneReminderData | null => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  };

  const saveReminderData = (data: PhoneReminderData) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    } catch {
      // Silently fail if localStorage is not available
    }
  };

  const shouldShowReminderToday = (): boolean => {
    const today = new Date().toDateString();
    const reminderData = getReminderData();

    if (!reminderData) {
      return true; // First time
    }

    if (reminderData.lastDate !== today) {
      return true; // New day
    }

    return reminderData.count < MAX_REMINDERS_PER_DAY;
  };

  const updateReminderCount = () => {
    const today = new Date().toDateString();
    const reminderData = getReminderData();

    if (!reminderData || reminderData.lastDate !== today) {
      // First reminder of the day
      saveReminderData({
        lastDate: today,
        count: 1,
      });
    } else {
      // Increment count for today
      saveReminderData({
        lastDate: today,
        count: reminderData.count + 1,
      });
    }
  };

  const scheduleReminders = () => {
    if (!shouldShowReminder) return;

    // Schedule reminders at different intervals
    REMINDER_INTERVALS.forEach((interval, _index) => {
      const timeoutId = setTimeout(() => {
        if (shouldShowReminderToday()) {
          showPhoneReminderToast();
          updateReminderCount();
        }
      }, interval);

      // Store timeout IDs to clear them if needed
      return () => clearTimeout(timeoutId);
    });
  };

  // Effect para resetar quando telefone for adicionado
  useEffect(() => {
    if (hasPhone) {
      // Se telefone foi adicionado, resetar estado de lembretes
      setHasShownReminder(false);
      // Opcional: limpar dados de lembrete no localStorage
      // localStorage.removeItem(STORAGE_KEY);
    }
  }, [hasPhone]);

  // Effect para iniciar lembretes quando usuário logar
  useEffect(() => {
    if (shouldShowReminder && shouldShowReminderToday()) {
      // Primeiro lembrete após 5 minutos
      const timeoutId = setTimeout(() => {
        showPhoneReminderToast();
        updateReminderCount();
        setHasShownReminder(true);
      }, 5 * 60 * 1000);

      return () => clearTimeout(timeoutId);
    }
  }, [shouldShowReminder]);

  // Effect para agendar lembretes adicionais
  useEffect(() => {
    if (shouldShowReminder) {
      const cleanupFunctions = scheduleReminders();
      return () => {
        if (Array.isArray(cleanupFunctions)) {
          cleanupFunctions.forEach(cleanup => cleanup());
        }
      };
    }
  }, [shouldShowReminder]);

  // Função para mostrar lembrete manualmente
  const showReminderNow = () => {
    if (shouldShowReminderToday()) {
      showPhoneReminderToast();
      updateReminderCount();
    }
  };

  // Função para resetar contadores (para testes)
  const resetReminderData = () => {
    localStorage.removeItem(STORAGE_KEY);
  };

  return {
    hasPhone,
    shouldShowReminder,
    showReminderNow,
    resetReminderData,
    reminderData: getReminderData(),
  };
}