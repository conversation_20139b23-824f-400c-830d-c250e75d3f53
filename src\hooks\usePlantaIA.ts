import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";

export interface PlantaAnalysisResult {
  areas: Array<{
    comodo: string;
    area: number;
    unidade: string;
  }>;
  dimensoes: Array<{
    tipo: string;
    valor: number;
    unidade: string;
  }>;
  materiais: string[];
  componentes: {
    portas: number;
    janelas: number;
    escadas: boolean;
  };
  observacoes: string[];
  estimativa_m2: number;
  area_total: number;
  escala_detectada?: string;
  extracted_text?: string;
}

export interface PlantaAnalysisData {
  dados_estruturados: {
    areas?: Array<{
      comodo: string;
      area: number;
      unidade: string;
    }>;
    dimensoes?: Array<{
      tipo: string;
      valor: number;
      unidade: string;
    }>;
    componentes?: {
      portas: number;
      janelas: number;
      escadas: boolean;
    };
    materiais?: string[];
    area_total_construida: string | number;
    numero_quartos: string | number;
    numero_banheiros: string | number;
    outros_comodos: string[];
    pavimentos: string | number;
  };
  resumo_analise: string;
  extracted_text?: string;
}

export interface PlantaAnalysisResponse {
  success: boolean;
  url_planta: string;
  analise: PlantaAnalysisData;
  error?: string;
  details?: string;
}

export const usePlantaIA = () => {
  const { toast } = useToast();
  const { user: _user } = useAuth();
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async (file: File): Promise<PlantaAnalysisResponse> => {
      // Validações antes do envio
      if (!file) {
        throw new Error('Arquivo é obrigatório');
      }

      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        throw new Error('Tipo de arquivo não suportado. Use PDF, JPEG, PNG ou WebP.');
      }

      const maxSize = 10 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        throw new Error('Arquivo muito grande. Máximo 10MB.');
      }

      // Preparar FormData
      const formData = new FormData();
      formData.append("file", file);
      formData.append("nome_projeto", file.name.replace(/\.[^/.]+$/, ''));

      console.log('Enviando arquivo para análise:', {
        name: file.name,
        size: file.size,
        type: file.type
      });

      const { data, error } = await supabase.functions.invoke(
        "analise-planta-ia",
        {
          body: formData,
        },
      );

      if (error) {
        console.error("Error calling analise-planta-ia:", error);
        throw new Error(error.message || "Erro ao analisar planta");
      }

      if (!data || !data.success) {
        console.error("Analysis failed:", data);
        throw new Error(data?.error || data?.details || "Erro na análise da planta");
      }

      console.log('Análise concluída com sucesso:', data);
      return data;
    },
    onError: (error) => {
      console.error("PlantaIA error:", error);
      toast({
        title: "Erro na análise",
        description: error.message ||
          "Erro ao analisar a planta. Tente novamente.",
        variant: "destructive",
      });
    },
    onSuccess: (data) => {
      const analise = data.analise?.dados_estruturados;
      const areaTotal = analise?.area_total_construida || 0;
      const quartos = analise?.numero_quartos || 0;
      const banheiros = analise?.numero_banheiros || 0;

      toast({
        title: "Análise concluída com sucesso!",
        description: `Área: ${areaTotal}m² | Quartos: ${quartos} | Banheiros: ${banheiros}`,
      });
      
      queryClient.invalidateQueries({ queryKey: ["plantas-analisadas"] });
      queryClient.invalidateQueries({ queryKey: ["plantaIAMetrics"] });
    },
  });

  // Função para testar se a Edge Function está funcionando
  const testarFuncao = async (): Promise<boolean> => {
    try {
      const { data, error } = await supabase.functions.invoke(
        "analise-planta-ia",
        {
          body: { test: true },
        },
      );

      if (error) {
        console.error("Erro ao testar função:", error);
        toast({
          title: "Erro no teste",
          description: "Função não está funcionando corretamente",
          variant: "destructive",
        });
        return false;
      }

      if (data.success) {
        toast({
          title: "Teste bem-sucedido",
          description: "Função de análise está funcionando!",
        });
        return true;
      } else {
        toast({
          title: "Teste falhou",
          description: "Função não retornou sucesso",
          variant: "destructive",
        });
        return false;
      }
    } catch (error) {
      console.error("Erro no teste:", error);
      toast({
        title: "Erro no teste",
        description: "Erro ao conectar com a função",
        variant: "destructive",
      });
      return false;
    }
  };

  return {
    analisarPlanta: mutation.mutate,
    testarFuncao,
    isLoading: mutation.isPending,
    error: mutation.error,
    data: mutation.data,
    isSuccess: mutation.isSuccess,
    reset: mutation.reset,
  };
};
