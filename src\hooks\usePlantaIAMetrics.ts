import { useQuery } from '@tanstack/react-query';

import { supabase } from '@/integrations/supabase/client';

export interface PeriodMetrics {
  totalPlantas: number;
  obrasGeradas: number;
  areaTotal: number;
  valorTotalEstimado: number;
}

interface MetricsData {
  currentPeriod: PeriodMetrics;
  previousPeriod: PeriodMetrics;
}

const getPlantaIAMetrics = async (period: '7d' | '30d' | '90d'): Promise<MetricsData> => {
  const { data, error } = await supabase.functions.invoke('get-planta-ia-metrics', {
    body: { period },
  });

  if (error) {
    throw new Error(error.message);
  }

  return data;
};

export const usePlantaIAMetrics = (period: '7d' | '30d' | '90d') => {
  return useQuery<MetricsData, Error>({
    queryKey: ['plantaIAMetrics', period],
    queryFn: () => getPlantaIAMetrics(period),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};
