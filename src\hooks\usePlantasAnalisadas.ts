import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";

export interface PlantaAnalisada {
  id: string;
  usuario_id: string;
  tenant_id: string;
  obra_id?: string;
  nome_projeto: string;
  nome_arquivo: string;
  url_planta: string;
  tamanho_arquivo?: number;
  tipo_arquivo?: string;
  resumo_analise?: string;
  dados_estruturados?: {
    area_total_construida: string;
    numero_quartos: string;
    numero_banheiros: string;
    pavimentos: string;
    outros_comodos: string[];
  };
  area_total_construida?: number;
  numero_quartos?: number;
  numero_banheiros?: number;
  numero_pavimentos?: number;
  outros_comodos?: string[];
  valor_orcamento_parametrico?: number;
  valor_estimado?: number;
  status: "analisada" | "obra_criada" | "arquivada";
  data_analise: string;
  created_at: string;
  updated_at?: string;
}

export interface CreatePlantaData {
  nome_projeto: string;
  nome_arquivo: string;
  url_planta: string;
  tamanho_arquivo?: number;
  tipo_arquivo?: string;
  resumo_analise?: string;
  dados_estruturados?: {
    area_total_construida: string;
    numero_quartos: string;
    numero_banheiros: string;
    pavimentos: string;
    outros_comodos: string[];
  };
  area_total_construida?: number;
  numero_quartos?: number;
  numero_banheiros?: number;
  numero_pavimentos?: number;
  outros_comodos?: string[];
  valor_orcamento_parametrico?: number;
  valor_estimado?: number;
}

export interface UpdatePlantaData {
  nome_projeto?: string;
  status?: "analisada" | "obra_criada" | "arquivada";
  obra_id?: string;
  valor_estimado?: number;
}

export const usePlantasAnalisadas = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Query para buscar todas as plantas
  const {
    data: plantas,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["plantas-analisadas"],
    queryFn: async (): Promise<PlantaAnalisada[]> => {
      if (!user) throw new Error("Usuário não autenticado");

      const { data, error } = await supabase
        .from("plantas_analisadas")
        .select("*")
        .order("data_analise", { ascending: false });

      if (error) {
        console.error("Erro ao buscar plantas:", error);
        // Se a tabela não existe ainda, retornar array vazio ao invés de erro
        if (
          error.code === "PGRST116" || error.message.includes("does not exist")
        ) {
          console.log(
            "Tabela plantas_analisadas ainda não existe, retornando array vazio",
          );
          return [];
        }
        throw error;
      }

      return data || [];
    },
    enabled: !!user,
    retry: 1, // Tentar apenas uma vez em caso de erro
  });

  // Mutation para criar uma nova planta
  const createPlanta = useMutation({
    mutationFn: async (data: CreatePlantaData): Promise<PlantaAnalisada> => {
      if (!user) throw new Error("Usuário não autenticado");

      // Buscar tenant_id do usuário
      const { data: userTenant, error: tenantError } = await supabase
        .from("user_tenants")
        .select("tenant_id")
        .eq("user_id", user.id)
        .single();

      let tenantId: string;
      if (tenantError || !userTenant) {
        console.warn("Tenant não encontrado, usando user.id como tenant_id");
        tenantId = user.id;
      } else {
        tenantId = userTenant.tenant_id;
      }

      const plantaData = {
        ...data,
        usuario_id: user.id,
        tenant_id: tenantId,
      };

      const { data: newPlanta, error } = await supabase
        .from("plantas_analisadas")
        .insert(plantaData)
        .select()
        .single();

      if (error) {
        console.error("Erro ao criar planta:", error);
        throw error;
      }

      return newPlanta;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["plantas-analisadas"] });
      toast.success("Planta salva com sucesso!");
    },
    onError: (error: Error) => {
      console.error("Erro ao criar planta:", error);
      toast.error(error.message || "Erro ao salvar planta");
    },
  });

  // Mutation para atualizar uma planta
  const updatePlanta = useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdatePlantaData;
    }): Promise<PlantaAnalisada> => {
      const { data: updatedPlanta, error } = await supabase
        .from("plantas_analisadas")
        .update(data)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        console.error("Erro ao atualizar planta:", error);
        throw error;
      }

      return updatedPlanta;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["plantas-analisadas"] });
      toast.success("Planta atualizada com sucesso!");
    },
    onError: (error: Error) => {
      console.error("Erro ao atualizar planta:", error);
      toast.error(error.message || "Erro ao atualizar planta");
    },
  });

  // Mutation para excluir uma planta
  const deletePlanta = useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const { error } = await supabase
        .from("plantas_analisadas")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Erro ao excluir planta:", error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["plantas-analisadas"] });
      toast.success("Planta excluída com sucesso!");
    },
    onError: (error: Error) => {
      console.error("Erro ao excluir planta:", error);
      toast.error(error.message || "Erro ao excluir planta");
    },
  });

  // Mutation para excluir múltiplas plantas
  const deletePlantas = useMutation({
    mutationFn: async (ids: string[]): Promise<void> => {
      const { error } = await supabase
        .from("plantas_analisadas")
        .delete()
        .in("id", ids);

      if (error) {
        console.error("Erro ao excluir plantas:", error);
        throw error;
      }
    },
    onSuccess: (_, ids) => {
      queryClient.invalidateQueries({ queryKey: ["plantas-analisadas"] });
      toast.success(`${ids.length} plantas excluídas com sucesso!`);
    },
    onError: (error: Error) => {
      console.error("Erro ao excluir plantas:", error);
      toast.error(error.message || "Erro ao excluir plantas");
    },
  });

  // Função para calcular métricas
  const getMetricas = () => {
    if (!plantas || plantas.length === 0) {
      return {
        totalPlantas: 0,
        plantasAnalisadas: 0,
        obrasGeradas: 0,
        areaTotal: 0,
        valorTotalEstimado: 0,
        taxaConversao: 0,
      };
    }

    const totalPlantas = plantas.length;
    const plantasAnalisadas =
      plantas.filter((p) => p.status === "analisada").length;
    const obrasGeradas =
      plantas.filter((p) => p.status === "obra_criada").length;
    const areaTotal = plantas.reduce(
      (sum, p) => sum + (Number(p.area_total_construida) || 0),
      0,
    );
    const valorTotalEstimado = plantas.reduce(
      (sum, p) => sum + (Number(p.valor_estimado) || 0),
      0,
    );
    const taxaConversao = totalPlantas > 0
      ? (obrasGeradas / totalPlantas) * 100
      : 0;

    return {
      totalPlantas,
      plantasAnalisadas,
      obrasGeradas,
      areaTotal,
      valorTotalEstimado,
      taxaConversao,
    };
  };

  return {
    plantas: plantas || [],
    isLoading,
    error,
    refetch,
    createPlanta,
    updatePlanta,
    deletePlanta,
    deletePlantas,
    getMetricas,
    isCreating: createPlanta.isPending,
    isUpdating: updatePlanta.isPending,
    isDeleting: deletePlanta.isPending,
  };
};
