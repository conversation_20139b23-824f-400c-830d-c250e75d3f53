import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { useAuth } from '@/contexts/auth/hooks';
import { supabase } from '@/integrations/supabase/client';
import type {
    AppearancePreferences,
    DevicePreferences,
    LanguagePreferences,
    NotificationPreferences,
    SecurityPreferences,
    UserPreferences
} from '@/types';

// Chaves de query para cache
const QUERY_KEYS = {
  userPreferences: (userId: string) => ['user-preferences', userId],
} as const;

// Valores padrão para preferências
const DEFAULT_PREFERENCES: Omit<UserPreferences, 'id' | 'user_id' | 'created_at' | 'updated_at'> = {
  notifications: {
    email_enabled: true,
    push_enabled: true,
    obra_alerts: true,
    despesa_alerts: true,
    contrato_alerts: true,
    orcamento_alerts: true,
    marketing_emails: false,
    weekly_reports: true,
    alert_frequency: 'immediate',
    quiet_hours: {
      enabled: false,
      start_time: '22:00',
      end_time: '08:00',
    },
  },
  appearance: {
    theme: 'system',
    sidebar_collapsed: false,
    compact_mode: false,
    animations_enabled: true,
    high_contrast: false,
    font_size: 'medium',
    color_scheme: 'default',
  },
  security: {
    two_factor_enabled: false,
    session_timeout: 480,
    login_notifications: true,
    suspicious_activity_alerts: true,
    data_export_notifications: true,
    password_change_notifications: true,
    device_login_notifications: true,
  },
  language: {
    language: 'pt-BR',
    timezone: 'America/Sao_Paulo',
    date_format: 'DD/MM/YYYY',
    time_format: '24h',
    currency: 'BRL',
    number_format: 'pt-BR',
  },
  devices: {
    remember_devices: true,
    auto_logout_inactive: false,
    max_concurrent_sessions: 5,
    mobile_notifications: true,
    desktop_notifications: true,
  },
};

/**
 * Hook para gerenciar preferências do usuário
 * Segue os padrões do projeto usando TanStack Query
 */
export function useUserPreferences() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Query para buscar preferências
  const {
    data: preferences,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: QUERY_KEYS.userPreferences(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        console.log('❌ Usuário não autenticado');
        throw new Error('Usuário não autenticado');
      }

      console.log('🔍 Buscando preferências para usuário:', user.id);

      try {
        const { data, error } = await supabase
          .from('user_preferences')
          .select('*')
          .eq('user_id', user.id)
          .single();

        console.log('📊 Resultado da query:', { data, error });

        if (error && error.code !== 'PGRST116') {
          console.error('❌ Erro na query:', error);
          throw error;
        }

        // Se não existir, retorna valores padrão
        if (!data) {
          console.log('📝 Usando valores padrão');
          return {
            user_id: user.id,
            ...DEFAULT_PREFERENCES,
          } as UserPreferences;
        }

        console.log('✅ Preferências carregadas:', data);
        return data as UserPreferences;
      } catch (err) {
        console.error('💥 Erro ao buscar preferências:', err);
        // Em caso de erro, retorna valores padrão
        return {
          user_id: user.id,
          ...DEFAULT_PREFERENCES,
        } as UserPreferences;
      }
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  // Mutation para atualizar preferências
  const updatePreferencesMutation = useMutation({
    mutationFn: async (updates: Partial<UserPreferences>) => {
      if (!user?.id) throw new Error('Usuário não autenticado');

      const { data, error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          ...updates,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Atualiza o cache
      queryClient.setQueryData(
        QUERY_KEYS.userPreferences(user?.id || ''),
        data
      );
      toast.success('Configurações salvas com sucesso!');
    },
    onError: (error) => {
      console.error('Erro ao salvar configurações:', error);
      toast.error('Erro ao salvar configurações. Tente novamente.');
    },
  });

  // Funções específicas para cada tipo de preferência
  const updateNotifications = (notifications: Partial<NotificationPreferences>) => {
    if (!preferences) return;
    
    updatePreferencesMutation.mutate({
      notifications: {
        ...preferences.notifications,
        ...notifications,
      },
    });
  };

  const updateAppearance = (appearance: Partial<AppearancePreferences>) => {
    if (!preferences) return;
    
    updatePreferencesMutation.mutate({
      appearance: {
        ...preferences.appearance,
        ...appearance,
      },
    });
  };

  const updateSecurity = (security: Partial<SecurityPreferences>) => {
    if (!preferences) return;
    
    updatePreferencesMutation.mutate({
      security: {
        ...preferences.security,
        ...security,
      },
    });
  };

  const updateLanguage = (language: Partial<LanguagePreferences>) => {
    if (!preferences) return;
    
    updatePreferencesMutation.mutate({
      language: {
        ...preferences.language,
        ...language,
      },
    });
  };

  const updateDevices = (devices: Partial<DevicePreferences>) => {
    if (!preferences) return;
    
    updatePreferencesMutation.mutate({
      devices: {
        ...preferences.devices,
        ...devices,
      },
    });
  };

  // Reset para valores padrão
  const resetToDefaults = () => {
    updatePreferencesMutation.mutate(DEFAULT_PREFERENCES);
  };

  return {
    // Dados
    preferences: preferences || { user_id: user?.id || '', ...DEFAULT_PREFERENCES },
    isLoading,
    error,
    
    // Ações
    refetch,
    updatePreferences: updatePreferencesMutation.mutate,
    updateNotifications,
    updateAppearance,
    updateSecurity,
    updateLanguage,
    updateDevices,
    resetToDefaults,
    
    // Estados
    isUpdating: updatePreferencesMutation.isPending,
    updateError: updatePreferencesMutation.error,
  };
}
