/* Importar sistema de cores para melhorias de contraste */
@import './styles/colors.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode colors - paleta profissional corporativa para construção civil */
    --background: 0 0% 100%;
    --foreground: 218 23% 23%;

    --card: 0 0% 100%;
    --card-foreground: 218 23% 23%;

    --popover: 0 0% 100%;
    --popover-foreground: 218 23% 23%;

    /* Azul corporativo como cor principal */
    --primary: 214 32% 27%;
    --primary-foreground: 0 0% 100%;

    /* Cinza médio como secundário */
    --secondary: 214 13% 93%;
    --secondary-foreground: 218 23% 23%;

    --muted: 214 13% 96%;
    --muted-foreground: 214 16% 46%;

    --accent: 214 13% 93%;
    --accent-foreground: 218 23% 23%;

    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 100%;

    --border: 214 20% 88%;
    --input: 214 20% 88%;
    --ring: 214 32% 27%;

    --radius: 0.75rem;

    /* Sidebar light mode - tons corporativos */
    --sidebar-background: 216 20% 98%;
    --sidebar-foreground: 218 23% 23%;
    --sidebar-primary: 214 32% 27%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 214 13% 93%;
    --sidebar-accent-foreground: 218 23% 23%;
    --sidebar-border: 214 20% 88%;
    --sidebar-ring: 214 32% 27%;

    /* Cores funcionais light mode - paleta profissional */
    --gradient-start: 216 20% 98%;
    --gradient-end: 214 13% 93%;
    --card-hover: 214 15% 95%;
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 32 95% 44%;
    --warning-foreground: 0 0% 100%;
    --info: 214 84% 56%;
    --info-foreground: 0 0% 100%;

    /* Cores específicas da construção civil - versão profissional */
    --construction-primary: 214 32% 27%;
    /* Azul corporativo */
    --construction-secondary: 214 13% 93%;
    /* Cinza claro */
    --construction-accent: 214 84% 56%;
    /* Azul informativo */
    --construction-success: 142 71% 45%;
    /* Verde sucesso */
    --construction-warning: 32 95% 44%;
    /* Laranja alerta */
    --construction-danger: 0 72% 51%;
    /* Vermelho erro */
    --gold: 45 85% 52%;
    /* Dourado padrão ObrasAI */
  }

  .dark {
    /* Dark mode colors - elegante e profissional */
    --background: 218 23% 6%;
    --foreground: 214 20% 95%;

    --card: 218 23% 8%;
    --card-foreground: 214 20% 95%;

    --popover: 218 23% 8%;
    --popover-foreground: 214 20% 95%;

    /* Azul claro como primário no escuro */
    --primary: 214 84% 70%;
    --primary-foreground: 218 23% 6%;

    /* Cinza escuro como secundário */
    --secondary: 218 23% 14%;
    --secondary-foreground: 214 20% 95%;

    --muted: 218 23% 14%;
    --muted-foreground: 214 16% 65%;

    --accent: 218 23% 14%;
    --accent-foreground: 214 20% 95%;

    --destructive: 0 72% 61%;
    --destructive-foreground: 0 0% 100%;

    --border: 218 23% 14%;
    --input: 218 23% 14%;
    --ring: 214 84% 70%;

    /* Sidebar dark mode - tons corporativos */
    --sidebar-background: 218 23% 6%;
    --sidebar-foreground: 214 20% 95%;
    --sidebar-primary: 214 84% 70%;
    --sidebar-primary-foreground: 218 23% 6%;
    --sidebar-accent: 218 23% 14%;
    --sidebar-accent-foreground: 214 20% 95%;
    --sidebar-border: 218 23% 14%;
    --sidebar-ring: 214 84% 70%;

    /* Cores funcionais dark mode - paleta profissional */
    --gradient-start: 218 23% 8%;
    --gradient-end: 218 23% 12%;
    --card-hover: 218 23% 10%;
    --success: 142 71% 55%;
    --success-foreground: 0 0% 100%;
    --warning: 32 95% 54%;
    --warning-foreground: 0 0% 100%;
    --info: 214 84% 70%;
    --info-foreground: 218 23% 6%;

    /* Cores específicas da construção civil - modo escuro */
    --construction-primary: 214 84% 70%;
    /* Azul claro */
    --construction-secondary: 218 23% 14%;
    /* Cinza escuro */
    --construction-accent: 214 84% 56%;
    /* Azul informativo */
    --construction-success: 142 71% 55%;
    /* Verde sucesso */
    --construction-warning: 32 95% 54%;
    /* Laranja alerta */
    --construction-danger: 0 72% 61%;
    /* Vermelho erro */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Nunito Sans', system-ui, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Scrollbar personalizada */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted/30;
    border-radius: 5px;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 hover:bg-muted-foreground/50;
    border-radius: 5px;
    transition: background-color 0.2s;
  }

  /* Animações suaves */
  * {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  }

  /* Gradientes e efeitos */
  .gradient-bg {
    background: linear-gradient(135deg, hsl(var(--gradient-start)) 0%, hsl(var(--gradient-end)) 100%);
  }

  .glass-effect {
    @apply backdrop-blur-md bg-background/50 border border-border/50;
  }

  .card-hover-effect {
    @apply transition-all duration-300 hover:scale-[1.02] hover:shadow-lg;
  }

  /* Estilos para métricas */
  .metric-card {
    @apply rounded-xl border bg-card p-6 shadow-sm transition-all duration-300 hover:shadow-md hover:bg-[hsl(var(--card-hover))];
  }

  .metric-icon {
    @apply h-12 w-12 rounded-lg bg-primary/10 p-2.5 text-primary;
  }

  /* Efeitos de luz */
  .glow-effect {
    box-shadow: 0 0 20px rgba(var(--primary), 0.1);
  }

  .dark .glow-effect {
    box-shadow: 0 0 30px rgba(var(--primary), 0.2);
  }
}