// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';

import { createSecureStorage } from '@/lib/secure-storage';

import type { Database } from './types';

// ✅ Usando variáveis de ambiente em vez de credenciais hardcoded
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;

// ✅ Validação de variáveis de ambiente obrigatórias
if (!SUPABASE_URL || !SUPABASE_PUBLISHABLE_KEY) {
  throw new Error("Missing required environment variables: VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY");
}

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: createSecureStorage(),
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
    debug: false,
  },
  global: {
    headers: {
      'X-Client-Info': 'obrasai-webapp'
    }
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});
