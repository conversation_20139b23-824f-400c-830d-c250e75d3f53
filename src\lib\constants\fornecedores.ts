/**
 * 🏢 Constantes para Fornecedores - ObrasAI
 * 
 * Definições de categorias e tipos para fornecedores PJ e PF
 */

// Categorias para Fornecedores PJ (Empresas)
export const CATEGORIAS_FORNECEDOR_PJ = {
  MATERIAL_CONSTRUCAO: 'MATERIAL_CONSTRUCAO',
  EQUIPAMENTOS: 'EQUIPAMENTOS', 
  SERVICOS_ESPECIALIZADOS: 'SERVICOS_ESPECIALIZADOS',
  TRANSPORTE_LOGISTICA: 'TRANSPORTE_LOGISTICA',
  CONSULTORIA_PROJETOS: 'CONSULTORIA_PROJETOS',
  OUTRO: 'OUTRO'
} as const;

// Labels amigáveis para categorias PJ
export const LABELS_CATEGORIA_PJ: Record<keyof typeof CATEGORIAS_FORNECEDOR_PJ, string> = {
  MATERIAL_CONSTRUCAO: '🏪 Material de Construção',
  EQUIPAMENTOS: '🔧 Equipamentos e Ferramentas',
  SERVICOS_ESPECIALIZADOS: '🏗️ Serviços Especializados',
  TRANSPORTE_LOGISTICA: '🚛 Transporte e Logística',
  CONSULTORIA_PROJETOS: '📋 Consultoria e Projetos',
  OUTRO: '📦 Outros'
};

// Categorias para Fornecedores PF (Pessoas)
export const CATEGORIAS_FORNECEDOR_PF = {
  PEDREIRO: 'PEDREIRO',
  ELETRICISTA: 'ELETRICISTA',
  ENCANADOR: 'ENCANADOR',
  PINTOR: 'PINTOR',
  CARPINTEIRO: 'CARPINTEIRO',
  SERVICOS_GERAIS: 'SERVICOS_GERAIS',
  OUTRO: 'OUTRO'
} as const;

// Labels amigáveis para categorias PF
export const LABELS_CATEGORIA_PF: Record<keyof typeof CATEGORIAS_FORNECEDOR_PF, string> = {
  PEDREIRO: '👷 Pedreiro',
  ELETRICISTA: '⚡ Eletricista',
  ENCANADOR: '🔧 Encanador',
  PINTOR: '🎨 Pintor',
  CARPINTEIRO: '🪚 Carpinteiro',
  SERVICOS_GERAIS: '🔨 Serviços Gerais',
  OUTRO: '📦 Outros'
};

// Arrays para uso em Select components
export const OPCOES_CATEGORIA_PJ = Object.entries(LABELS_CATEGORIA_PJ).map(([value, label]) => ({
  value,
  label
}));

export const OPCOES_CATEGORIA_PF = Object.entries(LABELS_CATEGORIA_PF).map(([value, label]) => ({
  value,
  label
}));

// Tipos TypeScript
export type CategoriaFornecedorPJ = keyof typeof CATEGORIAS_FORNECEDOR_PJ;
export type CategoriaFornecedorPF = keyof typeof CATEGORIAS_FORNECEDOR_PF;

// Função helper para obter label da categoria PJ
export function getLabelCategoriaPJ(categoria: string): string {
  return LABELS_CATEGORIA_PJ[categoria as CategoriaFornecedorPJ] || categoria;
}

// Função helper para obter label da categoria PF
export function getLabelCategoriaPF(categoria: string): string {
  return LABELS_CATEGORIA_PF[categoria as CategoriaFornecedorPF] || categoria;
}

// Função helper genérica
export function getLabelCategoria(categoria: string, tipo: 'PJ' | 'PF'): string {
  if (tipo === 'PJ') {
    return getLabelCategoriaPJ(categoria);
  }
  return getLabelCategoriaPF(categoria);
}
