
// Função para formatar CNPJ: 00000000000000 -> 00.000.000/0000-00
export function formatCNPJ(cnpj: string | null | undefined): string {
  if (!cnpj) return '';

  // Remove caracteres não numéricos
  const cleaned = cnpj.replace(/\D/g, '');

  // Garante que o CNPJ tenha 14 dígitos
  if (cleaned.length !== 14) return cleaned;

  // Aplica a formatação do CNPJ
  return cleaned.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
}

// Função para formatar CPF: 00000000000 -> 000.000.000-00
export function formatCPF(cpf: string | null | undefined): string {
  if (!cpf) return '';

  // Remove caracteres não numéricos
  const cleaned = cpf.replace(/\D/g, '');

  // Garante que o CPF tenha 11 dígitos
  if (cleaned.length !== 11) return cleaned;

  // Aplica a formatação do CPF
  return cleaned.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
}

// Função para formatar CEP: 00000000 -> 00000-000
export function formatCEP(cep: string | null | undefined): string {
  if (!cep) return '';

  // Remove caracteres não numéricos
  const cleaned = cep.replace(/\D/g, '');

  // Garante que o CEP tenha 8 dígitos
  if (cleaned.length !== 8) return cleaned;

  // Aplica a formatação do CEP
  return cleaned.replace(/(\d{5})(\d{3})/, '$1-$2');
}

// Função para formatar telefone: 00000000000 -> (00) 00000-0000 ou (00) 0000-0000
export function formatPhone(phone: string | null | undefined): string {
  if (!phone) return '';

  // Remove caracteres não numéricos
  const cleaned = phone.replace(/\D/g, '');

  // Formata o telefone dependendo da quantidade de dígitos
  if (cleaned.length === 11) {
    // Celular com 9 dígitos: (00) 00000-0000
    return cleaned.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  } else if (cleaned.length === 10) {
    // Fixo com 8 dígitos: (00) 0000-0000
    return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  }

  return cleaned;
}

// Função para formatar valores monetários
export function formatCurrency(value: number | null | undefined): string {
  if (value === null || value === undefined || isNaN(value)) {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(0);
  }

  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}

// Função para formatar datas
export function formatDate(date: Date | string | null | undefined): string {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(dateObj);
}

// Função para calcular a diferença em dias entre duas datas
export function daysBetweenDates(startDate: Date | string, endDate: Date | string): number {
  const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
  
  // Retorna a diferença em dias
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
}

// Função para converter string para slug
export function slugify(text: string): string {
  return text
    .toString()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-');
}
