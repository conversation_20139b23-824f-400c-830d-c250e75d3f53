import {
    Building2,
    Droplets,
    FileText,
    Hammer,
    HardHat,
    Package,
    Paintbrush,
    Scissors,
    Settings,
    Truck,
    Wrench,
    Zap
} from "lucide-react";

import { Badge } from "@/components/ui/badge";

// Tipos para as categorias
export type CategoriaPJ = 
  | "MATERIAL_CONSTRUCAO"
  | "EQUIPAMENTOS" 
  | "SERVICOS_ESPECIALIZADOS"
  | "TRANSPORTE_LOGISTICA"
  | "CONSULTORIA_PROJETOS"
  | "OUTRO";

export type CategoriaPF = 
  | "PEDREIRO"
  | "ELETRICISTA"
  | "ENCANADOR"
  | "PINTOR"
  | "CARPINTEIRO"
  | "SERVICOS_GERAIS"
  | "OUTRO";

// Configurações visuais para categorias PJ
export const CATEGORIA_PJ_CONFIG = {
  MATERIAL_CONSTRUCAO: {
    label: "Material de Construção",
    icon: Building2,
    color: "bg-blue-100 text-blue-800 border-blue-200",
    darkColor: "dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800",
    badgeVariant: "secondary" as const
  },
  EQUIPAMENTOS: {
    label: "Equipamentos e Ferramentas",
    icon: Wrench,
    color: "bg-orange-100 text-orange-800 border-orange-200",
    darkColor: "dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-800",
    badgeVariant: "secondary" as const
  },
  SERVICOS_ESPECIALIZADOS: {
    label: "Serviços Especializados",
    icon: HardHat,
    color: "bg-purple-100 text-purple-800 border-purple-200",
    darkColor: "dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800",
    badgeVariant: "secondary" as const
  },
  TRANSPORTE_LOGISTICA: {
    label: "Transporte e Logística",
    icon: Truck,
    color: "bg-green-100 text-green-800 border-green-200",
    darkColor: "dark:bg-green-900/20 dark:text-green-300 dark:border-green-800",
    badgeVariant: "secondary" as const
  },
  CONSULTORIA_PROJETOS: {
    label: "Consultoria e Projetos",
    icon: FileText,
    color: "bg-indigo-100 text-indigo-800 border-indigo-200",
    darkColor: "dark:bg-indigo-900/20 dark:text-indigo-300 dark:border-indigo-800",
    badgeVariant: "secondary" as const
  },
  OUTRO: {
    label: "Outros",
    icon: Package,
    color: "bg-gray-100 text-gray-800 border-gray-200",
    darkColor: "dark:bg-gray-900/20 dark:text-gray-300 dark:border-gray-800",
    badgeVariant: "outline" as const
  }
};

// Configurações visuais para categorias PF
export const CATEGORIA_PF_CONFIG = {
  PEDREIRO: {
    label: "Pedreiro",
    icon: Hammer,
    color: "bg-amber-100 text-amber-800 border-amber-200",
    darkColor: "dark:bg-amber-900/20 dark:text-amber-300 dark:border-amber-800",
    badgeVariant: "secondary" as const
  },
  ELETRICISTA: {
    label: "Eletricista",
    icon: Zap,
    color: "bg-yellow-100 text-yellow-800 border-yellow-200",
    darkColor: "dark:bg-yellow-900/20 dark:text-yellow-300 dark:border-yellow-800",
    badgeVariant: "secondary" as const
  },
  ENCANADOR: {
    label: "Encanador",
    icon: Droplets,
    color: "bg-cyan-100 text-cyan-800 border-cyan-200",
    darkColor: "dark:bg-cyan-900/20 dark:text-cyan-300 dark:border-cyan-800",
    badgeVariant: "secondary" as const
  },
  PINTOR: {
    label: "Pintor",
    icon: Paintbrush,
    color: "bg-pink-100 text-pink-800 border-pink-200",
    darkColor: "dark:bg-pink-900/20 dark:text-pink-300 dark:border-pink-800",
    badgeVariant: "secondary" as const
  },
  CARPINTEIRO: {
    label: "Carpinteiro",
    icon: Scissors,
    color: "bg-emerald-100 text-emerald-800 border-emerald-200",
    darkColor: "dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800",
    badgeVariant: "secondary" as const
  },
  SERVICOS_GERAIS: {
    label: "Serviços Gerais",
    icon: Settings,
    color: "bg-violet-100 text-violet-800 border-violet-200",
    darkColor: "dark:bg-violet-900/20 dark:text-violet-300 dark:border-violet-800",
    badgeVariant: "secondary" as const
  },
  OUTRO: {
    label: "Outros",
    icon: Package,
    color: "bg-gray-100 text-gray-800 border-gray-200",
    darkColor: "dark:bg-gray-900/20 dark:text-gray-300 dark:border-gray-800",
    badgeVariant: "outline" as const
  }
};

// Função para obter configuração de categoria PJ
export const getCategoriaPJConfig = (categoria: string | null | undefined) => {
  if (!categoria || !(categoria in CATEGORIA_PJ_CONFIG)) {
    return CATEGORIA_PJ_CONFIG.OUTRO;
  }
  return CATEGORIA_PJ_CONFIG[categoria as CategoriaPJ];
};

// Função para obter configuração de categoria PF
export const getCategoriaPFConfig = (categoria: string | null | undefined) => {
  if (!categoria || !(categoria in CATEGORIA_PF_CONFIG)) {
    return CATEGORIA_PF_CONFIG.OUTRO;
  }
  return CATEGORIA_PF_CONFIG[categoria as CategoriaPF];
};

// Componente Badge para categoria PJ
export const CategoriaPJBadge = ({ categoria }: { categoria: string | null | undefined }) => {
  const config = getCategoriaPJConfig(categoria);
  const Icon = config.icon;

  return (
    <Badge
      variant={config.badgeVariant}
      className={`${config.color} ${config.darkColor} flex items-center gap-1.5 font-medium transition-all duration-200 hover:shadow-sm cursor-default`}
    >
      <Icon className="h-3.5 w-3.5" />
      {config.label}
    </Badge>
  );
};

// Componente Badge para categoria PF
export const CategoriaPFBadge = ({ categoria }: { categoria: string | null | undefined }) => {
  const config = getCategoriaPFConfig(categoria);
  const Icon = config.icon;

  return (
    <Badge
      variant={config.badgeVariant}
      className={`${config.color} ${config.darkColor} flex items-center gap-1.5 font-medium transition-all duration-200 hover:shadow-sm cursor-default`}
    >
      <Icon className="h-3.5 w-3.5" />
      {config.label}
    </Badge>
  );
};

// Função para obter todas as categorias PJ para filtros
export const getAllCategoriasPJ = () => {
  return Object.entries(CATEGORIA_PJ_CONFIG).map(([value, config]) => ({
    value,
    label: config.label,
    icon: config.icon
  }));
};

// Função para obter todas as categorias PF para filtros
export const getAllCategoriasPF = () => {
  return Object.entries(CATEGORIA_PF_CONFIG).map(([value, config]) => ({
    value,
    label: config.label,
    icon: config.icon
  }));
};
