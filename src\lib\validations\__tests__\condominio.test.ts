import { describe, expect, it } from 'vitest';

import {
    condominioDataSchema,
    isCondominio,
    isObraUnica,
    isUnidadeCondominio,
    obraCondominioSchema,
    TipoProjetoEnum,
    unidadeFormDataSchema
} from '../condominio';

describe('Condomínio Validation Schemas', () => {
  describe('TipoProjetoEnum', () => {
    it('deve aceitar valores válidos', () => {
      expect(TipoProjetoEnum.parse('UNICO')).toBe('UNICO');
      expect(TipoProjetoEnum.parse('CONDOMINIO_MASTER')).toBe('CONDOMINIO_MASTER');
      expect(TipoProjetoEnum.parse('UNIDADE_CONDOMINIO')).toBe('UNIDADE_CONDOMINIO');
    });

    it('deve rejeitar valores inválidos', () => {
      expect(() => TipoProjetoEnum.parse('INVALID')).toThrow();
    });
  });

  describe('unidadeFormDataSchema', () => {
    it('deve validar dados válidos de unidade', () => {
      const validData = {
        identificador_unidade: 'APTO-101',
        nome: 'Apartamento 101',
        descricao: 'Apartamento de 2 quartos',
        area_total: 80,
        orcamento: 150000,
      };

      const result = unidadeFormDataSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('deve rejeitar identificador inválido', () => {
      const invalidData = {
        identificador_unidade: 'APTO@101', // caractere inválido
        nome: 'Apartamento 101',
      };

      expect(() => unidadeFormDataSchema.parse(invalidData)).toThrow();
    });
  });

  describe('condominioDataSchema', () => {
    it('deve validar dados válidos de condomínio', () => {
      const validData = {
        obra_mae: {
          nome: 'Condomínio Residencial',
          endereco: 'Rua das Flores, 123',
          cidade: 'São Paulo',
          estado: 'SP',
          cep: '01234-567',
          construtora_id: '123e4567-e89b-12d3-a456-426614174000',
        },
        unidades: [
          {
            identificador_unidade: 'APTO-101',
            nome: 'Apartamento 101',
          },
          {
            identificador_unidade: 'APTO-102',
            nome: 'Apartamento 102',
          },
        ],
      };

      const result = condominioDataSchema.parse(validData);
      expect(result.unidades).toHaveLength(2);
    });

    it('deve rejeitar identificadores duplicados', () => {
      const invalidData = {
        obra_mae: {
          nome: 'Condomínio Residencial',
          endereco: 'Rua das Flores, 123',
          cidade: 'São Paulo',
          estado: 'SP',
          cep: '01234-567',
          construtora_id: '123e4567-e89b-12d3-a456-426614174000',
        },
        unidades: [
          {
            identificador_unidade: 'APTO-101',
            nome: 'Apartamento 101',
          },
          {
            identificador_unidade: 'APTO-101', // duplicado
            nome: 'Apartamento 102',
          },
        ],
      };

      expect(() => condominioDataSchema.parse(invalidData)).toThrow();
    });
  });

  describe('obraCondominioSchema', () => {
    it('deve validar obra única', () => {
      const validData = {
        nome: 'Casa Residencial',
        endereco: 'Rua das Flores, 123',
        cidade: 'São Paulo',
        estado: 'SP',
        cep: '01234-567',
        orcamento: 200000,
        area_total: 120,
        construtora_id: '123e4567-e89b-12d3-a456-426614174000',
        tipo_projeto: 'UNICO' as const,
      };

      const result = obraCondominioSchema.parse(validData);
      expect(result.tipo_projeto).toBe('UNICO');
    });

    it('deve validar unidade de condomínio com parent_obra_id', () => {
      const validData = {
        nome: 'Apartamento 101',
        endereco: 'Rua das Flores, 123',
        cidade: 'São Paulo',
        estado: 'SP',
        cep: '01234-567',
        orcamento: 150000,
        area_total: 80,
        construtora_id: '123e4567-e89b-12d3-a456-426614174000',
        tipo_projeto: 'UNIDADE_CONDOMINIO' as const,
        parent_obra_id: '123e4567-e89b-12d3-a456-426614174001',
        identificador_unidade: 'APTO-101',
      };

      const result = obraCondominioSchema.parse(validData);
      expect(result.tipo_projeto).toBe('UNIDADE_CONDOMINIO');
      expect(result.parent_obra_id).toBeDefined();
      expect(result.identificador_unidade).toBe('APTO-101');
    });

    it('deve rejeitar unidade sem parent_obra_id', () => {
      const invalidData = {
        nome: 'Apartamento 101',
        endereco: 'Rua das Flores, 123',
        cidade: 'São Paulo',
        estado: 'SP',
        cep: '01234-567',
        construtora_id: '123e4567-e89b-12d3-a456-426614174000',
        tipo_projeto: 'UNIDADE_CONDOMINIO' as const,
        // parent_obra_id ausente
        identificador_unidade: 'APTO-101',
      };

      expect(() => obraCondominioSchema.parse(invalidData)).toThrow();
    });
  });

  describe('Utility functions', () => {
    it('isCondominio deve identificar condomínios corretamente', () => {
      expect(isCondominio({ tipo_projeto: 'CONDOMINIO_MASTER' })).toBe(true);
      expect(isCondominio({ tipo_projeto: 'UNICO' })).toBe(false);
      expect(isCondominio({ tipo_projeto: 'UNIDADE_CONDOMINIO' })).toBe(false);
    });

    it('isUnidadeCondominio deve identificar unidades corretamente', () => {
      expect(isUnidadeCondominio({ tipo_projeto: 'UNIDADE_CONDOMINIO' })).toBe(true);
      expect(isUnidadeCondominio({ tipo_projeto: 'UNICO' })).toBe(false);
      expect(isUnidadeCondominio({ tipo_projeto: 'CONDOMINIO_MASTER' })).toBe(false);
    });

    it('isObraUnica deve identificar obras únicas corretamente', () => {
      expect(isObraUnica({ tipo_projeto: 'UNICO' })).toBe(true);
      expect(isObraUnica({})).toBe(true); // sem tipo_projeto
      expect(isObraUnica({ tipo_projeto: 'CONDOMINIO_MASTER' })).toBe(false);
      expect(isObraUnica({ tipo_projeto: 'UNIDADE_CONDOMINIO' })).toBe(false);
    });
  });
});