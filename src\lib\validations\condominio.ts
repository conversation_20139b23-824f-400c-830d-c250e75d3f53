import { z } from "zod";

import { t } from "@/lib/i18n";

// Enum para tipos de projeto
export const TipoProjetoEnum = z.enum(['UNICO', 'CONDOMINIO_MASTER', 'UNIDADE_CONDOMINIO'], {
  errorMap: () => ({ message: "Tipo de projeto inválido" })
});

// Schema para dados de unidade individual
export const unidadeFormDataSchema = z.object({
  identificador_unidade: z.string()
    .min(1, { message: "Identificador da unidade é obrigatório" })
    .max(50, { message: "Identificador deve ter no máximo 50 caracteres" })
    .regex(/^[A-Za-z0-9\-_\s]+$/, { message: "Identificador deve conter apenas letras, números, hífens e underscores" }),
  nome: z.string()
    .min(3, { message: "Nome da unidade deve ter pelo menos 3 caracteres" })
    .max(255, { message: "Nome deve ter no máximo 255 caracteres" }),
  descricao: z.string()
    .max(1000, { message: "Descrição deve ter no máximo 1000 caracteres" })
    .optional(),
  area_total: z.coerce
    .number()
    .min(1, { message: "Área total deve ser maior que 0 m²" })
    .max(10000, { message: "Área total não pode exceder 10.000 m²" })
    .optional(),
  orcamento: z.coerce
    .number()
    .min(0, { message: t("messages.invalidNumber") })
    .max(999999999.99, { message: "Orçamento excede o limite máximo" })
    .optional(),
});

// Schema para dados do condomínio (obra-mãe)
export const condominioDataSchema = z.object({
  obra_mae: z.object({
    nome: z.string().min(3, { message: t("messages.requiredField") }),
    endereco: z.string().min(3, { message: t("messages.requiredField") }),
    cidade: z.string().min(1, { message: t("messages.requiredField") }),
    estado: z.string().min(2, { message: t("messages.requiredField") }),
    cep: z.string()
      .min(8, { message: "CEP deve ter pelo menos 8 dígitos" })
      .regex(/^(\d{5}-?\d{3})$/, { message: "CEP deve estar no formato 00000-000" })
      .transform((val) => {
        const cleaned = val.replace(/\D/g, '');
        return cleaned.length === 8 ? cleaned.replace(/(\d{5})(\d{3})/, '$1-$2') : val;
      }),
    descricao: z.string().max(1000, { message: "Descrição deve ter no máximo 1000 caracteres" }).optional(),
    bairro: z.string().max(100, { message: "Bairro deve ter no máximo 100 caracteres" }).optional(),
    orcamento: z.coerce
      .number()
      .min(0, { message: t("messages.invalidNumber") })
      .max(999999999.99, { message: "Orçamento excede o limite máximo" })
      .optional(),
    area_total: z.coerce
      .number()
      .min(1, { message: "Área total é obrigatória e deve ser maior que 0 m²" })
      .max(100000, { message: "Área total não pode exceder 100.000 m²" })
      .optional(),
    data_inicio: z.date().nullable().optional(),
    data_prevista_termino: z.date().nullable().optional(),
    construtora_id: z.string().min(1, { message: 'Selecione a construtora/autônomo responsável' }),
    usuario_id: z.string().optional(),
    tenant_id: z.string().optional(),
    status: z.enum(['planejamento', 'em_andamento', 'concluida', 'pausada']).default('planejamento').optional(),
    responsavel_tecnico: z.string().max(255).optional(),
    observacoes: z.string().max(2000).optional(),
  }),
  unidades: z.array(unidadeFormDataSchema)
    .min(1, { message: "Deve haver pelo menos 1 unidade no condomínio" })
    .max(500, { message: "Máximo de 500 unidades por condomínio" })
    .refine((unidades) => {
      // Validar identificadores únicos
      const identificadores = unidades.map(u => u.identificador_unidade.toLowerCase().trim());
      const identificadoresUnicos = new Set(identificadores);
      return identificadores.length === identificadoresUnicos.size;
    }, {
      message: "Identificadores de unidades devem ser únicos dentro do condomínio"
    })
    .refine((unidades) => {
      // Validar nomes únicos
      const nomes = unidades.map(u => u.nome.toLowerCase().trim());
      const nomesUnicos = new Set(nomes);
      return nomes.length === nomesUnicos.size;
    }, {
      message: "Nomes de unidades devem ser únicos dentro do condomínio"
    })
});

// Schema para request de criação de condomínio
export const createCondominioRequestSchema = z.object({
  condominio_data: condominioDataSchema.shape.obra_mae,
  unidades_data: condominioDataSchema.shape.unidades
});

// Schema para atualização de obra com suporte a condomínios
export const obraCondominioSchema = z.object({
  nome: z.string().min(3, { message: t("messages.requiredField") }),
  endereco: z.string().min(3, { message: t("messages.requiredField") }),
  cidade: z.string().min(1, { message: t("messages.requiredField") }),
  estado: z.string().min(2, { message: t("messages.requiredField") }),
  cep: z.string()
    .min(8, { message: "CEP deve ter pelo menos 8 dígitos" })
    .regex(/^(\d{5}-?\d{3})$/, { message: "CEP deve estar no formato 00000-000" })
    .transform((val) => {
      const cleaned = val.replace(/\D/g, '');
      return cleaned.length === 8 ? cleaned.replace(/(\d{5})(\d{3})/, '$1-$2') : val;
    }),
  orcamento: z.coerce
    .number()
    .min(0, { message: t("messages.invalidNumber") })
    .optional(),
  area_total: z.coerce
    .number()
    .min(1, { message: "Área total é obrigatória e deve ser maior que 0 m²" })
    .optional(),
  data_inicio: z.date().nullable().optional(),
  data_prevista_termino: z.date().nullable().optional(),
  construtora_id: z.string().min(1, { message: 'Selecione a construtora/autônomo responsável' }),
  // Campos específicos para condomínios
  tipo_projeto: TipoProjetoEnum.default('UNICO'),
  parent_obra_id: z.string().uuid().optional().nullable(),
  identificador_unidade: z.string()
    .max(50, { message: "Identificador deve ter no máximo 50 caracteres" })
    .regex(/^[A-Za-z0-9\-_\s]*$/, { message: "Identificador deve conter apenas letras, números, hífens e underscores" })
    .optional(),
  // Campos opcionais
  descricao: z.string().max(1000).optional(),
  bairro: z.string().max(100).optional(),
  status: z.enum(['planejamento', 'em_andamento', 'concluida', 'pausada']).optional(),
  responsavel_tecnico: z.string().max(255).optional(),
  observacoes: z.string().max(2000).optional(),
}).refine((data) => {
  // Validação: UNIDADE_CONDOMINIO deve ter parent_obra_id
  if (data.tipo_projeto === 'UNIDADE_CONDOMINIO' && !data.parent_obra_id) {
    return false;
  }
  return true;
}, {
  message: "Unidades de condomínio devem ter uma obra-mãe associada",
  path: ["parent_obra_id"]
}).refine((data) => {
  // Validação: UNIDADE_CONDOMINIO deve ter identificador_unidade
  if (data.tipo_projeto === 'UNIDADE_CONDOMINIO' && !data.identificador_unidade?.trim()) {
    return false;
  }
  return true;
}, {
  message: "Unidades de condomínio devem ter um identificador único",
  path: ["identificador_unidade"]
}).refine((data) => {
  // Validação: CONDOMINIO_MASTER não deve ter parent_obra_id
  if (data.tipo_projeto === 'CONDOMINIO_MASTER' && data.parent_obra_id) {
    return false;
  }
  return true;
}, {
  message: "Condomínios principais não podem ter obra-mãe",
  path: ["parent_obra_id"]
});

// Schema para filtros de listagem de obras com condomínios
export const obrasCondominioFilterSchema = z.object({
  tipo_projeto: TipoProjetoEnum.optional(),
  parent_obra_id: z.string().uuid().optional(),
  incluir_unidades: z.boolean().default(false),
  apenas_condominios: z.boolean().default(false),
  search: z.string().max(255).optional(),
  status: z.enum(['planejamento', 'em_andamento', 'concluida', 'pausada']).optional(),
  construtora_id: z.string().uuid().optional(),
  cidade: z.string().max(100).optional(),
  estado: z.string().length(2).optional(),
});

// Tipos inferidos dos schemas
export type TipoProjetoType = z.infer<typeof TipoProjetoEnum>;
export type UnidadeFormDataValues = z.infer<typeof unidadeFormDataSchema>;
export type CondominioDataValues = z.infer<typeof condominioDataSchema>;
export type CreateCondominioRequestValues = z.infer<typeof createCondominioRequestSchema>;
export type ObraCondominioFormValues = z.infer<typeof obraCondominioSchema>;
export type ObrasCondominioFilterValues = z.infer<typeof obrasCondominioFilterSchema>;

// Constantes para uso em formulários
export const TIPOS_PROJETO_OPTIONS = [
  { value: 'UNICO', label: 'Obra Única' },
  { value: 'CONDOMINIO_MASTER', label: 'Condomínio' },
] as const;

export const STATUS_OBRA_OPTIONS = [
  { value: 'planejamento', label: 'Planejamento' },
  { value: 'em_andamento', label: 'Em Andamento' },
  { value: 'concluida', label: 'Concluída' },
  { value: 'pausada', label: 'Pausada' },
] as const;

// Utilitários de validação
export const isCondominio = (obra: { tipo_projeto?: string }): boolean => {
  return obra.tipo_projeto === 'CONDOMINIO_MASTER';
};

export const isUnidadeCondominio = (obra: { tipo_projeto?: string }): boolean => {
  return obra.tipo_projeto === 'UNIDADE_CONDOMINIO';
};

export const isObraUnica = (obra: { tipo_projeto?: string }): boolean => {
  return !obra.tipo_projeto || obra.tipo_projeto === 'UNICO';
};