import { z } from "zod";

// Regex para validar CEP brasileiro (formato: 00000-000 ou 00000000)
// const cepRegex = /^(\d{5}-?\d{3})$/;

// Enum para tipos de projeto (compatibilidade com condomínios)
export const TipoProjetoEnum = z.enum(
  ["UNICO", "CONDOMINIO_MASTER", "UNIDADE_CONDOMINIO"],
  {
    errorMap: () => ({ message: "Tipo de projeto inválido" }),
  }
);

// Schema para dados de unidade individual
export const unidadeSchema = z.object({
  identificador_unidade: z
    .string()
    .min(1, "Identificador da unidade é obrigatório"),
  nome: z.string().min(1, "Nome da unidade é obrigatório"),
});

export const obraSchema = z.object({
  // Detalhes básicos
  nome: z.string().min(3, "O nome da obra é obrigatório"),
  construtora_id: z.string().min(1, "A construtora é obrigatória"),

  // Localização
  cep: z.string().min(8, "O CEP é obrigatório"),
  endereco: z.string().min(3, "O endereço é obrigatório"),
  cidade: z.string().min(3, "A cidade é obrigatória"),
  estado: z.string().min(2, "O estado é obrigatório"),

  // Financeiro e Métricas
  orcamento: z.number().nonnegative("O orçamento deve ser um número positivo"),
  area_total: z
    .number()
    .nonnegative("A área total deve ser um número positivo ou zero"),

  // Datas
  data_inicio: z.date().nullable(),
  data_prevista_termino: z.date().nullable(),

  // Campos de Condomínio (opcionais)
  tipo_projeto: TipoProjetoEnum.default("UNICO"),
  tipo_condominio: z.enum(["VERTICAL", "HORIZONTAL"]).optional(),
  numero_blocos: z.number().optional(),
  andares_por_bloco: z.number().optional(),
  unidades_por_andar: z.number().optional(),
  numero_unidades: z.number().optional(),
  area_lote: z.number().optional(),
  area_construida_unidade: z.number().optional(),
});

// Schema específico para formulário de condomínio
export const obraComCondomínioSchema = obraSchema.extend({
  tipo_projeto: TipoProjetoEnum,
  unidades: z
    .array(unidadeSchema)
    .min(1, "Pelo menos uma unidade é necessária para condomínios"),
});

export type ObraFormValues = z.infer<typeof obraSchema>;
export type ObraComCondomínioFormValues = z.infer<
  typeof obraComCondomínioSchema
>;
export type UnidadeFormData = z.infer<typeof unidadeSchema>;
export type TipoProjetoType = z.infer<typeof TipoProjetoEnum>;
