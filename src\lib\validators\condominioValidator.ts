import * as z from 'zod';

export const unidadeSchema = z.object({
  identificador_unidade: z.string().min(1, 'O identificador da unidade é obrigatório.'),
  nome: z.string().min(1, 'O nome da unidade é obrigatório.'),
  // Adicione outras validações de unidade aqui
});

export const condominioSchema = z.object({
  obra_mae: z.object({
    nome: z.string().min(1, 'O nome do condomínio é obrigatório.'),
    endereco: z.string().min(1, 'O endereço é obrigatório.'),
    cidade: z.string().min(1, 'A cidade é obrigatória.'),
    estado: z.string().min(2, 'O estado é obrigatório.'),
    cep: z.string().min(8, 'O CEP é obrigatório.'),
    // Adicione outras validações da obra mãe aqui
  }),
  unidades: z.array(unidadeSchema).min(1, 'É necessário adicionar pelo menos uma unidade.'),
});