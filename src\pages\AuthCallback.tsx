import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

import { supabase } from '@/integrations/supabase/client';

const AuthCallback = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Processar o callback do OAuth
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Erro no callback OAuth:', error);
          toast.error('Erro na autenticação com Google');
          navigate('/login');
          return;
        }

        if (data.session) {
          console.log('🎉 OAuth bem-sucedido! Redirecionando para dashboard');
          toast.success('Login realizado com sucesso!');
          navigate('/dashboard');
        } else {
          console.log('⚠️ Sem sessão no callback, redirecionando para login');
          navigate('/login');
        }
      } catch (error) {
        console.error('Exceção no callback OAuth:', error);
        toast.error('Erro na autenticação');
        navigate('/login');
      }
    };

    handleAuthCallback();
  }, [navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
        <h2 className="text-lg font-semibold text-gray-800 mb-2">
          Processando autenticação...
        </h2>
        <p className="text-gray-600">
          Aguarde enquanto concluímos seu login com Google
        </p>
      </div>
    </div>
  );
};

export default AuthCallback;