import { motion } from "framer-motion";
import {
    Bell,
    Settings as SettingsI<PERSON>,
    Shield,
    Smartphone,
    User
} from "lucide-react";
import { useState } from "react";

import DashboardLayout from "@/components/layouts/DashboardLayout";
import { DeviceSettings } from "@/components/settings/DeviceSettings";
import { NotificationSettings } from "@/components/settings/NotificationSettings";
import { ProfileFormSimple } from "@/components/settings/ProfileFormSimple";
import { SecuritySettings } from "@/components/settings/SecuritySettings";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";

const Settings = () => {
  const [activeTab, setActiveTab] = useState("profile");
  
  const tabs = [
    { id: "profile", label: "Perfil", icon: User, color: "text-blue-600" },
    { id: "notifications", label: "Notificações", icon: Bell, color: "text-green-600" },
    { id: "security", label: "Segurança", icon: Shield, color: "text-red-600" },
    { id: "devices", label: "Dispositivos", icon: Smartphone, color: "text-orange-600" },
  ];
  
  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex items-center gap-3">
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ 
              type: "spring",
              stiffness: 260,
              damping: 20
            }}
            className="h-10 w-10 rounded-lg bg-gradient-to-br from-gray-500 to-gray-600 flex items-center justify-center shadow-lg"
          >
            <SettingsIcon className="h-6 w-6 text-white" />
          </motion.div>
          <div>
            <h2 className="text-2xl font-bold">Configurações</h2>
            <p className="text-muted-foreground">Gerencie notificações, segurança e dispositivos conectados.</p>
          </div>
        </div>
        
        <div className="grid grid-cols-12 gap-6">
          {/* Sidebar de navegação */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="col-span-12 lg:col-span-3"
          >
            <Card className="border-border/50 bg-card/50 backdrop-blur-sm sticky top-6">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Menu de Configurações
                </CardTitle>
              </CardHeader>
              <CardContent className="p-2">
                <nav className="space-y-1">
                  {tabs.map((tab, index) => {
                    const Icon = tab.icon;
                    const isActive = activeTab === tab.id;
                    
                    return (
                      <motion.button
                        key={tab.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 + index * 0.05 }}
                        onClick={() => setActiveTab(tab.id)}
                        className={cn(
                          "w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                          "hover:bg-accent/50",
                          isActive && "bg-accent shadow-sm"
                        )}
                      >
                        <div className={cn(
                          "h-8 w-8 rounded-lg flex items-center justify-center transition-all duration-300",
                          isActive ? "bg-background shadow-sm" : "bg-muted/50",
                          isActive && "scale-110"
                        )}>
                          <Icon className={cn("h-4 w-4", tab.color)} />
                        </div>
                        <span className={cn(
                          "transition-colors",
                          isActive ? "text-foreground" : "text-muted-foreground"
                        )}>
                          {tab.label}
                        </span>
                        {isActive && (
                          <motion.div
                            layoutId="activeTab"
                            className="ml-auto h-2 w-2 rounded-full bg-primary"
                            transition={{ type: "spring", duration: 0.5 }}
                          />
                        )}
                      </motion.button>
                    );
                  })}
                </nav>
              </CardContent>
            </Card>
          </motion.div>
          
          {/* Conteúdo principal */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="col-span-12 lg:col-span-9"
          >
            <Card className="border-border/50 bg-card/95 backdrop-blur-sm">
              <CardContent className="p-6">
                <Tabs value={activeTab} className="w-full">
                  <TabsContent value="profile" className="space-y-6 mt-0">
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold">Informações do Perfil</h3>
                      <p className="text-sm text-muted-foreground">
                        Gerencie suas informações pessoais e de contato. O telefone é essencial para comunicação.
                      </p>
                    </div>
                    <ProfileFormSimple />
                  </TabsContent>
                  
                  <TabsContent value="notifications" className="space-y-6 mt-0">
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold">Notificações do Sistema</h3>
                      <p className="text-sm text-muted-foreground">
                        Configure alertas para obras, leads, contratos e processos de IA.
                      </p>
                    </div>
                    <NotificationSettings />
                  </TabsContent>
                  
                  <TabsContent value="security" className="space-y-6 mt-0">
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold">Segurança e Privacidade</h3>
                      <p className="text-sm text-muted-foreground">
                        Proteja sua conta com autenticação em dois fatores e auditoria de acesso.
                      </p>
                    </div>
                    <SecuritySettings />
                  </TabsContent>
                  
                  <TabsContent value="devices" className="space-y-6 mt-0">
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold">Dispositivos e Sessões</h3>
                      <p className="text-sm text-muted-foreground">
                        Gerencie acesso de dispositivos móveis e sessões ativas no sistema.
                      </p>
                    </div>
                    <DeviceSettings />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </motion.div>
    </DashboardLayout>
  );
};

export default Settings;
