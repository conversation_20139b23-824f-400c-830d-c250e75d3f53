import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { useMemo, useState } from "react";

import ChatContextSidebar from "@/components/ai/ChatContextSidebar";
import InterfaceChat from "@/components/ai/InterfaceChat";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import { useAuth } from "@/contexts/auth";
import { validateTenantId } from "@/contexts/auth/utils";
import { obrasApi } from "@/services/api";

const ChatAIPage = () => {
  const { user } = useAuth();
  const TOPICS = ["despesas", "obras", "orcamento", "contratos", "vendas", "controle_orcamentario", "sinapi", "fornecedores", "assistente_pessoal"] as const;

  const [selection, setSelection] = useState<string>("");

  const tenantId = validateTenantId(user?.profile?.tenant_id);

  // Carregar obras para incluir no seletor
  const { data: obras = [] } = useQuery({
    queryKey: ["obras", tenantId],
    queryFn: () => tenantId ? obrasApi.getAll(tenantId) : Promise.resolve([]),
    enabled: !!tenantId, // Só carrega se tiver tenantId válido
  });

  const [selectedObraId, setSelectedObraId] = useState<string | null>(null);

  const { mode, topic } = useMemo(() => {
    if (selection.startsWith("topic:")) {
      const t = selection.replace("topic:", "");
      return { mode: "training" as const, topic: t };
    }
    // Default para chat geral quando não há seleção de tópico
    return { mode: "chat" as const, topic: undefined };
  }, [selection]);

  const handleObraChange = (obraId: string | null) => {
    setSelectedObraId(obraId);
    // Se selecionar uma obra, limpar seleção de tópico para voltar ao chat geral
    if (obraId) {
      setSelection("");
    }
  };

  return (
    <DashboardLayout>
      <div className="flex flex-col lg:flex-row h-[calc(100vh-theme(spacing.16))] max-h-[calc(100vh-theme(spacing.16))] overflow-hidden">
        <div className="flex-1 flex flex-col min-h-0 order-2 lg:order-1">
          {/* Área do chat - altura responsiva */}
          <div className="flex-1 min-h-0 p-2 sm:p-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="h-full flex justify-center"
            >
              <div className="w-full max-w-4xl h-full min-h-0">
                <InterfaceChat 
                  mode={mode} 
                  topic={topic} 
                  obraId={selectedObraId} 
                  onObraChange={handleObraChange}
                />
              </div>
            </motion.div>
          </div>
        </div>

        {/* Sidebar responsivo */}
        <div className="w-full lg:w-80 border-b lg:border-b-0 lg:border-l border-border/50 order-1 lg:order-2 max-h-48 lg:max-h-full lg:min-h-0 overflow-hidden">
          <ChatContextSidebar topics={[...TOPICS]} obras={obras} value={selection} onChange={setSelection} />
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ChatAIPage;
