import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { ColumnDef } from "@tanstack/react-table";
import { motion } from "framer-motion";
import {
    AlertTriangle,
    Building2,
    Loader2,
    <PERSON><PERSON><PERSON>,
    Plus,
    Trash2,
    User,
    Users
} from "lucide-react";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";

import { BarraBusca } from "@/components/fornecedores/BarraBusca";
import { FiltroCategoria } from "@/components/fornecedores/FiltroCategoria";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTable } from "@/components/ui/data-table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/contexts/auth";
import { formatDateBR } from "@/lib/i18n";
import { cn } from "@/lib/utils";
import { CategoriaPFBadge } from "@/lib/utils/fornecedores";
import { fornecedoresPFApi } from "@/services/api";

interface FornecedorPF {
  id: string;
  nome: string;
  cpf: string;
  tipo_fornecedor: string | null;
  email: string | null;
  telefone_principal: string | null;
  data_nascimento: string | null;
}

const FornecedoresPFLista = () => {
  const navigate = useNavigate();
  const [fornecedorToDelete, setFornecedorToDelete] = useState<string | null>(null);
  const [selectedFornecedores, setSelectedFornecedores] = useState<string[]>([]);
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoriaFilter, setCategoriaFilter] = useState<string | undefined>();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Obter tenant_id corretamente do usuário logado
  const tenantId = user?.profile?.tenant_id;
  const validTenantId = tenantId && typeof tenantId === 'string' ? tenantId : null;

  const { data: fornecedores, isLoading, isError, refetch } = useQuery({
    queryKey: ["fornecedores_pf", validTenantId],
    queryFn: () => {
      if (!validTenantId) {
        throw new Error('Tenant ID não encontrado ou inválido');
      }
      return fornecedoresPFApi.getAll(validTenantId);
    },
    enabled: !!validTenantId,
    retry: (failureCount, _error) => {
      return failureCount < 1; // Máximo 1 tentativa
    },
  });

  // Filtrar dados baseado na busca e categoria
  const filteredFornecedores = fornecedores?.filter((fornecedor) => {
    const matchesSearch = !searchTerm ||
      fornecedor.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fornecedor.cpf.includes(searchTerm) ||
      fornecedor.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fornecedor.telefone_principal?.includes(searchTerm);

    const matchesCategoria = !categoriaFilter || fornecedor.tipo_fornecedor === categoriaFilter;

    return matchesSearch && matchesCategoria;
  }) || [];

  const deleteMutation = useMutation({
    mutationFn: (id: string) => fornecedoresPFApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries(["fornecedores_pf", validTenantId]);
      setFornecedorToDelete(null);
    },
    onError: (error) => {
      console.error("Error deleting fornecedor PF:", error);
    },
  });

  const handleDelete = async () => {
    if (!fornecedorToDelete) return;
    deleteMutation.mutate(fornecedorToDelete);
  };

  const handleBulkDelete = async () => {
    if (selectedFornecedores.length === 0) return;

    try {
      for (const id of selectedFornecedores) {
        await fornecedoresPFApi.delete(id);
      }
      queryClient.invalidateQueries(["fornecedores_pf", validTenantId]);
      setSelectedFornecedores([]);
      setShowBulkDeleteDialog(false);
    } catch (_error) {
      console.error("Error deleting fornecedores:", _error);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedFornecedores(fornecedores?.map(f => f.id) || []);
    } else {
      setSelectedFornecedores([]);
    }
  };

  const handleSelectFornecedor = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedFornecedores(prev => [...prev, id]);
    } else {
      setSelectedFornecedores(prev => prev.filter(fId => fId !== id));
    }
  };

  const columns: ColumnDef<FornecedorPF>[] = [
    {
      id: "select",
      header: ({ _table }) => (
        <Checkbox
          checked={selectedFornecedores.length === fornecedores?.length && fornecedores?.length > 0}
          onCheckedChange={(checked) => handleSelectAll(!!checked)}
          aria-label="Selecionar todos"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={selectedFornecedores.includes(row.original.id)}
          onCheckedChange={(checked) => handleSelectFornecedor(row.original.id, !!checked)}
          aria-label="Selecionar linha"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "nome",
      header: "Nome",
      cell: ({ row }) => (
        <div className="font-semibold text-slate-900 dark:text-slate-100 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200 cursor-pointer">
          {row.original.nome}
        </div>
      ),
    },
    {
      accessorKey: "cpf",
      header: "CPF",
      cell: ({ row }) => (
        <span className="font-mono text-sm text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors duration-200 bg-slate-50 dark:bg-slate-800 px-2 py-1 rounded">
          {row.original.cpf}
        </span>
      ),
    },
    {
      accessorKey: "tipo_fornecedor",
      header: "Tipo de Serviço",
      cell: ({ row }) => (
        <CategoriaPFBadge categoria={row.original.tipo_fornecedor} />
      ),
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => (
        <span className="text-sm text-slate-600 dark:text-slate-400">
          {row.original.email || "-"}
        </span>
      ),
    },
    {
      accessorKey: "telefone_principal",
      header: "Telefone",
      cell: ({ row }) => (
        <span className="font-mono text-sm text-slate-600 dark:text-slate-400">
          {row.original.telefone_principal || "-"}
        </span>
      ),
    },
    {
      accessorKey: "data_nascimento",
      header: "Nascimento",
      cell: ({ row }) => (
        <span className="text-sm text-slate-500 dark:text-slate-400">
          {formatDateBR(row.original.data_nascimento)}
        </span>
      ),
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            title="Editar"
            onClick={() => navigate(`/dashboard/fornecedores/pf/${row.original.id}/editar`)}
            className="h-8 w-8 text-sky-600 dark:text-sky-400 hover:bg-sky-100 dark:hover:bg-sky-900/30 hover:text-sky-700 dark:hover:text-sky-300 transition-colors"
          >
            <Pencil className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            title="Excluir"
            onClick={() => setFornecedorToDelete(row.original.id)}
            className="h-8 w-8 text-rose-600 dark:text-rose-400 hover:bg-rose-100 dark:hover:bg-rose-900/30 hover:text-rose-700 dark:hover:text-rose-300 transition-colors"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return (
      <DashboardLayout>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex items-center justify-center h-96"
        >
          <div className="text-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-green-500" />
            <p className="text-muted-foreground">Carregando fornecedores...</p>
          </div>
        </motion.div>
      </DashboardLayout>
    );
  }

  if (isError) {
    return (
      <DashboardLayout>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col items-center justify-center h-96 space-y-4"
        >
          <div className="h-16 w-16 rounded-full bg-red-500/10 flex items-center justify-center">
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
          <div className="text-center space-y-2">
            <h3 className="text-lg font-semibold">Erro ao carregar fornecedores</h3>
            <p className="text-muted-foreground">Não foi possível carregar a lista de fornecedores.</p>
          </div>
          <Button onClick={() => refetch()} variant="outline">
            Tentar novamente
          </Button>
        </motion.div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex items-center justify-between">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="flex items-center gap-3"
          >
            <div className="h-10 w-10 rounded-lg bg-green-500/10 dark:bg-green-400/10 flex items-center justify-center">
              <Users className="h-6 w-6 text-green-500 dark:text-green-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Fornecedores</h1>
              <p className="text-sm text-muted-foreground">
                Gerencie fornecedores pessoa física e jurídica
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="flex gap-2"
          >
            {selectedFornecedores.length > 0 && (
              <Button
                variant="destructive"
                onClick={() => setShowBulkDeleteDialog(true)}
                className="bg-red-500 hover:bg-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Excluir Selecionados ({selectedFornecedores.length})
              </Button>
            )}

            <Button
              asChild
              className={cn(
                              "bg-gradient-to-r from-green-500 to-green-600",
              "hover:from-green-600 hover:to-green-700",
                "text-white shadow-lg",
                "transition-all duration-300"
              )}
            >
              <Link to="/dashboard/fornecedores/novo">
                <Plus className="h-4 w-4 mr-2" />
                Novo Fornecedor
              </Link>
            </Button>
          </motion.div>
        </div>

        {/* Tabs para alternar entre PF e PJ */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Tabs defaultValue="pf" className="space-y-6">
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="pf" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Pessoa Física
              </TabsTrigger>
              <TabsTrigger value="pj" asChild>
                <Link to="/dashboard/fornecedores/pj" className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Pessoa Jurídica
                </Link>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="pf">
              {/* Filtros */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mb-6"
              >
                <Card className="border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-br from-purple-50/50 to-pink-50/50 dark:from-purple-900/10 dark:to-pink-900/10 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-slate-700 dark:text-slate-300">
                      Buscar e Filtrar
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
                      <div className="flex-1 min-w-0">
                        <BarraBusca
                          value={searchTerm}
                          onValueChange={setSearchTerm}
                          placeholder="Buscar por nome, CPF, email..."
                          className="w-full"
                        />
                      </div>
                      <div className="flex gap-3">
                        <FiltroCategoria
                          tipo="pf"
                          value={categoriaFilter}
                          onValueChange={setCategoriaFilter}
                        />
                        {(searchTerm || categoriaFilter) && (
                          <Button
                            variant="outline"
                            onClick={() => {
                              setSearchTerm("");
                              setCategoriaFilter(undefined);
                            }}
                            className="text-slate-600 dark:text-slate-400"
                          >
                            Limpar filtros
                          </Button>
                        )}
                      </div>
                    </div>
                    {filteredFornecedores.length !== fornecedores?.length && (
                      <div className="mt-3 text-sm text-slate-600 dark:text-slate-400">
                        Mostrando {filteredFornecedores.length} de {fornecedores?.length || 0} fornecedores
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>

              {/* Tabela */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Card className="border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-br from-white/95 to-slate-50/95 dark:from-slate-900/95 dark:to-slate-800/95 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <div className="h-8 w-8 rounded-lg bg-green-500/10 dark:bg-green-400/10 flex items-center justify-center">
                        <User className="h-5 w-5 text-green-500 dark:text-green-400" />
                      </div>
                      <span className="text-green-700 dark:text-green-300">Fornecedores Pessoa Física</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    <DataTable columns={columns} data={filteredFornecedores} />
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          </Tabs>
        </motion.div>

        {/* Modal de confirmação de exclusão */}
        <AlertDialog open={!!fornecedorToDelete} onOpenChange={() => setFornecedorToDelete(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza de que deseja excluir este fornecedor? Esta ação não pode ser desfeita.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-red-500 hover:bg-red-600"
              >
                Excluir
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Modal de confirmação de exclusão em massa */}
        <AlertDialog open={showBulkDeleteDialog} onOpenChange={setShowBulkDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirmar exclusão em massa</AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza de que deseja excluir {selectedFornecedores.length} fornecedor(es) selecionado(s)? Esta ação não pode ser desfeita.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleBulkDelete}
                className="bg-red-500 hover:bg-red-600"
              >
                Excluir Todos
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </motion.div>
    </DashboardLayout>
  );
};

export default FornecedoresPFLista;
