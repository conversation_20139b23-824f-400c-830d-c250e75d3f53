import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import {
    <PERSON>ertCircle,
    ArrowLeft,
    Building2,
    CheckCircle,
    FileText,
    Globe,
    Loader2,
    Mail,
    MapPin,
    Phone,
    Save,
    Search,
    User
} from "lucide-react";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

import DashboardLayout from "@/components/layouts/DashboardLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DatePicker } from "@/components/ui/date-picker";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/contexts/auth";
import { useLoading } from "@/contexts/LoadingContext";
import { useCNPJLookup } from "@/hooks/useCNPJLookup";
import { useTenantValidation } from "@/hooks/useTenantValidation";
import { OPCOES_CATEGORIA_PF, OPCOES_CATEGORIA_PJ } from "@/lib/constants/fornecedores";
import { brazilianStates } from "@/lib/i18n";
import { cn } from "@/lib/utils";
import { formatCEP, formatCNPJ, formatCPF, formatPhone, isComplete } from "@/lib/utils/formatters";
import type {
    FornecedorPFFormValues,
    FornecedorPJFormValues,
    FornecedorType
} from "@/lib/validations/fornecedor";
import {
    fornecedorPFSchema,
    fornecedorPJSchema
} from "@/lib/validations/fornecedor";
import { fornecedoresPFApi, fornecedoresPJApi } from "@/services/api";

// Componente interno que usa o FormContext
const NovoFornecedorForm = memo(() => {
  const navigate = useNavigate();
  const { user: _user } = useAuth(); // Não utilizado diretamente, mas mantido por compatibilidade
  const { setLoading: _setLoading, isLoading: _isLoading } = useLoading(); // Loading é gerenciado localmente
  const [fornecedorType, setFornecedorType] = useState<FornecedorType>("pj");
  const { lookupCNPJ, isLoading: isLoadingCNPJ, data: cnpjData, reset: resetCNPJ } = useCNPJLookup();
  
  // Flag para controlar se o preenchimento automático já foi feito
  const filledFromCNPJRef = useRef<string | null>(null);
  
  // Obter tenant_id corretamente
  const { validTenantId } = useTenantValidation();

  // Debug logs removidos - implementação limpa
  
  // Form para PJ usando useForm diretamente
  const pjForm = useForm<FornecedorPJFormValues>({
    resolver: zodResolver(fornecedorPJSchema),
    defaultValues: {
      cnpj: "",
      razao_social: "",
      nome_fantasia: "",
      categoria: "",
      email: "",
      telefone_principal: "",
      endereco: "",
      numero: "",
      complemento: "",
      bairro: "",
      cidade: "",
      estado: "",
      cep: "",
      observacoes: "",
    },
  });

  // Form para PF usando useForm diretamente
  const pfForm = useForm<FornecedorPFFormValues>({
    resolver: zodResolver(fornecedorPFSchema),
    defaultValues: {
      cpf: "",
      nome: "",
      tipo_fornecedor: "",
      email: "",
      telefone_principal: "",
      data_nascimento: null,
      observacoes: "",
    },
  });

  // Watch do campo CNPJ para busca automática
  const cnpjValue = pjForm.watch("cnpj");

  // Effect para buscar CNPJ automaticamente quando o campo for preenchido
  useEffect(() => {
    const timer = setTimeout(async () => {
      // Verificar se o CNPJ está completo e se ainda não foi preenchido
      if (cnpjValue && isComplete(cnpjValue, 'cnpj') && filledFromCNPJRef.current !== cnpjValue) {
        const data = await lookupCNPJ(cnpjValue);
        
        if (data) {
          // Marcar que este CNPJ já foi processado
          filledFromCNPJRef.current = cnpjValue;
          
          // Preencher automaticamente TODOS os campos disponíveis
          pjForm.setValue("razao_social", data.razao_social);
          pjForm.setValue("nome_fantasia", data.nome_fantasia || "");
          
          if (data.email) {
            pjForm.setValue("email", data.email);
          }
          
          if (data.telefone_principal) {
            pjForm.setValue("telefone_principal", data.telefone_principal);
          }
          
          // Preencher dados de endereço se disponíveis
          if (data.endereco) {
            if (data.endereco.logradouro) {
              pjForm.setValue("endereco", data.endereco.logradouro);
            }
            if (data.endereco.numero) {
              pjForm.setValue("numero", data.endereco.numero);
            }
            if (data.endereco.complemento) {
              pjForm.setValue("complemento", data.endereco.complemento);
            }
            if (data.endereco.bairro) {
              pjForm.setValue("bairro", data.endereco.bairro);
            }
            if (data.endereco.municipio) {
              pjForm.setValue("cidade", data.endereco.municipio);
            }
            if (data.endereco.uf) {
              pjForm.setValue("estado", data.endereco.uf);
            }
            if (data.endereco.cep) {
              pjForm.setValue("cep", formatCEP(data.endereco.cep));
            }
          }
          
          // Mostrar feedback sobre o preenchimento automático
          const fieldsCount = [
            data.razao_social,
            data.nome_fantasia,
            data.telefone_principal,
            data.endereco?.logradouro,
            data.endereco?.bairro,
            data.endereco?.municipio,
            data.endereco?.uf,
            data.endereco?.cep
          ].filter(Boolean).length;
          
          if (fieldsCount > 3) {
            toast.success(`${fieldsCount} campos preenchidos automaticamente!`);
          }
        }
      }
    }, 1000); // Delay de 1 segundo para evitar muitas requisições

    return () => clearTimeout(timer);
  }, [cnpjValue, lookupCNPJ, pjForm]); // Adicionado pjForm nas dependências

  // Resetar dados do CNPJ quando mudar de aba
  useEffect(() => {
    resetCNPJ();
    // Resetar a flag também
    filledFromCNPJRef.current = null;
  }, [fornecedorType, resetCNPJ]);

  // Resetar flag quando o CNPJ for alterado manualmente
  useEffect(() => {
    if (!cnpjValue || !isComplete(cnpjValue, 'cnpj')) {
      filledFromCNPJRef.current = null;
    }
  }, [cnpjValue]);

  // Estado manual para controle de loading e erro
  const [isCreatingPJ, setIsCreatingPJ] = useState(false);

  const [isCreatingPF, setIsCreatingPF] = useState(false);

  const onSubmitPJ = useCallback(async (values: FornecedorPJFormValues) => {
    console.log('🔍 === INÍCIO DO CADASTRO PJ ===');
    console.log('🔍 Valores recebidos:', values);
    console.log('🔍 validTenantId:', validTenantId);

    setIsCreatingPJ(true);

    try {
      // Verificar tenant ID
      if (!validTenantId) {
        throw new Error('Tenant ID não encontrado');
      }

      // Validar dados com schema
      console.log('🔍 Validando dados com schema...');
      const validationResult = fornecedorPJSchema.safeParse(values);
      if (!validationResult.success) {
        console.error('🔍 Erro de validação:', validationResult.error);
        const errorMessages = validationResult.error.issues.map(i => `${i.path.join('.')}: ${i.message}`).join(', ');
        throw new Error(`Erro de validação: ${errorMessages}`);
      }

      console.log('🔍 Dados validados com sucesso');
      console.log('🔍 Chamando fornecedoresPJApi.create...');

      // Chamar API diretamente
      const result = await fornecedoresPJApi.create(values, validTenantId);

      console.log('🔍 ✅ Fornecedor criado com sucesso:', result);
      toast.success("Fornecedor PJ criado com sucesso!");
      navigate("/dashboard/fornecedores/pj");

    } catch (error) {
      console.error('🔍 ❌ ERRO COMPLETO:', error);
      console.error('🔍 Tipo do erro:', typeof error);
      console.error('🔍 Erro como string:', String(error));
      console.error('🔍 Erro.message:', error?.message);
      console.error('🔍 Erro.code:', error?.code);
      console.error('🔍 Erro.details:', error?.details);
      console.error('🔍 Erro.hint:', error?.hint);
      console.error('🔍 Erro completo JSON:', JSON.stringify(error, null, 2));

      // Extrair mensagem de erro mais específica
      let errorMessage = 'Erro desconhecido ao criar fornecedor';

      if (error?.message) {
        errorMessage = error.message;
      } else if (error?.details) {
        errorMessage = error.details;
      } else if (error?.hint) {
        errorMessage = error.hint;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      toast.error(`Erro ao criar fornecedor PJ: ${errorMessage}`);
    } finally {
      setIsCreatingPJ(false);
    }
  }, [validTenantId, navigate]);

  const onSubmitPF = useCallback(async (values: FornecedorPFFormValues) => {
    console.log('🔍 === INÍCIO DO CADASTRO PF ===');
    console.log('🔍 Valores recebidos:', values);
    console.log('🔍 validTenantId:', validTenantId);

    setIsCreatingPF(true);

    try {
      // Verificar tenant ID
      if (!validTenantId) {
        throw new Error('Tenant ID não encontrado');
      }

      // Validar dados com schema
      console.log('🔍 Validando dados com schema...');
      const validationResult = fornecedorPFSchema.safeParse(values);
      if (!validationResult.success) {
        console.error('🔍 Erro de validação:', validationResult.error);
        const errorMessages = validationResult.error.issues.map(i => `${i.path.join('.')}: ${i.message}`).join(', ');
        throw new Error(`Erro de validação: ${errorMessages}`);
      }

      console.log('🔍 Dados validados com sucesso');
      console.log('🔍 Chamando fornecedoresPFApi.create...');

      // Chamar API diretamente
      const result = await fornecedoresPFApi.create(values, validTenantId);

      console.log('🔍 ✅ Fornecedor PF criado com sucesso:', result);
      toast.success("Fornecedor PF criado com sucesso!");
      navigate("/dashboard/fornecedores/pf");

    } catch (error) {
      console.error('🔍 ❌ ERRO COMPLETO PF:', error);
      console.error('🔍 Tipo do erro:', typeof error);
      console.error('🔍 Erro como string:', String(error));
      console.error('🔍 Erro.message:', error?.message);
      console.error('🔍 Erro.code:', error?.code);
      console.error('🔍 Erro.details:', error?.details);
      console.error('🔍 Erro.hint:', error?.hint);

      // Extrair mensagem de erro mais específica
      let errorMessage = 'Erro desconhecido ao criar fornecedor PF';

      if (error?.message) {
        errorMessage = error.message;
      } else if (error?.details) {
        errorMessage = error.details;
      } else if (error?.hint) {
        errorMessage = error.hint;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      toast.error(`Erro ao criar fornecedor PF: ${errorMessage}`);
    } finally {
      setIsCreatingPF(false);
    }
  }, [validTenantId, navigate]);

  // Função para buscar CNPJ manualmente
  const handleManualCNPJLookup = useCallback(async () => {
    const cnpjValue = pjForm.getValues("cnpj");
    if (cnpjValue) {
      const data = await lookupCNPJ(cnpjValue);
      
      if (data) {
        // Marcar que este CNPJ já foi processado para evitar duplicação
        filledFromCNPJRef.current = cnpjValue;
        
        // Mesmo preenchimento automático da função principal
        pjForm.setValue("razao_social", data.razao_social);
        pjForm.setValue("nome_fantasia", data.nome_fantasia || "");
        
        if (data.email) {
          pjForm.setValue("email", data.email);
        }
        
        if (data.telefone_principal) {
          pjForm.setValue("telefone_principal", data.telefone_principal);
        }
        
        if (data.endereco) {
          if (data.endereco.logradouro) pjForm.setValue("endereco", data.endereco.logradouro);
          if (data.endereco.numero) pjForm.setValue("numero", data.endereco.numero);
          if (data.endereco.complemento) pjForm.setValue("complemento", data.endereco.complemento);
          if (data.endereco.bairro) pjForm.setValue("bairro", data.endereco.bairro);
          if (data.endereco.municipio) pjForm.setValue("cidade", data.endereco.municipio);
          if (data.endereco.uf) pjForm.setValue("estado", data.endereco.uf);
          if (data.endereco.cep) pjForm.setValue("cep", formatCEP(data.endereco.cep));
        }
        
        // Mostrar feedback sobre o preenchimento manual
        const fieldsCount = [
          data.razao_social,
          data.nome_fantasia,
          data.telefone_principal,
          data.endereco?.logradouro,
          data.endereco?.bairro,
          data.endereco?.municipio,
          data.endereco?.uf,
          data.endereco?.cep
        ].filter(Boolean).length;
        
        if (fieldsCount > 3) {
          toast.success(`${fieldsCount} campos preenchidos manualmente!`);
        }
      }
    }
  }, [pjForm, lookupCNPJ]);

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex items-center justify-between">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="flex items-center gap-3"
          >
            <div className="h-10 w-10 rounded-lg bg-green-500/10 dark:bg-green-400/10 flex items-center justify-center">
              {fornecedorType === "pj" ? (
                <Building2 className="h-6 w-6 text-green-500 dark:text-green-400" />
              ) : (
                <User className="h-6 w-6 text-green-500 dark:text-green-400" />
              )}
            </div>
            <div>
              <h1 className="text-2xl font-bold">
                {fornecedorType === "pj" 
                  ? "Novo Fornecedor PJ" 
                  : "Novo Fornecedor PF"}
              </h1>
              <p className="text-sm text-muted-foreground">
                Cadastre um novo fornecedor no sistema
              </p>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Button
              variant="outline"
              onClick={() => navigate(
                fornecedorType === "pj" 
                  ? "/dashboard/fornecedores/pj" 
                  : "/dashboard/fornecedores/pf"
              )}
              className="group"
            >
              <ArrowLeft className="h-4 w-4 mr-2 transition-transform group-hover:-translate-x-1" />
              Voltar
            </Button>
          </motion.div>
        </div>

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Tabs 
            defaultValue="pj" 
            onValueChange={(value) => setFornecedorType(value as FornecedorType)}
            className="space-y-6"
          >
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="pj" className="flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                Pessoa Jurídica
              </TabsTrigger>
              <TabsTrigger value="pf" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Pessoa Física
              </TabsTrigger>
            </TabsList>

            {/* Formulário PJ */}
            <TabsContent value="pj">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="border-border/50 bg-card/95 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="h-5 w-5 text-green-500" />
                      Dados da Pessoa Jurídica
                    </CardTitle>
                    <CardDescription>
                      Preencha os dados do fornecedor pessoa jurídica. Os campos serão preenchidos automaticamente após digitar o CNPJ.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...pjForm}>
                      <form onSubmit={pjForm.handleSubmit(onSubmitPJ)} className="space-y-6">
                        {/* Seção: Dados Principais */}
                        <div className="space-y-4">
                          <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            Dados Principais
                          </h3>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormField
                              control={pjForm.control}
                              name="cnpj"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>CNPJ</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Input 
                                        placeholder="00.000.000/0000-00" 
                                        {...field} 
                                        value={formatCNPJ(field.value || '')}
                                        onChange={(e) => {
                                          const formatted = formatCNPJ(e.target.value);
                                          field.onChange(formatted);
                                        }}
                                        className={cn(
                                          "bg-background/50 focus:bg-background transition-colors pr-10",
                                          isLoadingCNPJ && "pr-16"
                                        )}
                                      />
                                      {/* Indicadores visuais do estado da busca */}
                                      <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
                                        {isLoadingCNPJ && (
                                          <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                                        )}
                                        {cnpjData && cnpjData.situacao_ativa && (
                                          <CheckCircle className="h-4 w-4 text-green-500" />
                                        )}
                                        {cnpjData && !cnpjData.situacao_ativa && (
                                          <AlertCircle className="h-4 w-4 text-yellow-500" />
                                        )}
                                        {/* Botão para busca manual */}
                                        {field.value && isComplete(field.value, 'cnpj') && !isLoadingCNPJ && (
                                          <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            onClick={handleManualCNPJLookup}
                                            className="h-6 w-6 p-0 hover:bg-green-50 dark:hover:bg-green-950"
                                          >
                                            <Search className="h-3 w-3 text-green-500" />
                                          </Button>
                                        )}
                                      </div>
                                    </div>
                                  </FormControl>
                                  <FormDescription>
                                    {isLoadingCNPJ && "Buscando dados do CNPJ..."}
                                    {cnpjData && cnpjData.situacao_ativa && "✓ Empresa ativa encontrada"}
                                    {cnpjData && !cnpjData.situacao_ativa && "⚠ Empresa encontrada mas inativa"}
                                    {!isLoadingCNPJ && !cnpjData && "Digite o CNPJ para busca automática"}
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="razao_social"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Razão Social</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="Razão Social da empresa" 
                                      {...field} 
                                      className={cn(
                                        "bg-background/50 focus:bg-background transition-colors",
                                        cnpjData && "border-green-200 dark:border-green-800"
                                      )}
                                      disabled={isLoadingCNPJ}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="nome_fantasia"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Nome Fantasia</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="Nome fantasia (opcional)" 
                                      {...field} 
                                      className={cn(
                                        "bg-background/50 focus:bg-background transition-colors",
                                        cnpjData && "border-green-200 dark:border-green-800"
                                      )}
                                      disabled={isLoadingCNPJ}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    Nome comercial da empresa
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="inscricao_estadual"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Inscrição Estadual</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Inscrição estadual (opcional)"
                                      {...field}
                                      value={field.value ?? ''}
                                      className="bg-background/50 focus:bg-background transition-colors"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="categoria"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Categoria do Fornecedor</FormLabel>
                                  <FormControl>
                                    <Select
                                      value={field.value || ''}
                                      onValueChange={field.onChange}
                                    >
                                      <SelectTrigger className="bg-background/50 focus:bg-background transition-colors">
                                        <SelectValue placeholder="Selecione a categoria" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {OPCOES_CATEGORIA_PJ.map((opcao) => (
                                          <SelectItem key={opcao.value} value={opcao.value}>
                                            {opcao.label}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormDescription>
                                    Tipo de fornecedor para melhor organização
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        {/* Seção: Contato */}
                        <div className="space-y-4">
                          <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            Informações de Contato
                          </h3>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormField
                              control={pjForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Email</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                      <Input 
                                        type="email" 
                                        placeholder="<EMAIL>" 
                                        {...field} 
                                        value={field.value ?? ''} 
                                        className="pl-9 bg-background/50 focus:bg-background transition-colors"
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="telefone_principal"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Telefone Principal</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                      <Input 
                                        placeholder="(00) 00000-0000" 
                                        {...field} 
                                        value={formatPhone(field.value || '')}
                                        onChange={(e) => {
                                          const formatted = formatPhone(e.target.value);
                                          field.onChange(formatted);
                                        }}
                                        className={cn(
                                          "pl-9 bg-background/50 focus:bg-background transition-colors",
                                          cnpjData && cnpjData.telefone_principal && "border-green-200 dark:border-green-800"
                                        )}
                                        disabled={isLoadingCNPJ}
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="website"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Website</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Globe className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                      <Input 
                                        placeholder="https://www.empresa.com" 
                                        {...field} 
                                        value={field.value ?? ''} 
                                        className="pl-9 bg-background/50 focus:bg-background transition-colors"
                                      />
                                    </div>
                                  </FormControl>
                                  <FormDescription>
                                    Site da empresa (opcional)
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        {/* Seção: Endereço */}
                        <div className="space-y-4">
                          <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                            <MapPin className="h-4 w-4" />
                            Endereço
                          </h3>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <FormField
                              control={pjForm.control}
                              name="cep"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>CEP</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="00000-000" 
                                      {...field} 
                                      value={formatCEP(field.value || '')}
                                      onChange={(e) => {
                                        const formatted = formatCEP(e.target.value);
                                        field.onChange(formatted);
                                      }}
                                      className={cn(
                                        "bg-background/50 focus:bg-background transition-colors",
                                        cnpjData && cnpjData.endereco?.cep && "border-green-200 dark:border-green-800"
                                      )}
                                      disabled={isLoadingCNPJ}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="endereco"
                              render={({ field }) => (
                                <FormItem className="md:col-span-2">
                                  <FormLabel>Logradouro</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="Rua, Avenida, etc." 
                                      {...field} 
                                      value={field.value ?? ''} 
                                      className={cn(
                                        "bg-background/50 focus:bg-background transition-colors",
                                        cnpjData && cnpjData.endereco?.logradouro && "border-green-200 dark:border-green-800"
                                      )}
                                      disabled={isLoadingCNPJ}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="numero"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Número</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="123" 
                                      {...field} 
                                      value={field.value ?? ''} 
                                      className={cn(
                                        "bg-background/50 focus:bg-background transition-colors",
                                        cnpjData && cnpjData.endereco?.numero && "border-green-200 dark:border-green-800"
                                      )}
                                      disabled={isLoadingCNPJ}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="complemento"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Complemento</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="Sala, Andar, etc." 
                                      {...field} 
                                      value={field.value ?? ''} 
                                      className={cn(
                                        "bg-background/50 focus:bg-background transition-colors",
                                        cnpjData && cnpjData.endereco?.complemento && "border-green-200 dark:border-green-800"
                                      )}
                                      disabled={isLoadingCNPJ}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="bairro"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Bairro</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="Nome do bairro" 
                                      {...field} 
                                      value={field.value ?? ''} 
                                      className={cn(
                                        "bg-background/50 focus:bg-background transition-colors",
                                        cnpjData && cnpjData.endereco?.bairro && "border-green-200 dark:border-green-800"
                                      )}
                                      disabled={isLoadingCNPJ}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="cidade"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Cidade</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="Nome da cidade" 
                                      {...field} 
                                      value={field.value ?? ''} 
                                      className={cn(
                                        "bg-background/50 focus:bg-background transition-colors",
                                        cnpjData && cnpjData.endereco?.municipio && "border-green-200 dark:border-green-800"
                                      )}
                                      disabled={isLoadingCNPJ}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pjForm.control}
                              name="estado"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Estado</FormLabel>
                                  <FormControl>
                                    <Select 
                                      value={field.value ?? ''} 
                                      onValueChange={field.onChange}
                                      disabled={isLoadingCNPJ}
                                    >
                                      <SelectTrigger 
                                        className={cn(
                                          "bg-background/50 focus:bg-background transition-colors",
                                          cnpjData && cnpjData.endereco?.uf && "border-green-200 dark:border-green-800"
                                        )}
                                      >
                                        <SelectValue placeholder="Selecione o estado" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {brazilianStates.map((state) => (
                                          <SelectItem key={state.value} value={state.value}>
                                            {state.label}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        {/* Seção: Observações */}
                        <div className="space-y-4">
                          <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            Informações Adicionais
                          </h3>
                          
                          <FormField
                            control={pjForm.control}
                            name="observacoes"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Observações</FormLabel>
                                <FormControl>
                                  <Textarea 
                                    placeholder="Informações adicionais sobre o fornecedor..." 
                                    {...field} 
                                    value={field.value ?? ''} 
                                    className="bg-background/50 focus:bg-background transition-colors min-h-[80px]"
                                    rows={3}
                                  />
                                </FormControl>
                                <FormDescription>
                                  Informações extras que possam ser úteis (opcional)
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        {/* Botões de ação */}
                        <div className="flex justify-end gap-3 pt-6 border-t">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => navigate("/dashboard/fornecedores/pj")}
                            disabled={isCreatingPJ}
                          >
                            Cancelar
                          </Button>
                          <Button 
                            type="submit" 
                            disabled={isCreatingPJ || isLoadingCNPJ}
                            className={cn(
                              "min-w-[140px]",
                              "bg-gradient-to-r from-green-500 to-green-600",
                              "hover:from-green-600 hover:to-green-700",
                              "text-white shadow-lg",
                              "transition-all duration-300"
                            )}
                          >
                            {isCreatingPJ ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Salvando...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                Criar Fornecedor
                              </>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            {/* Formulário PF */}
            <TabsContent value="pf">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card className="border-border/50 bg-card/95 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="h-5 w-5 text-green-500" />
                      Dados da Pessoa Física
                    </CardTitle>
                    <CardDescription>
                      Preencha os dados do fornecedor pessoa física
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...pfForm}>
                      <form onSubmit={pfForm.handleSubmit(onSubmitPF)} className="space-y-6">
                        {/* Seção: Dados Pessoais */}
                        <div className="space-y-4">
                          <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            Dados Pessoais
                          </h3>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormField
                              control={pfForm.control}
                              name="nome"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Nome Completo</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="Nome completo da pessoa" 
                                      {...field} 
                                      className="bg-background/50 focus:bg-background transition-colors"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pfForm.control}
                              name="cpf"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>CPF</FormLabel>
                                  <FormControl>
                                    <Input 
                                      placeholder="000.000.000-00" 
                                      {...field} 
                                      value={formatCPF(field.value || '')}
                                      onChange={(e) => {
                                        const formatted = formatCPF(e.target.value);
                                        field.onChange(formatted);
                                      }}
                                      className="bg-background/50 focus:bg-background transition-colors"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pfForm.control}
                              name="data_nascimento"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Data de Nascimento</FormLabel>
                                  <FormControl>
                                    <DatePicker
                                      date={field.value || undefined}
                                      onSelect={field.onChange}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    Data de nascimento (opcional)
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pfForm.control}
                              name="tipo_fornecedor"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Tipo de Serviço</FormLabel>
                                  <FormControl>
                                    <Select
                                      value={field.value || ''}
                                      onValueChange={field.onChange}
                                    >
                                      <SelectTrigger className="bg-background/50 focus:bg-background transition-colors">
                                        <SelectValue placeholder="Selecione o tipo de serviço" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {OPCOES_CATEGORIA_PF.map((opcao) => (
                                          <SelectItem key={opcao.value} value={opcao.value}>
                                            {opcao.label}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormDescription>
                                    Especialidade do fornecedor
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        {/* Seção: Contato */}
                        <div className="space-y-4">
                          <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            Informações de Contato
                          </h3>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormField
                              control={pfForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Email</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                      <Input 
                                        type="email" 
                                        placeholder="<EMAIL>" 
                                        {...field} 
                                        value={field.value ?? ''} 
                                        className="pl-9 bg-background/50 focus:bg-background transition-colors"
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={pfForm.control}
                              name="telefone_principal"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Telefone Principal</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                      <Input 
                                        placeholder="(00) 00000-0000" 
                                        {...field} 
                                        value={formatPhone(field.value || '')}
                                        onChange={(e) => {
                                          const formatted = formatPhone(e.target.value);
                                          field.onChange(formatted);
                                        }}
                                        className="pl-9 bg-background/50 focus:bg-background transition-colors"
                                      />
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        {/* Botões de ação */}
                        <div className="flex justify-end gap-3 pt-6 border-t">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => navigate("/dashboard/fornecedores/pf")}
                            disabled={isCreatingPF}
                          >
                            Cancelar
                          </Button>
                          <Button 
                            type="submit" 
                            disabled={isCreatingPF}
                            className={cn(
                              "min-w-[140px]",
                              "bg-gradient-to-r from-green-500 to-green-600",
                              "hover:from-green-600 hover:to-green-700",
                              "text-white shadow-lg",
                              "transition-all duration-300"
                            )}
                          >
                            {isCreatingPF ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Salvando...
                              </>
                            ) : (
                              <>
                                <Save className="mr-2 h-4 w-4" />
                                Criar Fornecedor
                              </>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          </Tabs>
        </motion.div>
      </motion.div>
    </DashboardLayout>
  );
});

NovoFornecedorForm.displayName = 'NovoFornecedorForm';

// Componente principal
const NovoFornecedor: React.FC = () => {
  return <NovoFornecedorForm />;
};

export default NovoFornecedor;
