import { useQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import {
    AlertCircle,
    Building2,
    Calendar,
    ChevronDown,
    Clock,
    DollarSign,
    ExternalLink,
    FileText,
    Filter,
    Gavel,
    Heart,
    RefreshCw,
    Search,
    Sparkles,
    Trophy
} from 'lucide-react'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

import DashboardLayout from '@/components/layouts/DashboardLayout'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { MetricCard } from '@/components/ui/metric-card'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'
import { cn } from '@/lib/utils'
import type {
    BuscarLicitacoesRequest,
    BuscarLicitacoesResponse,
    FiltrosLicitacao
} from '@/types'

const BuscarLicitacoes = () => {
  const { toast } = useToast()
  const navigate = useNavigate()
  const [filtros, setFiltros] = useState<FiltrosLicitacao>({
    sortBy: 'data_abertura',
    sortOrder: 'desc',
    limit: 20,
    offset: 0
  })
  const [mostrarFiltros, setMostrarFiltros] = useState(false)

  // Query para buscar licitações
  const { 
    data: resultado,
    isLoading,
    error,
    refetch
  } = useQuery<BuscarLicitacoesResponse>({
    queryKey: ['licitacoes', filtros],
    queryFn: async () => {
      const request: BuscarLicitacoesRequest = {
        filtros,
        ordenacao: {
          campo: (filtros.sortBy as 'data_abertura' | 'valor_estimado' | 'numero_licitacao' | 'orgao' | 'created_at') || 'data_abertura',
          direcao: filtros.sortOrder || 'desc'
        },
        paginacao: {
          pagina: Math.floor((filtros.offset || 0) / (filtros.limit || 20)) + 1,
          limite: filtros.limit || 20
        }
      }

      const { data, error } = await supabase.functions.invoke('buscar-licitacoes', {
        body: request
      })

      if (error) throw error
      return data
    },
    retry: 1
  })

  // Calcular métricas
  const totalLicitacoes = resultado?.metadata.paginacao.total_items || 0
  const licitacoesEmAndamento = resultado?.data?.filter(l => l.situacao === 'em_andamento').length || 0
  const licitacoesFavoritas = resultado?.data?.filter(l => l.is_favorita).length || 0
  const valorTotalEstimado = resultado?.data?.reduce((sum, l) => sum + (l.valor_estimado || 0), 0) || 0

  const handleFavoritar = async (licitacaoId: string) => {
    try {
      const { error } = await supabase
        .from('licitacoes_favoritas')
        .insert({
          licitacao_id: licitacaoId,
          usuario_id: (await supabase.auth.getUser()).data.user?.id || '',
          tenant_id: (await supabase.auth.getUser()).data.user?.user_metadata?.tenant_id || ''
        })

      if (error) throw error

      toast({
        title: 'Sucesso',
        description: 'Licitação adicionada aos favoritos!'
      })
      
      refetch()
    } catch (_error) {
      toast({
        title: 'Erro',
        description: 'Erro ao favoritar licitação',
        variant: 'destructive'
      })
    }
  }

  const handleDesfavoritar = async (licitacaoId: string) => {
    try {
      const { error } = await supabase
        .from('licitacoes_favoritas')
        .delete()
        .eq('licitacao_id', licitacaoId)
        .eq('usuario_id', (await supabase.auth.getUser()).data.user?.id || '')

      if (error) throw error

      toast({
        title: 'Sucesso',
        description: 'Licitação removida dos favoritos!'
      })
      
      refetch()
    } catch (_error) {
      toast({
        title: 'Erro',
        description: 'Erro ao remover dos favoritos',
        variant: 'destructive'
      })
    }
  }

  const formatarMoeda = (valor?: number) => {
    if (!valor) return 'Não informado'
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(valor)
  }

  const formatarData = (data?: string) => {
    if (!data) return 'Não informado'
    return new Date(data).toLocaleDateString('pt-BR')
  }

  const getSituacaoBadge = (situacao: string) => {
    const config: Record<string, { variant: string; label: string; icon?: React.ComponentType<{ className?: string }> }> = {
      'em_andamento': { 
        variant: 'success', 
        label: 'Em Andamento', 
        icon: Clock 
      },
      'suspensa': { 
        variant: 'warning', 
        label: 'Suspensa', 
        icon: AlertCircle 
      },
      'cancelada': { 
        variant: 'destructive', 
        label: 'Cancelada', 
        icon: AlertCircle 
      },
      'concluida': { 
        variant: 'default', 
        label: 'Concluída', 
        icon: Trophy 
      }
    }

    const situacaoConfig = config[situacao] || config['em_andamento']
    const Icon = situacaoConfig.icon

    return (
      <Badge variant={situacaoConfig.variant as 'default' | 'destructive' | 'outline' | 'secondary'} className="gap-1">
        {Icon && <Icon className="h-3 w-3" />}
        {situacaoConfig.label}
      </Badge>
    )
  }

  const getModalidadeBadge = (modalidade: string) => {
    const modalidadeFormatada = modalidade
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase())
    
    return (
      <Badge variant="outline" className="gap-1">
        <Gavel className="h-3 w-3" />
        {modalidadeFormatada}
      </Badge>
    )
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground">Carregando licitações...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center space-y-4">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto" />
            <p className="text-muted-foreground">Erro ao carregar licitações</p>
            <Button onClick={() => refetch()}>Tentar novamente</Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex items-center justify-between">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center gap-3"
          >
            <div className="h-10 w-10 rounded-lg bg-purple-500/10 dark:bg-purple-400/10 flex items-center justify-center">
              <Gavel className="h-6 w-6 text-purple-500 dark:text-purple-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Licitações Públicas</h1>
              <p className="text-sm text-muted-foreground">
                Encontre oportunidades em licitações de construção civil
              </p>
            </div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
          >
            <Button 
              onClick={() => refetch()} 
              variant="outline" 
              disabled={isLoading}
              className="border-purple-200 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20"
            >
              <RefreshCw className={cn(
                "w-4 h-4 mr-2",
                isLoading && "animate-spin"
              )} />
              Atualizar
            </Button>
          </motion.div>
        </div>

        {/* Cards de métricas */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-4"
        >
          <MetricCard
            title="Total de Licitações"
            value={totalLicitacoes.toString()}
            icon={FileText}
            iconColor="primary"
            className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900/50 dark:to-slate-800/50 border-slate-200 dark:border-slate-700"
          />
          <MetricCard
            title="Em Andamento"
            value={licitacoesEmAndamento.toString()}
            icon={Clock}
            iconColor="success"
            className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-700"
          />
          <MetricCard
            title="Favoritas"
            value={licitacoesFavoritas.toString()}
            icon={Heart}
            iconColor="warning"
            className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border-yellow-200 dark:border-yellow-700"
          />
          <MetricCard
            title="Valor Total"
            value={formatarMoeda(valorTotalEstimado)}
            icon={DollarSign}
            iconColor="info"
            className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-700"
          />
        </motion.div>

        {/* Filtros */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="border-purple-200/50 dark:border-purple-700/50 bg-gradient-to-br from-purple-50/50 to-indigo-50/50 dark:from-purple-900/10 dark:to-indigo-900/10 backdrop-blur-sm">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <div className="h-8 w-8 rounded-lg bg-purple-500/10 dark:bg-purple-400/10 flex items-center justify-center">
                    <Filter className="h-5 w-5 text-purple-500 dark:text-purple-400" />
                  </div>
                  <span className="text-purple-700 dark:text-purple-300">Filtros de Busca</span>
                </CardTitle>
                <Button
                  variant="ghost"
                  onClick={() => setMostrarFiltros(!mostrarFiltros)}
                  className="hover:bg-purple-100 dark:hover:bg-purple-900/20"
                >
                  {mostrarFiltros ? 'Ocultar' : 'Mostrar'} Filtros
                  <ChevronDown className={cn(
                    "w-4 h-4 ml-2 transition-transform",
                    mostrarFiltros && "rotate-180"
                  )} />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Busca rápida sempre visível */}
              <div className="flex gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Buscar por número, objeto ou órgão..."
                    value={filtros.search || ''}
                    onChange={(e) => setFiltros({ ...filtros, search: e.target.value })}
                    className="pl-10 bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600 transition-colors"
                  />
                </div>
                <Button 
                  onClick={() => refetch()}
                  disabled={isLoading}
                  className={cn(
                    "bg-gradient-to-r from-purple-600 to-indigo-600",
                    "hover:from-purple-700 hover:to-indigo-700",
                    "text-white shadow-lg",
                    "transition-all duration-300"
                  )}
                >
                  <Search className="w-4 h-4 mr-2" />
                  Buscar
                </Button>
              </div>

              {/* Filtros avançados */}
              {mostrarFiltros && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Separator className="my-4" />
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block text-slate-700 dark:text-slate-300">
                        Modalidade
                      </label>
                      <Select
                        value={filtros.modalidade || ''}
                        onValueChange={(value) => setFiltros({ ...filtros, modalidade: value || undefined })}
                      >
                        <SelectTrigger className="bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600 transition-colors">
                          <SelectValue placeholder="Todas" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">Todas</SelectItem>
                          <SelectItem value="pregao_eletronico">Pregão Eletrônico</SelectItem>
                          <SelectItem value="pregao_presencial">Pregão Presencial</SelectItem>
                          <SelectItem value="concorrencia">Concorrência</SelectItem>
                          <SelectItem value="tomada_precos">Tomada de Preços</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block text-slate-700 dark:text-slate-300">
                        Situação
                      </label>
                      <Select
                        value={filtros.situacao || ''}
                        onValueChange={(value) => setFiltros({ ...filtros, situacao: value || undefined })}
                      >
                        <SelectTrigger className="bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600 transition-colors">
                          <SelectValue placeholder="Todas" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">Todas</SelectItem>
                          <SelectItem value="em_andamento">Em Andamento</SelectItem>
                          <SelectItem value="suspensa">Suspensa</SelectItem>
                          <SelectItem value="cancelada">Cancelada</SelectItem>
                          <SelectItem value="concluida">Concluída</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block text-slate-700 dark:text-slate-300">
                        Ordenar por
                      </label>
                      <Select
                        value={filtros.sortBy || 'data_abertura'}
                        onValueChange={(value) => setFiltros({ ...filtros, sortBy: value })}
                      >
                        <SelectTrigger className="bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600 transition-colors">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="data_abertura">Data de Abertura</SelectItem>
                          <SelectItem value="valor_estimado">Valor Estimado</SelectItem>
                          <SelectItem value="numero_licitacao">Número</SelectItem>
                          <SelectItem value="orgao">Órgão</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex gap-4 mt-4">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setFiltros({
                          sortBy: 'data_abertura',
                          sortOrder: 'desc',
                          limit: 20,
                          offset: 0
                        })
                        refetch()
                      }}
                      className="border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800"
                    >
                      Limpar Filtros
                    </Button>
                  </div>
                </motion.div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Resultados */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="space-y-4"
        >
        {isLoading && (
          <div className="grid gap-4">
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

                  {resultado?.data && (
            <>
              <div className="flex justify-between items-center">
                <p className="text-sm text-muted-foreground">
                  {resultado.metadata.paginacao.total_items} licitações encontradas
                </p>
              </div>

              <div className="grid gap-4">
                {resultado.data.map((licitacao, index) => (
                  <motion.div
                    key={licitacao.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="hover:shadow-xl transition-all duration-300 border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-br from-white/95 to-slate-50/95 dark:from-slate-900/95 dark:to-slate-800/95 backdrop-blur-sm hover:scale-[1.01]">
                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold text-lg text-slate-900 dark:text-slate-100">
                                {licitacao.numero_licitacao}
                              </h3>
                              {getSituacaoBadge(licitacao.situacao)}
                              {licitacao.modalidade && getModalidadeBadge(licitacao.modalidade)}
                            </div>
                            <p className="text-sm text-muted-foreground mb-2 flex items-center gap-1">
                              <Building2 className="w-4 h-4 text-purple-500 dark:text-purple-400" />
                              {licitacao.orgao}
                            </p>
                            <p className="text-sm font-medium mb-3 text-slate-700 dark:text-slate-300 line-clamp-2">
                              {licitacao.objeto}
                            </p>
                          </div>
                          
                          <Button
                            variant={licitacao.is_favorita ? "default" : "outline"}
                            size="sm"
                            onClick={() => 
                              licitacao.is_favorita 
                                ? handleDesfavoritar(licitacao.id)
                                : handleFavoritar(licitacao.id)
                            }
                            className={cn(
                              "transition-all duration-300",
                              licitacao.is_favorita 
                                ? "bg-gradient-to-r from-pink-500 to-red-500 hover:from-pink-600 hover:to-red-600 text-white" 
                                : "hover:bg-pink-50 dark:hover:bg-pink-900/20"
                            )}
                          >
                            <Heart 
                              className={cn(
                                "w-4 h-4 transition-all",
                                licitacao.is_favorita && "fill-current"
                              )} 
                            />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div className="flex items-center gap-2 text-sm bg-emerald-50 dark:bg-emerald-900/20 p-2 rounded-lg">
                            <DollarSign className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                            <span className="text-emerald-700 dark:text-emerald-300 font-medium">
                              {formatarMoeda(licitacao.valor_estimado)}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 text-sm bg-blue-50 dark:bg-blue-900/20 p-2 rounded-lg">
                            <Calendar className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                            <span className="text-blue-700 dark:text-blue-300">
                              Abertura: {formatarData(licitacao.data_abertura)}
                            </span>
                          </div>
                          <div className="flex items-center gap-2 text-sm bg-orange-50 dark:bg-orange-900/20 p-2 rounded-lg">
                            <Calendar className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                            <span className="text-orange-700 dark:text-orange-300">
                              Entrega: {formatarData(licitacao.data_limite_entrega)}
                            </span>
                          </div>
                        </div>

                        <div className="flex justify-between items-center">
                          <div className="flex gap-2">
                            {licitacao.link_edital && (
                              <Button 
                                variant="outline" 
                                size="sm" 
                                asChild
                                className="border-purple-200 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20"
                              >
                                <a 
                                  href={licitacao.link_edital} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                >
                                  <FileText className="w-4 h-4 mr-1" />
                                  Edital
                                  <ExternalLink className="w-3 h-3 ml-1" />
                                </a>
                              </Button>
                            )}
                            {licitacao.link_portal && (
                              <Button 
                                variant="outline" 
                                size="sm" 
                                asChild
                                className="border-indigo-200 dark:border-indigo-700 hover:bg-indigo-50 dark:hover:bg-indigo-900/20"
                              >
                                <a 
                                  href={licitacao.link_portal} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                >
                                  Portal
                                  <ExternalLink className="w-3 h-3 ml-1" />
                                </a>
                              </Button>
                            )}
                          </div>
                          
                          <Button 
                            size="sm" 
                            onClick={() => navigate(`/dashboard/licitacoes/${licitacao.id}`)}
                            className={cn(
                              "bg-gradient-to-r from-purple-600 to-indigo-600",
                              "hover:from-purple-700 hover:to-indigo-700",
                              "text-white shadow-lg",
                              "transition-all duration-300"
                            )}
                          >
                            Ver Detalhes
                          </Button>
                        </div>

                        {licitacao.analise_ia && (
                          <motion.div 
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 + 0.2 }}
                            className="mt-4 p-4 bg-gradient-to-br from-blue-50/80 to-indigo-50/80 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700"
                          >
                            <div className="flex items-center gap-2 mb-2">
                              <Sparkles className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                              <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                                Análise de IA disponível
                              </span>
                              <Badge 
                                variant="outline" 
                                className="border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300"
                              >
                                {licitacao.analise_ia.nivel_complexidade}
                              </Badge>
                            </div>
                            <p className="text-xs text-blue-700 dark:text-blue-300 line-clamp-2">
                              {licitacao.analise_ia.resumo_objeto}
                            </p>
                          </motion.div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

            {/* Paginação */}
            {resultado.metadata.paginacao.total_pages > 1 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="flex justify-center gap-2 mt-8"
              >
                <Button
                  variant="outline"
                  disabled={!resultado.metadata.paginacao.has_prev}
                  onClick={() => {
                    setFiltros({
                      ...filtros,
                      offset: Math.max(0, (filtros.offset || 0) - (filtros.limit || 20))
                    })
                  }}
                  className="border-purple-200 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20 disabled:opacity-50"
                >
                  Anterior
                </Button>
                <span className="flex items-center px-4 text-sm bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
                  Página <span className="font-semibold mx-1">{resultado.metadata.paginacao.pagina_atual}</span> de <span className="font-semibold mx-1">{resultado.metadata.paginacao.total_pages}</span>
                </span>
                <Button
                  variant="outline"
                  disabled={!resultado.metadata.paginacao.has_next}
                  onClick={() => {
                    setFiltros({
                      ...filtros,
                      offset: (filtros.offset || 0) + (filtros.limit || 20)
                    })
                  }}
                  className="border-purple-200 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20 disabled:opacity-50"
                >
                  Próxima
                </Button>
              </motion.div>
            )}
          </>
        )}

        {resultado?.data && resultado.data.length === 0 && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="border-slate-200/50 dark:border-slate-700/50 bg-gradient-to-br from-white/95 to-slate-50/95 dark:from-slate-900/95 dark:to-slate-800/95 backdrop-blur-sm">
              <CardContent className="p-12 text-center">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ 
                    type: "spring",
                    stiffness: 260,
                    damping: 20,
                    delay: 0.1 
                  }}
                >
                  <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-purple-900/20 dark:to-indigo-900/20 flex items-center justify-center">
                    <Search className="w-10 h-10 text-purple-500 dark:text-purple-400" />
                  </div>
                </motion.div>
                <h3 className="text-xl font-semibold mb-2 text-slate-900 dark:text-slate-100">
                  Nenhuma licitação encontrada
                </h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  Tente ajustar os filtros de busca ou verificar novamente mais tarde. Novas licitações são adicionadas diariamente.
                </p>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setFiltros({
                      sortBy: 'data_abertura',
                      sortOrder: 'desc',
                      limit: 20,
                      offset: 0
                    })
                    refetch()
                  }}
                  className="border-purple-200 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Limpar Filtros e Buscar Novamente
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}
        </motion.div>
      </motion.div>
    </DashboardLayout>
  )
}

export default BuscarLicitacoes