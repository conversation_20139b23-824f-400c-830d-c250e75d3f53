import { useMutation, useQuery, useQ<PERSON>y<PERSON>lient } from '@tanstack/react-query'
import { AnimatePresence, motion } from 'framer-motion'
import {
    AlertTriangle,
    BookOpen,
    Building2,
    Calendar,
    CheckCircle,
    Clock,
    DollarSign,
    Download,
    ExternalLink,
    FileText,
    Gavel,
    Heart,
    Share2,
    Sparkles,
    Target,
    TrendingUp
} from 'lucide-react'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'

import DashboardLayout from '@/components/layouts/DashboardLayout'
import AnaliseIATab from '@/components/licitacoes/AnaliseIATab'
import CompatibilidadeTab from '@/components/licitacoes/CompatibilidadeTab'
import PreparacaoTab from '@/components/licitacoes/PreparacaoTab'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'
import { cn } from '@/lib/utils'
import type {
    AnalisarEditalRequest,
    LicitacaoDetalhada
} from '@/types'

const LicitacaoDetalhes = () => {
  const { id } = useParams<{ id: string }>()
  const { toast } = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [activeTab, setActiveTab] = useState('visao-geral')

  // Query para buscar detalhes da licitação
  const { data: licitacao, isLoading, error } = useQuery<LicitacaoDetalhada>({
    queryKey: ['licitacao-detalhes', id],
    queryFn: async () => {
      // Validar se o ID é válido (UUID)
      if (!id || !/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)) {
        throw new Error('ID de licitação inválido')
      }

      const { data, error } = await supabase
        .from('licitacoes')
        .select(`
          *,
          licitacoes_favoritas(id),
          licitacoes_analises_ia(*)
        `)
        .eq('id', id)
        .single()

      if (error) throw error
      
      return {
        ...data,
        is_favorita: data.licitacoes_favoritas && data.licitacoes_favoritas.length > 0,
        analise_ia: data.licitacoes_analises_ia?.[0] || null
      }
    },
    enabled: !!id
  })

  // Query para buscar tarefas do checklist
  const { data: tarefas } = useQuery({
    queryKey: ['licitacao-tarefas', id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('licitacao_tarefas')
        .select('*')
        .eq('licitacao_id', id)
        .order('ordem', { ascending: true })

      if (error) throw error
      return data
    },
    enabled: !!id
  })

  // Mutation para favoritar/desfavoritar
  const { mutate: toggleFavorito } = useMutation({
    mutationFn: async () => {
      const { data: user } = await supabase.auth.getUser()
      const tenantId = user.user?.user_metadata?.tenant_id

      if (licitacao?.is_favorita) {
        // Remover dos favoritos
        const { error } = await supabase
          .from('licitacoes_favoritas')
          .delete()
          .eq('licitacao_id', id)
          .eq('usuario_id', user.user?.id || '')

        if (error) throw error
      } else {
        // Adicionar aos favoritos
        const { error } = await supabase
          .from('licitacoes_favoritas')
          .insert({
            licitacao_id: id || '',
            usuario_id: user.user?.id || '',
            tenant_id: tenantId || ''
          })

        if (error) throw error
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['licitacao-detalhes', id] })
      toast({
        title: 'Sucesso',
        description: licitacao?.is_favorita 
          ? 'Licitação removida dos favoritos'
          : 'Licitação adicionada aos favoritos'
      })
    }
  })

  // Mutation para criar checklist
  const { mutate: criarChecklist, isPending: criandoChecklist } = useMutation({
    mutationFn: async () => {
      const { error } = await supabase.functions.invoke('criar-checklist-licitacao', {
        body: { licitacao_id: id }
      })
      if (error) throw error
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['licitacao-tarefas', id] })
      toast({
        title: 'Checklist criado!',
        description: 'Tarefas de preparação foram geradas automaticamente'
      })
    }
  })

  // Mutation para analisar edital
  const { mutate: analisarEdital, isPending: analisando } = useMutation({
    mutationFn: async (request: AnalisarEditalRequest) => {
      const { data, error } = await supabase.functions.invoke('analisar-edital-ia', {
        body: request
      })
      if (error) throw error
      return data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['licitacao-detalhes', id] })
      toast({
        title: 'Análise concluída!',
        description: 'A IA analisou o edital e gerou insights estratégicos'
      })
    }
  })

  const formatarMoeda = (valor?: number) => {
    if (!valor) return 'Não informado'
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(valor)
  }

  const formatarData = (data?: string) => {
    if (!data) return 'Não informado'
    return new Date(data).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    })
  }

  const getSituacaoBadge = (situacao: string) => {
    const config: Record<string, { variant: string; label: string; icon?: React.ComponentType<{ className?: string }> }> = {
      'em_andamento': { 
        variant: 'success', 
        label: 'Em Andamento', 
        icon: Clock 
      },
      'suspensa': { 
        variant: 'warning', 
        label: 'Suspensa', 
        icon: AlertTriangle 
      },
      'cancelada': { 
        variant: 'destructive', 
        label: 'Cancelada', 
        icon: AlertTriangle 
      },
      'concluida': { 
        variant: 'default', 
        label: 'Concluída', 
        icon: CheckCircle 
      }
    }

    const situacaoConfig = config[situacao] || config['em_andamento']
    const Icon = situacaoConfig.icon

    return (
      <Badge variant={situacaoConfig.variant as 'default' | 'destructive' | 'outline' | 'secondary'} className="gap-1">
        {Icon && <Icon className="h-3 w-3" />}
        {situacaoConfig.label}
      </Badge>
    )
  }

  const _getCompatibilidadeBadge = (nivel?: string) => {
    if (!nivel) return null
    
    const configs = {
      'alto': { color: 'bg-green-100 text-green-800 border-green-200', icon: Target },
      'medio': { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: TrendingUp },
      'baixo': { color: 'bg-red-100 text-red-800 border-red-200', icon: AlertTriangle }
    }

    const config = configs[nivel as keyof typeof configs]
    if (!config) return null

    const Icon = config.icon

    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        Compatibilidade {nivel.toUpperCase()}
      </Badge>
    )
  }

  const calcularProgressoTarefas = () => {
    if (!tarefas?.length) return 0
    const concluidas = tarefas.filter(t => t.concluida).length
    return Math.round((concluidas / tarefas.length) * 100)
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground">Carregando detalhes da licitação...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (error || !licitacao) {
    // Log do erro para debug
    console.error('Erro ao carregar licitação:', error)
    console.log('ID da licitação:', id)
    
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center space-y-4">
            <AlertTriangle className="h-12 w-12 text-destructive mx-auto" />
            <p className="text-muted-foreground">
              {id && !/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id) 
                ? `ID inválido: "${id}". Verifique a URL ou navegue pela lista de licitações.`
                : 'Licitação não encontrada ou erro ao carregar dados'
              }
            </p>
            <Button onClick={() => navigate('/dashboard/licitacoes')}>Voltar para Lista</Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex items-center justify-between">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center gap-3"
          >
            <div className="h-10 w-10 rounded-lg bg-purple-500/10 dark:bg-purple-400/10 flex items-center justify-center">
              <Gavel className="h-6 w-6 text-purple-500 dark:text-purple-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Detalhes da Licitação</h1>
              <p className="text-sm text-muted-foreground">
                {licitacao.numero_licitacao}
              </p>
            </div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex gap-2"
          >
            <Button
              variant={licitacao.is_favorita ? "default" : "outline"}
              onClick={() => toggleFavorito()}
              className={cn(
                "transition-all duration-300",
                licitacao.is_favorita 
                  ? "bg-gradient-to-r from-pink-500 to-red-500 hover:from-pink-600 hover:to-red-600 text-white" 
                  : "hover:bg-pink-50 dark:hover:bg-pink-900/20"
              )}
            >
              <Heart className={cn(
                "w-4 h-4",
                licitacao.is_favorita && "fill-current"
              )} />
            </Button>
            <Button 
              variant="outline"
              className="border-purple-200 dark:border-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20"
            >
              <Share2 className="w-4 h-4 mr-2" />
              Compartilhar
            </Button>
          </motion.div>
        </div>

        {/* Hero Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="relative overflow-hidden border-purple-200/50 dark:border-purple-700/50 bg-gradient-to-br from-purple-50/50 to-indigo-50/50 dark:from-purple-900/10 dark:to-indigo-900/10 backdrop-blur-sm">
            <CardContent className="p-8">
              <div className="space-y-6">
                {/* Informações principais */}
                <div className="space-y-4">
                  <div className="flex items-center gap-3 flex-wrap">
                    <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                      {licitacao.numero_licitacao}
                    </h2>
                    {getSituacaoBadge(licitacao.situacao)}
                    {licitacao.modalidade && (
                      <Badge variant="outline" className="gap-1">
                        <Gavel className="h-3 w-3" />
                        {licitacao.modalidade.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Badge>
                    )}
                  </div>
                  <p className="text-lg text-slate-700 dark:text-slate-300">
                    {licitacao.objeto}
                  </p>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Building2 className="w-4 h-4 text-purple-500 dark:text-purple-400" />
                    <span>{licitacao.orgao}</span>
                  </div>
                </div>

                {/* Métricas importantes */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-emerald-50 dark:bg-emerald-900/20 rounded-xl p-4 border border-emerald-200 dark:border-emerald-700">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-lg bg-emerald-500/10 dark:bg-emerald-400/10 flex items-center justify-center">
                        <DollarSign className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
                      </div>
                      <div>
                        <p className="text-sm text-emerald-700 dark:text-emerald-300">Valor Estimado</p>
                        <p className="text-lg font-bold text-emerald-900 dark:text-emerald-100">
                          {formatarMoeda(licitacao.valor_estimado)}
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-700">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-lg bg-blue-500/10 dark:bg-blue-400/10 flex items-center justify-center">
                        <Calendar className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="text-sm text-blue-700 dark:text-blue-300">Data de Abertura</p>
                        <p className="text-lg font-bold text-blue-900 dark:text-blue-100">
                          {formatarData(licitacao.data_abertura)}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-orange-50 dark:bg-orange-900/20 rounded-xl p-4 border border-orange-200 dark:border-orange-700">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-lg bg-orange-500/10 dark:bg-orange-400/10 flex items-center justify-center">
                        <Clock className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                      </div>
                      <div>
                        <p className="text-sm text-orange-700 dark:text-orange-300">Prazo de Entrega</p>
                        <p className="text-lg font-bold text-orange-900 dark:text-orange-100">
                          {formatarData(licitacao.data_limite_entrega)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-300/10 to-indigo-300/10 dark:from-purple-600/10 dark:to-indigo-600/10 rounded-full -translate-y-32 translate-x-32 blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-indigo-300/10 to-purple-300/10 dark:from-indigo-600/10 dark:to-purple-600/10 rounded-full translate-y-24 -translate-x-24 blur-3xl"></div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
        >
          <Button
            onClick={() => analisarEdital({ licitacao_id: id! })}
            disabled={analisando}
            className={cn(
              "h-16 relative overflow-hidden group",
              "bg-gradient-to-r from-purple-600 via-violet-600 to-indigo-600",
              "hover:from-purple-700 hover:via-violet-700 hover:to-indigo-700",
              "text-white shadow-lg",
              "transition-all duration-300"
            )}
          >
            <div className="absolute inset-0 bg-white/20 translate-y-full group-hover:translate-y-0 transition-transform duration-300"></div>
            <div className="relative flex items-center justify-center gap-2">
              <Sparkles className="w-5 h-5" />
              {analisando ? 'Analisando...' : 'Analisar com IA'}
            </div>
          </Button>

          <Button
            onClick={() => criarChecklist()}
            disabled={criandoChecklist || (tarefas && tarefas.length > 0)}
            variant="outline"
            className="h-16 border-green-200 dark:border-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 group"
          >
            <CheckCircle className="w-5 h-5 mr-2 text-green-600 dark:text-green-400 group-hover:scale-110 transition-transform" />
            {tarefas?.length ? 'Checklist Criado' : 'Criar Checklist'}
          </Button>

          {licitacao.link_edital && (
            <Button 
              variant="outline" 
              className="h-16 border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20 group" 
              asChild
            >
              <a href={licitacao.link_edital} target="_blank" rel="noopener noreferrer">
                <Download className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform" />
                Baixar Edital
              </a>
            </Button>
          )}

          {licitacao.link_portal && (
            <Button 
              variant="outline" 
              className="h-16 border-indigo-200 dark:border-indigo-700 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 group" 
              asChild
            >
              <a href={licitacao.link_portal} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="w-5 h-5 mr-2 text-indigo-600 dark:text-indigo-400 group-hover:scale-110 transition-transform" />
                Portal Oficial
              </a>
            </Button>
          )}
        </motion.div>

        {/* Tabs Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 h-14 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 border border-purple-200 dark:border-purple-700">
              <TabsTrigger 
                value="visao-geral" 
                className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-md"
              >
                <BookOpen className="w-4 h-4" />
                <span className="hidden sm:inline">Visão Geral</span>
              </TabsTrigger>
              <TabsTrigger 
                value="analise-ia" 
                className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-md"
              >
                <Sparkles className="w-4 h-4" />
                <span className="hidden sm:inline">Análise IA</span>
              </TabsTrigger>
              <TabsTrigger 
                value="preparacao" 
                className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-md"
              >
                <CheckCircle className="w-4 h-4" />
                <span className="hidden sm:inline">Preparação</span>
                {tarefas?.length ? (
                  <Badge 
                    variant="secondary" 
                    className="ml-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300"
                  >
                    {calcularProgressoTarefas()}%
                  </Badge>
                ) : null}
              </TabsTrigger>
              <TabsTrigger 
                value="compatibilidade" 
                className="flex items-center gap-2 data-[state=active]:bg-white dark:data-[state=active]:bg-slate-800 data-[state=active]:shadow-md"
              >
                <Target className="w-4 h-4" />
                <span className="hidden sm:inline">Compatibilidade</span>
              </TabsTrigger>
            </TabsList>

          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              <TabsContent value="visao-geral" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Informações Básicas */}
                  <Card className="lg:col-span-2">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        Informações da Licitação
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Número</p>
                          <p className="font-semibold">{licitacao.numero_licitacao}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Modalidade</p>
                          <p className="font-semibold">{licitacao.modalidade?.replace('_', ' ') || 'Não informado'}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Órgão</p>
                          <p className="font-semibold">{licitacao.orgao}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">Situação</p>
                          <div className="mt-1">{getSituacaoBadge(licitacao.situacao)}</div>
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-2">Objeto</p>
                        <p className="text-sm leading-relaxed">{licitacao.objeto}</p>
                      </div>

                      {licitacao.observacoes && (
                        <>
                          <Separator />
                          <div>
                            <p className="text-sm font-medium text-muted-foreground mb-2">Observações</p>
                            <p className="text-sm leading-relaxed text-muted-foreground">{licitacao.observacoes}</p>
                          </div>
                        </>
                      )}
                    </CardContent>
                  </Card>

                  {/* Timeline/Cronograma */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Calendar className="w-5 h-5" />
                        Cronograma
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                          <Calendar className="w-4 h-4 text-blue-600" />
                          <div className="flex-1">
                            <p className="text-sm font-medium">Abertura</p>
                            <p className="text-xs text-muted-foreground">{formatarData(licitacao.data_abertura)}</p>
                          </div>
                        </div>
                        
                        {licitacao.data_limite_entrega && (
                          <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                            <Clock className="w-4 h-4 text-orange-600" />
                            <div className="flex-1">
                              <p className="text-sm font-medium">Limite Entrega</p>
                              <p className="text-xs text-muted-foreground">{formatarData(licitacao.data_limite_entrega)}</p>
                            </div>
                          </div>
                        )}
                        
                        <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <div className="flex-1">
                            <p className="text-sm font-medium">Status</p>
                            <p className="text-xs text-muted-foreground capitalize">{licitacao.situacao.replace('_', ' ')}</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="analise-ia">
                <AnaliseIATab 
                  analise={licitacao.analise_ia} 
                  isLoading={analisando}
                />
              </TabsContent>

              <TabsContent value="preparacao">
                <PreparacaoTab 
                  tarefas={tarefas}
                  licitacaoId={id!}
                  isLoading={criandoChecklist}
                />
              </TabsContent>

              <TabsContent value="compatibilidade">
                <CompatibilidadeTab 
                  analiseCompatibilidade={licitacao.analise_ia?.analise_compatibilidade}
                  isLoading={analisando}
                />
              </TabsContent>
            </motion.div>
          </AnimatePresence>
        </Tabs>
      </motion.div>
      </motion.div>
    </DashboardLayout>
  )
}

export default LicitacaoDetalhes