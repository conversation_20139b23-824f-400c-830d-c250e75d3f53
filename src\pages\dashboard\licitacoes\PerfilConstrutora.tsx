import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { 
  AlertCircle,
  Award,
  Building2, 
  CheckCircle,
  DollarSign, 
  FileText, 
  Plus,
  Save, 
  X} from 'lucide-react'
import { useEffect,useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import DashboardLayout from '@/components/layouts/DashboardLayout'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage 
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'

// Schema de validação
const perfilSchema = z.object({
  especialidades: z.array(z.string()).min(1, 'Selecione pelo menos uma especialidade'),
  capital_social: z.number().positive('Capital social deve ser positivo').optional(),
  observacoes: z.string().optional()
})

type PerfilFormData = z.infer<typeof perfilSchema>

interface ConstrutoraPerfilData {
  id?: string
  tenant_id: string
  especialidades: string[]
  capital_social?: number
  observacoes?: string
  documentos_padrao: Record<string, unknown>
  certidoes: Record<string, unknown>
  experiencias_anteriores: Record<string, unknown>
  equipe_tecnica: Record<string, unknown>
  capacidade_operacional: Record<string, unknown>
}

// Especialidades disponíveis
const ESPECIALIDADES_OPCOES = [
  { value: 'edificacoes', label: 'Edificações' },
  { value: 'saneamento', label: 'Saneamento' },
  { value: 'urbanizacao', label: 'Urbanização' },
  { value: 'rodovias', label: 'Rodovias' },
  { value: 'pontes_viadutos', label: 'Pontes e Viadutos' },
  { value: 'instalacoes_eletricas', label: 'Instalações Elétricas' },
  { value: 'instalacoes_hidraulicas', label: 'Instalações Hidráulicas' },
  { value: 'estruturas_metalicas', label: 'Estruturas Metálicas' },
  { value: 'reformas', label: 'Reformas' },
  { value: 'demolicoes', label: 'Demolições' }
]

const PerfilConstrutora = () => {
  const { toast } = useToast()
  const queryClient = useQueryClient()
  const [novaEspecialidade, setNovaEspecialidade] = useState('')

  const form = useForm<PerfilFormData>({
    resolver: zodResolver(perfilSchema),
    defaultValues: {
      especialidades: [],
      observacoes: ''
    }
  })

  // Query para buscar perfil existente
  const { data: perfil, isLoading, error } = useQuery({
    queryKey: ['construtora-perfil'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('construtora_perfil')
        .select('*')
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error
      }

      return data as ConstrutoraPerfilData | null
    }
  })

  // Mutation para salvar perfil
  const { mutate: salvarPerfil, isPending } = useMutation({
    mutationFn: async (data: PerfilFormData) => {
      const { data: user } = await supabase.auth.getUser()
      const tenantId = user.user?.user_metadata?.tenant_id

      if (!tenantId) {
        throw new Error('Tenant ID não encontrado')
      }

      const perfilData = {
        tenant_id: tenantId,
        especialidades: data.especialidades,
        capital_social: data.capital_social,
        observacoes: data.observacoes || '',
        documentos_padrao: perfil?.documentos_padrao || {},
        certidoes: perfil?.certidoes || {},
        experiencias_anteriores: perfil?.experiencias_anteriores || {},
        equipe_tecnica: perfil?.equipe_tecnica || {},
        capacidade_operacional: perfil?.capacidade_operacional || {}
      }

      if (perfil?.id) {
        // Atualizar
        const { data: result, error } = await supabase
          .from('construtora_perfil')
          .update(perfilData)
          .eq('id', perfil.id)
          .select()
          .single()

        if (error) throw error
        return result
      } else {
        // Inserir
        const { data: result, error } = await supabase
          .from('construtora_perfil')
          .insert(perfilData)
          .select()
          .single()

        if (error) throw error
        return result
      }
    },
    onSuccess: () => {
      toast({
        title: 'Sucesso',
        description: 'Perfil da construtora salvo com sucesso!'
      })
      queryClient.invalidateQueries({ queryKey: ['construtora-perfil'] })
    },
    onError: (error) => {
      toast({
        title: 'Erro',
        description: `Erro ao salvar perfil: ${error.message}`,
        variant: 'destructive'
      })
    }
  })

  // Carregar dados quando perfil for carregado
  useEffect(() => {
    if (perfil) {
      form.reset({
        especialidades: perfil.especialidades || [],
        capital_social: perfil.capital_social || undefined,
        observacoes: perfil.observacoes || ''
      })
    }
  }, [perfil, form])

  const adicionarEspecialidade = (especialidade: string) => {
    const especialidadesAtuais = form.getValues('especialidades')
    if (!especialidadesAtuais.includes(especialidade)) {
      form.setValue('especialidades', [...especialidadesAtuais, especialidade])
    }
    setNovaEspecialidade('')
  }

  const removerEspecialidade = (especialidade: string) => {
    const especialidadesAtuais = form.getValues('especialidades')
    form.setValue('especialidades', especialidadesAtuais.filter(e => e !== especialidade))
  }

  const onSubmit = (data: PerfilFormData) => {
    salvarPerfil(data)
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="p-8">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Erro ao carregar perfil da construtora. Tente novamente.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Perfil da Construtora</h1>
        <p className="text-muted-foreground">
          Configure o perfil da sua empresa para análises de compatibilidade com licitações
        </p>
      </div>

      {/* Alert informativo */}
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          Um perfil completo permite que a IA faça análises de compatibilidade mais precisas, 
          ajudando a identificar as melhores oportunidades de licitação para sua empresa.
        </AlertDescription>
      </Alert>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Especialidades */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="w-5 h-5" />
                Especialidades da Empresa
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="especialidades"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Áreas de Atuação</FormLabel>
                    <FormDescription>
                      Selecione as especialidades da sua construtora
                    </FormDescription>
                    
                    {/* Especialidades selecionadas */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {field.value.map((especialidade) => {
                        const opcao = ESPECIALIDADES_OPCOES.find(e => e.value === especialidade)
                        return (
                          <Badge key={especialidade} variant="default" className="flex items-center gap-1">
                            {opcao?.label || especialidade}
                            <X 
                              className="w-3 h-3 cursor-pointer" 
                              onClick={() => removerEspecialidade(especialidade)}
                            />
                          </Badge>
                        )
                      })}
                    </div>

                    {/* Adicionar nova especialidade */}
                    <div className="flex gap-2">
                      <Select value={novaEspecialidade} onValueChange={setNovaEspecialidade}>
                        <SelectTrigger className="flex-1">
                          <SelectValue placeholder="Selecione uma especialidade" />
                        </SelectTrigger>
                        <SelectContent>
                          {ESPECIALIDADES_OPCOES
                            .filter(opcao => !field.value.includes(opcao.value))
                            .map((opcao) => (
                              <SelectItem key={opcao.value} value={opcao.value}>
                                {opcao.label}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <Button
                        type="button"
                        variant="outline"
                        disabled={!novaEspecialidade}
                        onClick={() => adicionarEspecialidade(novaEspecialidade)}
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    </div>
                    
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Informações Financeiras */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="w-5 h-5" />
                Informações Financeiras
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="capital_social"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Capital Social (R$)</FormLabel>
                    <FormDescription>
                      Valor do capital social da empresa conforme contrato social
                    </FormDescription>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Ex: 100000"
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Observações */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Observações Adicionais
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="observacoes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Informações Complementares</FormLabel>
                    <FormDescription>
                      Adicione informações relevantes sobre a empresa, experiência, 
                      equipe técnica, certificações, etc.
                    </FormDescription>
                    <FormControl>
                      <Textarea
                        placeholder="Ex: Empresa com 15 anos de experiência em obras públicas, certificação ISO 9001, equipe de 50 colaboradores..."
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Botões de ação */}
          <div className="flex justify-end gap-4">
            <Button type="submit" disabled={isPending}>
              <Save className="w-4 h-4 mr-2" />
              {isPending ? 'Salvando...' : 'Salvar Perfil'}
            </Button>
          </div>
        </form>
      </Form>

      {/* Informações sobre próximos passos */}
      {perfil && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="w-5 h-5" />
              Próximos Passos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>• Complete seu perfil para melhorar as análises de compatibilidade</p>
              <p>• Adicione informações sobre experiências anteriores</p>
              <p>• Configure documentos padrão da empresa</p>
              <p>• Mantenha as informações sempre atualizadas</p>
            </div>
          </CardContent>
        </Card>
      )}
      </div>
    </DashboardLayout>
  )
}

export default PerfilConstrutora