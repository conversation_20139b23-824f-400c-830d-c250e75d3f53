import type { ColumnDef } from "@tanstack/react-table";
import { useParams } from "react-router-dom";

import { CondominioDashboard } from "@/components/obras/CondominioDashboard";
import { DataTable } from "@/components/ui/data-table";
import { GradientCard } from "@/components/ui/GradientCard";
import { PageHeader } from "@/components/ui/PageHeader";
import { useTenantQuery } from "@/hooks/useTenantQuery";
import { supabase } from "@/integrations/supabase/client";
import type { Obra } from "@/types/api";
import type { CondominioDetails } from "@/types/condominio";

const fetchCondominioDetails = async (obraId: string) => {
  const { data, error } = await supabase.rpc("get_condominio_details", {
    p_obra_id: obraId,
  });

  if (error) {
    console.error("Erro ao buscar detalhes do condomínio:", error);
    throw new Error("Não foi possível carregar os detalhes do condomínio.");
  }

  return data as CondominioDetails;
};

const CondominioDetalhePage = () => {
  const { id } = useParams<{ id: string }>();

  const {
    data: condominio,
    isLoading,
    isError,
    error,
  } = useTenantQuery({
    queryKey: ["condominio", id],
    queryFn: () => fetchCondominioDetails(id!),
    enabled: !!id,
  });

  if (isLoading) {
    return <div>Carregando...</div>;
  }

  if (isError) {
    return <div>Erro: {error.message}</div>;
  }

  if (!condominio) {
    return <div>Condomínio não encontrado.</div>;
  }

  const { master, unidades } = condominio;

  // Definir colunas para a tabela de unidades
  const unidadeColumns: ColumnDef<Obra>[] = [
    {
      accessorKey: "identificador_unidade",
      header: "Unidade",
    },
    {
      accessorKey: "nome",
      header: "Nome",
    },
    {
      accessorKey: "area_total",
      header: "Área (m²)",
      cell: ({ row }) => {
        const area = row.getValue("area_total") as number;
        return area ? `${area.toLocaleString()} m²` : "-";
      },
    },
    {
      accessorKey: "orcamento",
      header: "Orçamento",
      cell: ({ row }) => {
        const orcamento = row.getValue("orcamento") as number;
        return orcamento ? `R$ ${orcamento.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}` : "-";
      },
    },
    {
      accessorKey: "status",
      header: "Status",
    },
  ];

  return (
    <div>
      <PageHeader
        title={`Detalhes do Condomínio: ${master.nome}`}
        description={`Visualize as informações do projeto ${master.nome_identificador} e suas unidades.`}
      />

      <div className="grid gap-6 mt-6 md:grid-cols-2 lg:grid-cols-3">
        <GradientCard
          title="Endereço"
          content={`${master.endereco}, ${master.cidade} - ${master.estado}`}
        />
        <GradientCard title="CEP" content={master.cep} />
        <GradientCard title="Status" content={master.status} />
      </div>

      <div className="mt-8">
        <h2 className="text-2xl font-semibold tracking-tight">
          Dashboard do Condomínio
        </h2>
        <CondominioDashboard obraId={id!} />
      </div>

      <div className="mt-8">
        <h2 className="text-2xl font-semibold tracking-tight">
          Unidades do Condomínio
        </h2>
        <p className="text-muted-foreground">
          Abaixo está a lista de todas as unidades associadas a este condomínio.
        </p>
        <div className="mt-4">
          <DataTable columns={unidadeColumns} data={unidades} />
        </div>
      </div>
    </div>
  );
};

export default CondominioDetalhePage;
