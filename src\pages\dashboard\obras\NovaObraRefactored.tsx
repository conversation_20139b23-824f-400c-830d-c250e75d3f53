// Bibliotecas externas
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { motion } from "framer-motion";
import {
  Building,
  DollarSign,
  Info,
  Loader2,
  MapPin,
  Search,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

// Componentes de layout
import DashboardLayout from "@/components/layouts/DashboardLayout";
// Componentes específicos
// Componentes UI
import { Alert, AlertDescription } from "@/components/ui/alert";
import { DatePicker } from "@/components/ui/date-picker";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { FormWrapper } from "@/components/ui/FormWrapper";
import { Input } from "@/components/ui/input";
import { PageHeader } from "@/components/ui/PageHeader";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
// Contextos
import { useAuth } from "@/contexts/auth";
// Hooks
import { useCEP } from "@/hooks/useCEP";
import { useObrasCondominio } from "@/hooks/useObrasCondominio";
// Integrações
import { supabase } from "@/integrations/supabase/client";
// Utilitários e validações
import { brazilianStates } from "@/lib/i18n";
import { cn } from "@/lib/utils";
import type { ObraFormValues } from "@/lib/validations/obra";
import { obraSchema } from "@/lib/validations/obra";
// Serviços
import { obrasApi } from "@/services/api";

const NovaObraRefactored = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const {
    buscarCEP,
    formatarCEP,
    isLoading: isLoadingCEP,
    error: cepError,
  } = useCEP();
  const { user } = useAuth();
  const { createCondominioAsync } = useObrasCondominio();
  const tenantId = user?.profile?.tenant_id;
  const [construtoras, setConstrutoras] = useState<
    Array<{
      id: string;
      tipo: string;
      nome_razao_social: string;
      nome_fantasia?: string | null;
      documento: string;
    }>
  >([]);
  const [loadingConstrutoras, setLoadingConstrutoras] = useState(true);
  const [tipoProjeto, setTipoProjeto] = useState<"UNICO" | "CONDOMINIO_MASTER">(
    "UNICO"
  );
  const [tipoCondominio, setTipoCondominio] = useState<
    "VERTICAL" | "HORIZONTAL" | null
  >(null);

  const form = useForm<ObraFormValues>({
    resolver: zodResolver(obraSchema),
    defaultValues: {
      nome: "",
      endereco: "",
      cidade: "",
      estado: "",
      cep: "",
      orcamento: 0,
      area_total: 0,
      construtora_id: "",
      data_inicio: null,
      data_prevista_termino: null,
      tipo_projeto: "UNICO" as const,
      tipo_condominio: undefined,
      numero_blocos: 0,
      andares_por_bloco: 0,
      unidades_por_andar: 0,
      numero_unidades: 0,
      area_lote: 0,
      area_construida_unidade: 0,
    },
  });

  // Watch para campos que afetam o cálculo da área total
  const numeroUnidades = form.watch("numero_unidades");
  const areaConstruidaUnidade = form.watch("area_construida_unidade");
  const numeroBlocos = form.watch("numero_blocos");
  const andaresPorBloco = form.watch("andares_por_bloco");
  const unidadesPorAndar = form.watch("unidades_por_andar");

  // ✅ Cálculo automático da área total baseado no tipo de condomínio
  useEffect(() => {
    const tipoProjetoAtual = form.getValues("tipo_projeto");

    // Só calcular se for condomínio master
    if (tipoProjetoAtual !== "CONDOMINIO_MASTER") return;

    let novaAreaTotal = 0;

    if (tipoCondominio === "HORIZONTAL") {
      // Para horizontal: numero_unidades * area_construida_unidade
      if (numeroUnidades && areaConstruidaUnidade) {
        novaAreaTotal = numeroUnidades * areaConstruidaUnidade;
      }
    } else if (tipoCondominio === "VERTICAL") {
      // Para vertical: numero_blocos * andares_por_bloco * unidades_por_andar * area_construida_unidade
      if (
        numeroBlocos &&
        andaresPorBloco &&
        unidadesPorAndar &&
        areaConstruidaUnidade
      ) {
        novaAreaTotal =
          numeroBlocos *
          andaresPorBloco *
          unidadesPorAndar *
          areaConstruidaUnidade;
      }
    }

    // Atualizar área total se calculada
    if (novaAreaTotal > 0) {
      const areaAtualFormulario = form.getValues("area_total");
      // Só atualizar se for diferente para evitar loops
      if (Math.abs(areaAtualFormulario - novaAreaTotal) > 0.01) {
        form.setValue("area_total", novaAreaTotal, { shouldValidate: true });
      }
    }
  }, [
    tipoCondominio,
    numeroUnidades,
    areaConstruidaUnidade,
    numeroBlocos,
    andaresPorBloco,
    unidadesPorAndar,
    form,
  ]);

  // Usando mutação direta
  const { mutate, isPending } = useMutation({
    mutationFn: obrasApi.create,
    onSuccess: async (_data) => {
      await queryClient.invalidateQueries({ queryKey: ["obras"] });
      await queryClient.invalidateQueries({
        queryKey: ["orcamentos-parametricos"],
      });
      await queryClient.invalidateQueries({
        queryKey: ["orcamentos-com-itens"],
      });
      toast.success("Obra criada com sucesso!", {
        description: `A obra "${form.getValues("nome")}" foi registrada.`,
      });
      navigate("/dashboard/obras");
    },
    onError: (error) => {
      console.error("Error creating obra:", error);
      toast.error("Erro ao criar obra. Tente novamente.");
    },
  });

  // Função para gerar unidades automaticamente baseado no tipo de condomínio
  const generateUnidades = (values: ObraFormValues) => {
    const unidades = [];

    if (values.tipo_condominio === "VERTICAL") {
      // Para condomínio vertical: blocos x andares x unidades por andar
      const totalBlocos = values.numero_blocos || 0;
      const andaresPorBloco = values.andares_por_bloco || 0;
      const unidadesPorAndar = values.unidades_por_andar || 0;
      const totalUnidadesCalculadas =
        totalBlocos * andaresPorBloco * unidadesPorAndar;

      for (let bloco = 1; bloco <= totalBlocos; bloco++) {
        for (let andar = 1; andar <= andaresPorBloco; andar++) {
          for (let unidade = 1; unidade <= unidadesPorAndar; unidade++) {
            const identificador = `${bloco}${andar
              .toString()
              .padStart(2, "0")}${unidade}`;
            unidades.push({
              identificador_unidade: identificador,
              nome: `Apartamento ${identificador}`,
              orcamento: Math.round(
                (values.orcamento || 0) / totalUnidadesCalculadas
              ),
              area_total: values.area_construida_unidade || 0, // Área construída por unidade
            });
          }
        }
      }
    } else if (values.tipo_condominio === "HORIZONTAL") {
      // Para condomínio horizontal: número de unidades
      const totalUnidades = values.numero_unidades || 0;

      for (let i = 1; i <= totalUnidades; i++) {
        const identificador = `CASA-${i.toString().padStart(2, "0")}`;
        unidades.push({
          identificador_unidade: identificador,
          nome: `Casa ${i}`,
          orcamento: Math.round((values.orcamento || 0) / totalUnidades),
          area_total: values.area_construida_unidade || 0, // Área construída por unidade
        });
      }
    }

    return unidades;
  };

  const onSubmit = (values: ObraFormValues) => {
    // Validar se é condomínio e tem os campos necessários
    if (values.tipo_projeto === "CONDOMINIO_MASTER") {
      if (!values.tipo_condominio) {
        toast.error("Selecione o tipo de condomínio (Vertical ou Horizontal)");
        return;
      }

      if (values.tipo_condominio === "VERTICAL") {
        if (
          !values.numero_blocos ||
          values.numero_blocos <= 0 ||
          !values.andares_por_bloco ||
          values.andares_por_bloco <= 0 ||
          !values.unidades_por_andar ||
          values.unidades_por_andar <= 0
        ) {
          toast.error(
            "Para condomínio vertical, preencha: número de blocos, andares por bloco e unidades por andar (valores maiores que 0)"
          );
          return;
        }
      }

      if (values.tipo_condominio === "HORIZONTAL") {
        if (!values.numero_unidades || values.numero_unidades <= 0) {
          toast.error(
            "Para condomínio horizontal, preencha o número de unidades (valor maior que 0)"
          );
          return;
        }
      }
    }

    // Limpar valores null/undefined para campos opcionais
    const cleanedValues = {
      ...values,
      // Remover campos null/undefined para campos opcionais
      tipo_condominio: values.tipo_condominio || undefined,
      numero_blocos:
        values.numero_blocos > 0 ? values.numero_blocos : undefined,
      andares_por_bloco:
        values.andares_por_bloco > 0 ? values.andares_por_bloco : undefined,
      unidades_por_andar:
        values.unidades_por_andar > 0 ? values.unidades_por_andar : undefined,
      numero_unidades:
        values.numero_unidades > 0 ? values.numero_unidades : undefined,
      area_lote: values.area_lote > 0 ? values.area_lote : undefined,
      area_construida_unidade:
        values.area_construida_unidade > 0
          ? values.area_construida_unidade
          : undefined,
      // Garantir que datas sejam null se não preenchidas
      data_inicio: values.data_inicio || null,
      data_prevista_termino: values.data_prevista_termino || null,
    };

    console.log("Valores originais:", values);
    console.log("Valores limpos sendo enviados:", cleanedValues);

    // Se for condomínio, usar a função RPC
    if (values.tipo_projeto === "CONDOMINIO_MASTER") {
      // Gerar unidades automaticamente baseado no tipo de condomínio
      const unidades = generateUnidades(values);

      const condominioData = {
        condominio_data: {
          ...cleanedValues,
          usuario_id: user?.id, // Adicionar usuario_id obrigatório
        },
        unidades_data: unidades,
      };

      console.log("Criando condomínio via RPC:", condominioData);

      // Usar a função RPC para condomínios
      createCondominioAsync(condominioData)
        .then(() => {
          navigate("/dashboard/obras");
        })
        .catch((error) => {
          console.error("Erro ao criar condomínio:", error);
          toast.error("Erro ao criar condomínio. Tente novamente.");
        });

      return;
    }

    // Para obras únicas, usar a API normal
    try {
      const validatedValues = obraSchema.parse(cleanedValues);
      console.log("Validação passou:", validatedValues);
      mutate(validatedValues);
    } catch (error) {
      console.error("Erro de validação:", error);
      if (error instanceof Error) {
        toast.error(`Erro de validação: ${error.message}`);
      }
      // Log detalhado do erro de validação
      if (error && typeof error === "object" && "issues" in error) {
        console.error("Detalhes da validação:", error.issues);
        const issues = error.issues as Array<{
          path: string[];
          message: string;
        }>;
        const firstIssue = issues[0];
        if (firstIssue) {
          toast.error(
            `Campo ${firstIssue.path.join(".")}: ${firstIssue.message}`
          );
        }
      }
    }
  };

  // Função para buscar endereço automaticamente quando CEP for preenchido
  const handleCEPChange = async (cep: string) => {
    try {
      // Formatar CEP enquanto digita
      const cepFormatado = formatarCEP(cep || "");
      form.setValue("cep", cepFormatado);

      // Buscar endereço quando CEP estiver completo (8 dígitos)
      const cepLimpo = (cep || "").replace(/\D/g, "");
      if (cepLimpo.length === 8) {
        try {
          const dadosCEP = await buscarCEP(cep);

          if (dadosCEP) {
            // Preencher automaticamente os campos de endereço
            form.setValue("endereco", dadosCEP.logradouro || "");
            form.setValue("cidade", dadosCEP.localidade || "");
            form.setValue("estado", dadosCEP.uf || "");

            toast.success("Endereço preenchido automaticamente!");
          }
        } catch (error) {
          // Tratamento específico para erros de CEP
          const errorMessage =
            error instanceof Error ? error.message : "CEP não encontrado";
          toast.error(errorMessage);
        }
      }
    } catch (error) {
      // Tratamento de erro melhorado
      const errorMessage =
        error instanceof Error ? error.message : "Erro ao buscar CEP";
      toast.error(errorMessage);
    }
  };

  useEffect(() => {
    if (!tenantId) return;
    setLoadingConstrutoras(true);
    supabase
      .from("construtoras")
      .select("id, tipo, nome_razao_social, nome_fantasia, documento")
      .eq("tenant_id", tenantId)
      .order("created_at", { ascending: false })
      .then(({ data, error: _error }) => {
        setConstrutoras(data || []);
        setLoadingConstrutoras(false);
      });
  }, [tenantId]);

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        {/* Header usando o componente refatorado */}
        <PageHeader
          icon={
            <Building className="h-6 w-6 text-blue-500 dark:text-blue-400" />
          }
          title="Nova Obra"
          description="Cadastre uma nova obra no sistema"
          backTo="/dashboard/obras"
          backLabel="Voltar"
        />

        {/* Alert informativo */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Alert className="border-info/50 bg-info/10">
            <Info className="h-4 w-4 text-info" />
            <AlertDescription className="text-sm">
              <strong>Dica:</strong> Comece preenchendo o CEP para que o
              endereço seja preenchido automaticamente. Você poderá editar esses
              dados posteriormente se necessário.
            </AlertDescription>
          </Alert>
        </motion.div>

        {/* Formulário usando o componente refatorado */}
        <FormWrapper
          form={form}
          onSubmit={onSubmit}
          title="Informações da Obra"
          description="Preencha os dados básicos da obra que será cadastrada"
          isLoading={isPending}
          submitLabel="Salvar Obra"
        >
          {/* Seção: Tipo de Projeto */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              Tipo de Projeto
            </h3>
            <FormField
              control={form.control}
              name="tipo_projeto"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                        const newTipoProjeto = value as
                          | "UNICO"
                          | "CONDOMINIO_MASTER";
                        setTipoProjeto(newTipoProjeto);
                        if (newTipoProjeto === "UNICO") {
                          setTipoCondominio(null);
                          form.setValue("tipo_condominio", undefined);
                          form.setValue("numero_blocos", 0);
                          form.setValue("andares_por_bloco", 0);
                          form.setValue("unidades_por_andar", 0);
                          form.setValue("numero_unidades", 0);
                        }
                      }}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="UNICO" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Obra Única
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="CONDOMINIO_MASTER" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Condomínio (Múltiplas Unidades)
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Seção Condomínio - APARECE CONDICIONALMENTE */}
          {tipoProjeto === "CONDOMINIO_MASTER" && (
            <motion.div
              initial={{ opacity: 0, height: 0, y: -10 }}
              animate={{ opacity: 1, height: "auto", y: 0 }}
              exit={{ opacity: 0, height: 0, y: -10 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="space-y-4 rounded-md border border-dashed p-4"
            >
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <Building className="h-5 w-5 text-blue-500" />
                Configuração do Condomínio
              </h3>

              {/* Seleção do Tipo de Condomínio */}
              <FormField
                control={form.control}
                name="tipo_condominio"
                render={({ field }) => (
                  <FormItem className="space-y-2 pt-2">
                    <FormLabel className="text-sm font-medium">
                      Qual o tipo de condomínio?
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) => {
                          field.onChange(value);
                          setTipoCondominio(value as "VERTICAL" | "HORIZONTAL");
                        }}
                        defaultValue={field.value}
                        className="flex items-center gap-4"
                      >
                        <FormItem className="flex items-center space-x-2 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="VERTICAL" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Vertical (Prédios)
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-2 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="HORIZONTAL" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Horizontal (Casas/Lotes)
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Campos para Condomínio Vertical */}
              {tipoCondominio === "VERTICAL" && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-2"
                >
                  <FormField
                    control={form.control}
                    name="numero_blocos"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nº de Blocos/Torres</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Ex: 2"
                            {...field}
                            value={field.value || 0}
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value, 10) || 0)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="andares_por_bloco"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Andares por Bloco</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Ex: 10"
                            {...field}
                            value={field.value || 0}
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value, 10) || 0)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="unidades_por_andar"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Unidades por Andar</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Ex: 4"
                            {...field}
                            value={field.value || 0}
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value, 10) || 0)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Campos de Área para Condomínio Vertical */}
                  <FormField
                    control={form.control}
                    name="area_lote"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Área do Terreno (m²)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Ex: 5000"
                            {...field}
                            value={field.value || 0}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                        </FormControl>
                        <FormDescription>
                          Área total do terreno onde será construído o
                          condomínio.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="area_construida_unidade"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>Área Construída por Unidade (m²)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Ex: 80"
                            {...field}
                            value={field.value || 0}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                        </FormControl>
                        <FormDescription>
                          Área média construída de cada apartamento/unidade.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>
              )}

              {/* Campo para Condomínio Horizontal */}
              {tipoCondominio === "HORIZONTAL" && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="pt-2 grid grid-cols-1 md:grid-cols-2 gap-4"
                >
                  <FormField
                    control={form.control}
                    name="numero_unidades"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nº de Unidades (Casas/Lotes)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Ex: 100"
                            {...field}
                            value={field.value || 0}
                            onChange={(e) =>
                              field.onChange(parseInt(e.target.value, 10) || 0)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="area_lote"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Área do Lote por Unidade (m²)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Ex: 300"
                            {...field}
                            value={field.value || 0}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                        </FormControl>
                        <FormDescription>
                          Área média de cada lote/terreno.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="area_construida_unidade"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>Área Construída por Unidade (m²)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Ex: 150"
                            {...field}
                            value={field.value || 0}
                            onChange={(e) =>
                              field.onChange(parseFloat(e.target.value) || 0)
                            }
                          />
                        </FormControl>
                        <FormDescription>
                          Área média construída em cada unidade/casa.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>
              )}
            </motion.div>
          )}

          {/* Seção: Localização (CEP primeiro) */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Localização
            </h3>

            {/* CEP como primeiro campo */}
            <FormField
              control={form.control}
              name="cep"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    CEP
                    {isLoadingCEP && (
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    )}
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        placeholder="00000-000"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          handleCEPChange(e.target.value);
                        }}
                        className="bg-background/50 focus:bg-background transition-colors pr-10"
                        maxLength={9}
                      />
                      <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Digite o CEP para preenchimento automático do endereço
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="endereco"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Endereço</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Rua, número, complemento"
                        {...field}
                        className="bg-background/50 focus:bg-background transition-colors"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="cidade"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cidade</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nome da cidade"
                        {...field}
                        className="bg-background/50 focus:bg-background transition-colors"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="estado"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estado</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger className="bg-background/50 focus:bg-background transition-colors">
                          <SelectValue placeholder="Selecione o estado" />
                        </SelectTrigger>
                        <SelectContent>
                          {brazilianStates.map((state) => (
                            <SelectItem key={state.value} value={state.value}>
                              {state.value} - {state.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Seção: Identificação */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <div className="h-px flex-1 bg-border" />
              Identificação
              <div className="h-px flex-1 bg-border" />
            </h3>

            <FormField
              control={form.control}
              name="nome"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nome da Obra</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Ex: Edifício Residencial Aurora"
                      {...field}
                      className="bg-background/50 focus:bg-background transition-colors"
                    />
                  </FormControl>
                  <FormDescription>
                    Nome que identificará a obra no sistema
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="construtora_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Construtora / Autônomo Responsável</FormLabel>
                  <FormControl>
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={loadingConstrutoras}
                    >
                      <SelectTrigger className="bg-background/50 focus:bg-background transition-colors">
                        <SelectValue
                          placeholder={
                            loadingConstrutoras
                              ? "Carregando..."
                              : "Selecione a construtora/autônomo"
                          }
                        />
                      </SelectTrigger>
                      <SelectContent>
                        {construtoras.map((c) => (
                          <SelectItem key={c.id} value={c.id}>
                            {c.tipo === "pj"
                              ? `${c.nome_razao_social} (CNPJ: ${c.documento})`
                              : `${
                                  c.nome_fantasia || c.nome_razao_social
                                } (CPF/CNPJ: ${c.documento})`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>
                    Selecione a construtora ou autônomo responsável pela obra
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Seção: Financeiro e Prazos */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Financeiro e Prazos
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="orcamento"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Orçamento</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="number"
                          placeholder="0,00"
                          step="0.01"
                          {...field}
                          onChange={(e) => {
                            const value = parseFloat(e.target.value);
                            field.onChange(isNaN(value) ? 0 : value);
                          }}
                          className="pl-9 bg-background/50 focus:bg-background transition-colors"
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Valor total do orçamento da obra
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="area_total"
                render={({ field }) => {
                  const isCondominio =
                    form.watch("tipo_projeto") === "CONDOMINIO_MASTER";

                  return (
                    <FormItem>
                      <FormLabel>
                        Área Total (m²)
                        {isCondominio && (
                          <span className="text-xs text-muted-foreground ml-2">
                            (Calculado automaticamente)
                          </span>
                        )}
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                            m²
                          </span>
                          <Input
                            type="number"
                            placeholder="0,00"
                            step="0.01"
                            {...field}
                            onChange={(e) => {
                              if (isCondominio) return; // Não permitir edição manual em condomínios

                              const value = parseFloat(e.target.value);
                              field.onChange(isNaN(value) ? 0 : value);
                            }}
                            readOnly={isCondominio}
                            className={cn(
                              "pl-9 transition-colors",
                              isCondominio
                                ? "bg-muted/50 cursor-not-allowed text-muted-foreground"
                                : "bg-background/50 focus:bg-background"
                            )}
                          />
                        </div>
                      </FormControl>
                      <FormDescription>
                        {isCondominio
                          ? "Área calculada automaticamente baseada nas configurações do condomínio"
                          : "Área total da construção (necessária para orçamento paramétrico)"}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />

              <FormField
                control={form.control}
                name="data_inicio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Início</FormLabel>
                    <FormControl>
                      <DatePicker
                        date={field.value || undefined}
                        onSelect={field.onChange}
                      />
                    </FormControl>
                    <FormDescription>Previsão de início</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="data_prevista_termino"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Término</FormLabel>
                    <FormControl>
                      <DatePicker
                        date={field.value || undefined}
                        onSelect={field.onChange}
                      />
                    </FormControl>
                    <FormDescription>Previsão de conclusão</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </FormWrapper>
      </motion.div>
    </DashboardLayout>
  );
};

export default NovaObraRefactored;
