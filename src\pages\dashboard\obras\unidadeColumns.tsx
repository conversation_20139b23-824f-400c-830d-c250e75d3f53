"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { Edit, Eye } from "lucide-react";
import { useNavigate } from "react-router-dom";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { Obra } from "@/types/api";

// Função para formatar status
const formatStatus = (status?: string) => {
  if (!status) return "Não definido";

  const statusMap: Record<
    string,
    {
      label: string;
      variant: "default" | "secondary" | "destructive" | "outline";
    }
  > = {
    planejamento: { label: "Planejamento", variant: "outline" },
    em_andamento: { label: "Em Andamento", variant: "default" },
    concluida: { label: "Concluída", variant: "secondary" },
    pausada: { label: "Pausada", variant: "destructive" },
  };

  const statusInfo = statusMap[status] || {
    label: status,
    variant: "outline" as const,
  };

  return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
};

export const useUnidadeColumns = () => {
  const navigate = useNavigate();

  return [
    {
      accessorKey: "identificador_unidade",
      header: "Identificador",
      cell: ({ row }) => (
        <div className="font-medium">
          {row.getValue("identificador_unidade") || "N/A"}
        </div>
      ),
    },
    {
      accessorKey: "nome",
      header: "Nome da Unidade",
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate">{row.getValue("nome")}</div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => formatStatus(row.getValue("status")),
    },
    {
      accessorKey: "area_total",
      header: "Área Total (m²)",
      cell: ({ row }) => {
        const area = row.getValue("area_total") as number;
        if (!area) return "N/A";

        return (
          <div className="text-right font-medium">
            {new Intl.NumberFormat("pt-BR", {
              style: "decimal",
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            }).format(area)}
          </div>
        );
      },
    },
    {
      accessorKey: "orcamento",
      header: "Orçamento Atual",
      cell: ({ row }) => {
        const orcamentoInvestimento = row.getValue("orcamento") as number;
        const orcamentoParametrico = row.original
          .valor_orcamento_parametrico as number;
        const orcamento = orcamentoInvestimento || orcamentoParametrico;

        if (!orcamento) return "N/A";

        return (
          <div className="text-right font-medium">
            {new Intl.NumberFormat("pt-BR", {
              style: "currency",
              currency: "BRL",
            }).format(orcamento)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Ações",
      cell: ({ row }) => {
        const unidade = row.original;

        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate(`/dashboard/obras/${unidade.id}`)}
              className="h-8 w-8 text-sky-600 dark:text-sky-400 hover:bg-sky-100 dark:hover:bg-sky-900/30"
              title="Ver detalhes"
            >
              <Eye className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate(`/dashboard/obras/${unidade.id}/editar`)}
              className="h-8 w-8 text-amber-600 dark:text-amber-400 hover:bg-amber-100 dark:hover:bg-amber-900/30"
              title="Editar"
            >
              <Edit className="h-4 w-4" />
            </Button>
          </div>
        );
      },
      enableSorting: false,
    },
  ] as ColumnDef<Obra>[];
};
