import { zodResolver } from '@hookform/resolvers/zod';
import type { ColumnDef } from '@tanstack/react-table';
import { AnimatePresence, motion } from 'framer-motion';
import {
    AlertCircle,
    Archive,
    BarChart3,
    Brain,
    Building,
    CheckCircle,
    ClipboardList,
    Copy,
    Download,
    Edit,
    Eye,
    Filter,
    Home,
    Layers,
    MapPin,
    MoreHorizontal,
    Plus,
    RefreshCw,
    Ruler,
    Search,
    Sparkles,
    Target,
    Trash2,
    TrendingUp,
    Upload,
    Zap
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';

import DashboardLayout from '@/components/layouts/DashboardLayout';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { MetricCard } from '@/components/ui/metric-card';
import { Progress } from '@/components/ui/progress';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { useCrudOperations } from '@/hooks/useCrudOperations';
import { useGerarOrcamento } from '@/hooks/useGerarOrcamento';
import { type PlantaAnalysisData,usePlantaIA } from '@/hooks/usePlantaIA';
import { usePlantaIAMetrics } from '@/hooks/usePlantaIAMetrics';
import { type PlantaAnalisada,usePlantasAnalisadas } from '@/hooks/usePlantasAnalisadas';
import { supabase } from '@/integrations/supabase/client';
import { formatCurrencyBR, formatDateBR } from '@/lib/i18n';
import { cn } from '@/lib/utils';
import { obrasApi } from '@/services/api';

const enderecoSchema = z.object({
  nome: z.string().min(1, 'Nome é obrigatório'),
  endereco: z.string().min(1, 'Endereço é obrigatório'),
  numero: z.string().optional(),
  bairro: z.string().min(1, 'Bairro é obrigatório'),
  cidade: z.string().min(1, 'Cidade é obrigatória'),
  estado: z.string().min(2, 'Estado é obrigatório').max(2, 'Estado deve ter 2 caracteres'),
  cep: z.string().min(8, 'CEP deve ter 8 dígitos').regex(/^\d{8}$/, 'CEP deve conter apenas números'),
});

type EnderecoForm = z.infer<typeof enderecoSchema>;

const calculateTrend = (current: number, previous: number) => {
  if (previous === 0) {
    return { value: current > 0 ? 100 : 0, isPositive: current > 0 };
  }
  const percentage = ((current - previous) / previous) * 100;
  return { value: Math.abs(percentage), isPositive: percentage >= 0 };
};

export default function PlantaIA() {
  const navigate = useNavigate();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [analysisData, setAnalysisData] = useState<PlantaAnalysisData | null>(null);
  const [plantaUrl, setPlantaUrl] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [plantaToDelete, setPlantaToDelete] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('upload');
  const [isDragActive, setIsDragActive] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedPlantas, setSelectedPlantas] = useState<string[]>([]);
  const [filteredPlantas, setFilteredPlantas] = useState<PlantaAnalisada[]>([]);
  const [period, setPeriod] = useState<'7d' | '30d' | '90d'>('30d');
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const { analisarPlanta, testarFuncao, isLoading, error, data, isSuccess, reset } = usePlantaIA();
  const { data: metricsData, isLoading: isLoadingMetrics } = usePlantaIAMetrics(period);
  const { 
    plantas, 
    isLoading: isLoadingPlantas, 
    createPlanta, 
    updatePlanta: _updatePlanta, 
    deletePlanta, 
    deletePlantas,
    getMetricas 
  } = usePlantasAnalisadas();
  const { gerarOrcamento, isLoading: isGeneratingBudget } = useGerarOrcamento();
  
  // Configurar API CRUD para obras
  const obrasApiCrud = {
    getAll: obrasApi.getAll,
    getById: async () => { throw new Error('Not implemented'); },
    create: obrasApi.create,
    update: obrasApi.update,
    delete: obrasApi.delete,
  };

  const { createMutation: createObra } = useCrudOperations(obrasApiCrud, {
    resource: 'obras',
    messages: {
      createSuccess: 'Obra criada com sucesso!',
      createError: 'Erro ao criar obra',
    },
  });

  const isCreatingObra = createObra.isPending;

  const form = useForm<EnderecoForm>({
    resolver: zodResolver(enderecoSchema),
    defaultValues: {
      nome: `Obra gerada pela PlantaIA - ${new Date().toLocaleDateString()}`,
      endereco: '',
      numero: '',
      bairro: '',
      cidade: '',
      estado: '',
      cep: '',
    },
  });

  // Métricas calculadas dos dados reais - não utilizado na UI atual
  const _metricas = React.useMemo(() => {
    try {
      return getMetricas();
    } catch (error) {
      console.error('Erro ao calcular métricas:', error);
      return {
        totalPlantas: 0,
        plantasAnalisadas: 0,
        obrasGeradas: 0,
        areaTotal: 0,
        valorTotalEstimado: 0,
        taxaConversao: 0,
      };
    }
  }, [getMetricas]);

  // Filtrar plantas
  useEffect(() => {
    let filtered = plantas;

    if (searchTerm) {
      filtered = filtered.filter(p => 
        p.nome_projeto.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (p.resumo_analise && p.resumo_analise.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(p => p.status === statusFilter);
    }

    setFilteredPlantas(filtered);
  }, [searchTerm, statusFilter, plantas]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      reset();
      setAnalysisData(null);
      setPlantaUrl(null);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
    
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      setSelectedFile(files[0]);
      reset();
      setAnalysisData(null);
      setPlantaUrl(null);
    }
  };

  const handleAnalyze = () => {
    if (selectedFile) {
      analisarPlanta(selectedFile);
    }
  };

  React.useEffect(() => {
    if (isSuccess && data) {
      setAnalysisData(data.analise);
      setPlantaUrl(data.url_planta);
      setActiveTab('results');
      
      // Salvar automaticamente no banco de dados
      if (selectedFile) {
        // Verificar se dados_estruturados existe antes de acessar suas propriedades
        const dadosEstruturados = data.analise?.dados_estruturados || {};
        
        const plantaData = {
          nome_projeto: `Planta ${new Date().toLocaleDateString()}`,
          nome_arquivo: selectedFile.name,
          url_planta: data.url_planta,
          tamanho_arquivo: selectedFile.size,
          tipo_arquivo: selectedFile.type,
          resumo_analise: data.analise?.resumo_analise || 'Análise concluída',
          dados_estruturados: dadosEstruturados,
          area_total_construida: parseFloat(dadosEstruturados.area_total_construida) || 0,
          numero_quartos: parseInt(dadosEstruturados.numero_quartos) || 0,
          numero_banheiros: parseInt(dadosEstruturados.numero_banheiros) || 0,
          numero_pavimentos: parseInt(dadosEstruturados.pavimentos) || 1,
          outros_comodos: dadosEstruturados.outros_comodos || [],
        };
        
        createPlanta.mutate(plantaData);
      }
    }
  }, [isSuccess, data, selectedFile, createPlanta]);

  const handleCreateObra = async (values: EnderecoForm) => {
    if (!analysisData || !plantaUrl) return;

    const obraBasica = {
      nome: values.nome,
      endereco: values.endereco,
      cidade: values.cidade,
      estado: values.estado,
      cep: values.cep.replace(/\D/g, ''),
      orcamento: 0,
      construtora_id: 'planta-ia-temp',
    };

    try {
      const obraCriada = await createObra.mutateAsync(obraBasica);
      
      if (obraCriada?.id) {
        const areaTotal = parseFloat(analysisData.dados_estruturados?.area_total_construida) || 0;
        
        // Atualizar obra com dados da PlantaIA
        const { error: updateError } = await supabase
          .from('obras')
          .update({
            numero: values.numero || null,
            bairro: values.bairro,
            area_total: areaTotal,
            descricao_ia: analysisData?.resumo_analise || 'Análise de planta por IA',
            url_planta: plantaUrl,
          })
          .eq('id', obraCriada.id);

        if (updateError) {
          console.error('Erro ao atualizar dados da PlantaIA:', updateError);
        }

        // Gerar orçamento paramétrico baseado na área analisada
        if (areaTotal > 0) {
          try {
            const { data: orcamentoData, error: orcamentoError } = await supabase.functions.invoke(
              'gerar-orcamento-parametrico',
              {
                body: { obra_id: obraCriada.id }
              }
            );

            if (!orcamentoError && orcamentoData?.success) {
              // Atualizar também o valor estimado na tabela plantas_analisadas
              await supabase
                .from('plantas_analisadas')
                .update({
                  valor_orcamento_parametrico: orcamentoData.dados.valor_orcamento_parametrico,
                  valor_estimado: orcamentoData.dados.valor_orcamento_parametrico,
                  status: 'obra_criada',
                  obra_id: obraCriada.id
                })
                .eq('url_planta', plantaUrl);

              toast({
                title: "Obra criada com sucesso!",
                description: `Orçamento paramétrico gerado: ${orcamentoData.dados.valor_formatado}`,
              });
            } else {
              console.error('Erro ao gerar orçamento paramétrico:', orcamentoError);
              toast({
                title: "Obra criada",
                description: "Obra criada, mas houve erro ao gerar orçamento paramétrico.",
                variant: "destructive",
              });
            }
          } catch (orcamentoError) {
            console.error('Erro na chamada do orçamento paramétrico:', orcamentoError);
          }
        }
      }
      
      setShowCreateModal(false);
      navigate('/dashboard/obras');
    } catch (error) {
      console.error('Erro ao criar obra:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'analisada': { variant: 'warning', label: 'Analisada', icon: Eye },
      'obra_criada': { variant: 'success', label: 'Obra Criada', icon: CheckCircle },
      'arquivada': { variant: 'secondary', label: 'Arquivada', icon: Archive }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.analisada;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant as "default" | "secondary" | "destructive" | "outline"} className="gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const columns: ColumnDef<PlantaAnalisada>[] = [
    {
      id: "select",
      header: ({ table: _table }) => (
        <Checkbox
          checked={selectedPlantas.length === filteredPlantas.length && filteredPlantas.length > 0}
          onCheckedChange={(checked) => {
            if (checked) {
              setSelectedPlantas(filteredPlantas.map(p => p.id));
            } else {
              setSelectedPlantas([]);
            }
          }}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={selectedPlantas.includes(row.original.id)}
          onCheckedChange={(checked) => {
            if (checked) {
              setSelectedPlantas(prev => [...prev, row.original.id]);
            } else {
              setSelectedPlantas(prev => prev.filter(id => id !== row.original.id));
            }
          }}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "nome_projeto",
      header: "Nome do Projeto",
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Building className="h-5 w-5 text-white" />
          </div>
          <div>
            <div className="font-medium">{row.original.nome_projeto}</div>
            <div className="text-sm text-muted-foreground">
              {row.original.area_total_construida || 0}m² • {row.original.numero_quartos || 0} quartos
            </div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: "area_total_construida",
      header: "Área Total",
      cell: ({ row }) => (
        <div className="text-right font-medium">
          {row.original.area_total_construida || 0}m²
        </div>
      ),
    },
    {
      accessorKey: "numero_quartos",
      header: "Quartos",
      cell: ({ row }) => (
        <div className="text-center">{row.original.numero_quartos || 0}</div>
      ),
    },
    {
      accessorKey: "numero_banheiros",
      header: "Banheiros",
      cell: ({ row }) => (
        <div className="text-center">{row.original.numero_banheiros || 0}</div>
      ),
    },
    {
      accessorKey: "valor_estimado",
      header: "Valor Estimado",
      cell: ({ row }) => (
        <div className="text-right font-medium">
          {row.original.valor_estimado ? formatCurrencyBR(row.original.valor_estimado) : '-'}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => getStatusBadge(row.original.status),
    },
    {
      accessorKey: "data_analise",
      header: "Data Análise",
      cell: ({ row }) => formatDateBR(row.original.data_analise),
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Ações</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => window.open(row.original.url_planta, '_blank')}>
              <Eye className="mr-2 h-4 w-4" />
              Visualizar
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => {
              // TODO: Implementar edição de planta
            }}>
              <Edit className="mr-2 h-4 w-4" />
              Editar
            </DropdownMenuItem>
            {row.original.status === 'analisada' && (
              <DropdownMenuItem onClick={() => {
                // TODO: Implementar criação de obra a partir da planta
              }}>
                <Plus className="mr-2 h-4 w-4" />
                Criar Obra
              </DropdownMenuItem>
            )}
            {row.original.obra_id && !row.original.valor_orcamento_parametrico && (
              <DropdownMenuItem 
                onClick={() => gerarOrcamento(row.original.obra_id!)}
                disabled={isGeneratingBudget}
              >
                <TrendingUp className="mr-2 h-4 w-4" />
                Gerar Orçamento
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => window.open(row.original.url_planta, '_blank')}>
              <Download className="mr-2 h-4 w-4" />
              Download
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => {
              // TODO: Implementar duplicação
            }}>
              <Copy className="mr-2 h-4 w-4" />
              Duplicar
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => {
                setPlantaToDelete(row.original.id);
                setShowDeleteDialog(true);
              }} 
              className="text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Excluir
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between"
        >
          <div className="flex items-center gap-4">
            <div className="h-12 w-12 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <Ruler className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-cyan-600 to-blue-600 bg-clip-text text-transparent">
                PlantaIA
              </h1>
              <p className="text-muted-foreground">
                Análise inteligente de plantas arquitetônicas com IA avançada
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Select value={period} onValueChange={(value) => setPeriod(value as '7d' | '30d' | '90d')}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Selecionar Período" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Últimos 7 dias</SelectItem>
                <SelectItem value="30d">Últimos 30 dias</SelectItem>
                <SelectItem value="90d">Últimos 90 dias</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Relatório
            </Button>
          </div>
        </motion.div>

        {/* Métricas */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
        >
          {isLoadingMetrics || !metricsData ? (
            <>
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
            </>
          ) : (
            <>
              <MetricCard
                title="Total de Plantas"
                value={metricsData.currentPeriod.totalPlantas.toString()}
                description="Plantas analisadas no período"
                icon={ClipboardList}
                trend={calculateTrend(metricsData.currentPeriod.totalPlantas, metricsData.previousPeriod.totalPlantas)}
                className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20"
              />
              <MetricCard
                title="Área Total Analisada"
                value={`${(metricsData.currentPeriod.areaTotal || 0).toFixed(1)}m²`}
                description="Área construída total no período"
                icon={Layers}
                trend={calculateTrend(metricsData.currentPeriod.areaTotal, metricsData.previousPeriod.areaTotal)}
                className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20"
              />
              <MetricCard
                title="Obras Geradas"
                value={(metricsData.currentPeriod.obrasGeradas || 0).toString()}
                description="Conversão de planta para obra"
                icon={Building}
                trend={calculateTrend(metricsData.currentPeriod.obrasGeradas, metricsData.previousPeriod.obrasGeradas)}
                className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20"
              />
              <MetricCard
                title="Valor Orçado (Paramétrico)"
                value={formatCurrencyBR(metricsData.currentPeriod.valorTotalEstimado || 0)}
                description="Potencial de negócio no período"
                icon={TrendingUp}
                trend={calculateTrend(metricsData.currentPeriod.valorTotalEstimado, metricsData.previousPeriod.valorTotalEstimado)}
                className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20"
              />
            </>
          )}
        </motion.div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Upload & Análise
            </TabsTrigger>
            <TabsTrigger value="results" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Resultados
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Histórico
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card className="relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5" />
                <CardHeader className="relative">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <CardTitle>Upload de Planta</CardTitle>
                      <p className="text-sm text-muted-foreground">
                        Faça o upload de uma planta arquitetônica para análise por IA
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="relative space-y-6">
                  <div
                    className={cn(
                      "relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300",
                      "hover:border-blue-400 hover:bg-blue-50/50 dark:hover:bg-blue-900/10",
                      isDragActive && "border-blue-500 bg-blue-50 dark:bg-blue-900/20",
                      selectedFile && "border-green-500 bg-green-50 dark:bg-green-900/20"
                    )}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                  >
                    <AnimatePresence mode="wait">
                      {selectedFile ? (
                        <motion.div
                          key="selected"
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.8 }}
                          className="space-y-4"
                        >
                          <div className="h-16 w-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto">
                            <CheckCircle className="h-8 w-8 text-green-600" />
                          </div>
                          <div>
                            <p className="font-medium text-green-700 dark:text-green-400">
                              {selectedFile.name}
                            </p>
                            <p className="text-sm text-green-600 dark:text-green-500">
                              {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </motion.div>
                      ) : (
                        <motion.div
                          key="upload"
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.8 }}
                          className="space-y-4"
                        >
                          <div className="h-16 w-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto">
                            <Upload className="h-8 w-8 text-blue-600" />
                          </div>
                          <div>
                            <p className="text-lg font-medium">
                              Arraste e solte ou clique para selecionar
                            </p>
                            <p className="text-sm text-muted-foreground">
                              PDF, JPG, PNG (máx. 10MB)
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                    
                    <Input
                      ref={fileInputRef}
                      type="file"
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={handleFileSelect}
                      className="absolute inset-0 opacity-0 cursor-pointer"
                    />
                  </div>

                  <div className="flex justify-center gap-4">
                    <Button
                      onClick={() => {
                        if (fileInputRef.current) {
                          fileInputRef.current.click();
                        }
                      }}
                      variant="outline"
                      size="lg"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Selecionar Arquivo
                    </Button>
                    <Button
                      onClick={testarFuncao}
                      variant="ghost"
                      size="lg"
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      Testar Conexão
                    </Button>
                  </div>

                  {selectedFile && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex justify-center gap-4"
                    >
                      <Button
                        onClick={handleAnalyze}
                        disabled={isLoading}
                        size="lg"
                        className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                      >
                        {isLoading ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Analisando...
                          </>
                        ) : (
                          <>
                            <Brain className="h-4 w-4 mr-2" />
                            Analisar com IA
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setSelectedFile(null)}
                        size="lg"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Remover
                      </Button>
                    </motion.div>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {error.message || 'Erro ao analisar a planta'}
                </AlertDescription>
              </Alert>
            )}

            {isLoading && (
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />
                      <span className="font-medium">Processando com IA...</span>
                    </div>
                    <Progress value={65} className="h-2" />
                    <div className="grid grid-cols-2 gap-4">
                      <Skeleton className="h-20" />
                      <Skeleton className="h-20" />
                      <Skeleton className="h-20" />
                      <Skeleton className="h-20" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="results" className="space-y-6">
            {analysisData ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <CardTitle className="text-green-800 dark:text-green-400">
                            Análise Concluída
                          </CardTitle>
                          <p className="text-sm text-green-600 dark:text-green-500">
                            IA processou sua planta com sucesso
                          </p>
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        <Zap className="h-3 w-3 mr-1" />
                        Sucesso
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {analysisData?.resumo_analise || 'Análise concluída com sucesso'}
                    </p>
                  </CardContent>
                </Card>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-2">
                        <Layers className="h-4 w-4 text-blue-600" />
                        <CardTitle className="text-sm font-medium">Área Total</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-blue-600">
                        {analysisData.dados_estruturados?.area_total_construida || 0}m²
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20">
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-2">
                        <Home className="h-4 w-4 text-purple-600" />
                        <CardTitle className="text-sm font-medium">Quartos</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-purple-600">
                        {analysisData.dados_estruturados?.numero_quartos || 0}
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-2">
                        <Target className="h-4 w-4 text-green-600" />
                        <CardTitle className="text-sm font-medium">Banheiros</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-green-600">
                        {analysisData.dados_estruturados?.numero_banheiros || 0}
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-orange-600" />
                        <CardTitle className="text-sm font-medium">Pavimentos</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-orange-600">
                        {analysisData.dados_estruturados?.pavimentos || 1}
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="md:col-span-2 bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-900/20 dark:to-gray-900/20">
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-slate-600" />
                        <CardTitle className="text-sm font-medium">Outros Cômodos</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {(analysisData.dados_estruturados?.outros_comodos || []).map((comodo, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {comodo}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="flex justify-center">
                  <Button
                    onClick={() => setShowCreateModal(true)}
                    size="lg"
                    className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Criar Obra com esta Análise
                  </Button>
                </div>
              </motion.div>
            ) : (
              <div className="text-center py-12">
                <div className="h-16 w-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Eye className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Nenhuma análise encontrada
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Faça o upload de uma planta na aba "Upload & Análise" para ver os resultados aqui.
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Buscar plantas..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-72"
                    />
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Filtrar por status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os Status</SelectItem>
                      <SelectItem value="analisada">Analisada</SelectItem>
                      <SelectItem value="obra_criada">Obra Criada</SelectItem>
                      <SelectItem value="arquivada">Arquivada</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center gap-2">
                  {selectedPlantas.length > 0 && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        deletePlantas.mutate(selectedPlantas);
                        setSelectedPlantas([]);
                      }}
                      disabled={deletePlantas.isPending}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Excluir ({selectedPlantas.length})
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filtros
                  </Button>
                </div>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Histórico de Análises
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingPlantas ? (
                    <div className="space-y-4">
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-full" />
                    </div>
                  ) : (
                                      <DataTable
                    columns={columns}
                    data={filteredPlantas}
                    searchKey="nome_projeto"
                    searchPlaceholder="Buscar por nome..."
                  />
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>
        </Tabs>

        {/* Modal de Criação de Obra */}
        <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Criar Nova Obra
              </DialogTitle>
              <DialogDescription>
                Preencha os dados de endereço para criar a obra baseada na análise da planta
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleCreateObra)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="nome"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nome da Obra</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-4 gap-4">
                  <FormField
                    control={form.control}
                    name="cep"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>CEP</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="00000000" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="estado"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estado</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="SP" maxLength={2} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="cidade"
                    render={({ field }) => (
                      <FormItem className="col-span-2">
                        <FormLabel>Cidade</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-4 gap-4">
                  <FormField
                    control={form.control}
                    name="endereco"
                    render={({ field }) => (
                      <FormItem className="col-span-3">
                        <FormLabel>Endereço</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="numero"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Número</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="bairro"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bairro</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowCreateModal(false)}
                  >
                    Cancelar
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={isCreatingObra}
                    className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                  >
                    {isCreatingObra ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Criando...
                      </>
                    ) : (
                      <>
                        <Building className="h-4 w-4 mr-2" />
                        Criar Obra
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>

        {/* Dialog de Confirmação de Exclusão */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Confirmar Exclusão</AlertDialogTitle>
              <AlertDialogDescription>
                Tem certeza que deseja excluir esta planta? Esta ação não pode ser desfeita.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancelar</AlertDialogCancel>
              <AlertDialogAction
                onClick={async () => {
                  if (plantaToDelete) {
                    await deletePlanta.mutateAsync(plantaToDelete);
                    setPlantaToDelete(null);
                    setShowDeleteDialog(false);
                  }
                }}
                className="bg-red-600 hover:bg-red-700"
              >
                Excluir
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </DashboardLayout>
  );
}