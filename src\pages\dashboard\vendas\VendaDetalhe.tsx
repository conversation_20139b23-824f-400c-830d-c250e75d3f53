import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { 
  AlertCircle,
  ArrowLeft,
  Building,
  Calculator,
  CheckCircle2,
  Clock,
  DollarSign, 
  TrendingDown, 
  TrendingUp} from "lucide-react";
import { useNavigate, useParams } from "react-router-dom";

import VendaLucroTab from "@/components/dashboard/obras/VendaLucroTab";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { GradientCard } from "@/components/ui/GradientCard";
import { supabase } from "@/integrations/supabase/client";
import { formatCurrencyBR } from "@/lib/i18n";
import { cn } from "@/lib/utils";

interface VendaDetalheData {
  id: string;
  nome: string;
  cidade: string;
  estado: string;
  orcamento: number;
  valor_venda: number | null;
  status_venda: string;
  data_venda: string | null;
  custo_total_real: number | null;
  lucro_bruto: number | null;
  lucro_liquido: number | null;
  margem_lucro_percentual: number | null;
  roi_percentual: number | null;
}

const VendaDetalhe = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // Query para buscar dados específicos da venda
  const { data: venda, isLoading, error } = useQuery({
    queryKey: ["venda-detalhe", id],
    queryFn: async () => {
      if (!id) throw new Error("ID da obra é obrigatório");

      const { data, error } = await supabase
        .from("v_obras_lucratividade")
        .select("*")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data as VendaDetalheData;
    },
    enabled: !!id,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "VENDIDO":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700";
      case "EM_NEGOCIACAO":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700";
      default:
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "VENDIDO":
        return <CheckCircle2 className="h-4 w-4" />;
      case "EM_NEGOCIACAO":
        return <Clock className="h-4 w-4" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "VENDIDO":
        return "Vendido";
      case "EM_NEGOCIACAO":
        return "Em Negociação";
      default:
        return "À Venda";
    }
  };

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center space-y-4">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
            <div>
              <h3 className="text-lg font-semibold">Erro ao carregar dados da venda</h3>
              <p className="text-muted-foreground">Não foi possível carregar os dados da venda.</p>
            </div>
            <Button onClick={() => navigate("/dashboard/vendas")} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar para Lista
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
            <p className="text-muted-foreground">Carregando dados da venda...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!venda) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center space-y-4">
            <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto" />
            <div>
              <h3 className="text-lg font-semibold">Venda não encontrada</h3>
              <p className="text-muted-foreground">Os dados da venda solicitada não foram encontrados.</p>
            </div>
            <Button onClick={() => navigate("/dashboard/vendas")} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar para Lista
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <div className="flex items-center gap-3 mb-2">
              <Button
                onClick={() => navigate("/dashboard/vendas")}
                variant="ghost"
                size="sm"
                className="h-8 px-2"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Voltar
              </Button>
              <div className="h-6 w-px bg-border" />
              <Badge className={cn("flex items-center gap-1", getStatusColor(venda.status_venda))}>
                {getStatusIcon(venda.status_venda)}
                {getStatusLabel(venda.status_venda)}
              </Badge>
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {venda.nome}
            </h1>
            <p className="text-muted-foreground mt-1">
              {venda.cidade}, {venda.estado}
            </p>
          </motion.div>
        </div>

        {/* Resumo da Venda */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
        >
          <GradientCard variant="blue" className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-blue-500/10 flex items-center justify-center">
                <Building className="h-5 w-5 text-blue-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Orçamento</p>
                <p className="text-xl font-bold text-blue-600">
                  {formatCurrencyBR(venda.orcamento)}
                </p>
              </div>
            </div>
          </GradientCard>

          <GradientCard variant="green" className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-green-500/10 flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-green-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Valor de Venda</p>
                <p className="text-xl font-bold text-green-600">
                  {venda.valor_venda ? formatCurrencyBR(venda.valor_venda) : "Não definido"}
                </p>
              </div>
            </div>
          </GradientCard>

          <GradientCard variant="emerald" className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-emerald-500/10 flex items-center justify-center">
                <Calculator className="h-5 w-5 text-emerald-500" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Lucro Líquido</p>
                <p className={cn(
                  "text-xl font-bold",
                  (venda.lucro_liquido || 0) >= 0 ? "text-emerald-600" : "text-red-600"
                )}>
                  {venda.lucro_liquido ? formatCurrencyBR(venda.lucro_liquido) : "-"}
                </p>
              </div>
            </div>
          </GradientCard>

          <GradientCard variant="purple" className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-lg bg-purple-500/10 flex items-center justify-center">
                {(venda.roi_percentual || 0) >= 0 ? (
                  <TrendingUp className="h-5 w-5 text-purple-500" />
                ) : (
                  <TrendingDown className="h-5 w-5 text-purple-500" />
                )}
              </div>
              <div>
                <p className="text-sm text-muted-foreground">ROI</p>
                <p className={cn(
                  "text-xl font-bold",
                  (venda.roi_percentual || 0) >= 0 ? "text-purple-600" : "text-red-600"
                )}>
                  {venda.roi_percentual ? `${venda.roi_percentual.toFixed(1)}%` : "-"}
                </p>
              </div>
            </div>
          </GradientCard>
        </motion.div>

        {/* Componente de Gerenciamento de Venda */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <VendaLucroTab obraId={venda.id} />
        </motion.div>
      </motion.div>
    </DashboardLayout>
  );
};

export default VendaDetalhe;