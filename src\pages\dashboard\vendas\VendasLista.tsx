import { useQuery } from "@tanstack/react-query";
import type { ColumnDef } from "@tanstack/react-table";
import { motion } from "framer-motion";
import { 
  AlertCircle,
  Building,
  Calendar,
  CheckCircle2,
  Clock,
  DollarSign, 
  Eye,
  TrendingDown, 
  TrendingUp} from "lucide-react";
import { useNavigate } from "react-router-dom";

import DashboardLayout from "@/components/layouts/DashboardLayout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { GradientCard } from "@/components/ui/GradientCard";
import { supabase } from "@/integrations/supabase/client";
import { formatCurrencyBR, formatDateBR } from "@/lib/i18n";
import { cn } from "@/lib/utils";

interface VendaData {
  id: string;
  nome: string;
  cidade: string;
  estado: string;
  orcamento: number;
  valor_venda: number | null;
  status_venda: string;
  data_venda: string | null;
  custo_total_real: number | null;
  lucro_bruto: number | null;
  lucro_liquido: number | null;
  margem_lucro_percentual: number | null;
  roi_percentual: number | null;
}

const VendasLista = () => {
  const navigate = useNavigate();

  // Query para buscar dados de vendas
  const { data: vendas, isLoading, error } = useQuery({
    queryKey: ["vendas-lista"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("v_obras_lucratividade")
        .select("*")
        .order("nome");

      if (error) throw error;
      return data as VendaData[];
    },
  });

  // Cálculos de métricas gerais
  const metricas = vendas ? {
    totalObras: vendas.length,
    obrasVendidas: vendas.filter(v => v.status_venda === 'VENDIDO').length,
    obrasAVenda: vendas.filter(v => v.status_venda === 'A_VENDA').length,
    obrasEmNegociacao: vendas.filter(v => v.status_venda === 'EM_NEGOCIACAO').length,
    faturamentoTotal: vendas
      .filter(v => v.valor_venda && v.status_venda === 'VENDIDO')
      .reduce((acc, v) => acc + (v.valor_venda || 0), 0),
    lucroTotal: vendas
      .filter(v => v.lucro_liquido && v.status_venda === 'VENDIDO')
      .reduce((acc, v) => acc + (v.lucro_liquido || 0), 0),
  } : null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "VENDIDO":
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border-green-200 dark:border-green-700";
      case "EM_NEGOCIACAO":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700";
      default:
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "VENDIDO":
        return <CheckCircle2 className="h-3 w-3" />;
      case "EM_NEGOCIACAO":
        return <Clock className="h-3 w-3" />;
      default:
        return <DollarSign className="h-3 w-3" />;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "VENDIDO":
        return "Vendido";
      case "EM_NEGOCIACAO":
        return "Em Negociação";
      default:
        return "À Venda";
    }
  };

  const columns: ColumnDef<VendaData>[] = [
    {
      accessorKey: "nome",
      header: "Obra",
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-lg bg-blue-500/10 flex items-center justify-center">
            <Building className="h-5 w-5 text-blue-500" />
          </div>
          <div>
            <div className="font-medium">{row.original.nome}</div>
            <div className="text-sm text-muted-foreground">
              {row.original.cidade}, {row.original.estado}
            </div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: "status_venda",
      header: "Status",
      cell: ({ row }) => (
        <Badge className={cn("flex items-center gap-1", getStatusColor(row.original.status_venda))}>
          {getStatusIcon(row.original.status_venda)}
          {getStatusLabel(row.original.status_venda)}
        </Badge>
      ),
    },
    {
      accessorKey: "valor_venda",
      header: "Valor de Venda",
      cell: ({ row }) => (
        <div className="text-right">
          {row.original.valor_venda ? (
            <span className="font-mono font-medium text-green-600 dark:text-green-400">
              {formatCurrencyBR(row.original.valor_venda)}
            </span>
          ) : (
            <span className="text-muted-foreground text-sm">Não definido</span>
          )}
        </div>
      ),
    },
    {
      accessorKey: "lucro_liquido",
      header: "Lucro Líquido",
      cell: ({ row }) => (
        <div className="text-right">
          {row.original.lucro_liquido !== null ? (
            <span className={cn(
              "font-mono font-medium",
              row.original.lucro_liquido >= 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
            )}>
              {formatCurrencyBR(row.original.lucro_liquido)}
            </span>
          ) : (
            <span className="text-muted-foreground text-sm">-</span>
          )}
        </div>
      ),
    },
    {
      accessorKey: "roi_percentual",
      header: "ROI",
      cell: ({ row }) => (
        <div className="text-right">
          {row.original.roi_percentual !== null ? (
            <div className="flex items-center justify-end gap-1">
              <span className={cn(
                "font-medium",
                row.original.roi_percentual >= 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
              )}>
                {row.original.roi_percentual.toFixed(1)}%
              </span>
              {row.original.roi_percentual >= 0 ? (
                <TrendingUp className="h-3 w-3 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-500" />
              )}
            </div>
          ) : (
            <span className="text-muted-foreground text-sm">-</span>
          )}
        </div>
      ),
    },
    {
      accessorKey: "data_venda",
      header: "Data da Venda",
      cell: ({ row }) => (
        <div className="text-sm">
          {row.original.data_venda ? (
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3 text-muted-foreground" />
              {formatDateBR(row.original.data_venda)}
            </div>
          ) : (
            <span className="text-muted-foreground">-</span>
          )}
        </div>
      ),
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate(`/dashboard/vendas/${row.original.id}`)}
          className="h-8 px-2 hover:bg-blue-500/10"
        >
          <Eye className="h-4 w-4 mr-1" />
          Ver Detalhes
        </Button>
      ),
    },
  ];

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center space-y-4">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
            <div>
              <h3 className="text-lg font-semibold">Erro ao carregar vendas</h3>
              <p className="text-muted-foreground">Não foi possível carregar os dados de vendas.</p>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Gestão de Vendas
            </h1>
            <p className="text-muted-foreground mt-1">
              Acompanhe o desempenho comercial das suas obras
            </p>
          </motion.div>
        </div>

        {/* Métricas Gerais */}
        {metricas && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
          >
            <GradientCard variant="blue" className="p-4">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-lg bg-blue-500/10 flex items-center justify-center">
                  <Building className="h-5 w-5 text-blue-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total de Obras</p>
                  <p className="text-2xl font-bold">{metricas.totalObras}</p>
                </div>
              </div>
            </GradientCard>

            <GradientCard variant="green" className="p-4">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-lg bg-green-500/10 flex items-center justify-center">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Obras Vendidas</p>
                  <p className="text-2xl font-bold">{metricas.obrasVendidas}</p>
                </div>
              </div>
            </GradientCard>

            <GradientCard variant="emerald" className="p-4">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-lg bg-emerald-500/10 flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-emerald-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Faturamento Total</p>
                  <p className="text-2xl font-bold text-emerald-600">
                    {formatCurrencyBR(metricas.faturamentoTotal)}
                  </p>
                </div>
              </div>
            </GradientCard>

            <GradientCard variant="purple" className="p-4">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-lg bg-purple-500/10 flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-purple-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Lucro Total</p>
                  <p className={cn(
                    "text-2xl font-bold",
                    metricas.lucroTotal >= 0 ? "text-green-600" : "text-red-600"
                  )}>
                    {formatCurrencyBR(metricas.lucroTotal)}
                  </p>
                </div>
              </div>
            </GradientCard>
          </motion.div>
        )}

        {/* Tabela de Vendas */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <GradientCard>
            <div className="p-6">
              <div className="flex items-center gap-2 mb-6">
                <DollarSign className="h-5 w-5 text-blue-500" />
                <h2 className="text-xl font-semibold">Obras e Status de Venda</h2>
              </div>
              
              <DataTable
                columns={columns}
                data={vendas || []}
                searchColumn="nome"
                searchPlaceholder="Buscar por nome da obra..."
                isLoading={isLoading}
              />
            </div>
          </GradientCard>
        </motion.div>
      </motion.div>
    </DashboardLayout>
  );
};

export default VendasLista;