/* Landing Page - Sistema de Cores para Melhorias de Contraste */
/* Baseado nas especificações WCAG 2.1 AA */

:root {
  /* === CORES PRIMÁRIAS === */
  /* Texto principal com contraste otimizado */
  --color-text-primary: #1a1a1a;        /* Contraste: 15.8:1 sobre branco */
  --color-text-critical: #000000;       /* Contraste: 21:1 sobre branco */
  --color-background-primary: #ffffff;
  
  /* === CORES SECUNDÁRIAS === */
  /* Textos informativos e auxiliares */
  --color-text-info: #4a4a4a;          /* Contraste: 9.7:1 sobre branco */
  --color-text-aux: #666666;           /* Contraste: 6.3:1 sobre branco */
  --color-borders: #e0e0e0;
  
  /* === CORES DE DESTAQUE === */
  /* CTAs e elementos de ação */
  --color-cta-primary: #ff6b35;        /* Laranja vibrante da marca */
  --color-cta-text: #ffffff;           /* Contraste: 4.8:1 sobre laranja */
  --color-cta-hover: #e55a2b;          /* Estado hover mais escuro */
  
  /* Status e feedback */
  --color-success: #2d5a2d;            /* Verde escuro - Contraste: 8.2:1 */
  --color-success-bg: #f0f9f0;         /* Fundo claro para badges */
  --color-alert: #d32f2f;              /* Vermelho para alertas */
  --color-warning: #f57c00;            /* Laranja para avisos */
  
  /* === CORES DE FUNDO === */
  /* Seções e cards */
  --color-bg-sections: #f8f9fa;        /* Cinza muito claro para seções */
  --color-bg-cards: #ffffff;           /* Branco para cards */
  --color-bg-header-footer: #1a1a1a;   /* Escuro para header/footer */
  
  /* === SOMBRAS E EFEITOS === */
  /* Profundidade visual */
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-card-hover: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-cta: 0 4px 12px rgba(255, 107, 53, 0.3);
  --shadow-cta-hover: 0 6px 20px rgba(255, 107, 53, 0.4);
  
  /* === BADGES E STATUS === */
  /* Cores específicas para diferentes tipos de badge */
  --color-badge-implemented-bg: #e8f5e8;
  --color-badge-implemented-text: #2d5a2d;
  --color-badge-coming-soon-bg: #e3f2fd;
  --color-badge-coming-soon-text: #1565c0;
  --color-badge-premium-bg: #fff8e1;
  --color-badge-premium-text: #f57c00;
  --color-badge-problem-bg: #ffebee;
  --color-badge-problem-text: #c62828;
  --color-badge-solution-bg: #e8f5e8;
  --color-badge-solution-text: #2e7d32;
}

/* === FALLBACKS PARA NAVEGADORES ANTIGOS === */
/* Garantir compatibilidade com IE11+ */
.text-primary {
  color: #1a1a1a; /* Fallback */
  color: var(--color-text-primary);
}

.text-critical {
  color: #000000; /* Fallback */
  color: var(--color-text-critical);
}

.text-info {
  color: #4a4a4a; /* Fallback */
  color: var(--color-text-info);
}

.text-aux {
  color: #666666; /* Fallback */
  color: var(--color-text-aux);
}

.bg-cta {
  background-color: #ff6b35; /* Fallback */
  background-color: var(--color-cta-primary);
}

.bg-cta:hover {
  background-color: #e55a2b; /* Fallback */
  background-color: var(--color-cta-hover);
}

/* === CLASSES UTILITÁRIAS === */
/* Classes para aplicação rápida das cores */

/* Textos */
.text-contrast-primary {
  color: var(--color-text-primary);
}

.text-contrast-critical {
  color: var(--color-text-critical);
  font-weight: 600;
}

.text-contrast-info {
  color: var(--color-text-info);
}

.text-contrast-aux {
  color: var(--color-text-aux);
}

/* CTAs e botões */
.btn-cta-primary {
  background-color: var(--color-cta-primary);
  color: var(--color-cta-text);
  box-shadow: var(--shadow-cta);
  transition: all 0.2s ease;
}

.btn-cta-primary:hover {
  background-color: var(--color-cta-hover);
  box-shadow: var(--shadow-cta-hover);
  transform: translateY(-1px);
}

.btn-cta-primary:focus {
  outline: 2px solid var(--color-cta-primary);
  outline-offset: 2px;
}

/* Cards */
.card-contrast {
  background-color: var(--color-bg-cards);
  box-shadow: var(--shadow-card);
  transition: box-shadow 0.2s ease;
}

.card-contrast:hover {
  box-shadow: var(--shadow-card-hover);
}

/* Badges */
.badge-implemented {
  background-color: var(--color-badge-implemented-bg);
  color: var(--color-badge-implemented-text);
  font-weight: 500;
}

.badge-coming-soon {
  background-color: var(--color-badge-coming-soon-bg);
  color: var(--color-badge-coming-soon-text);
  font-weight: 500;
}

.badge-premium {
  background-color: var(--color-badge-premium-bg);
  color: var(--color-badge-premium-text);
  font-weight: 500;
}

.badge-problem {
  background-color: var(--color-badge-problem-bg);
  color: var(--color-badge-problem-text);
  font-weight: 500;
}

.badge-solution {
  background-color: var(--color-badge-solution-bg);
  color: var(--color-badge-solution-text);
  font-weight: 500;
}

/* === HIERARQUIA TIPOGRÁFICA === */
/* Escalas com contraste garantido */

.heading-critical {
  color: var(--color-text-critical);
  font-weight: 700;
  line-height: 1.2;
}

.heading-primary {
  color: var(--color-text-primary);
  font-weight: 600;
  line-height: 1.3;
}

.body-info {
  color: var(--color-text-info);
  font-weight: 400;
  line-height: 1.6;
}

.caption-aux {
  color: var(--color-text-aux);
  font-weight: 400;
  line-height: 1.4;
  font-size: 0.875rem;
}

/* === ESTADOS INTERATIVOS === */
/* Garantir acessibilidade em todos os estados */

.interactive-element {
  transition: all 0.2s ease;
}

.interactive-element:hover {
  transform: translateY(-1px);
}

.interactive-element:focus {
  outline: 2px solid var(--color-cta-primary);
  outline-offset: 2px;
}

.interactive-element:active {
  transform: translateY(0);
}

/* === MODO DE ALTO CONTRASTE === */
/* Suporte para preferências do sistema */

@media (prefers-contrast: high) {
  :root {
    --color-text-primary: #000000;
    --color-text-info: #000000;
    --color-borders: #000000;
    --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}

/* === MODO DE MOVIMENTO REDUZIDO === */
/* Respeitar preferências de acessibilidade */

@media (prefers-reduced-motion: reduce) {
  .interactive-element,
  .btn-cta-primary,
  .card-contrast {
    transition: none;
  }
  
  .interactive-element:hover,
  .btn-cta-primary:hover {
    transform: none;
  }
}

/* === VALIDAÇÃO DE DESENVOLVIMENTO === */
/* Helpers para desenvolvimento (remover em produção) */

.debug-contrast {
  position: relative;
}

.debug-contrast::after {
  content: attr(data-contrast-ratio);
  position: absolute;
  top: -20px;
  left: 0;
  background: #000;
  color: #fff;
  padding: 2px 4px;
  font-size: 10px;
  border-radius: 2px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
}

.debug-contrast:hover::after {
  opacity: 1;
}