/**
 * 🧪 Testes do Chat de Fornecedores
 */

import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach,describe, expect, it, vi } from 'vitest';

import { FornecedoresChat } from '@/components/fornecedores/FornecedoresChat';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';

// Mocks
vi.mock('@/hooks/useAuth');
vi.mock('@/lib/supabase', () => ({
  supabase: {
    functions: {
      invoke: vi.fn()
    }
  }
}));

const mockUser = {
  id: 'user-123',
  email: '<EMAIL>'
};

const mockChatResponse = {
  response: 'Encontrei 3 fornecedores de eletricista em São Paulo.',
  intent: 'buscar_fornecedor',
  confidence: 0.95,
  fornecedores: [
    {
      id: 'forn-1',
      tipo: 'PF',
      nome: '<PERSON>',
      documento: '123.456.789-00',
      telefone: '(11) 99999-9999',
      email: '<EMAIL>',
      cidade: 'São Paulo',
      estado: 'SP',
      categoria: 'ELETRICISTA',
      rating: 4.5
    },
    {
      id: 'forn-2',
      tipo: 'PJ',
      nome: 'Elétrica Santos LTDA',
      documento: '12.345.678/0001-90',
      telefone: '(11) 88888-8888',
      cidade: 'São Paulo',
      estado: 'SP',
      categoria: 'ELETRICISTA',
      rating: 4.2
    }
  ],
  total_encontrados: 2,
  filtros_aplicados: ['categoria: ELETRICISTA', 'cidade: São Paulo'],
  sugestoes: [],
  stats: {
    total_pj: 127,
    total_pf: 89,
    categorias_populares: ['MATERIAL_CONSTRUCAO', 'ELETRICISTA', 'PEDREIRO']
  }
};

describe('FornecedoresChat', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useAuth as unknown as ReturnType<typeof vi.fn>).mockReturnValue({ user: mockUser });
  });

  it('deve renderizar o chat corretamente', () => {
    render(<FornecedoresChat />);
    
    expect(screen.getByText('Chat de Fornecedores')).toBeInTheDocument();
    expect(screen.getByText('Encontre e gerencie fornecedores com IA')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...')).toBeInTheDocument();
  });

  it('deve mostrar mensagem inicial', () => {
    render(<FornecedoresChat />);
    
    expect(screen.getByText(/Olá! Sou seu assistente para fornecedores/)).toBeInTheDocument();
  });

  it('deve mostrar sugestões rápidas', () => {
    render(<FornecedoresChat />);
    
    expect(screen.getByText('Preciso de um eletricista em São Paulo')).toBeInTheDocument();
    expect(screen.getByText('Qual o melhor fornecedor de cimento?')).toBeInTheDocument();
    expect(screen.getByText('Como cadastrar um novo fornecedor?')).toBeInTheDocument();
  });

  it('deve enviar mensagem quando usuário digita e pressiona Enter', async () => {
    (supabase.functions.invoke as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({
      data: mockChatResponse,
      error: null
    });

    render(<FornecedoresChat />);
    
    const input = screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...');
    const sendButton = screen.getByRole('button', { name: /send/i });

    fireEvent.change(input, { target: { value: 'Preciso de um eletricista' } });
    fireEvent.click(sendButton);

    await waitFor(() => {
      expect(supabase.functions.invoke).toHaveBeenCalledWith('fornecedores-chat', {
        body: {
          user_id: mockUser.id,
          message: 'Preciso de um eletricista',
          context: 'geral'
        }
      });
    });
  });

  it('deve enviar mensagem quando usuário pressiona Enter', async () => {
    (supabase.functions.invoke as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({
      data: mockChatResponse,
      error: null
    });

    render(<FornecedoresChat />);
    
    const input = screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...');

    fireEvent.change(input, { target: { value: 'Buscar eletricista' } });
    fireEvent.keyPress(input, { key: 'Enter', code: 'Enter' });

    await waitFor(() => {
      expect(supabase.functions.invoke).toHaveBeenCalled();
    });
  });

  it('deve mostrar loading durante processamento', async () => {
    (supabase.functions.invoke as unknown as ReturnType<typeof vi.fn>).mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({ data: mockChatResponse }), 100))
    );

    render(<FornecedoresChat />);
    
    const input = screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...');
    fireEvent.change(input, { target: { value: 'Teste' } });
    fireEvent.keyPress(input, { key: 'Enter' });

    expect(screen.getByText('Processando...')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.queryByText('Processando...')).not.toBeInTheDocument();
    });
  });

  it('deve exibir resposta da IA', async () => {
    (supabase.functions.invoke as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({
      data: mockChatResponse,
      error: null
    });

    render(<FornecedoresChat />);
    
    const input = screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...');
    fireEvent.change(input, { target: { value: 'Buscar eletricista' } });
    fireEvent.keyPress(input, { key: 'Enter' });

    await waitFor(() => {
      expect(screen.getByText(mockChatResponse.response)).toBeInTheDocument();
    });
  });

  it('deve exibir fornecedores encontrados', async () => {
    (supabase.functions.invoke as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({
      data: mockChatResponse,
      error: null
    });

    render(<FornecedoresChat />);
    
    const input = screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...');
    fireEvent.change(input, { target: { value: 'Buscar eletricista' } });
    fireEvent.keyPress(input, { key: 'Enter' });

    await waitFor(() => {
      expect(screen.getByText('João Silva')).toBeInTheDocument();
      expect(screen.getByText('Elétrica Santos LTDA')).toBeInTheDocument();
      expect(screen.getByText('(11) 99999-9999')).toBeInTheDocument();
      expect(screen.getByText('⭐ 4.5/5')).toBeInTheDocument();
    });
  });

  it('deve mostrar filtros aplicados', async () => {
    (supabase.functions.invoke as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({
      data: mockChatResponse,
      error: null
    });

    render(<FornecedoresChat />);
    
    const input = screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...');
    fireEvent.change(input, { target: { value: 'Buscar eletricista' } });
    fireEvent.keyPress(input, { key: 'Enter' });

    await waitFor(() => {
      expect(screen.getByText(/Filtros:/)).toBeInTheDocument();
      expect(screen.getByText(/categoria: ELETRICISTA, cidade: São Paulo/)).toBeInTheDocument();
    });
  });

  it('deve tratar erro na API', async () => {
    (supabase.functions.invoke as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({
      data: null,
      error: new Error('API Error')
    });

    render(<FornecedoresChat />);
    
    const input = screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...');
    fireEvent.change(input, { target: { value: 'Teste erro' } });
    fireEvent.keyPress(input, { key: 'Enter' });

    await waitFor(() => {
      expect(screen.getByText(/Desculpe, ocorreu um erro/)).toBeInTheDocument();
    });
  });

  it('deve preencher input ao clicar em sugestão', () => {
    render(<FornecedoresChat />);
    
    const suggestion = screen.getByText('Preciso de um eletricista em São Paulo');
    fireEvent.click(suggestion);

    const input = screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...');
    expect(input).toHaveValue('Preciso de um eletricista em São Paulo');
  });

  it('deve desabilitar input durante loading', async () => {
    (supabase.functions.invoke as unknown as ReturnType<typeof vi.fn>).mockImplementation(() => 
      new Promise(resolve => setTimeout(() => resolve({ data: mockChatResponse }), 100))
    );

    render(<FornecedoresChat />);
    
    const input = screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...');
    const sendButton = screen.getByRole('button', { name: /send/i });

    fireEvent.change(input, { target: { value: 'Teste' } });
    fireEvent.click(sendButton);

    expect(input).toBeDisabled();
    expect(sendButton).toBeDisabled();

    await waitFor(() => {
      expect(input).not.toBeDisabled();
      expect(sendButton).not.toBeDisabled();
    });
  });

  it('deve não enviar mensagem vazia', () => {
    render(<FornecedoresChat />);
    
    const sendButton = screen.getByRole('button', { name: /send/i });
    fireEvent.click(sendButton);

    expect(supabase.functions.invoke).not.toHaveBeenCalled();
  });

  it('deve não enviar mensagem sem usuário autenticado', () => {
    (useAuth as unknown as ReturnType<typeof vi.fn>).mockReturnValue({ user: null });

    render(<FornecedoresChat />);
    
    const input = screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...');
    fireEvent.change(input, { target: { value: 'Teste' } });
    fireEvent.keyPress(input, { key: 'Enter' });

    expect(supabase.functions.invoke).not.toHaveBeenCalled();
  });

  it('deve mostrar diferentes tipos de fornecedores com ícones corretos', async () => {
    (supabase.functions.invoke as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({
      data: mockChatResponse,
      error: null
    });

    render(<FornecedoresChat />);
    
    const input = screen.getByPlaceholderText('Digite sua pergunta sobre fornecedores...');
    fireEvent.change(input, { target: { value: 'Buscar fornecedores' } });
    fireEvent.keyPress(input, { key: 'Enter' });

    await waitFor(() => {
      // Verifica se os ícones de PJ e PF estão presentes
      const pjBadges = screen.getAllByText('PJ');
      const pfBadges = screen.getAllByText('PF');
      
      expect(pjBadges.length).toBeGreaterThan(0);
      expect(pfBadges.length).toBeGreaterThan(0);
    });
  });
});
