import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { createM<PERSON>oryRouter, RouterProvider } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { AuthContext } from '@/contexts/auth/AuthContext';
import { LoadingContext } from '@/contexts/LoadingContext';
import CondominioDetalhe from '@/pages/dashboard/obras/CondominioDetalhe';
import NovoCondominio from '@/pages/dashboard/obras/NovoCondominio';
// Páginas para teste
import ObrasLista from '@/pages/dashboard/obras/ObrasLista';

// Mock do Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    rpc: vi.fn(),
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          is: vi.fn(() => ({
            order: vi.fn(() => ({
              range: vi.fn(() => Promise.resolve({ data: [], error: null })),
            })),
          })),
        })),
      })),
    })),
    auth: {
      getSession: vi.fn(() => Promise.resolve({ data: { session: null }, error: null })),
    },
  },
}));

// Mock dos contextos
const mockAuthContext = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
  },
  profile: {
    id: 'test-profile-id',
    tenant_id: 'test-tenant-id',
    nome: 'Test User',
  },
  loading: false,
  signOut: vi.fn(),
};

const mockLoadingContext = {
  isLoading: false,
  setLoading: vi.fn(),
  loadingMessage: '',
  setLoadingMessage: vi.fn(),
};

describe('E2E - Fluxo Completo de Condomínios', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
          staleTime: 0,
        },
      },
    });
    vi.clearAllMocks();
  });

  const renderWithRouter = (initialEntries: string[] = ['/obras']) => {
    const router = createMemoryRouter(
      [
        {
          path: '/obras',
          element: <ObrasLista />,
        },
        {
          path: '/obras/novo-condominio',
          element: <NovoCondominio />,
        },
        {
          path: '/obras/:id',
          element: <CondominioDetalhe />,
        },
      ],
      {
        initialEntries,
      }
    );

    return render(
      <QueryClientProvider client={queryClient}>
        <AuthContext.Provider value={mockAuthContext}>
          <LoadingContext.Provider value={mockLoadingContext}>
            <RouterProvider router={router} />
          </LoadingContext.Provider>
        </AuthContext.Provider>
      </QueryClientProvider>
    );
  };

  describe('Jornada Completa do Usuário', () => {
    it('deve permitir o fluxo completo: listar → criar condomínio → visualizar dashboard', async () => {
      const user = userEvent.setup();

      // Mock da listagem inicial (vazia)
      const { supabase } = await import('@/integrations/supabase/client');
      const mockFromChain = {
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            is: vi.fn().mockReturnValue({
              order: vi.fn().mockReturnValue({
                range: vi.fn().mockResolvedValue({
                  data: [],
                  error: null,
                  count: 0,
                }),
              }),
            }),
          }),
        }),
      };
      vi.mocked(supabase.from).mockReturnValue(mockFromChain as never);

      // 1. Renderizar lista de obras
      renderWithRouter(['/obras']);

      // Verificar que a lista está vazia
      await waitFor(() => {
        expect(screen.getByText(/nenhuma obra encontrada/i)).toBeInTheDocument();
      });

      // 2. Navegar para criação de condomínio
      const novoCondominioButton = screen.getByRole('button', { name: /novo condomínio/i });
      await user.click(novoCondominioButton);

      // 3. Preencher formulário de condomínio
      await waitFor(() => {
        expect(screen.getByRole('textbox', { name: /nome/i })).toBeInTheDocument();
      });

      await user.type(screen.getByRole('textbox', { name: /nome/i }), 'Residencial Teste E2E');

      // Adicionar unidades
      const addUnitButton = screen.getByRole('button', { name: /adicionar unidade/i });
      await user.click(addUnitButton);
      await user.click(addUnitButton);

      // Mock da criação bem-sucedida
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: {
          condominio_master_id: 'condominio-e2e-id',
          unidades_criadas: [
            { id: 'unidade-1', identificador_unidade: 'A101' },
            { id: 'unidade-2', identificador_unidade: 'A102' },
          ],
        },
        error: null,
      });

      // 4. Submeter formulário
      const submitButton = screen.getByRole('button', { name: /criar condomínio/i });
      await user.click(submitButton);

      // Verificar que a criação foi bem-sucedida
      await waitFor(() => {
        expect(supabase.rpc).toHaveBeenCalledWith(
          'create_condominio_project',
          expect.any(Object)
        );
      });

      // 5. Verificar redirecionamento para dashboard do condomínio
      // (Simularia a navegação que aconteceria após criação bem-sucedida)
    });

    it('deve filtrar corretamente a listagem principal (sem mostrar unidades)', async () => {
      const { supabase } = await import('@/integrations/supabase/client');

      // Mock da listagem com obras variadas
      const mockObras = [
        {
          id: 'obra-unica-1',
          nome: 'Casa Individual',
          tipo_projeto: 'UNICO',
          parent_obra_id: null,
        },
        {
          id: 'condominio-1',
          nome: 'Condomínio Central',
          tipo_projeto: 'CONDOMINIO_MASTER',
          parent_obra_id: null,
        },
        // Unidades não devem aparecer na listagem principal
        {
          id: 'unidade-1',
          nome: 'Unidade A101',
          tipo_projeto: 'UNIDADE_CONDOMINIO',
          parent_obra_id: 'condominio-1',
        },
      ];

      const mockFromChain2 = {
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            is: vi.fn().mockReturnValue({
              order: vi.fn().mockReturnValue({
                range: vi.fn().mockResolvedValue({
                  data: mockObras.filter(obra => obra.parent_obra_id === null),
                  error: null,
                  count: 2,
                }),
              }),
            }),
          }),
        }),
      };
      vi.mocked(supabase.from).mockReturnValue(mockFromChain2 as never);

      renderWithRouter(['/obras']);

      // Verificar que apenas obras principais aparecem
      await waitFor(() => {
        expect(screen.getByText('Casa Individual')).toBeInTheDocument();
        expect(screen.getByText('Condomínio Central')).toBeInTheDocument();
        expect(screen.queryByText('Unidade A101')).not.toBeInTheDocument();
      });

      // Verificar que a query foi feita com filtro correto
      expect(supabase.from).toHaveBeenCalledWith('obras');
    });

    it('deve navegar corretamente entre condomínio e unidades', async () => {
      const user = userEvent.setup();

      // Mock dos dados do condomínio com unidades
      const mockCondominioData = {
        data: {
          obra_mae: {
            id: 'condominio-nav-test',
            nome: 'Condomínio Navegação',
            tipo_projeto: 'CONDOMINIO_MASTER',
          },
          unidades: [
            {
              id: 'unidade-nav-1',
              identificador_unidade: 'B201',
              nome: 'Unidade B201',
              tipo_projeto: 'UNIDADE_CONDOMINIO',
              parent_obra_id: 'condominio-nav-test',
            },
            {
              id: 'unidade-nav-2',
              identificador_unidade: 'B202',
              nome: 'Unidade B202',
              tipo_projeto: 'UNIDADE_CONDOMINIO',
              parent_obra_id: 'condominio-nav-test',
            },
          ],
          estatisticas: {
            total_unidades: 2,
            progresso_medio: 75,
            custo_total: 800000,
            unidades_concluidas: 1,
          },
        },
        error: null,
      };

      const { supabase } = await import('@/integrations/supabase/client');
      vi.mocked(supabase.rpc).mockResolvedValue(mockCondominioData);

      // Renderizar dashboard do condomínio
      renderWithRouter(['/obras/condominio-nav-test']);

      // Verificar que o dashboard é carregado
      await waitFor(() => {
        expect(screen.getByText('Condomínio Navegação')).toBeInTheDocument();
        expect(screen.getByText('B201')).toBeInTheDocument();
        expect(screen.getByText('B202')).toBeInTheDocument();
        expect(screen.getByText('2')).toBeInTheDocument(); // total de unidades
      });

      // Simular clique em uma unidade
      const unidadeButton = screen.getByRole('button', { name: /b201/i });
      await user.click(unidadeButton);

      // Verificar que alguma ação de navegação foi acionada
      // (Na implementação real, isso redirecionaria para a página da unidade)
    });
  });

  describe('Cenários de Erro e Edge Cases', () => {
    it('deve lidar graciosamente com falhas na criação', async () => {
      const user = userEvent.setup();

      renderWithRouter(['/obras/novo-condominio']);

      // Preencher formulário básico
      await waitFor(() => {
        expect(screen.getByRole('textbox', { name: /nome/i })).toBeInTheDocument();
      });

      await user.type(screen.getByRole('textbox', { name: /nome/i }), 'Condomínio com Erro');

      // Adicionar uma unidade
      const addUnitButton = screen.getByRole('button', { name: /adicionar unidade/i });
      await user.click(addUnitButton);

      // Mock de erro na criação
      const { supabase } = await import('@/integrations/supabase/client');
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: null,
        error: {
          message: 'Erro de validação: dados inválidos',
          code: 'validation_error',
        },
      });

      // Tentar submeter
      const submitButton = screen.getByRole('button', { name: /criar condomínio/i });
      await user.click(submitButton);

      // Verificar que o erro é exibido adequadamente
      await waitFor(() => {
        expect(screen.getByText(/erro de validação/i)).toBeInTheDocument();
      });

      // Verificar que o formulário ainda está disponível para correção
      expect(screen.getByRole('textbox', { name: /nome/i })).toBeInTheDocument();
    });

    it('deve carregar estado de loading apropriado', async () => {
      const { supabase } = await import('@/integrations/supabase/client');

      // Mock que simula loading prolongado
      vi.mocked(supabase.rpc).mockImplementation(() => 
        new Promise(resolve => {
          // Simula demora na resposta
          setTimeout(() => {
            resolve({
              data: {
                obra_mae: { id: 'test', nome: 'Teste Loading' },
                unidades: [],
                estatisticas: { total_unidades: 0, progresso_medio: 0, custo_total: 0, unidades_concluidas: 0 },
              },
              error: null,
            });
          }, 500);
        })
      );

      renderWithRouter(['/obras/loading-test']);

      // Verificar que o loading é exibido
      expect(screen.getByText(/carregando/i)).toBeInTheDocument();

      // Aguardar carregamento completo
      await waitFor(() => {
        expect(screen.getByText('Teste Loading')).toBeInTheDocument();
      }, { timeout: 1000 });
    });

    it('deve validar dados antes da submissão', async () => {
      const user = userEvent.setup();

      renderWithRouter(['/obras/novo-condominio']);

      // Tentar submeter formulário vazio
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /criar condomínio/i })).toBeInTheDocument();
      });

      const submitButton = screen.getByRole('button', { name: /criar condomínio/i });
      await user.click(submitButton);

      // Verificar que erros de validação aparecem
      await waitFor(() => {
        expect(screen.getByText(/nome é obrigatório/i)).toBeInTheDocument();
      });
    });
  });

  describe('Performance e Otimizações', () => {
    it('deve carregar lista de unidades com paginação para condomínios grandes', async () => {
      // Mock de condomínio com muitas unidades
      const muitasUnidades = Array.from({ length: 50 }, (_, i) => ({
        id: `unidade-${i + 1}`,
        identificador_unidade: `U${String(i + 1).padStart(3, '0')}`,
        nome: `Unidade ${String(i + 1).padStart(3, '0')}`,
        tipo_projeto: 'UNIDADE_CONDOMINIO',
        parent_obra_id: 'condominio-grande',
      }));

      const mockCondominioGrande = {
        data: {
          obra_mae: {
            id: 'condominio-grande',
            nome: 'Condomínio Grande',
            tipo_projeto: 'CONDOMINIO_MASTER',
          },
          unidades: muitasUnidades.slice(0, 20), // Primeira página
          estatisticas: {
            total_unidades: 50,
            progresso_medio: 30,
            custo_total: 5000000,
            unidades_concluidas: 15,
          },
        },
        error: null,
      };

      const { supabase } = await import('@/integrations/supabase/client');
      vi.mocked(supabase.rpc).mockResolvedValue(mockCondominioGrande);

      renderWithRouter(['/obras/condominio-grande']);

      // Verificar que apenas as primeiras unidades são carregadas
      await waitFor(() => {
        expect(screen.getByText('Unidade 001')).toBeInTheDocument();
        expect(screen.getByText('Unidade 020')).toBeInTheDocument();
        expect(screen.queryByText('Unidade 021')).not.toBeInTheDocument();
      });

      // Verificar que informações de paginação estão presentes
      expect(screen.getByText('50')).toBeInTheDocument(); // total de unidades
    });
  });
});