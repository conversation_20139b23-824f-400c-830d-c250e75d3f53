/**
 * 🧪 Testes para useCrudOperations Hook
 * 
 * Testa o hook genérico de operações CRUD que é usado
 * por todos os módulos do sistema (obras, fornecedores, etc.)
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import type { ReactNode } from 'react';
import { beforeEach,describe, expect, it, vi } from 'vitest';

import type { CrudApi} from '@/hooks/useCrudOperations';
import {useCrudOperations } from '@/hooks/useCrudOperations';

// Mock do useTenantValidation
vi.mock('@/hooks/useTenantValidation', () => ({
  useTenantValidation: () => ({
    validTenantId: 'tenant-123'
  })
}));

// Mock do toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

// Tipo de teste para entidade
interface TestEntity {
  id: string;
  name: string;
  tenant_id: string;
}

interface TestCreateData {
  name: string;
}

interface TestUpdateData {
  name?: string;
}

// Mock da API
const mockApi: CrudApi<TestEntity, TestCreateData, TestUpdateData> = {
  getAll: vi.fn(),
  getById: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  delete: vi.fn()
};

// Wrapper para React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useCrudOperations', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Listagem de dados', () => {
    it('deve carregar lista de entidades com sucesso', async () => {
      const mockData: TestEntity[] = [
        { id: '1', name: 'Teste 1', tenant_id: 'tenant-123' },
        { id: '2', name: 'Teste 2', tenant_id: 'tenant-123' }
      ];

      mockApi.getAll = vi.fn().mockResolvedValue(mockData);

      const { result } = renderHook(
        () => useCrudOperations(mockApi, { resource: 'test' }),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.data).toEqual(mockData);
        expect(result.current.isLoading).toBe(false);
        expect(result.current.error).toBeNull();
      });

      expect(mockApi.getAll).toHaveBeenCalledWith('tenant-123');
    });

    it('deve lidar com erro na listagem', async () => {
      const mockError = new Error('Erro de rede');
      mockApi.getAll = vi.fn().mockRejectedValue(mockError);

      const { result } = renderHook(
        () => useCrudOperations(mockApi, { resource: 'test' }),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
        expect(result.current.error).toBeTruthy();
      });
    });
  });

  describe('Criação de entidades', () => {
    it('deve criar entidade com sucesso', async () => {
      const newEntity: TestEntity = { 
        id: '3', 
        name: 'Nova Entidade', 
        tenant_id: 'tenant-123' 
      };
      
      mockApi.create = vi.fn().mockResolvedValue(newEntity);

      const { result } = renderHook(
        () => useCrudOperations(mockApi, { 
          resource: 'test',
          messages: { createSuccess: 'Criado com sucesso!' }
        }),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.createMutation).toBeDefined();
      });

      result.current.createMutation.mutate({ name: 'Nova Entidade' });

      await waitFor(() => {
        expect(result.current.createMutation.isSuccess).toBe(true);
      });

      expect(mockApi.create).toHaveBeenCalledWith(
        { name: 'Nova Entidade' },
        'tenant-123'
      );
    });

    it('deve lidar com erro na criação', async () => {
      const mockError = new Error('Erro ao criar');
      mockApi.create = vi.fn().mockRejectedValue(mockError);

      const { result } = renderHook(
        () => useCrudOperations(mockApi, { resource: 'test' }),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.createMutation).toBeDefined();
      });

      result.current.createMutation.mutate({ name: 'Teste' });

      await waitFor(() => {
        expect(result.current.createMutation.isError).toBe(true);
      });
    });
  });

  describe('Atualização de entidades', () => {
    it('deve atualizar entidade com sucesso', async () => {
      const updatedEntity: TestEntity = { 
        id: '1', 
        name: 'Nome Atualizado', 
        tenant_id: 'tenant-123' 
      };
      
      mockApi.update = vi.fn().mockResolvedValue(updatedEntity);

      const { result } = renderHook(
        () => useCrudOperations(mockApi, { resource: 'test' }),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.updateMutation).toBeDefined();
      });

      result.current.updateMutation.mutate({
        id: '1',
        data: { name: 'Nome Atualizado' }
      });

      await waitFor(() => {
        expect(result.current.updateMutation.isSuccess).toBe(true);
      });

      expect(mockApi.update).toHaveBeenCalledWith(
        '1',
        { name: 'Nome Atualizado' },
        'tenant-123'
      );
    });
  });

  describe('Exclusão de entidades', () => {
    it('deve excluir entidade com sucesso', async () => {
      mockApi.delete = vi.fn().mockResolvedValue(undefined);

      const { result } = renderHook(
        () => useCrudOperations(mockApi, { resource: 'test' }),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.deleteMutation).toBeDefined();
      });

      result.current.deleteMutation.mutate('1');

      await waitFor(() => {
        expect(result.current.deleteMutation.isSuccess).toBe(true);
      });

      expect(mockApi.delete).toHaveBeenCalledWith('1', 'tenant-123');
    });
  });

  describe('Validação de tenant', () => {
    it('deve falhar quando tenant não está disponível', async () => {
      // Mock temporário para simular tenant inválido
      vi.mocked(vi.importActual('@/hooks/useTenantValidation')).useTenantValidation = () => ({
        validTenantId: null
      });

      const { result } = renderHook(
        () => useCrudOperations(mockApi, { resource: 'test' }),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.createMutation).toBeDefined();
      });

      result.current.createMutation.mutate({ name: 'Teste' });

      await waitFor(() => {
        expect(result.current.createMutation.isError).toBe(true);
      });
    });
  });

  describe('Mensagens customizadas', () => {
    it('deve usar mensagens customizadas quando fornecidas', async () => {
      const customMessages = {
        createSuccess: 'Entidade criada com sucesso!',
        createError: 'Falha ao criar entidade'
      };

      const newEntity: TestEntity = { 
        id: '1', 
        name: 'Teste', 
        tenant_id: 'tenant-123' 
      };
      
      mockApi.create = vi.fn().mockResolvedValue(newEntity);

      const { result } = renderHook(
        () => useCrudOperations(mockApi, { 
          resource: 'test',
          messages: customMessages
        }),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.createMutation).toBeDefined();
      });

      result.current.createMutation.mutate({ name: 'Teste' });

      await waitFor(() => {
        expect(result.current.createMutation.isSuccess).toBe(true);
      });

      // Verificar se a mensagem customizada foi usada
      const { toast } = await import('sonner');
      expect(toast.success).toHaveBeenCalledWith(customMessages.createSuccess);
    });
  });
});
