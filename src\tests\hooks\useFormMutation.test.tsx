/**
 * 🧪 Testes para useFormMutation Hook
 * 
 * Testa o hook genérico para mutações de formulário
 * usado em toda a aplicação para operações CRUD.
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import type { ReactNode } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { useFormMutation } from '@/hooks/useFormMutation';

// Mock do useNavigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate
}));

// Mock do useAuth
vi.mock('@/contexts/auth/hooks', () => ({
  useAuth: () => ({
    user: { id: 'user-123', email: '<EMAIL>' }
  })
}));

// Mock do useTenantValidation
vi.mock('@/hooks/useTenantValidation', () => ({
  useTenantValidation: () => ({
    validTenantId: 'tenant-123'
  })
}));

// Mock do toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

// Tipos de teste
interface TestFormData {
  name: string;
  description?: string;
}



// Wrapper para React Query
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useFormMutation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Mutação básica', () => {
    it('deve executar mutação com sucesso', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({
        id: '1',
        name: 'Teste',
        tenant_id: 'tenant-123'
      });

      const { result } = renderHook(
        () => useFormMutation({
          mutationFn: mockMutationFn,
          successMessage: 'Sucesso!',
          errorMessage: 'Erro!'
        }),
        { wrapper: createWrapper() }
      );

      const formData: TestFormData = { name: 'Teste' };
      result.current.mutate(formData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockMutationFn).toHaveBeenCalledWith(formData, 'tenant-123');
    });

    it('deve lidar com erro na mutação', async () => {
      const mockError = new Error('Erro de rede');
      const mockMutationFn = vi.fn().mockRejectedValue(mockError);

      const { result } = renderHook(
        () => useFormMutation({
          mutationFn: mockMutationFn,
          errorMessage: 'Erro personalizado!'
        }),
        { wrapper: createWrapper() }
      );

      result.current.mutate({ name: 'Teste' });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      const { toast } = await import('sonner');
      expect(toast.error).toHaveBeenCalledWith('Erro personalizado!');
    });
  });

  describe('Invalidação de queries', () => {
    it('deve invalidar query única após sucesso', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ id: '1' });
      const queryClient = new QueryClient();
      const invalidateSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useFormMutation({
          mutationFn: mockMutationFn,
          queryKey: 'test-query'
        }),
        { wrapper }
      );

      result.current.mutate({ name: 'Teste' });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: ['test-query'] });
    });

    it('deve invalidar múltiplas queries após sucesso', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ id: '1' });
      const queryClient = new QueryClient();
      const invalidateSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useFormMutation({
          mutationFn: mockMutationFn,
          queryKey: ['query1', 'query2']
        }),
        { wrapper }
      );

      result.current.mutate({ name: 'Teste' });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(invalidateSpy).toHaveBeenCalledTimes(2);
      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: ['query1'] });
      expect(invalidateSpy).toHaveBeenCalledWith({ queryKey: ['query2'] });
    });
  });

  describe('Redirecionamento', () => {
    it('deve redirecionar após sucesso quando redirectTo é fornecido', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ id: '1' });

      const { result } = renderHook(
        () => useFormMutation({
          mutationFn: mockMutationFn,
          redirectTo: '/success-page'
        }),
        { wrapper: createWrapper() }
      );

      result.current.mutate({ name: 'Teste' });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockNavigate).toHaveBeenCalledWith('/success-page');
    });

    it('não deve redirecionar quando redirectTo não é fornecido', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ id: '1' });

      const { result } = renderHook(
        () => useFormMutation({
          mutationFn: mockMutationFn
        }),
        { wrapper: createWrapper() }
      );

      result.current.mutate({ name: 'Teste' });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  describe('Callbacks personalizados', () => {
    it('deve executar callback de sucesso quando fornecido', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ id: '1', name: 'Teste' });
      const mockSuccessCallback = vi.fn();

      const { result } = renderHook(
        () => useFormMutation({
          mutationFn: mockMutationFn,
          onSuccessCallback: mockSuccessCallback
        }),
        { wrapper: createWrapper() }
      );

      const formData = { name: 'Teste' };
      result.current.mutate(formData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockSuccessCallback).toHaveBeenCalledWith(
        { id: '1', name: 'Teste' },
        formData
      );
    });

    it('deve executar callback de erro quando fornecido', async () => {
      const mockError = new Error('Erro de teste');
      const mockMutationFn = vi.fn().mockRejectedValue(mockError);
      const mockErrorCallback = vi.fn();

      const { result } = renderHook(
        () => useFormMutation({
          mutationFn: mockMutationFn,
          onErrorCallback: mockErrorCallback
        }),
        { wrapper: createWrapper() }
      );

      result.current.mutate({ name: 'Teste' });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(mockErrorCallback).toHaveBeenCalledWith(mockError);
    });
  });

  describe('Validação de tenant', () => {
    it('deve usar tenant válido quando disponível', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ id: '1' });

      const { result } = renderHook(
        () => useFormMutation({
          mutationFn: mockMutationFn
        }),
        { wrapper: createWrapper() }
      );

      result.current.mutate({ name: 'Teste' });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(mockMutationFn).toHaveBeenCalledWith({ name: 'Teste' }, 'tenant-123');
    });
  });

  describe('Mensagens padrão', () => {
    it('deve usar mensagens padrão quando não fornecidas', async () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ id: '1' });

      const { result } = renderHook(
        () => useFormMutation({
          mutationFn: mockMutationFn
        }),
        { wrapper: createWrapper() }
      );

      result.current.mutate({ name: 'Teste' });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      const { toast } = await import('sonner');
      expect(toast.success).toHaveBeenCalledWith('Operação realizada com sucesso!');
    });
  });
});
