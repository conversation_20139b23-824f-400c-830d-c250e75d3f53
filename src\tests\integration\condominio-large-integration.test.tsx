import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { beforeEach,describe, expect, it, vi } from 'vitest';

import { CondominioDashboard } from '@/components/obras/CondominioDashboard';
import { ListaUnidadesVirtualized } from '@/components/obras/ListaUnidadesVirtualized';

// Mock do Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    functions: {
      invoke: vi.fn(),
    },
    rpc: vi.fn(),
  },
}));

// Mock do hook de autenticação
vi.mock('@/hooks/useTenantValidation', () => ({
  useTenantValidation: () => ({
    validTenantId: 'test-tenant-id',
  }),
}));

// Mock do secure logger
vi.mock('@/lib/secure-logger', () => ({
  secureLogger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}));

// Mock da navegação
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
  };
});

const createTestWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <MemoryRouter>
        {children}
      </MemoryRouter>
    </QueryClientProvider>
  );
};

// Dados de teste para condomínio grande
const createLargeCondominioMockData = () => {
  const numUnidades = 1000;
  
  // Estatísticas otimizadas (sem unidades individuais)
  const dashboardData = {
    estatisticas: {
      total_unidades: numUnidades,
      unidades_vendidas: 600,
      unidades_disponiveis: 300,
      unidades_reservadas: 100,
      progresso_medio: 65.5,
      valor_total_vendas: 180000000, // 180M
      orcamento_total_unidades: 250000000, // 250M
      unidades_concluidas: 400,
      unidades_no_prazo: 800,
      unidades_atrasadas: 200,
    },
    unidades: [], // Dashboard otimizado não carrega unidades
  };

  // Dados paginados para lista de unidades
  const createPaginatedUnits = (page: number, pageSize: number) => {
    const startIndex = (page - 1) * pageSize;
    const units = Array.from({ length: pageSize }, (_, index) => {
      const unitIndex = startIndex + index;
      return {
        id: `unit-${unitIndex + 1}`,
        nome: `Unidade ${unitIndex + 1}`,
        identificador_unidade: `${Math.floor(unitIndex / 10) + 1}-${(unitIndex % 10) + 1}`,
        status_venda: ['DISPONIVEL', 'VENDIDA', 'RESERVADA'][unitIndex % 3],
        area_total: 70 + (unitIndex % 50),
        orcamento: 250000 + (unitIndex * 5000),
        valor_venda: unitIndex % 3 === 1 ? 300000 + (unitIndex * 7000) : null,
        progresso: Math.min(100, (unitIndex / numUnidades) * 120),
        parent_obra_id: 'condominio-master-id',
        tipo_projeto: 'UNIDADE_CONDOMINIO',
        tenant_id: 'test-tenant-id',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    });

    return {
      data: units,
      pagination: {
        page,
        pageSize,
        total: numUnidades,
        totalPages: Math.ceil(numUnidades / pageSize),
        hasNextPage: (page * pageSize) < numUnidades,
        hasPreviousPage: page > 1,
      },
    };
  };

  return { dashboardData, createPaginatedUnits };
};

describe('Integração - Condomínios Grandes (Task 22)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Fluxo Completo - Dashboard + Lista Paginada', () => {
    it('deve carregar dashboard otimizado e permitir navegação paginada', async () => {
      const { dashboardData, createPaginatedUnits } = createLargeCondominioMockData();
      
      // Mock das funções do Supabase
      const { supabase } = await import('@/integrations/supabase/client');
      
      // Mock da função RPC otimizada para dashboard
      (supabase.rpc as any).mockResolvedValue({
        data: dashboardData,
        error: null,
      });

      // Mock da Edge Function para unidades paginadas
      (supabase.functions.invoke as any).mockResolvedValue({
        data: createPaginatedUnits(1, 50),
        error: null,
      });

      // Renderizar dashboard
      const DashboardWrapper = createTestWrapper();
      const { rerender } = render(
        <CondominioDashboard obraId="test-condominio-large" />,
        { wrapper: DashboardWrapper }
      );

      // Aguardar carregamento do dashboard
      await waitFor(() => {
        expect(screen.getByText('1000')).toBeInTheDocument(); // Total de unidades
      });

      // Verificar métricas principais
      expect(screen.getByText('Dashboard Otimizado')).toBeInTheDocument();
      expect(screen.getByText('65.5%')).toBeInTheDocument(); // Progresso médio

      // Trocar para lista de unidades
      const ListWrapper = createTestWrapper();
      rerender(
        <ListaUnidadesVirtualized condominioId="test-condominio-large" />
      );

      // Aguardar carregamento da lista paginada
      await waitFor(() => {
        expect(screen.getByText('Unidade 1')).toBeInTheDocument();
      });

      // Verificar controles de paginação
      expect(screen.getByText(/Mostrando 1 até 50 de 1000 unidades/)).toBeInTheDocument();
      expect(screen.getByText(/Página 1 de 20/)).toBeInTheDocument();
    });
  });
});