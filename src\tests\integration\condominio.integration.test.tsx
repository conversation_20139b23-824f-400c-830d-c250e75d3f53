import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { AuthContext } from '@/contexts/auth/AuthContext';
import { LoadingContext } from '@/contexts/LoadingContext';
import { supabase } from '@/integrations/supabase/client';
import CondominioDetalhe from '@/pages/dashboard/obras/CondominioDetalhe';
// Componentes para teste
import NovoCondominio from '@/pages/dashboard/obras/NovoCondominio';

// Mock do Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    rpc: vi.fn(),
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(),
        })),
      })),
    })),
    auth: {
      getSession: vi.fn(() => Promise.resolve({ data: { session: null }, error: null })),
    },
  },
}));

// Mocks de contextos
const mockAuthContext = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
  },
  profile: {
    id: 'test-profile-id',
    tenant_id: 'test-tenant-id',
    nome: 'Test User',
  },
  loading: false,
  signOut: vi.fn(),
};

const mockLoadingContext = {
  isLoading: false,
  setLoading: vi.fn(),
  loadingMessage: '',
  setLoadingMessage: vi.fn(),
};

// Provider de teste
const TestProvider = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthContext.Provider value={mockAuthContext}>
          <LoadingContext.Provider value={mockLoadingContext}>
            {children}
          </LoadingContext.Provider>
        </AuthContext.Provider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Integração Condomínio - Fluxo Completo', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    vi.clearAllMocks();
  });

  describe('Criação de Condomínio', () => {
    it('deve permitir criar um condomínio com múltiplas unidades', async () => {
      const user = userEvent.setup();

      // Mock da resposta da RPC function
      const mockRpcResponse = {
        data: {
          condominio_master_id: 'test-condominio-id',
          unidades_criadas: [
            { id: 'unidade-1', identificador_unidade: 'A101' },
            { id: 'unidade-2', identificador_unidade: 'A102' },
          ],
        },
        error: null,
      };

      vi.mocked(supabase.rpc).mockResolvedValue(mockRpcResponse);

      render(
        <TestProvider>
          <NovoCondominio />
        </TestProvider>
      );

      // Verificar se os campos básicos estão presentes
      expect(screen.getByRole('textbox', { name: /nome/i })).toBeInTheDocument();
      
      // Preencher dados básicos do condomínio
      await user.type(screen.getByRole('textbox', { name: /nome/i }), 'Condomínio Teste');
      
      // Verificar se o botão de adicionar unidade está presente
      const addUnitButton = screen.getByRole('button', { name: /adicionar unidade/i });
      expect(addUnitButton).toBeInTheDocument();

      // Adicionar primeira unidade
      await user.click(addUnitButton);
      
      // Preencher dados da primeira unidade
      const unidadeInputs = screen.getAllByRole('textbox');
      const identificadorInput = unidadeInputs.find(input => 
        input.getAttribute('placeholder')?.includes('A101') || 
        input.getAttribute('name')?.includes('identificador')
      );
      
      if (identificadorInput) {
        await user.type(identificadorInput, 'A101');
      }

      // Adicionar segunda unidade
      await user.click(addUnitButton);
      
      // Submeter o formulário
      const submitButton = screen.getByRole('button', { name: /criar condomínio/i });
      await user.click(submitButton);

      // Verificar se a RPC function foi chamada
      await waitFor(() => {
        expect(supabase.rpc).toHaveBeenCalledWith(
          'create_condominio_project',
          expect.objectContaining({
            condominio_data: expect.any(Object),
            unidades_data: expect.any(Array),
          })
        );
      });
    });

    it('deve exibir erro quando a criação falha', async () => {
      const user = userEvent.setup();

      // Mock de erro na RPC function
      vi.mocked(supabase.rpc).mockResolvedValue({
        data: null,
        error: { message: 'Erro ao criar condomínio' },
      });

      render(
        <TestProvider>
          <NovoCondominio />
        </TestProvider>
      );

      // Preencher dados básicos
      await user.type(screen.getByRole('textbox', { name: /nome/i }), 'Condomínio Erro');
      
      // Tentar submeter
      const submitButton = screen.getByRole('button', { name: /criar condomínio/i });
      await user.click(submitButton);

      // Verificar se o erro é exibido
      await waitFor(() => {
        expect(screen.getByText(/erro ao criar condomínio/i)).toBeInTheDocument();
      });
    });
  });

  describe('Navegação e Visualização', () => {
    it('deve carregar dashboard do condomínio com dados agregados', async () => {
      // Mock dos dados do dashboard
      const mockDashboardData = {
        data: {
          obra_mae: {
            id: 'test-condominio-id',
            nome: 'Condomínio Teste',
            tipo_projeto: 'CONDOMINIO_MASTER',
          },
          unidades: [
            {
              id: 'unidade-1',
              identificador_unidade: 'A101',
              nome: 'Unidade A101',
              tipo_projeto: 'UNIDADE_CONDOMINIO',
            },
            {
              id: 'unidade-2',
              identificador_unidade: 'A102',
              nome: 'Unidade A102',
              tipo_projeto: 'UNIDADE_CONDOMINIO',
            },
          ],
          estatisticas: {
            total_unidades: 2,
            progresso_medio: 50,
            custo_total: 500000,
            unidades_concluidas: 0,
          },
        },
        error: null,
      };

      vi.mocked(supabase.rpc).mockResolvedValue(mockDashboardData);

      render(
        <TestProvider>
          <CondominioDetalhe />
        </TestProvider>
      );

      // Verificar se os dados são carregados
      await waitFor(() => {
        expect(screen.getByText('Condomínio Teste')).toBeInTheDocument();
        expect(screen.getByText('2')).toBeInTheDocument(); // total de unidades
        expect(screen.getByText('A101')).toBeInTheDocument();
        expect(screen.getByText('A102')).toBeInTheDocument();
      });
    });

    it('deve permitir navegar para detalhes de uma unidade específica', async () => {
      const user = userEvent.setup();

      // Mock dos dados do dashboard
      const mockDashboardData = {
        data: {
          obra_mae: {
            id: 'test-condominio-id',
            nome: 'Condomínio Teste',
            tipo_projeto: 'CONDOMINIO_MASTER',
          },
          unidades: [
            {
              id: 'unidade-1',
              identificador_unidade: 'A101',
              nome: 'Unidade A101',
              tipo_projeto: 'UNIDADE_CONDOMINIO',
            },
          ],
          estatisticas: {
            total_unidades: 1,
            progresso_medio: 25,
            custo_total: 250000,
            unidades_concluidas: 0,
          },
        },
        error: null,
      };

      vi.mocked(supabase.rpc).mockResolvedValue(mockDashboardData);

      render(
        <TestProvider>
          <CondominioDetalhe />
        </TestProvider>
      );

      // Aguardar carregamento dos dados
      await waitFor(() => {
        expect(screen.getByText('A101')).toBeInTheDocument();
      });

      // Simular clique na unidade
      const unidadeButton = screen.getByRole('button', { name: /a101/i });
      await user.click(unidadeButton);

      // Verificar se a navegação foi acionada (através da mudança de URL ou callback)
      // Isso dependeria da implementação específica do componente
    });
  });

  describe('Validações e Integridade', () => {
    it('deve validar que identificadores de unidades são únicos', async () => {
      const user = userEvent.setup();

      render(
        <TestProvider>
          <NovoCondominio />
        </TestProvider>
      );

      // Adicionar duas unidades com o mesmo identificador
      const addUnitButton = screen.getByRole('button', { name: /adicionar unidade/i });
      
      await user.click(addUnitButton);
      await user.click(addUnitButton);

      // Tentar colocar identificadores iguais
      const identificadorInputs = screen.getAllByRole('textbox');
      const relevantInputs = identificadorInputs.filter(input => 
        input.getAttribute('placeholder')?.includes('A') || 
        input.getAttribute('name')?.includes('identificador')
      );

      if (relevantInputs.length >= 2) {
        await user.type(relevantInputs[0], 'A101');
        await user.type(relevantInputs[1], 'A101');

        // Verificar se aparece erro de duplicação
        await waitFor(() => {
          expect(screen.getByText(/identificadores devem ser únicos/i)).toBeInTheDocument();
        });
      }
    });

    it('deve exigir pelo menos uma unidade para criar condomínio', async () => {
      const user = userEvent.setup();

      render(
        <TestProvider>
          <NovoCondominio />
        </TestProvider>
      );

      // Tentar submeter sem adicionar unidades
      await user.type(screen.getByRole('textbox', { name: /nome/i }), 'Condomínio Sem Unidades');
      
      const submitButton = screen.getByRole('button', { name: /criar condomínio/i });
      await user.click(submitButton);

      // Verificar se aparece erro
      await waitFor(() => {
        expect(screen.getByText(/pelo menos uma unidade é necessária/i)).toBeInTheDocument();
      });
    });
  });

  describe('Performance e UX', () => {
    it('deve exibir indicadores de loading durante criação', async () => {
      const user = userEvent.setup();

      // Mock que simula delay na resposta
      vi.mocked(supabase.rpc).mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => 
            resolve({
              data: { condominio_master_id: 'test-id', unidades_criadas: [] },
              error: null,
            }), 
            1000
          )
        )
      );

      render(
        <TestProvider>
          <NovoCondominio />
        </TestProvider>
      );

      // Preencher e submeter
      await user.type(screen.getByRole('textbox', { name: /nome/i }), 'Condomínio Loading');
      
      const addUnitButton = screen.getByRole('button', { name: /adicionar unidade/i });
      await user.click(addUnitButton);
      
      const submitButton = screen.getByRole('button', { name: /criar condomínio/i });
      await user.click(submitButton);

      // Verificar se o loading aparece
      expect(screen.getByText(/criando condomínio/i)).toBeInTheDocument();

      // Aguardar conclusão
      await waitFor(() => {
        expect(screen.queryByText(/criando condomínio/i)).not.toBeInTheDocument();
      }, { timeout: 2000 });
    });
  });
});