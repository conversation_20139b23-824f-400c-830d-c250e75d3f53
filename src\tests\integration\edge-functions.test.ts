/**
 * 🧪 Testes de Integração para Edge Functions
 * 
 * Testa as principais Edge Functions do sistema
 * usando MSW para interceptar chamadas HTTP.
 */

import { http, HttpResponse } from 'msw';
import { beforeEach,describe, expect, it, vi } from 'vitest';

import { server } from '@/mocks/server';

// URL base das Edge Functions
const EDGE_FUNCTIONS_URL = 'https://anrphijuostbgbscxmzx.supabase.co/functions/v1';

// Mock do token de autenticação
const mockAuthToken = 'mock-jwt-token';

// Helper para fazer chamadas para Edge Functions
async function callEdgeFunction(
  functionName: string, 
  payload: Record<string, unknown>, 
  options: { method?: string; headers?: Record<string, string> } = {}
) {
  const { method = 'POST', headers = {} } = options;
  
  const response = await fetch(`${EDGE_FUNCTIONS_URL}/${functionName}`, {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${mockAuthToken}`,
      ...headers
    },
    body: method !== 'GET' ? JSON.stringify(payload) : undefined
  });

  return {
    status: response.status,
    data: await response.json().catch(() => null),
    headers: response.headers
  };
}

describe('Edge Functions Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('lead-capture', () => {
    it('deve capturar lead com sucesso', async () => {
      // Mock da resposta da Edge Function
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/lead-capture`, () => {
          return HttpResponse.json({
            success: true,
            data: {
              id: 'lead-123',
              email: '<EMAIL>',
              nome: 'João Silva',
              prioridade: 'alta'
            }
          }, { status: 200 });
        })
      );

      const leadData = {
        email: '<EMAIL>',
        nome: 'João Silva',
        telefone: '(11) 98765-4321',
        empresa: 'Construtora ABC',
        interesse_nivel: 'alto'
      };

      const response = await callEdgeFunction('lead-capture', leadData);

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.email).toBe('<EMAIL>');
    });

    it('deve rejeitar lead com email inválido', async () => {
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/lead-capture`, () => {
          return HttpResponse.json({
            success: false,
            error: {
              message: 'Email inválido',
              code: 'VALIDATION_ERROR'
            }
          }, { status: 400 });
        })
      );

      const leadInvalido = {
        email: 'email-invalido',
        nome: 'João Silva'
      };

      const response = await callEdgeFunction('lead-capture', leadInvalido);

      expect(response.status).toBe(400);
      expect(response.data.success).toBe(false);
      expect(response.data.error.message).toBe('Email inválido');
    });

    it('deve lidar com rate limiting', async () => {
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/lead-capture`, () => {
          return HttpResponse.json({
            success: false,
            error: {
              message: 'Rate limit exceeded',
              code: 'RATE_LIMIT_EXCEEDED'
            }
          }, { status: 429 });
        })
      );

      const leadData = {
        email: '<EMAIL>',
        nome: 'João Silva'
      };

      const response = await callEdgeFunction('lead-capture', leadData);

      expect(response.status).toBe(429);
      expect(response.data.error.code).toBe('RATE_LIMIT_EXCEEDED');
    });
  });

  describe('ai-chat', () => {
    it('deve processar chat com IA com sucesso', async () => {
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/ai-chat`, () => {
          return HttpResponse.json({
            success: true,
            data: {
              id: 'chat-123',
              mensagem: 'Como posso ajudar com sua obra?',
              resposta_bot: 'Posso ajudar você com orçamentos, fornecedores e muito mais!',
              tokens_usados: 150,
              custo_api: 0.003
            }
          }, { status: 200 });
        })
      );

      const chatData = {
        mensagem: 'Como posso ajudar com sua obra?',
        obra_id: 'obra-123',
        contexto: 'obra_detalhes'
      };

      const response = await callEdgeFunction('ai-chat', chatData);

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.resposta_bot).toContain('orçamentos');
    });

    it('deve lidar com erro de quota de IA', async () => {
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/ai-chat`, () => {
          return HttpResponse.json({
            success: false,
            error: {
              message: 'Quota de IA excedida',
              code: 'AI_QUOTA_EXCEEDED'
            }
          }, { status: 402 });
        })
      );

      const chatData = {
        mensagem: 'Teste',
        obra_id: 'obra-123'
      };

      const response = await callEdgeFunction('ai-chat', chatData);

      expect(response.status).toBe(402);
      expect(response.data.error.code).toBe('AI_QUOTA_EXCEEDED');
    });
  });

  describe('ai-calculate-budget', () => {
    it('deve calcular orçamento com IA com sucesso', async () => {
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/ai-calculate-budget`, () => {
          return HttpResponse.json({
            success: true,
            data: {
              id: 'orcamento-123',
              valor_total: 150000,
              itens: [
                {
                  categoria: 'FUNDACAO',
                  valor: 25000,
                  descricao: 'Fundação em concreto armado'
                },
                {
                  categoria: 'ESTRUTURA',
                  valor: 45000,
                  descricao: 'Estrutura em concreto armado'
                }
              ],
              fonte_dados: 'SINAPI',
              data_calculo: '2024-01-15T10:30:00Z'
            }
          }, { status: 200 });
        })
      );

      const orcamentoData = {
        tipo_obra: 'RESIDENCIAL',
        padrao_obra: 'MEDIO',
        area_construcao: 120,
        estado: 'SP',
        cidade: 'São Paulo'
      };

      const response = await callEdgeFunction('ai-calculate-budget', orcamentoData);

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.valor_total).toBe(150000);
      expect(response.data.data.itens).toHaveLength(2);
    });

    it('deve rejeitar parâmetros inválidos', async () => {
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/ai-calculate-budget`, () => {
          return HttpResponse.json({
            success: false,
            error: {
              message: 'Área de construção deve ser positiva',
              code: 'VALIDATION_ERROR'
            }
          }, { status: 400 });
        })
      );

      const orcamentoInvalido = {
        tipo_obra: 'RESIDENCIAL',
        area_construcao: -50 // Área negativa
      };

      const response = await callEdgeFunction('ai-calculate-budget', orcamentoInvalido);

      expect(response.status).toBe(400);
      expect(response.data.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('sinapi-semantic-search', () => {
    it('deve realizar busca semântica no SINAPI', async () => {
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/sinapi-semantic-search`, () => {
          return HttpResponse.json({
            success: true,
            data: {
              resultados: [
                {
                  codigo_sinapi: '12345',
                  descricao: 'Concreto usinado fck=25MPa',
                  unidade: 'm³',
                  preco_unitario: 350.50,
                  similaridade: 0.95
                },
                {
                  codigo_sinapi: '12346',
                  descricao: 'Concreto usinado fck=30MPa',
                  unidade: 'm³',
                  preco_unitario: 380.75,
                  similaridade: 0.92
                }
              ],
              total_encontrados: 2,
              tempo_busca: 0.15
            }
          }, { status: 200 });
        })
      );

      const buscaData = {
        query: 'concreto usinado',
        limite: 10,
        incluir_similares: true
      };

      const response = await callEdgeFunction('sinapi-semantic-search', buscaData);

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.resultados).toHaveLength(2);
      expect(response.data.data.resultados[0].similaridade).toBeGreaterThan(0.9);
    });
  });

  describe('analise-planta-ia', () => {
    it('deve analisar planta com IA', async () => {
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/analise-planta-ia`, () => {
          return HttpResponse.json({
            success: true,
            data: {
              id: 'analise-123',
              area_total: 120.5,
              comodos: [
                { nome: 'Sala', area: 25.0 },
                { nome: 'Cozinha', area: 12.5 },
                { nome: 'Quarto 1', area: 15.0 }
              ],
              materiais_estimados: {
                concreto: '15 m³',
                ceramica: '80 m²',
                tinta: '25 litros'
              },
              orcamento_estimado: 145000,
              confianca_analise: 0.88
            }
          }, { status: 200 });
        })
      );

      const plantaData = {
        arquivo_url: 'https://storage.supabase.co/planta.pdf',
        tipo_analise: 'completa',
        incluir_orcamento: true
      };

      const response = await callEdgeFunction('analise-planta-ia', plantaData);

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.area_total).toBe(120.5);
      expect(response.data.data.comodos).toHaveLength(3);
    });
  });

  describe('Autenticação e Autorização', () => {
    it('deve rejeitar chamadas sem token de autenticação', async () => {
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/ai-chat`, () => {
          return HttpResponse.json({
            success: false,
            error: {
              message: 'Token de autenticação necessário',
              code: 'UNAUTHORIZED'
            }
          }, { status: 401 });
        })
      );

      const response = await fetch(`${EDGE_FUNCTIONS_URL}/ai-chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          // Sem Authorization header
        },
        body: JSON.stringify({ mensagem: 'teste' })
      });

      expect(response.status).toBe(401);
    });

    it('deve rejeitar tokens inválidos', async () => {
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/ai-chat`, () => {
          return HttpResponse.json({
            success: false,
            error: {
              message: 'Token inválido',
              code: 'INVALID_TOKEN'
            }
          }, { status: 401 });
        })
      );

      const response = await callEdgeFunction('ai-chat', 
        { mensagem: 'teste' },
        { headers: { 'Authorization': 'Bearer token-invalido' } }
      );

      expect(response.status).toBe(401);
      expect(response.data.error.code).toBe('INVALID_TOKEN');
    });
  });

  describe('CORS e Headers de Segurança', () => {
    it('deve incluir headers CORS corretos', async () => {
      server.use(
        http.options(`${EDGE_FUNCTIONS_URL}/lead-capture`, () => {
          return new HttpResponse(null, {
            status: 200,
            headers: {
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'POST, OPTIONS',
              'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
            }
          });
        })
      );

      const response = await fetch(`${EDGE_FUNCTIONS_URL}/lead-capture`, {
        method: 'OPTIONS'
      });

      expect(response.status).toBe(200);
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    });

    it('deve incluir headers de segurança', async () => {
      server.use(
        http.post(`${EDGE_FUNCTIONS_URL}/lead-capture`, () => {
          return HttpResponse.json({ success: true }, {
            status: 200,
            headers: {
              'X-Content-Type-Options': 'nosniff',
              'X-Frame-Options': 'DENY'
            }
          });
        })
      );

      const response = await callEdgeFunction('lead-capture', {
        email: '<EMAIL>'
      });

      expect(response.headers.get('X-Content-Type-Options')).toBe('nosniff');
      expect(response.headers.get('X-Frame-Options')).toBe('DENY');
    });
  });
});
