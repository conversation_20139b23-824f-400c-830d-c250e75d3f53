/**
 * 🧪 Testes para Formatadores
 * 
 * Testa as funções de formatação de dados usadas
 * em toda a aplicação (moeda, data, telefone, etc.)
 */

import { describe, expect, it } from 'vitest';

import {
    formatCEP,
    formatCNPJ,
    formatCPF,
    formatCurrency,
    formatDate,
    formatPhone
} from '@/lib/formatters';

describe('Formatadores', () => {
  describe('formatCurrency', () => {
    it('deve formatar valores monetários corretamente', () => {
      expect(formatCurrency(1000)).toMatch(/R\$\s*1\.000,00/);
      expect(formatCurrency(1234.56)).toMatch(/R\$\s*1\.234,56/);
      expect(formatCurrency(0)).toMatch(/R\$\s*0,00/);
      expect(formatCurrency(0.99)).toMatch(/R\$\s*0,99/);
    });

    it('deve lidar com valores negativos', () => {
      expect(formatCurrency(-1000)).toMatch(/-R\$\s*1\.000,00/);
      expect(formatCurrency(-123.45)).toMatch(/-R\$\s*123,45/);
    });

    it('deve lidar com valores null/undefined', () => {
      expect(formatCurrency(null)).toMatch(/R\$\s*0,00/);
      expect(formatCurrency(undefined)).toMatch(/R\$\s*0,00/);
    });
  });

  describe('formatDate', () => {
    it('deve formatar datas corretamente', () => {
      const date = new Date('2024-12-25T10:30:00');
      expect(formatDate(date)).toBe('25/12/2024');
    });

    it('deve lidar com datas inválidas', () => {
      expect(formatDate(null)).toBe('');
      expect(formatDate(undefined)).toBe('');
    });
  });

  describe('formatPhone', () => {
    it('deve formatar telefones celulares (11 dígitos)', () => {
      expect(formatPhone('11987654321')).toBe('(11) 98765-4321');
      expect(formatPhone('21999887766')).toBe('(21) 99988-7766');
    });

    it('deve formatar telefones fixos (10 dígitos)', () => {
      expect(formatPhone('1133334444')).toBe('(11) 3333-4444');
      expect(formatPhone('2155556666')).toBe('(21) 5555-6666');
    });

    it('deve lidar com valores inválidos', () => {
      expect(formatPhone('123')).toBe('123');
      expect(formatPhone('')).toBe('');
      expect(formatPhone(null)).toBe('');
    });
  });

  describe('formatCPF', () => {
    it('deve formatar CPF corretamente', () => {
      expect(formatCPF('12345678901')).toBe('123.456.789-01');
      expect(formatCPF('00000000000')).toBe('000.000.000-00');
    });

    it('deve lidar com valores inválidos', () => {
      expect(formatCPF('123')).toBe('123');
      expect(formatCPF('')).toBe('');
      expect(formatCPF(null)).toBe('');
    });
  });

  describe('formatCNPJ', () => {
    it('deve formatar CNPJ corretamente', () => {
      expect(formatCNPJ('12345678000195')).toBe('12.345.678/0001-95');
      expect(formatCNPJ('00000000000000')).toBe('00.000.000/0000-00');
    });

    it('deve lidar com valores inválidos', () => {
      expect(formatCNPJ('123')).toBe('123');
      expect(formatCNPJ('')).toBe('');
      expect(formatCNPJ(null)).toBe('');
    });
  });

  describe('formatCEP', () => {
    it('deve formatar CEP corretamente', () => {
      expect(formatCEP('01234567')).toBe('01234-567');
      expect(formatCEP('00000000')).toBe('00000-000');
    });

    it('deve lidar com valores inválidos', () => {
      expect(formatCEP('123')).toBe('123');
      expect(formatCEP('')).toBe('');
      expect(formatCEP(null)).toBe('');
    });
  });
});
