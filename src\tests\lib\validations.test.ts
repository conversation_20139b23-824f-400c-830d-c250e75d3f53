/**
 * 🧪 Testes para Validações Zod
 * 
 * Testa os schemas de validação usados em formulários
 * e APIs para garantir integridade dos dados.
 */

import { describe, expect,it } from 'vitest';
import { z } from 'zod';

// Importar schemas de validação (assumindo que existem)
// Vou criar alguns schemas de exemplo baseados no que vi no código

// Schema para obra
const obraSchema = z.object({
  nome: z.string().min(1, 'Nome é obrigatório').max(255, 'Nome muito longo'),
  endereco: z.string().min(1, 'Endereço é obrigatório'),
  cidade: z.string().min(1, 'Cidade é obrigatória'),
  estado: z.string().length(2, 'Estado deve ter 2 caracteres'),
  cep: z.string().regex(/^\d{5}-?\d{3}$/, 'CEP inválido'),
  orcamento: z.number().positive('Orçamento deve ser positivo').optional(),
  data_inicio: z.string().datetime().optional(),
  data_prevista_termino: z.string().datetime().optional()
});

// Schema para fornecedor PJ
const fornecedorPJSchema = z.object({
  cnpj: z.string().regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, 'CNPJ inválido'),
  razao_social: z.string().min(1, 'Razão social é obrigatória'),
  nome_fantasia: z.string().optional(),
  email: z.string().email('Email inválido').optional(),
  telefone: z.string().regex(/^\(\d{2}\) \d{4,5}-\d{4}$/, 'Telefone inválido').optional(),
  endereco: z.string().optional(),
  cidade: z.string().optional(),
  estado: z.string().length(2).optional(),
  cep: z.string().regex(/^\d{5}-\d{3}$/).optional()
});

// Schema para fornecedor PF
const fornecedorPFSchema = z.object({
  cpf: z.string().regex(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/, 'CPF inválido'),
  nome: z.string().min(1, 'Nome é obrigatório'),
  email: z.string().email('Email inválido').optional(),
  telefone: z.string().regex(/^\(\d{2}\) \d{4,5}-\d{4}$/, 'Telefone inválido').optional(),
  endereco: z.string().optional(),
  cidade: z.string().optional(),
  estado: z.string().length(2).optional(),
  cep: z.string().regex(/^\d{5}-\d{3}$/).optional()
});

// Schema para despesa
const despesaSchema = z.object({
  obra_id: z.string().uuid('ID da obra inválido'),
  categoria: z.enum([
    'MATERIAL_CONSTRUCAO',
    'MAO_DE_OBRA', 
    'ALUGUEL_EQUIPAMENTOS',
    'TRANSPORTE_FRETE',
    'TAXAS_LICENCAS',
    'SERVICOS_TERCEIRIZADOS',
    'ADMINISTRATIVO',
    'IMPREVISTOS',
    'OUTROS'
  ]),
  descricao: z.string().min(1, 'Descrição é obrigatória'),
  valor: z.number().positive('Valor deve ser positivo'),
  data_despesa: z.string().datetime(),
  fornecedor_pj_id: z.string().uuid().optional(),
  fornecedor_pf_id: z.string().uuid().optional(),
  nota_fiscal: z.string().optional()
});

// Schema para lead
const leadSchema = z.object({
  email: z.string().email('Email inválido'),
  nome: z.string().min(1, 'Nome é obrigatório').optional(),
  telefone: z.string().optional(),
  empresa: z.string().optional(),
  cargo: z.string().optional(),
  interesse: z.string().optional(),
  origem: z.string().default('landing_page'),
  tipo_empresa: z.enum(['construtora', 'engenharia', 'arquitetura', 'individual', 'outro']).optional(),
  porte_empresa: z.enum(['micro', 'pequena', 'media', 'grande']).optional(),
  numero_obras_mes: z.number().int().min(0).optional(),
  orcamento_mensal: z.number().positive().optional(),
  interesse_nivel: z.enum(['baixo', 'medio', 'alto', 'muito_alto']).default('medio')
});

describe('Validações Zod', () => {
  describe('Schema de Obra', () => {
    it('deve validar obra válida', () => {
      const obraValida = {
        nome: 'Casa Residencial',
        endereco: 'Rua das Flores, 123',
        cidade: 'São Paulo',
        estado: 'SP',
        cep: '01234-567',
        orcamento: 150000,
        data_inicio: '2024-01-15T08:00:00Z'
      };

      const result = obraSchema.safeParse(obraValida);
      expect(result.success).toBe(true);
    });

    it('deve rejeitar obra com dados inválidos', () => {
      const obraInvalida = {
        nome: '', // Nome vazio
        endereco: 'Rua das Flores, 123',
        cidade: 'São Paulo',
        estado: 'SAO', // Estado com mais de 2 caracteres
        cep: '12345', // CEP inválido
        orcamento: -1000 // Orçamento negativo
      };

      const result = obraSchema.safeParse(obraInvalida);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        expect(result.error.issues).toHaveLength(4);
        expect(result.error.issues.some(issue => issue.path.includes('nome'))).toBe(true);
        expect(result.error.issues.some(issue => issue.path.includes('estado'))).toBe(true);
        expect(result.error.issues.some(issue => issue.path.includes('cep'))).toBe(true);
        expect(result.error.issues.some(issue => issue.path.includes('orcamento'))).toBe(true);
      }
    });

    it('deve aceitar campos opcionais vazios', () => {
      const obraMinima = {
        nome: 'Casa Simples',
        endereco: 'Rua A, 1',
        cidade: 'Rio de Janeiro',
        estado: 'RJ',
        cep: '20000-000'
      };

      const result = obraSchema.safeParse(obraMinima);
      expect(result.success).toBe(true);
    });
  });

  describe('Schema de Fornecedor PJ', () => {
    it('deve validar fornecedor PJ válido', () => {
      const fornecedorValido = {
        cnpj: '12.345.678/0001-95',
        razao_social: 'Construtora ABC Ltda',
        nome_fantasia: 'ABC Construções',
        email: '<EMAIL>',
        telefone: '(11) 98765-4321',
        endereco: 'Av. Principal, 1000',
        cidade: 'São Paulo',
        estado: 'SP',
        cep: '01234-567'
      };

      const result = fornecedorPJSchema.safeParse(fornecedorValido);
      expect(result.success).toBe(true);
    });

    it('deve rejeitar CNPJ inválido', () => {
      const fornecedorInvalido = {
        cnpj: '12345678000195', // CNPJ sem formatação
        razao_social: 'Empresa XYZ'
      };

      const result = fornecedorPJSchema.safeParse(fornecedorInvalido);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.path.includes('cnpj') && issue.message === 'CNPJ inválido'
        )).toBe(true);
      }
    });

    it('deve aceitar apenas campos obrigatórios', () => {
      const fornecedorMinimo = {
        cnpj: '98.765.432/0001-10',
        razao_social: 'Empresa Mínima Ltda'
      };

      const result = fornecedorPJSchema.safeParse(fornecedorMinimo);
      expect(result.success).toBe(true);
    });
  });

  describe('Schema de Fornecedor PF', () => {
    it('deve validar fornecedor PF válido', () => {
      const fornecedorValido = {
        cpf: '123.456.789-01',
        nome: 'João da Silva',
        email: '<EMAIL>',
        telefone: '(11) 99999-8888'
      };

      const result = fornecedorPFSchema.safeParse(fornecedorValido);
      expect(result.success).toBe(true);
    });

    it('deve rejeitar CPF inválido', () => {
      const fornecedorInvalido = {
        cpf: '12345678901', // CPF sem formatação
        nome: 'Maria Santos'
      };

      const result = fornecedorPFSchema.safeParse(fornecedorInvalido);
      expect(result.success).toBe(false);
    });
  });

  describe('Schema de Despesa', () => {
    it('deve validar despesa válida', () => {
      const despesaValida = {
        obra_id: '123e4567-e89b-12d3-a456-************',
        categoria: 'MATERIAL_CONSTRUCAO' as const,
        descricao: 'Compra de cimento',
        valor: 500.50,
        data_despesa: '2024-01-15T10:30:00Z',
        fornecedor_pj_id: '123e4567-e89b-12d3-a456-426614174001'
      };

      const result = despesaSchema.safeParse(despesaValida);
      expect(result.success).toBe(true);
    });

    it('deve rejeitar categoria inválida', () => {
      const despesaInvalida = {
        obra_id: '123e4567-e89b-12d3-a456-************',
        categoria: 'CATEGORIA_INEXISTENTE',
        descricao: 'Despesa teste',
        valor: 100,
        data_despesa: '2024-01-15T10:30:00Z'
      };

      const result = despesaSchema.safeParse(despesaInvalida);
      expect(result.success).toBe(false);
    });

    it('deve rejeitar valor negativo', () => {
      const despesaInvalida = {
        obra_id: '123e4567-e89b-12d3-a456-************',
        categoria: 'MATERIAL_CONSTRUCAO' as const,
        descricao: 'Despesa teste',
        valor: -100, // Valor negativo
        data_despesa: '2024-01-15T10:30:00Z'
      };

      const result = despesaSchema.safeParse(despesaInvalida);
      expect(result.success).toBe(false);
    });
  });

  describe('Schema de Lead', () => {
    it('deve validar lead válido', () => {
      const leadValido = {
        email: '<EMAIL>',
        nome: 'Carlos Construtor',
        telefone: '(11) 98765-4321',
        empresa: 'Construtora Carlos',
        tipo_empresa: 'construtora' as const,
        porte_empresa: 'pequena' as const,
        numero_obras_mes: 5,
        orcamento_mensal: 50000,
        interesse_nivel: 'alto' as const
      };

      const result = leadSchema.safeParse(leadValido);
      expect(result.success).toBe(true);
    });

    it('deve aplicar valores padrão', () => {
      const leadMinimo = {
        email: '<EMAIL>'
      };

      const result = leadSchema.safeParse(leadMinimo);
      expect(result.success).toBe(true);
      
      if (result.success) {
        expect(result.data.origem).toBe('landing_page');
        expect(result.data.interesse_nivel).toBe('medio');
      }
    });

    it('deve rejeitar email inválido', () => {
      const leadInvalido = {
        email: 'email-invalido',
        nome: 'Teste'
      };

      const result = leadSchema.safeParse(leadInvalido);
      expect(result.success).toBe(false);
    });
  });

  describe('Casos extremos e edge cases', () => {
    it('deve lidar com strings muito longas', () => {
      const nomeMuitoLongo = 'a'.repeat(300);
      
      const obraInvalida = {
        nome: nomeMuitoLongo,
        endereco: 'Rua A',
        cidade: 'Cidade',
        estado: 'SP',
        cep: '12345-678'
      };

      const result = obraSchema.safeParse(obraInvalida);
      expect(result.success).toBe(false);
    });

    it('deve validar UUIDs corretamente', () => {
      const uuidValido = '123e4567-e89b-12d3-a456-************';
      const uuidInvalido = 'not-a-uuid';

      const despesaComUuidValido = {
        obra_id: uuidValido,
        categoria: 'OUTROS' as const,
        descricao: 'Teste',
        valor: 100,
        data_despesa: '2024-01-15T10:30:00Z'
      };

      const despesaComUuidInvalido = {
        obra_id: uuidInvalido,
        categoria: 'OUTROS' as const,
        descricao: 'Teste',
        valor: 100,
        data_despesa: '2024-01-15T10:30:00Z'
      };

      expect(despesaSchema.safeParse(despesaComUuidValido).success).toBe(true);
      expect(despesaSchema.safeParse(despesaComUuidInvalido).success).toBe(false);
    });

    it('deve validar datas ISO corretamente', () => {
      const dataValida = '2024-01-15T10:30:00Z';
      const dataInvalida = '2024-13-45';

      const obraComDataValida = {
        nome: 'Teste',
        endereco: 'Rua A',
        cidade: 'Cidade',
        estado: 'SP',
        cep: '12345-678',
        data_inicio: dataValida
      };

      const obraComDataInvalida = {
        nome: 'Teste',
        endereco: 'Rua A',
        cidade: 'Cidade',
        estado: 'SP',
        cep: '12345-678',
        data_inicio: dataInvalida
      };

      expect(obraSchema.safeParse(obraComDataValida).success).toBe(true);
      expect(obraSchema.safeParse(obraComDataInvalida).success).toBe(false);
    });
  });
});
