import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { afterEach,beforeEach, describe, expect, it, vi } from 'vitest';

import { CondominioDashboard } from '@/components/obras/CondominioDashboard';
import { ListaUnidadesVirtualized } from '@/components/obras/ListaUnidadesVirtualized';
import { useObrasCondominio } from '@/hooks/useObrasCondominio';

// Mock do hook useObrasCondominio
vi.mock('@/hooks/useObrasCondominio');

// Mock da função de navegação
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
  };
});

// Helper para criar dados de teste em grande escala
const createLargeCondominioData = (numUnidades: number) => {
  const unidades = Array.from({ length: numUnidades }, (_, index) => ({
    id: `unit-${index + 1}`,
    nome: `Unidade ${index + 1}`,
    identificador_unidade: `${Math.floor(index / 10) + 1}-${(index % 10) + 1}`,
    status_venda: ['DISPONIVEL', 'VENDIDA', 'RESERVADA'][index % 3],
    area_total: 70 + (index % 50),
    orcamento: 250000 + (index * 5000),
    valor_venda: index % 3 === 1 ? 300000 + (index * 7000) : null,
    progresso: Math.min(100, (index / numUnidades) * 120),
    parent_obra_id: 'condominio-master-id',
    tipo_projeto: 'UNIDADE_CONDOMINIO',
    tenant_id: 'test-tenant',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }));

  const estatisticas = {
    total_unidades: numUnidades,
    unidades_vendidas: Math.floor(numUnidades * 0.6),
    unidades_disponiveis: Math.floor(numUnidades * 0.3),
    unidades_reservadas: Math.floor(numUnidades * 0.1),
    progresso_medio: 65,
    valor_total_vendas: unidades.reduce((sum, u) => sum + (u.valor_venda || 0), 0),
    orcamento_total_unidades: unidades.reduce((sum, u) => sum + u.orcamento, 0),
    unidades_concluidas: Math.floor(numUnidades * 0.4),
    unidades_no_prazo: Math.floor(numUnidades * 0.8),
    unidades_atrasadas: Math.floor(numUnidades * 0.2),
  };

  return { unidades, estatisticas };
};

// Mock das funções de Edge Function
const mockEdgeFunction = (numUnidades: number, page = 1, pageSize = 50) => {
  const { unidades } = createLargeCondominioData(numUnidades);
  const startIndex = (page - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, numUnidades);
  const paginatedUnits = unidades.slice(startIndex, endIndex);

  return {
    data: paginatedUnits,
    pagination: {
      page,
      pageSize,
      total: numUnidades,
      totalPages: Math.ceil(numUnidades / pageSize),
      hasNextPage: endIndex < numUnidades,
      hasPreviousPage: page > 1,
    },
  };
};

const createTestWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <MemoryRouter>
        {children}
      </MemoryRouter>
    </QueryClientProvider>
  );
};

describe('Performance Tests - Condomínios Grandes', () => {
  const mockedUseObrasCondominio = vi.mocked(useObrasCondominio);

  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('CondominioDashboard Performance', () => {
    it('deve carregar dashboard para condomínio com 1000 unidades em menos de 2 segundos', async () => {
      const { estatisticas } = createLargeCondominioData(1000);

      // Mock do hook para dashboard otimizado
      mockedUseObrasCondominio.mockReturnValue({
        useCondominioDashboard: vi.fn(() => ({
          data: { estatisticas, unidades: [] }, // Dashboard otimizado não carrega unidades
          isLoading: false,
          error: null,
        })),
      } as any);

      const startTime = performance.now();

      render(<CondominioDashboard obraId="test-condominio-id" />, {
        wrapper: createTestWrapper(),
      });

      // Aguardar renderização completa
      await waitFor(() => {
        expect(screen.getByText('1000')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Verificar se renderizou em menos de 2 segundos
      expect(renderTime).toBeLessThan(2000);

      // Verificar se as métricas principais estão visíveis
      expect(screen.getByText('Total de Unidades')).toBeInTheDocument();
      expect(screen.getByText('Progresso Médio')).toBeInTheDocument();
      expect(screen.getByText('Dashboard Otimizado')).toBeInTheDocument();
    });

    it('deve mostrar badge de performance para condomínios grandes', async () => {
      const { estatisticas } = createLargeCondominioData(500);

      mockedUseObrasCondominio.mockReturnValue({
        useCondominioDashboard: vi.fn(() => ({
          data: { estatisticas, unidades: [] },
          isLoading: false,
          error: null,
        })),
      } as any);

      render(<CondominioDashboard obraId="test-condominio-id" />, {
        wrapper: createTestWrapper(),
      });

      await waitFor(() => {
        expect(screen.getByText('Dashboard Otimizado')).toBeInTheDocument();
        expect(screen.getByText(/500 unidades/)).toBeInTheDocument();
      });
    });

    it('deve carregar estatísticas sem carregar unidades individuais', async () => {
      const { estatisticas } = createLargeCondominioData(2000);

      const mockDashboardHook = vi.fn(() => ({
        data: { estatisticas, unidades: [] }, // Importante: array vazio de unidades
        isLoading: false,
        error: null,
      }));

      mockedUseObrasCondominio.mockReturnValue({
        useCondominioDashboard: mockDashboardHook,
      } as any);

      render(<CondominioDashboard obraId="test-condominio-id" />, {
        wrapper: createTestWrapper(),
      });

      await waitFor(() => {
        expect(screen.getByText('2000')).toBeInTheDocument();
      });

      // Verificar que o hook foi chamado (dados carregados)
      expect(mockDashboardHook).toHaveBeenCalled();

      // Verificar que não há progresso individual renderizado (performance)
      expect(screen.queryByText('Progresso por Unidade')).not.toBeInTheDocument();
    });
  });

  describe('ListaUnidadesVirtualized Performance', () => {
    it('deve carregar primeira página rapidamente mesmo com muitas unidades totais', async () => {
      const mockPaginatedHook = vi.fn(() => ({
        data: mockEdgeFunction(5000, 1, 50), // 5000 total, página 1
        isLoading: false,
        error: null,
        isPlaceholderData: false,
      }));

      mockedUseObrasCondominio.mockReturnValue({
        useUnidadesPaginadasSimple: mockPaginatedHook,
      } as any);

      const startTime = performance.now();

      render(<ListaUnidadesVirtualized condominioId="test-condominio-id" />, {
        wrapper: createTestWrapper(),
      });

      // Aguardar renderização da primeira página
      await waitFor(() => {
        expect(screen.getByText('Unidade 1')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Deve renderizar primeira página rapidamente
      expect(renderTime).toBeLessThan(1000);

      // Verificar que apenas 50 unidades foram carregadas
      expect(mockPaginatedHook).toHaveBeenCalledWith(
        'test-condominio-id',
        expect.objectContaining({
          page: 1,
          pageSize: 50,
        })
      );
    });

    it('deve virtualizar renderização de itens para economizar DOM', async () => {
      const mockPaginatedHook = vi.fn(() => ({
        data: mockEdgeFunction(1000, 1, 50),
        isLoading: false,
        error: null,
        isPlaceholderData: false,
      }));

      mockedUseObrasCondominio.mockReturnValue({
        useUnidadesPaginadasSimple: mockPaginatedHook,
      } as any);

      render(<ListaUnidadesVirtualized condominioId="test-condominio-id" />, {
        wrapper: createTestWrapper(),
      });

      await waitFor(() => {
        expect(screen.getByText('Unidade 1')).toBeInTheDocument();
      });

      // Verificar que nem todas as 50 unidades estão no DOM simultaneamente
      // (virtualização renderiza apenas itens visíveis)
      const renderedUnits = screen.getAllByText(/^Unidade \d+$/);
      
      // Com virtualização, deve renderizar menos itens que o total
      expect(renderedUnits.length).toBeLessThan(50);
      expect(renderedUnits.length).toBeGreaterThan(0);
    });

    it('deve mostrar informações de paginação corretamente', async () => {
      const totalUnidades = 2500;
      const mockPaginatedHook = vi.fn(() => ({
        data: mockEdgeFunction(totalUnidades, 1, 50),
        isLoading: false,
        error: null,
        isPlaceholderData: false,
      }));

      mockedUseObrasCondominio.mockReturnValue({
        useUnidadesPaginadasSimple: mockPaginatedHook,
      } as any);

      render(<ListaUnidadesVirtualized condominioId="test-condominio-id" />, {
        wrapper: createTestWrapper(),
      });

      await waitFor(() => {
        expect(screen.getByText(/Mostrando 1 até 50 de 2500 unidades/)).toBeInTheDocument();
        expect(screen.getByText(/Página 1 de 50/)).toBeInTheDocument();
      });
    });
  });

  describe('Memory Performance', () => {
    it('deve usar menos memória com paginação do que carregamento completo', async () => {
      // Simular cenário de memory usage
      const largeConductorData = createLargeCondominioData(10000);
      
      // Cenário 1: Carregamento paginado (50 itens)
      const paginatedSize = JSON.stringify(mockEdgeFunction(10000, 1, 50)).length;
      
      // Cenário 2: Carregamento completo simulado
      const fullDataSize = JSON.stringify(largeConductorData.unidades).length;
      
      // Paginação deve usar significativamente menos memória
      const memoryReduction = ((fullDataSize - paginatedSize) / fullDataSize) * 100;
      
      expect(memoryReduction).toBeGreaterThan(90); // Mais de 90% de redução
      expect(paginatedSize).toBeLessThan(100000); // Menos de 100KB por página
    });
  });

  describe('Edge Function Performance', () => {
    it('deve simular resposta rápida da Edge Function paginada', async () => {
      const startTime = performance.now();
      
      // Simular chamada para Edge Function
      const result = mockEdgeFunction(5000, 1, 50);
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      // Edge Function simulada deve responder rapidamente
      expect(responseTime).toBeLessThan(100);
      expect(result.data).toHaveLength(50);
      expect(result.pagination.total).toBe(5000);
      expect(result.pagination.hasNextPage).toBe(true);
    });

    it('deve retornar metadados de paginação corretos', () => {
      const result = mockEdgeFunction(1000, 5, 50);
      
      expect(result.pagination).toEqual({
        page: 5,
        pageSize: 50,
        total: 1000,
        totalPages: 20,
        hasNextPage: true,
        hasPreviousPage: true,
      });
    });
  });

  describe('Stress Testing', () => {
    it('deve manter performance mesmo com condomínios muito grandes', async () => {
      const scenarios = [100, 500, 1000, 5000, 10000];
      
      for (const numUnidades of scenarios) {
        const startTime = performance.now();
        
        // Dashboard otimizado - apenas estatísticas
        const { estatisticas } = createLargeCondominioData(numUnidades);
        
        const endTime = performance.now();
        const processingTime = endTime - startTime;
        
        // Processamento deve ser constante O(1), não dependente do número de unidades
        expect(processingTime).toBeLessThan(50); // 50ms máximo para qualquer tamanho
        expect(estatisticas.total_unidades).toBe(numUnidades);
      }
    });
  });
});

// Benchmark helper para testes manuais
export const runPerformanceBenchmark = async () => {
  const sizes = [100, 500, 1000, 2000, 5000];
  const results = [];

  for (const size of sizes) {
    const startTime = performance.now();
    
    // Simular carregamento de dashboard
    const { estatisticas } = createLargeCondominioData(size);
    
    const endTime = performance.now();
    
    results.push({
      size,
      time: endTime - startTime,
      memoryUsage: JSON.stringify(estatisticas).length,
    });
  }

  console.table(results);
  return results;
};