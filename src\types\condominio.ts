/**
 * Tipos TypeScript para a funcionalidade de Obras de Condomínio
 */

import type { Obra } from './api';

/**
 * Dados para a criação de uma unidade de condomínio.
 * Deriva da interface Obra, mas omite campos gerenciados pelo sistema
 * e define `identificador_unidade` como obrigatório.
 */
export type UnidadeData = Omit<
  Obra,
  | 'id'
  | 'tipo_projeto'
  | 'parent_obra_id'
  | 'created_at'
  | 'updated_at'
  | 'tenant_id'
  | 'usuario_id'
> & {
  identificador_unidade: string; // Garante que a unidade tenha um identificador
};

/**
 * Dados para a criação do projeto mestre de um condomínio.
 * Deriva da interface Obra, mas omite campos que não são relevantes
 * para o projeto mestre no momento da criação.
 */
export type CondominioMasterData = Omit<
  Obra,
  | 'id'
  | 'tipo_projeto'
  | 'parent_obra_id'
  | 'identificador_unidade'
  | 'created_at'
  | 'updated_at'
  | 'tenant_id'
  | 'usuario_id'
>;

/**
 * Estrutura de dados completa para a criação de um novo condomínio.
 * Contém os dados do projeto mestre e um array com os dados das unidades.
 */
export interface CondominioMasterData extends Obra {
  tipo_projeto: 'CONDOMINIO_MASTER';
}

export interface CreateCondominioData {
  master: Omit<CondominioMasterData, 'id' | 'created_at' | 'tenant_id' | 'tipo_projeto' | 'parent_obra_id' | 'identificador_unidade'>;
  unidades: Omit<UnidadeData, 'id' | 'created_at' | 'tenant_id' | 'tipo_projeto' | 'parent_obra_id'>[];
}

export interface CondominioDetails {
  master: CondominioMasterData;
  unidades: UnidadeData[];
}