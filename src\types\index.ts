/**
 * Arquivo de exportação central para todos os tipos do projeto
 * Facilita importações e mantém organização
 */

// Tipos de formulários
export type * from './forms';

// Tipos de API
export type * from './api';

// Tipos de alertas
export type * from './alerts';

// Tipos de licitações
export type * from './licitacoes';

// Tipos de Condomínio
export type * from './condominio';

// Tipos do Supabase
export type * from './supabase';

// Re-export specific condomínio validation types for easier access
export type {
    CondominioDataValues,
    CreateCondominioRequestValues,
    ObraCondominioFormValues,
    ObrasCondominioFilterValues, TipoProjetoType,
    UnidadeFormDataValues
} from '../lib/validations/condominio';
export {
condominioDataSchema,
    createCondominioRequestSchema, isCondominio, isObraUnica, isUnidadeCondominio, obraCondominioSchema,
    obrasCondominioFilterSchema,     STATUS_OBRA_OPTIONS, TipoProjetoEnum, TIPOS_PROJETO_OPTIONS, unidadeFormDataSchema
} from '../lib/validations/condominio';

// Convenções de tipos no projeto:
// 1. Use 'interface' para definições de objetos e props de componentes
// 2. Use 'type' para union types, aliases, e transformações de tipos
// 3. Use 'interface' quando precisar de extensibilidade (declaração merging)
// 4. Use 'type' para tipos mais complexos e computados

// Tipos utilitários globais
export type Status = 'pending' | 'completed' | 'error' | 'loading';

export type Priority = 'low' | 'medium' | 'high' | 'critical';

export type ActionType = 'create' | 'read' | 'update' | 'delete';

export type Theme = 'light' | 'dark' | 'system';

// Type guards utilitários
export const isError = (value: unknown): value is Error => {
  return value instanceof Error;
};

export const isString = (value: unknown): value is string => {
  return typeof value === 'string';
};

export const isNumber = (value: unknown): value is number => {
  return typeof value === 'number' && !isNaN(value);
};

export const isObject = (value: unknown): value is Record<string, unknown> => {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
};

export const isArray = <T>(value: unknown): value is T[] => {
  return Array.isArray(value);
};

// Utility types para padronização
export type WithId<T> = T & { id: string };

export type WithTimestamps<T> = T & {
  created_at: string;
  updated_at: string;
};

export type WithUser<T> = T & {
  usuario_id: string;
  tenant_id: string;
};

export type DatabaseEntity<T> = WithId<WithTimestamps<WithUser<T>>>;

export type CreateInput<T> = Omit<T, 'id' | 'created_at' | 'updated_at'>;

export type UpdateInput<T> = Partial<Omit<T, 'id' | 'created_at' | 'updated_at' | 'usuario_id' | 'tenant_id'>>;

// Tipos para operações assíncronas
export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

// Tipos para configurações
export interface AppConfig {
  api: {
    baseUrl: string;
    timeout: number;
    retries: number;
  };
  features: {
    aiEnabled: boolean;
    notificationsEnabled: boolean;
    analyticsEnabled: boolean;
  };
  ui: {
    theme: Theme;
    language: string;
    timezone: string;
  };
}

// Tipos para preferências do usuário
export interface UserPreferences {
  id?: string;
  user_id: string;
  notifications: NotificationPreferences;
  appearance: AppearancePreferences;
  security: SecurityPreferences;
  language: LanguagePreferences;
  devices: DevicePreferences;
  created_at?: string;
  updated_at?: string;
}

export interface NotificationPreferences {
  email_enabled: boolean;
  push_enabled: boolean;
  obra_alerts: boolean;
  despesa_alerts: boolean;
  contrato_alerts: boolean;
  orcamento_alerts: boolean;
  marketing_emails: boolean;
  weekly_reports: boolean;
  alert_frequency: 'immediate' | 'daily' | 'weekly';
  quiet_hours: {
    enabled: boolean;
    start_time: string;
    end_time: string;
  };
}

export interface AppearancePreferences {
  theme: 'light' | 'dark' | 'system';
  sidebar_collapsed: boolean;
  compact_mode: boolean;
  animations_enabled: boolean;
  high_contrast: boolean;
  font_size: 'small' | 'medium' | 'large';
  color_scheme: 'default' | 'blue' | 'green' | 'purple';
}

export interface SecurityPreferences {
  two_factor_enabled: boolean;
  session_timeout: number; // em minutos
  login_notifications: boolean;
  suspicious_activity_alerts: boolean;
  data_export_notifications: boolean;
  password_change_notifications: boolean;
  device_login_notifications: boolean;
}

export interface LanguagePreferences {
  language: 'pt-BR' | 'en-US' | 'es-ES';
  timezone: string;
  date_format: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
  time_format: '24h' | '12h';
  currency: 'BRL' | 'USD' | 'EUR';
  number_format: 'pt-BR' | 'en-US' | 'es-ES';
}

export interface DevicePreferences {
  remember_devices: boolean;
  auto_logout_inactive: boolean;
  max_concurrent_sessions: number;
  mobile_notifications: boolean;
  desktop_notifications: boolean;
}

// Tipos para navegação
export interface NavItem {
  label: string;
  href: string;
  icon?: string;
  children?: NavItem[];
  permission?: string;
}

export interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

// Tipos para notificações
export interface NotificationConfig {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  persistent?: boolean;
}

// Tipos para métricas e analytics
export interface MetricData {
  label: string;
  value: number;
  change?: number;
  changeType?: 'increase' | 'decrease';
  format?: 'number' | 'currency' | 'percentage';
}

export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

// Tipos para filtros genéricos
export interface BaseFilter {
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export interface DateRangeFilter {
  startDate?: Date;
  endDate?: Date;
}

export interface StatusFilter {
  status?: string[];
}

// Tipos para validação
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Tipos para permissões
export type Permission = 
  | 'admin'
  | 'manager'
  | 'user'
  | 'readonly'
  | 'obras:read'
  | 'obras:write'
  | 'obras:delete'
  | 'contratos:read'
  | 'contratos:write'
  | 'despesas:read'
  | 'despesas:write'
  | 'fornecedores:read'
  | 'fornecedores:write'
  | 'alertas:read'
  | 'alertas:configure'
  | 'orcamentos:read'
  | 'orcamentos:write'
  | 'sinapi:read'
  | 'ai:use'
  | 'licitacoes:read'
  | 'licitacoes:write'
  | 'licitacoes:favorite';

export interface UserPermissions {
  userId: string;
  tenantId: string;
  permissions: Permission[];
  role: 'admin' | 'manager' | 'user' | 'readonly';
}

// Tipos para Notas Fiscais
export interface NotaFiscal {
  id: string;
  obra_id: string;
  despesa_id?: string;
  fornecedor_pj_id?: string;
  fornecedor_pf_id?: string;
  numero?: string;
  serie?: string;
  data_emissao: string;
  valor_total: number;
  chave_acesso?: string;
  descricao?: string;
  arquivo_url?: string;
  arquivo_path?: string;
  impostos?: {
    icms?: number;
    ipi?: number;
    pis?: number;
    cofins?: number;
  };
  confianca?: 'alta' | 'media' | 'baixa';
  observacoes?: string;
  usuario_upload_id?: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
}

export interface NotaFiscalItem {
  id: string;
  nota_fiscal_id: string;
  descricao: string;
  quantidade: number;
  unidade?: string;
  valor_unitario: number;
  valor_total: number;
  codigo?: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
}

export interface NotaFiscalWithItems extends NotaFiscal {
  itens: NotaFiscalItem[];
}


