/**
 * Tipos TypeScript para o módulo de Licitações
 * Seguindo as convenções definidas em CLAUDE.md
 */

import type { BaseFilter, CreateInput, DatabaseEntity, DateRangeFilter,UpdateInput } from './index'

// Tipos base para licitações
export interface LicitacaoBase {
  numero_licitacao: string
  objeto: string
  orgao: string
  modalidade?: string
  valor_estimado?: number
  data_abertura?: string
  data_limite_entrega?: string
  situacao: 'em_andamento' | 'suspensa' | 'cancelada' | 'concluida'
  link_edital?: string
  link_portal?: string
  endereco_entrega?: string
  observacoes?: string
  fonte?: 'manual' | 'api_gov' | 'importacao'
}

// Entidade completa do banco
export type Licitacao = DatabaseEntity<LicitacaoBase>

// Tipos para operações CRUD
export type CreateLicitacao = CreateInput<Licitacao>
export type UpdateLicitacao = UpdateInput<Licitacao>

// Tipo para licitação com informações extras (favorita, análise)
export interface LicitacaoDetalhada extends Licitacao {
  is_favorita?: boolean
  analise_ia?: AnaliseIA
  total_favoritos?: number
}

// Tipos para favoritos
export interface LicitacaoFavoritaBase {
  licitacao_id: string
  usuario_id: string
  tenant_id: string
}

export type LicitacaoFavorita = DatabaseEntity<LicitacaoFavoritaBase>
export type CreateLicitacaoFavorita = CreateInput<LicitacaoFavorita>

// Tipos para análise de IA
export interface RequisitosPrincipais {
  qualificacao_tecnica: string[]
  qualificacao_economica: string[]
  documentos_habilitacao: string[]
  criterios_tecnicos: string[]
}

export interface AnaliseCompatibilidade {
  nivel: 'alto' | 'medio' | 'baixo'
  score: number
  justificativas: string[]
  recomendacao: 'participar' | 'avaliar' | 'nao_participar'
  pontos_fortes: string[]
  pontos_fracos: string[]
}

export interface AnaliseIABase {
  licitacao_id: string
  tenant_id: string
  resumo_objeto: string
  requisitos_principais: RequisitosPrincipais
  documentos_necessarios: string[]
  prazo_execucao: string
  valor_estimado_ia: string
  observacoes_ia: string
  nivel_complexidade: 'baixo' | 'medio' | 'alto'
  analise_compatibilidade?: AnaliseCompatibilidade
  processed_at: string
}

export type AnaliseIA = DatabaseEntity<AnaliseIABase>

// Tipos para filtros e busca
export interface FiltrosLicitacao extends BaseFilter, DateRangeFilter {
  numero_licitacao?: string
  objeto?: string
  orgao?: string
  modalidade?: string
  valor_min?: number
  valor_max?: number
  data_abertura_inicio?: string
  data_abertura_fim?: string
  situacao?: string
  apenas_favoritas?: boolean
  nivel_complexidade?: 'baixo' | 'medio' | 'alto'
}

export interface OrdenacaoLicitacao {
  campo: 'data_abertura' | 'valor_estimado' | 'numero_licitacao' | 'orgao' | 'created_at'
  direcao: 'asc' | 'desc'
}

export interface PaginacaoLicitacao {
  pagina: number
  limite: number
}

// Tipos para requisições das Edge Functions
export interface BuscarLicitacoesRequest {
  filtros?: FiltrosLicitacao
  ordenacao?: OrdenacaoLicitacao
  paginacao?: PaginacaoLicitacao
}

export interface BuscarLicitacoesResponse {
  success: boolean
  data: LicitacaoDetalhada[]
  metadata: {
    paginacao: {
      pagina_atual: number
      limite: number
      total_items: number
      total_pages: number
      has_next: boolean
      has_prev: boolean
    }
    filtros_aplicados: FiltrosLicitacao
    ordenacao: OrdenacaoLicitacao
  }
}

export interface AnalisarEditalRequest {
  licitacao_id: string
  texto_edital?: string
  url_edital?: string
}

export interface AnalisarEditalResponse {
  success: boolean
  data: {
    resumo_objeto: string
    requisitos_principais: RequisitosPrincipais
    documentos_necessarios: string[]
    prazo_execucao: string
    valor_estimado_ia: string
    observacoes_ia: string
    nivel_complexidade: 'baixo' | 'medio' | 'alto'
    analise_compatibilidade?: AnaliseCompatibilidade
  }
  cached: boolean
}

// Tipos para componentes React
export interface LicitacaoCardProps {
  licitacao: LicitacaoDetalhada
  onFavoritar?: (id: string) => void
  onDesfavoritar?: (id: string) => void
  onViewDetails?: (id: string) => void
  showAnalise?: boolean
  loading?: boolean
}

export interface LicitacaoListProps {
  licitacoes: LicitacaoDetalhada[]
  loading?: boolean
  error?: string
  onLoadMore?: () => void
  hasMore?: boolean
  onFiltrar?: (filtros: FiltrosLicitacao) => void
  filtrosAtivos?: FiltrosLicitacao
}

export interface LicitacaoFiltrosProps {
  filtros: FiltrosLicitacao
  onFiltrosChange: (filtros: FiltrosLicitacao) => void
  onLimpar: () => void
  loading?: boolean
}

export interface LicitacaoDetalhesProps {
  licitacao: LicitacaoDetalhada
  analise?: AnaliseIA
  onAnalisarEdital?: (request: AnalisarEditalRequest) => Promise<void>
  onFavoritar?: () => void
  onDesfavoritar?: () => void
  loading?: boolean
  analisando?: boolean
}

// Tipos para hooks
export interface UseLicitacoesOptions {
  filtros?: FiltrosLicitacao
  ordenacao?: OrdenacaoLicitacao
  autoFetch?: boolean
  keepPreviousData?: boolean
}

export interface UseLicitacoesReturn {
  licitacoes: LicitacaoDetalhada[]
  loading: boolean
  error: string | null
  hasMore: boolean
  totalItems: number
  buscar: (filtros?: FiltrosLicitacao) => Promise<void>
  loadMore: () => Promise<void>
  refresh: () => Promise<void>
}

export interface UseAnaliseIAOptions {
  licitacaoId: string
  autoFetch?: boolean
}

export interface UseAnaliseIAReturn {
  analise: AnaliseIA | null
  loading: boolean
  error: string | null
  analisar: (request: AnalisarEditalRequest) => Promise<AnaliseIA>
  refresh: () => Promise<void>
}

// Tipos para validação
export interface LicitacaoFormData {
  numero_licitacao: string
  objeto: string
  orgao: string
  modalidade?: string
  valor_estimado?: number
  data_abertura?: Date
  data_limite_entrega?: Date
  situacao: 'em_andamento' | 'suspensa' | 'cancelada' | 'concluida'
  link_edital?: string
  link_portal?: string
  endereco_entrega?: string
  observacoes?: string
}

export interface AnalisarEditalFormData {
  texto_edital?: string
  url_edital?: string
}

// Tipos para estatísticas e métricas
export interface LicitacoesStats {
  total: number
  em_andamento: number
  favoritas: number
  com_analise: number
  por_modalidade: Record<string, number>
  por_orgao: Record<string, number>
  valor_total_estimado: number
}

export interface LicitacaoMetrics {
  visualizacoes: number
  favoritos: number
  analyses_realizadas: number
  taxa_participacao: number
}

// Constantes e enums
export const MODALIDADES_LICITACAO = [
  'pregao_eletronico',
  'pregao_presencial', 
  'concorrencia',
  'tomada_precos',
  'convite',
  'concurso',
  'leilao'
] as const

export const SITUACOES_LICITACAO = [
  'em_andamento',
  'suspensa', 
  'cancelada',
  'concluida'
] as const

export const NIVEIS_COMPLEXIDADE = [
  'baixo',
  'medio',
  'alto'
] as const

export type ModalidadeLicitacao = typeof MODALIDADES_LICITACAO[number]
export type SituacaoLicitacao = typeof SITUACOES_LICITACAO[number]
export type NivelComplexidade = typeof NIVEIS_COMPLEXIDADE[number]