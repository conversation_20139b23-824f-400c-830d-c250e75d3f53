import { secureLogger } from "@/lib/secure-logger";

export interface ObraProgressoData {
  id: string;
  nome: string;
  status: string;
  progresso?: number;
  orcamento?: number;
  orcamento_total?: number;
  data_inicio?: string;
  data_prevista_termino?: string;
}

export interface DespesaData {
  obra_id: string;
  custo?: number;
  valor?: number;
  valor_unitario?: number;
}

export interface EstatisticasCondominio {
  progresso_medio?: number;
  unidades_concluidas?: number;
  total_unidades?: number;
}

/**
 * Calcula o progresso real de uma obra baseado no status e dados disponíveis
 */
export const calcularProgressoObra = (
  obra: ObraProgressoData,
  despesas: DespesaData[] = []
): number => {
  // Para obras concluídas, sempre 100%
  if (obra.status === "concluida" || obra.status === "finalizada") {
    return 100;
  }

  // Para obras em planejamento, usar progresso manual ou 0
  if (obra.status === "planejamento") {
    return obra.progresso || 0;
  }

  // Para obras em execução, calcular baseado em despesas vs orçamento
  if (obra.status === "execucao" || obra.status === "em_andamento") {
    const orcamento = obra.orcamento_total || obra.orcamento || 0;

    if (orcamento <= 0) {
      return obra.progresso || 0;
    }

    const totalGasto = despesas
      .filter((d) => d.obra_id === obra.id)
      .reduce((sum, d) => {
        const valor = d.custo || d.valor || d.valor_unitario || 0;
        return sum + valor;
      }, 0);

    const progressoFinanceiro = Math.min(
      Math.round((totalGasto / orcamento) * 100),
      100
    );

    // Se há progresso manual definido, usar o maior entre os dois
    // (mais otimista, mas realista)
    if (obra.progresso && obra.progresso > 0) {
      return Math.max(progressoFinanceiro, obra.progresso);
    }

    return progressoFinanceiro;
  }

  // Fallback para progresso manual ou 0
  return obra.progresso || 0;
};

/**
 * Calcula progresso real para dashboard de condomínio
 */
export const calcularProgressoCondominio = (
  estatisticas: EstatisticasCondominio
): number => {
  // Se não temos dados válidos, retorna 0
  if (!estatisticas || isNaN(estatisticas.progresso_medio || 0)) {
    return 0;
  }

  const progressoMedio = estatisticas.progresso_medio || 0;
  const unidadesConcluidas = estatisticas.unidades_concluidas || 0;
  const totalUnidades = estatisticas.total_unidades || 1;

  // Calcular progresso baseado em unidades realmente concluídas
  const progressoPorConclusao = (unidadesConcluidas / totalUnidades) * 100;

  // NOVA LÓGICA: Se o progresso médio é alto (>= 90%), considerar como concluído
  // mesmo que as unidades não estejam marcadas como "concluida" no status
  if (progressoMedio >= 90) {
    secureLogger.info(
      "Obra com progresso alto detectada - considerando como concluída:",
      {
        progressoMedio,
        progressoPorConclusao,
        unidadesConcluidas,
        totalUnidades,
      }
    );

    // Retornar o progresso médio real, pois indica conclusão física
    return Math.min(progressoMedio, 100);
  }

  // Se há uma grande discrepância entre progresso médio e conclusões reais,
  // usar o menor valor (mais conservador e realista)
  if (Math.abs(progressoMedio - progressoPorConclusao) > 20) {
    secureLogger.warn("Inconsistência detectada no progresso do condomínio:", {
      progressoMedio,
      progressoPorConclusao,
      unidadesConcluidas,
      totalUnidades,
    });

    // Retornar o progresso mais conservador
    return Math.min(progressoMedio, progressoPorConclusao);
  }

  return progressoMedio;
};

/**
 * Calcula progresso temporal baseado em datas
 */
export const calcularProgressoTemporal = (
  dataInicio?: string,
  dataPrevistaTermino?: string
): number => {
  if (!dataInicio || !dataPrevistaTermino) {
    return 0;
  }

  try {
    const inicio = new Date(dataInicio);
    const fim = new Date(dataPrevistaTermino);
    const hoje = new Date();

    // Se ainda não começou, progresso = 0%
    if (hoje < inicio) {
      return 0;
    }

    // Se já terminou, progresso = 100%
    if (hoje > fim) {
      return 100;
    }

    const duracaoTotal = fim.getTime() - inicio.getTime();
    const tempoDecorrido = hoje.getTime() - inicio.getTime();

    const progresso = Math.max(
      0,
      Math.min(100, (tempoDecorrido / duracaoTotal) * 100)
    );

    return Math.round(progresso);
  } catch (error) {
    secureLogger.error("Erro ao calcular progresso temporal:", error);
    return 0;
  }
};
