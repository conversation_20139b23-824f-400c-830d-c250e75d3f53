/**
 * Utilitário para testar a API de CEP
 * Este arquivo pode ser usado para verificar se a busca de CEP está funcionando corretamente
 */

interface CEPData {
  cep: string;
  logradouro: string;
  complemento: string;
  bairro: string;
  localidade: string;
  uf: string;
  ibge: string;
  gia: string;
  ddd: string;
  siafi: string;
}

/**
 * Testa a busca de CEP diretamente
 * @param cep - CEP para testar
 * @returns Dados do CEP ou erro
 */
export async function testCEPAPI(cep: string): Promise<CEPData | null> {
  try {
    const cepLimpo = cep.replace(/\D/g, "");
    
    if (cepLimpo.length !== 8) {
      throw new Error("CEP deve ter 8 dígitos");
    }

    console.log(`🔍 Testando CEP: ${cepLimpo}`);
    
    const response = await fetch(`https://viacep.com.br/ws/${cepLimpo}/json/`);
    
    if (!response.ok) {
      throw new Error(`Erro HTTP: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.erro) {
      throw new Error("CEP não encontrado");
    }

    console.log("✅ CEP encontrado:", data);
    return data;
    
  } catch (error) {
    console.error("❌ Erro ao buscar CEP:", error);
    return null;
  }
}

/**
 * Testa múltiplos CEPs
 */
export async function testMultipleCEPs() {
  const testCEPs = [
    "01310-100", // Av. Paulista, São Paulo
    "20040-020", // Centro, Rio de Janeiro
    "70040-010", // Brasília
    "30112-000", // Belo Horizonte
    "80010-000", // Curitiba
  ];

  console.log("🧪 Iniciando teste de múltiplos CEPs...");
  
  for (const cep of testCEPs) {
    await testCEPAPI(cep);
    // Pequena pausa entre as requisições
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log("✅ Teste de múltiplos CEPs concluído");
}

// Para usar no console do navegador:
// import { testCEPAPI, testMultipleCEPs } from './src/utils/testCEP.ts';
// testCEPAPI('01310-100');
// testMultipleCEPs();
