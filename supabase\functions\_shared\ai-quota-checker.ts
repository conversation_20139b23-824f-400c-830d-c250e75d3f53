import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Definição dos tipos
export type AIFeatureType = 'chat' | 'budget' | 'contract' | 'sinapi';

// Quotas por tipo de plano
const AI_QUOTAS = {
  free: {
    chat: 20,      // 20 perguntas/dia
    budget: 1,     // 1 orçamento/dia
    contract: 1,   // 1 análise/dia
    sinapi: 15,    // 15 consultas/dia
  },
  basic: {
    chat: 0,       // Sem IA no plano básico
    budget: 0,
    contract: 0,
    sinapi: 0,
  },
  pro: {
    chat: -1,      // Ilimitado
    budget: -1,
    contract: -1,
    sinapi: -1,
  },
  enterprise: {
    chat: -1,      // Ilimitado
    budget: -1,
    contract: -1,
    sinapi: -1,
  },
  trialing: {
    chat: 3,       // 3 requests/dia (DeepSeek)
    budget: 1,     // 1 orçamento TOTAL (não por dia)
    contract: 0,   // Sem contratos no trial
    sinapi: 15,    // 15 consultas/dia
  }
} as const;

interface QuotaCheckResult {
  canUse: boolean;
  current: number;
  limit: number;
  remaining: number;
  message?: string;
}

interface QuotaIncrementResult {
  success: boolean;
  message?: string;
  newUsage?: any;
}

export class AIQuotaChecker {
  private supabase;

  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  /**
   * Verifica se o usuário pode usar uma funcionalidade de IA
   */
  async checkQuota(userId: string, feature: AIFeatureType): Promise<QuotaCheckResult> {
    try {
      // Buscar assinatura do usuário
      const { data: subscription, error: subError } = await this.supabase
        .from('subscriptions')
        .select('plan_type, status')
        .eq('user_id', userId)
        .single();

      if (subError && subError.code !== 'PGRST116') {
        throw new Error(`Erro ao buscar assinatura: ${subError.message}`);
      }

      // Determinar plano atual
      const currentPlan = subscription?.status === 'trialing' 
        ? 'trialing' 
        : subscription?.plan_type || 'free';

      const planLimits = AI_QUOTAS[currentPlan as keyof typeof AI_QUOTAS];
      const limit = planLimits[feature];

      // Se ilimitado (-1), permitir uso
      if (limit === -1) {
        return {
          canUse: true,
          current: 0,
          limit: -1,
          remaining: -1,
          message: 'Uso ilimitado'
        };
      }

      // Se limite é 0, bloquear
      if (limit === 0) {
        return {
          canUse: false,
          current: 0,
          limit: 0,
          remaining: 0,
          message: feature === 'contract' && currentPlan === 'trialing'
            ? 'Análise de contratos disponível apenas nos planos pagos.'
            : 'Funcionalidade não disponível no seu plano. Faça upgrade.'
        };
      }

      // Para orçamentos em trial, verificar uso total desde o início do trial
      if (feature === 'budget' && currentPlan === 'trialing') {
        const { data: trialUsage, error: trialError } = await this.supabase
          .from('ai_trial_usage')
          .select('total_budget_requests')
          .eq('user_id', userId)
          .single();

        if (trialError && trialError.code !== 'PGRST116') {
          console.error('Erro ao buscar uso do trial:', trialError);
        }

        const current = trialUsage?.total_budget_requests || 0;
        const canUse = current < limit;

        return {
          canUse,
          current,
          limit,
          remaining: Math.max(0, limit - current),
          message: canUse 
            ? '1 orçamento disponível no trial'
            : 'Você já usou seu 1 orçamento do trial. Faça upgrade para orçamentos ilimitados.'
        };
      }

      // Para outras funcionalidades, usar contagem diária normal
      const today = new Date().toISOString().split('T')[0];
      const { data: usage, error: usageError } = await this.supabase
        .from('ai_usage_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      if (usageError && usageError.code !== 'PGRST116') {
        throw new Error(`Erro ao buscar uso: ${usageError.message}`);
      }

      const columnMap = {
        chat: 'chat_requests',
        budget: 'budget_requests',
        contract: 'contract_requests',
        sinapi: 'sinapi_requests',
      };

      const current = usage?.[columnMap[feature]] || 0;
      const remaining = Math.max(0, limit - current);
      const canUse = current < limit;

      return {
        canUse,
        current,
        limit,
        remaining,
        message: canUse 
          ? `${remaining} usos restantes hoje`
          : feature === 'chat' && currentPlan === 'trialing'
            ? 'Limite de 3 requests diários atingido. Aguarde até amanhã ou faça upgrade.'
            : `Limite de ${limit} usos diários atingido. Faça upgrade do seu plano.`
      };

    } catch (error) {
      console.error('Erro ao verificar quota:', error);
      return {
        canUse: false,
        current: 0,
        limit: 0,
        remaining: 0,
        message: 'Erro interno ao verificar quota'
      };
    }
  }

  /**
   * Incrementa o uso de uma funcionalidade de IA
   */
  async incrementUsage(userId: string, feature: AIFeatureType, tokens = 0): Promise<QuotaIncrementResult> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const columnMap = {
        chat: 'chat_requests',
        budget: 'budget_requests',
        contract: 'contract_requests',
        sinapi: 'sinapi_requests',
      };

      // Primeiro, buscar uso atual
      const { data: currentUsage } = await this.supabase
        .from('ai_usage_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      // Preparar dados para upsert
      const updateData = {
        user_id: userId,
        date: today,
        [columnMap[feature]]: (currentUsage?.[columnMap[feature]] || 0) + 1,
        total_tokens: (currentUsage?.total_tokens || 0) + tokens,
      };

      // Fazer upsert
      const { data, error } = await this.supabase
        .from('ai_usage_tracking')
        .upsert(updateData)
        .select()
        .single();

      if (error) {
        throw new Error(`Erro ao incrementar uso: ${error.message}`);
      }

      // Se for orçamento em trial, também atualizar ai_trial_usage
      if (feature === 'budget') {
        const { data: subscription } = await this.supabase
          .from('subscriptions')
          .select('status')
          .eq('user_id', userId)
          .single();

        if (subscription?.status === 'trialing') {
          const { data: trialUsage } = await this.supabase
            .from('ai_trial_usage')
            .select('total_budget_requests')
            .eq('user_id', userId)
            .single();

          const { error: trialError } = await this.supabase
            .from('ai_trial_usage')
            .upsert({
              user_id: userId,
              total_budget_requests: (trialUsage?.total_budget_requests || 0) + 1,
              trial_start_date: new Date().toISOString(),
              trial_end_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            })
            .eq('user_id', userId);

          if (trialError) {
            console.error('Erro ao atualizar uso do trial:', trialError);
          }
        }
      }

      return {
        success: true,
        message: 'Uso incrementado com sucesso',
        newUsage: data
      };

    } catch (error) {
      console.error('Erro ao incrementar uso:', error);
      return {
        success: false,
        message: `Erro ao incrementar uso: ${error.message}`
      };
    }
  }

  /**
   * Função helper para verificar e incrementar em uma operação
   */
  async checkAndIncrement(userId: string, feature: AIFeatureType, tokens = 0): Promise<{
    allowed: boolean;
    quotaResult: QuotaCheckResult;
    incrementResult?: QuotaIncrementResult;
  }> {
    // Primeiro verificar quota
    const quotaResult = await this.checkQuota(userId, feature);

    if (!quotaResult.canUse) {
      return {
        allowed: false,
        quotaResult
      };
    }

    // Se pode usar, incrementar
    const incrementResult = await this.incrementUsage(userId, feature, tokens);

    return {
      allowed: incrementResult.success,
      quotaResult,
      incrementResult
    };
  }
}

/**
 * Função helper para uso nas Edge Functions
 */
export async function checkAIQuota(
  supabaseUrl: string,
  supabaseKey: string,
  userId: string,
  feature: AIFeatureType
): Promise<QuotaCheckResult> {
  const checker = new AIQuotaChecker(supabaseUrl, supabaseKey);
  return await checker.checkQuota(userId, feature);
}

/**
 * Função helper para incrementar uso nas Edge Functions
 */
export async function incrementAIUsage(
  supabaseUrl: string,
  supabaseKey: string,
  userId: string,
  feature: AIFeatureType,
  tokens = 0
): Promise<QuotaIncrementResult> {
  const checker = new AIQuotaChecker(supabaseUrl, supabaseKey);
  return await checker.incrementUsage(userId, feature, tokens);
}

/**
 * Função helper para verificar e incrementar em uma operação
 */
export async function checkAndIncrementAIUsage(
  supabaseUrl: string,
  supabaseKey: string,
  userId: string,
  feature: AIFeatureType,
  tokens = 0
): Promise<{
  allowed: boolean;
  quotaResult: QuotaCheckResult;
  incrementResult?: QuotaIncrementResult;
}> {
  const checker = new AIQuotaChecker(supabaseUrl, supabaseKey);
  return await checker.checkAndIncrement(userId, feature, tokens);
}