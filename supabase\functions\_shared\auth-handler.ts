/**
 * 🔐 Sistema de Autenticação e Autorização - ObrasAI Edge Functions
 * 
 * Middleware centralizado para autenticação JWT e validação de permissões
 */

import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { createErrorResponse, ERROR_CODES } from './response-handler.ts';

// Tipos para autenticação
export interface AuthUser {
  id: string;
  email: string;
  role?: string;
  tenant_id?: string;
  permissions?: string[];
}

export interface AuthContext {
  user: AuthUser;
  supabase: any;
  isAuthenticated: boolean;
  hasPermission: (permission: string) => boolean;
  isTenantMember: (tenantId: string) => boolean;
}

// Configuração do Supabase
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

/**
 * Extrai token JWT do header Authorization
 */
export function extractToken(req: Request): string | null {
  const authHeader = req.headers.get('authorization');
  if (!authHeader) return null;
  
  // Formato: "Bearer <token>"
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') return null;
  
  return parts[1];
}

/**
 * Valida token JWT e retorna dados do usuário
 */
export async function validateToken(token: string): Promise<AuthUser | null> {
  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Verifica o token JWT
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      console.error('Token validation error:', error);
      return null;
    }
    
    // Busca informações adicionais do usuário
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role, tenant_id, permissions')
      .eq('id', user.id)
      .single();
    
    if (profileError) {
      console.error('Profile fetch error:', profileError);
      // Continua mesmo sem profile, usando dados básicos
    }
    
    return {
      id: user.id,
      email: user.email!,
      role: profile?.role || 'user',
      tenant_id: profile?.tenant_id,
      permissions: profile?.permissions || []
    };
  } catch (error) {
    console.error('Token validation exception:', error);
    return null;
  }
}

/**
 * Middleware de autenticação obrigatória
 */
export async function requireAuth(req: Request): Promise<{
  success: boolean;
  context?: AuthContext;
  response?: Response;
}> {
  const origin = req.headers.get('origin');
  const token = extractToken(req);
  
  if (!token) {
    return {
      success: false,
      response: createErrorResponse(
        'Token de autenticação necessário',
        'UNAUTHORIZED',
        { origin }
      )
    };
  }
  
  const user = await validateToken(token);
  if (!user) {
    return {
      success: false,
      response: createErrorResponse(
        'Token inválido ou expirado',
        'INVALID_TOKEN',
        { origin }
      )
    };
  }
  
  // Cria cliente Supabase com o token do usuário
  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    global: {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  });
  
  const context: AuthContext = {
    user,
    supabase,
    isAuthenticated: true,
    hasPermission: (permission: string) => {
      return user.permissions?.includes(permission) || 
             user.permissions?.includes('*') ||
             user.role === 'admin';
    },
    isTenantMember: (tenantId: string) => {
      return user.tenant_id === tenantId || user.role === 'admin';
    }
  };
  
  return {
    success: true,
    context
  };
}

/**
 * Middleware de autenticação opcional
 */
export async function optionalAuth(req: Request): Promise<AuthContext | null> {
  const token = extractToken(req);
  if (!token) return null;
  
  const user = await validateToken(token);
  if (!user) return null;
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    global: {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  });
  
  return {
    user,
    supabase,
    isAuthenticated: true,
    hasPermission: (permission: string) => {
      return user.permissions?.includes(permission) || 
             user.permissions?.includes('*') ||
             user.role === 'admin';
    },
    isTenantMember: (tenantId: string) => {
      return user.tenant_id === tenantId || user.role === 'admin';
    }
  };
}

/**
 * Middleware de verificação de permissões
 */
export function requirePermission(permission: string) {
  return (context: AuthContext): boolean => {
    return context.hasPermission(permission);
  };
}

/**
 * Middleware de verificação de tenant
 */
export function requireTenant(tenantId: string) {
  return (context: AuthContext): boolean => {
    return context.isTenantMember(tenantId);
  };
}

/**
 * Middleware de verificação de role
 */
export function requireRole(role: string) {
  return (context: AuthContext): boolean => {
    return context.user.role === role || context.user.role === 'admin';
  };
}

/**
 * Valida se usuário tem acesso a um tenant específico
 */
export async function validateTenantAccess(
  context: AuthContext,
  tenantId: string
): Promise<{
  success: boolean;
  response?: Response;
}> {
  if (!context.isTenantMember(tenantId)) {
    return {
      success: false,
      response: createErrorResponse(
        'Acesso negado ao tenant especificado',
        'FORBIDDEN'
      )
    };
  }
  
  return { success: true };
}

/**
 * Extrai tenant_id da requisição (query param ou body)
 */
export async function extractTenantId(req: Request): Promise<string | null> {
  // Tenta extrair da URL
  const url = new URL(req.url);
  const tenantFromQuery = url.searchParams.get('tenant_id');
  if (tenantFromQuery) return tenantFromQuery;
  
  // Tenta extrair do body (se for POST/PUT/PATCH)
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    try {
      const body = await req.clone().json();
      if (body.tenant_id) return body.tenant_id;
    } catch {
      // Ignora erro de parsing
    }
  }
  
  return null;
}

/**
 * Middleware completo de autenticação + tenant
 */
export async function requireAuthAndTenant(req: Request): Promise<{
  success: boolean;
  context?: AuthContext;
  tenantId?: string;
  response?: Response;
}> {
  // Primeiro, valida autenticação
  const authResult = await requireAuth(req);
  if (!authResult.success) {
    return authResult;
  }
  
  const context = authResult.context!;
  
  // Extrai tenant_id da requisição
  const tenantId = await extractTenantId(req);
  if (!tenantId) {
    return {
      success: false,
      response: createErrorResponse(
        'tenant_id é obrigatório',
        'MISSING_REQUIRED_FIELD'
      )
    };
  }
  
  // Valida acesso ao tenant
  const tenantResult = await validateTenantAccess(context, tenantId);
  if (!tenantResult.success) {
    return {
      success: false,
      response: tenantResult.response
    };
  }
  
  return {
    success: true,
    context,
    tenantId
  };
}

/**
 * Cria cliente Supabase com autenticação de serviço
 */
export function createServiceClient() {
  return createClient(supabaseUrl, supabaseServiceKey);
}

/**
 * Cria cliente Supabase com token do usuário
 */
export function createUserClient(token: string) {
  return createClient(supabaseUrl, supabaseServiceKey, {
    global: {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  });
}

/**
 * Verifica se usuário é admin
 */
export function isAdmin(context: AuthContext): boolean {
  return context.user.role === 'admin';
}

/**
 * Verifica se usuário é owner do tenant
 */
export function isTenantOwner(context: AuthContext, tenantId?: string): boolean {
  return context.user.role === 'owner' && 
         (!tenantId || context.user.tenant_id === tenantId);
}

/**
 * Lista de permissões padrão do sistema
 */
export const PERMISSIONS = {
  // Obras
  'obras:read': 'Visualizar obras',
  'obras:write': 'Criar/editar obras',
  'obras:delete': 'Excluir obras',
  
  // Contratos
  'contratos:read': 'Visualizar contratos',
  'contratos:write': 'Criar/editar contratos',
  'contratos:delete': 'Excluir contratos',
  
  // Despesas
  'despesas:read': 'Visualizar despesas',
  'despesas:write': 'Criar/editar despesas',
  'despesas:delete': 'Excluir despesas',
  
  // Fornecedores
  'fornecedores:read': 'Visualizar fornecedores',
  'fornecedores:write': 'Criar/editar fornecedores',
  'fornecedores:delete': 'Excluir fornecedores',
  
  // IA
  'ia:chat': 'Usar chat IA',
  'ia:orcamento': 'Gerar orçamentos IA',
  'ia:analise': 'Análises IA',
  
  // Admin
  'admin:users': 'Gerenciar usuários',
  'admin:tenants': 'Gerenciar tenants',
  'admin:system': 'Configurações do sistema',
  
  // Wildcard
  '*': 'Todas as permissões'
} as const;

export type Permission = keyof typeof PERMISSIONS;
