/**
 * 🚀 Template Base para Edge Functions - ObrasAI 2.2
 * 
 * Template padronizado com todas as melhores práticas implementadas
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { z } from "https://deno.land/x/zod@v3.23.8/mod.ts";

// Imports dos utilitários padronizados
import { 
  createSuccessResponse, 
  createErrorResponse, 
  createOptionsResponse,
  createMethodNotAllowedResponse,
  handleUnexpectedError,
  ERROR_CODES
} from './response-handler.ts';

import { 
  requireAuth, 
  optionalAuth, 
  requireAuthAndTenant,
  type AuthContext 
} from './auth-handler.ts';

import { 
  validateData, 
  SCHEMAS 
} from './validation-schemas.ts';

import { 
  createFunctionLogger, 
  createTimer,
  type Logger 
} from './logger.ts';

import { 
  checkRateLimit,
  getSecureCorsHeaders 
} from './cors.ts';

// ========================================
// CONFIGURAÇÃO DA FUNÇÃO
// ========================================

interface FunctionConfig {
  name: string;
  version: string;
  requiresAuth: boolean;
  requiresTenant: boolean;
  allowedMethods: string[];
  rateLimit?: {
    maxRequests: number;
    windowMs: number;
  };
  validation?: {
    bodySchema?: z.ZodTypeAny;
    querySchema?: z.ZodTypeAny;
  };
}

// ========================================
// FACTORY PARA EDGE FUNCTIONS
// ========================================

/**
 * Cria uma Edge Function padronizada com todas as validações
 */
export function createEdgeFunction<TBody = any, TQuery = any>(
  config: FunctionConfig,
  handler: (context: {
    req: Request;
    body?: TBody;
    query?: TQuery;
    auth?: AuthContext;
    tenantId?: string;
    logger: Logger;
    requestId: string;
  }) => Promise<Response>
) {
  return async (req: Request): Promise<Response> => {
    const requestId = crypto.randomUUID();
    const timer = createTimer();
    const logger = createFunctionLogger(config.name, requestId);
    const origin = req.headers.get('origin');

    try {
      // Log início da requisição
      logger.requestStart(req.method, req.url, {
        userAgent: req.headers.get('user-agent'),
        contentLength: req.headers.get('content-length'),
        version: config.version
      });

      // 1. CORS Preflight
      if (req.method === 'OPTIONS') {
        logger.debug('Handling CORS preflight');
        return createOptionsResponse(origin);
      }

      // 2. Validação de método
      if (!config.allowedMethods.includes(req.method)) {
        logger.warn('Method not allowed', { method: req.method });
        return createMethodNotAllowedResponse(config.allowedMethods, origin);
      }

      // 3. Rate Limiting
      if (config.rateLimit) {
        const clientIP = req.headers.get('x-forwarded-for') || 
                        req.headers.get('x-real-ip') || 
                        'unknown';
        
        const rateLimitKey = `${config.name}:${clientIP}`;
        const isAllowed = checkRateLimit(
          rateLimitKey, 
          config.rateLimit.maxRequests, 
          config.rateLimit.windowMs
        );
        
        if (!isAllowed) {
          logger.warn('Rate limit exceeded', { clientIP, rateLimitKey });
          return createErrorResponse(
            'Muitas requisições. Tente novamente em alguns minutos.',
            'RATE_LIMIT_EXCEEDED',
            { origin }
          );
        }
      }

      // 4. Autenticação
      let authContext: AuthContext | undefined;
      let tenantId: string | undefined;

      if (config.requiresAuth) {
        if (config.requiresTenant) {
          const authResult = await requireAuthAndTenant(req);
          if (!authResult.success) {
            logger.warn('Authentication/tenant validation failed');
            return authResult.response!;
          }
          authContext = authResult.context;
          tenantId = authResult.tenantId;
        } else {
          const authResult = await requireAuth(req);
          if (!authResult.success) {
            logger.warn('Authentication failed');
            return authResult.response!;
          }
          authContext = authResult.context;
        }
        
        logger.info('User authenticated', {
          userId: authContext.user.id,
          role: authContext.user.role,
          tenantId: authContext.user.tenant_id
        });
      } else {
        // Autenticação opcional
        authContext = await optionalAuth(req);
        if (authContext) {
          logger.info('Optional auth successful', {
            userId: authContext.user.id
          });
        }
      }

      // 5. Validação de entrada
      let body: TBody | undefined;
      let query: TQuery | undefined;

      // Validação do body
      if (config.validation?.bodySchema && ['POST', 'PUT', 'PATCH'].includes(req.method)) {
        try {
          const rawBody = await req.json();
          const validation = validateData(rawBody, config.validation.bodySchema);
          
          if (!validation.success) {
            logger.warn('Body validation failed', { errors: validation.errors });
            return createErrorResponse(
              'Dados inválidos',
              'VALIDATION_ERROR',
              { origin, details: validation.errors }
            );
          }
          
          body = validation.data;
          logger.debug('Body validation successful');
        } catch (error) {
          logger.error('Failed to parse request body', error);
          return createErrorResponse(
            'Formato de dados inválido',
            'INVALID_INPUT',
            { origin }
          );
        }
      }

      // Validação da query string
      if (config.validation?.querySchema) {
        const url = new URL(req.url);
        const queryParams = Object.fromEntries(url.searchParams.entries());
        
        const validation = validateData(queryParams, config.validation.querySchema);
        if (!validation.success) {
          logger.warn('Query validation failed', { errors: validation.errors });
          return createErrorResponse(
            'Parâmetros de consulta inválidos',
            'VALIDATION_ERROR',
            { origin, details: validation.errors }
          );
        }
        
        query = validation.data;
        logger.debug('Query validation successful');
      }

      // 6. Execução do handler
      logger.info('Executing function handler');
      const response = await handler({
        req,
        body,
        query,
        auth: authContext,
        tenantId,
        logger,
        requestId
      });

      // 7. Log de sucesso
      const duration = timer.getDuration();
      logger.requestEnd(response.status, duration);
      
      return response;

    } catch (error) {
      // 8. Tratamento de erros não capturados
      const duration = timer.getDuration();
      logger.error('Unhandled error in function', error, { duration });
      
      return handleUnexpectedError(error, origin, requestId);
    }
  };
}

// ========================================
// HELPERS ESPECÍFICOS
// ========================================

/**
 * Configuração padrão para funções de IA
 */
export const AI_FUNCTION_CONFIG: Partial<FunctionConfig> = {
  requiresAuth: true,
  requiresTenant: false,
  allowedMethods: ['POST'],
  rateLimit: {
    maxRequests: 50,
    windowMs: 60000 // 1 minuto
  }
};

/**
 * Configuração padrão para funções CRUD
 */
export const CRUD_FUNCTION_CONFIG: Partial<FunctionConfig> = {
  requiresAuth: true,
  requiresTenant: true,
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE'],
  rateLimit: {
    maxRequests: 100,
    windowMs: 60000 // 1 minuto
  }
};

/**
 * Configuração padrão para funções públicas
 */
export const PUBLIC_FUNCTION_CONFIG: Partial<FunctionConfig> = {
  requiresAuth: false,
  requiresTenant: false,
  allowedMethods: ['POST'],
  rateLimit: {
    maxRequests: 20,
    windowMs: 60000 // 1 minuto
  }
};

// ========================================
// EXEMPLO DE USO
// ========================================

/*
// Exemplo de função IA
export default createEdgeFunction({
  name: 'ai-chat',
  version: '1.0.0',
  ...AI_FUNCTION_CONFIG,
  validation: {
    bodySchema: SCHEMAS.aiChat
  }
}, async ({ body, auth, logger }) => {
  logger.info('Processing AI chat request');
  
  // Lógica da função aqui
  const result = await processAIChat(body);
  
  return createSuccessResponse(result);
});

// Exemplo de função CRUD
export default createEdgeFunction({
  name: 'obras-crud',
  version: '1.0.0',
  ...CRUD_FUNCTION_CONFIG,
  allowedMethods: ['GET', 'POST'],
  validation: {
    bodySchema: SCHEMAS.obra,
    querySchema: SCHEMAS.pagination
  }
}, async ({ req, body, query, auth, tenantId, logger }) => {
  if (req.method === 'GET') {
    const obras = await getObras(tenantId, query);
    return createPaginatedResponse(obras.data, obras.pagination);
  }
  
  if (req.method === 'POST') {
    const obra = await createObra(tenantId, body, auth.user.id);
    return createSuccessResponse(obra);
  }
});
*/
