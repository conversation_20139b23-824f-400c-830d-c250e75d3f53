/**
 * 📝 Sistema de Logging Estruturado - ObrasAI Edge Functions
 * 
 * Logger centralizado com níveis, contexto e formatação consistente
 */

// Níveis de log
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

// Interface para entrada de log
export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  functionName?: string;
  requestId?: string;
  userId?: string;
  tenantId?: string;
  context?: Record<string, any>;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
  performance?: {
    duration: number;
    memoryUsage?: number;
  };
  metadata?: Record<string, any>;
}

// Configuração do logger
interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableStructured: boolean;
  includeStack: boolean;
  maxContextSize: number;
}

// Configuração padrão
const defaultConfig: LoggerConfig = {
  level: LogLevel.INFO,
  enableConsole: true,
  enableStructured: true,
  includeStack: true,
  maxContextSize: 1000
};

// Configuração atual (pode ser alterada via env vars)
let currentConfig: LoggerConfig = {
  ...defaultConfig,
  level: getLogLevelFromEnv()
};

/**
 * Obtém nível de log das variáveis de ambiente
 */
function getLogLevelFromEnv(): LogLevel {
  const envLevel = Deno.env.get('LOG_LEVEL')?.toUpperCase();
  switch (envLevel) {
    case 'DEBUG': return LogLevel.DEBUG;
    case 'INFO': return LogLevel.INFO;
    case 'WARN': return LogLevel.WARN;
    case 'ERROR': return LogLevel.ERROR;
    case 'FATAL': return LogLevel.FATAL;
    default: return LogLevel.INFO;
  }
}

/**
 * Converte LogLevel para string
 */
function logLevelToString(level: LogLevel): string {
  switch (level) {
    case LogLevel.DEBUG: return 'DEBUG';
    case LogLevel.INFO: return 'INFO';
    case LogLevel.WARN: return 'WARN';
    case LogLevel.ERROR: return 'ERROR';
    case LogLevel.FATAL: return 'FATAL';
    default: return 'UNKNOWN';
  }
}

/**
 * Sanitiza contexto para evitar logs muito grandes
 */
function sanitizeContext(context: any, maxSize: number): any {
  const str = JSON.stringify(context);
  if (str.length <= maxSize) return context;
  
  return {
    ...context,
    _truncated: true,
    _originalSize: str.length,
    _maxSize: maxSize
  };
}

/**
 * Formata erro para log
 */
function formatError(error: unknown): LogEntry['error'] | undefined {
  if (!error) return undefined;
  
  if (error instanceof Error) {
    return {
      name: error.name,
      message: error.message,
      stack: currentConfig.includeStack ? error.stack : undefined
    };
  }
  
  return {
    name: 'UnknownError',
    message: String(error)
  };
}

/**
 * Cria entrada de log estruturada
 */
function createLogEntry(
  level: LogLevel,
  message: string,
  options: {
    functionName?: string;
    requestId?: string;
    userId?: string;
    tenantId?: string;
    context?: Record<string, any>;
    error?: unknown;
    performance?: LogEntry['performance'];
    metadata?: Record<string, any>;
  } = {}
): LogEntry {
  return {
    level,
    message,
    timestamp: new Date().toISOString(),
    functionName: options.functionName,
    requestId: options.requestId,
    userId: options.userId,
    tenantId: options.tenantId,
    context: options.context ? sanitizeContext(options.context, currentConfig.maxContextSize) : undefined,
    error: formatError(options.error),
    performance: options.performance,
    metadata: options.metadata
  };
}

/**
 * Escreve log no console
 */
function writeToConsole(entry: LogEntry): void {
  const levelStr = logLevelToString(entry.level);
  const prefix = `[${entry.timestamp}] ${levelStr}`;
  
  if (currentConfig.enableStructured) {
    // Log estruturado (JSON)
    console.log(JSON.stringify(entry));
  } else {
    // Log simples para desenvolvimento
    let message = `${prefix} ${entry.message}`;
    
    if (entry.functionName) {
      message += ` [${entry.functionName}]`;
    }
    
    if (entry.requestId) {
      message += ` (${entry.requestId})`;
    }
    
    console.log(message);
    
    if (entry.context) {
      console.log('Context:', entry.context);
    }
    
    if (entry.error) {
      console.error('Error:', entry.error);
    }
  }
}

/**
 * Verifica se deve logar baseado no nível
 */
function shouldLog(level: LogLevel): boolean {
  return level >= currentConfig.level;
}

/**
 * Classe principal do Logger
 */
export class Logger {
  private functionName?: string;
  private requestId?: string;
  private userId?: string;
  private tenantId?: string;
  private baseContext?: Record<string, any>;

  constructor(options: {
    functionName?: string;
    requestId?: string;
    userId?: string;
    tenantId?: string;
    context?: Record<string, any>;
  } = {}) {
    this.functionName = options.functionName;
    this.requestId = options.requestId;
    this.userId = options.userId;
    this.tenantId = options.tenantId;
    this.baseContext = options.context;
  }

  /**
   * Cria novo logger com contexto adicional
   */
  child(options: {
    functionName?: string;
    requestId?: string;
    userId?: string;
    tenantId?: string;
    context?: Record<string, any>;
  }): Logger {
    return new Logger({
      functionName: options.functionName || this.functionName,
      requestId: options.requestId || this.requestId,
      userId: options.userId || this.userId,
      tenantId: options.tenantId || this.tenantId,
      context: { ...this.baseContext, ...options.context }
    });
  }

  /**
   * Log genérico
   */
  private log(
    level: LogLevel,
    message: string,
    options: {
      context?: Record<string, any>;
      error?: unknown;
      performance?: LogEntry['performance'];
      metadata?: Record<string, any>;
    } = {}
  ): void {
    if (!shouldLog(level)) return;

    const entry = createLogEntry(level, message, {
      functionName: this.functionName,
      requestId: this.requestId,
      userId: this.userId,
      tenantId: this.tenantId,
      context: { ...this.baseContext, ...options.context },
      error: options.error,
      performance: options.performance,
      metadata: options.metadata
    });

    if (currentConfig.enableConsole) {
      writeToConsole(entry);
    }
  }

  /**
   * Log de debug
   */
  debug(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, { context });
  }

  /**
   * Log de informação
   */
  info(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, { context });
  }

  /**
   * Log de aviso
   */
  warn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, { context });
  }

  /**
   * Log de erro
   */
  error(message: string, error?: unknown, context?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, { error, context });
  }

  /**
   * Log de erro fatal
   */
  fatal(message: string, error?: unknown, context?: Record<string, any>): void {
    this.log(LogLevel.FATAL, message, { error, context });
  }

  /**
   * Log de performance
   */
  performance(message: string, duration: number, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, {
      performance: { duration },
      context
    });
  }

  /**
   * Log de início de requisição
   */
  requestStart(method: string, url: string, context?: Record<string, any>): void {
    this.info('Request started', {
      method,
      url,
      ...context
    });
  }

  /**
   * Log de fim de requisição
   */
  requestEnd(status: number, duration: number, context?: Record<string, any>): void {
    this.info('Request completed', {
      status,
      duration,
      ...context
    });
  }
}

/**
 * Logger global padrão
 */
export const logger = new Logger();

/**
 * Cria logger para uma Edge Function específica
 */
export function createFunctionLogger(functionName: string, requestId?: string): Logger {
  return new Logger({ functionName, requestId });
}

/**
 * Cria logger com contexto de autenticação
 */
export function createAuthLogger(
  functionName: string,
  requestId: string,
  userId?: string,
  tenantId?: string
): Logger {
  return new Logger({ functionName, requestId, userId, tenantId });
}

/**
 * Middleware de logging para requisições
 */
export function logRequest(
  req: Request,
  functionName: string,
  requestId: string
): Logger {
  const logger = createFunctionLogger(functionName, requestId);
  
  logger.requestStart(req.method, req.url, {
    userAgent: req.headers.get('user-agent'),
    origin: req.headers.get('origin'),
    contentType: req.headers.get('content-type')
  });
  
  return logger;
}

/**
 * Configura o logger
 */
export function configureLogger(config: Partial<LoggerConfig>): void {
  currentConfig = { ...currentConfig, ...config };
}

/**
 * Obtém configuração atual do logger
 */
export function getLoggerConfig(): LoggerConfig {
  return { ...currentConfig };
}

/**
 * Utilitários de timing para performance
 */
export class Timer {
  private startTime: number;

  constructor() {
    this.startTime = performance.now();
  }

  /**
   * Obtém duração em milissegundos
   */
  getDuration(): number {
    return performance.now() - this.startTime;
  }

  /**
   * Reinicia o timer
   */
  reset(): void {
    this.startTime = performance.now();
  }
}

/**
 * Cria timer para medir performance
 */
export function createTimer(): Timer {
  return new Timer();
}
