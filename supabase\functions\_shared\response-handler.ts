/**
 * 🚀 Padronização de Respostas - ObrasAI Edge Functions
 * 
 * Sistema centralizado para respostas consistentes em todas as Edge Functions
 */

import { getSecureCorsHeaders } from './cors.ts';

// Tipos padronizados para respostas
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId?: string;
    version?: string;
  };
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Códigos de erro padronizados
export const ERROR_CODES = {
  // Validação
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // Autenticação/Autorização
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  INVALID_TOKEN: 'INVALID_TOKEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  
  // Recursos
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  CONFLICT: 'CONFLICT',
  
  // Sistema
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_API_ERROR: 'EXTERNAL_API_ERROR',
  
  // IA/ML
  AI_QUOTA_EXCEEDED: 'AI_QUOTA_EXCEEDED',
  AI_SERVICE_ERROR: 'AI_SERVICE_ERROR',
  PROCESSING_ERROR: 'PROCESSING_ERROR',
  
  // Negócio
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  TENANT_NOT_FOUND: 'TENANT_NOT_FOUND'
} as const;

// Mapeamento de códigos para status HTTP
const ERROR_STATUS_MAP: Record<string, number> = {
  [ERROR_CODES.VALIDATION_ERROR]: 400,
  [ERROR_CODES.INVALID_INPUT]: 400,
  [ERROR_CODES.MISSING_REQUIRED_FIELD]: 400,
  
  [ERROR_CODES.UNAUTHORIZED]: 401,
  [ERROR_CODES.INVALID_TOKEN]: 401,
  [ERROR_CODES.TOKEN_EXPIRED]: 401,
  
  [ERROR_CODES.FORBIDDEN]: 403,
  [ERROR_CODES.INSUFFICIENT_PERMISSIONS]: 403,
  
  [ERROR_CODES.NOT_FOUND]: 404,
  [ERROR_CODES.TENANT_NOT_FOUND]: 404,
  
  [ERROR_CODES.ALREADY_EXISTS]: 409,
  [ERROR_CODES.CONFLICT]: 409,
  [ERROR_CODES.BUSINESS_RULE_VIOLATION]: 409,
  
  [ERROR_CODES.RATE_LIMIT_EXCEEDED]: 429,
  [ERROR_CODES.QUOTA_EXCEEDED]: 429,
  [ERROR_CODES.AI_QUOTA_EXCEEDED]: 429,
  
  [ERROR_CODES.INTERNAL_ERROR]: 500,
  [ERROR_CODES.DATABASE_ERROR]: 500,
  [ERROR_CODES.AI_SERVICE_ERROR]: 500,
  [ERROR_CODES.PROCESSING_ERROR]: 500,
  
  [ERROR_CODES.SERVICE_UNAVAILABLE]: 503,
  [ERROR_CODES.EXTERNAL_API_ERROR]: 503,
};

/**
 * Cria uma resposta de sucesso padronizada
 */
export function createSuccessResponse<T>(
  data: T,
  options: {
    origin?: string | null;
    requestId?: string;
    meta?: Record<string, any>;
  } = {}
): Response {
  const response: ApiResponse<T> = {
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      requestId: options.requestId || crypto.randomUUID(),
      version: '2.2.0',
      ...options.meta
    }
  };

  return new Response(JSON.stringify(response), {
    status: 200,
    headers: {
      ...getSecureCorsHeaders(options.origin),
      'Content-Type': 'application/json',
      'X-Request-ID': response.meta!.requestId!
    }
  });
}

/**
 * Cria uma resposta de erro padronizada
 */
export function createErrorResponse(
  message: string,
  code: keyof typeof ERROR_CODES,
  options: {
    origin?: string | null;
    requestId?: string;
    details?: any;
    status?: number;
  } = {}
): Response {
  const status = options.status || ERROR_STATUS_MAP[code] || 500;
  
  const response: ApiResponse = {
    success: false,
    error: {
      message,
      code,
      details: options.details
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId: options.requestId || crypto.randomUUID(),
      version: '2.2.0'
    }
  };

  return new Response(JSON.stringify(response), {
    status,
    headers: {
      ...getSecureCorsHeaders(options.origin),
      'Content-Type': 'application/json',
      'X-Request-ID': response.meta!.requestId!
    }
  });
}

/**
 * Cria uma resposta paginada padronizada
 */
export function createPaginatedResponse<T>(
  data: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
  },
  options: {
    origin?: string | null;
    requestId?: string;
    meta?: Record<string, any>;
  } = {}
): Response {
  const totalPages = Math.ceil(pagination.total / pagination.limit);
  
  const response: PaginatedResponse<T> = {
    success: true,
    data,
    pagination: {
      ...pagination,
      totalPages,
      hasNext: pagination.page < totalPages,
      hasPrev: pagination.page > 1
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId: options.requestId || crypto.randomUUID(),
      version: '2.2.0',
      ...options.meta
    }
  };

  return new Response(JSON.stringify(response), {
    status: 200,
    headers: {
      ...getSecureCorsHeaders(options.origin),
      'Content-Type': 'application/json',
      'X-Request-ID': response.meta!.requestId!
    }
  });
}

/**
 * Cria resposta para requisições OPTIONS (preflight)
 */
export function createOptionsResponse(origin?: string | null): Response {
  return new Response('ok', {
    status: 200,
    headers: getSecureCorsHeaders(origin)
  });
}

/**
 * Cria resposta para métodos não permitidos
 */
export function createMethodNotAllowedResponse(
  allowedMethods: string[] = ['POST'],
  origin?: string | null
): Response {
  return createErrorResponse(
    `Método não permitido. Métodos aceitos: ${allowedMethods.join(', ')}`,
    'VALIDATION_ERROR',
    { origin, status: 405 }
  );
}

/**
 * Wrapper para tratamento de erros não capturados
 */
export function handleUnexpectedError(
  error: unknown,
  origin?: string | null,
  requestId?: string
): Response {
  console.error('Unexpected error:', error);
  
  // Em desenvolvimento, incluir stack trace
  const isDevelopment = Deno.env.get('ENVIRONMENT') === 'development';
  const details = isDevelopment ? {
    stack: error instanceof Error ? error.stack : String(error)
  } : undefined;

  return createErrorResponse(
    'Erro interno do servidor',
    'INTERNAL_ERROR',
    { origin, requestId, details }
  );
}

/**
 * Middleware para logging de requisições
 */
export function logRequest(
  req: Request,
  functionName: string,
  requestId: string
): void {
  const timestamp = new Date().toISOString();
  const method = req.method;
  const url = req.url;
  const userAgent = req.headers.get('user-agent') || 'unknown';
  const origin = req.headers.get('origin');
  
  console.log(JSON.stringify({
    timestamp,
    requestId,
    functionName,
    method,
    url,
    origin,
    userAgent,
    type: 'request'
  }));
}

/**
 * Middleware para logging de respostas
 */
export function logResponse(
  response: Response,
  functionName: string,
  requestId: string,
  duration: number
): void {
  const timestamp = new Date().toISOString();
  
  console.log(JSON.stringify({
    timestamp,
    requestId,
    functionName,
    status: response.status,
    duration,
    type: 'response'
  }));
}

/**
 * Tipos exportados para TypeScript
 */
export type ErrorCode = keyof typeof ERROR_CODES;
export type ResponseOptions = {
  origin?: string | null;
  requestId?: string;
  meta?: Record<string, any>;
};
export type ErrorResponseOptions = ResponseOptions & {
  details?: any;
  status?: number;
};
