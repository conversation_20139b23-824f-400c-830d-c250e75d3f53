/**
 * 🔍 Schemas de Validação Centralizados - ObrasAI Edge Functions
 *
 * Schemas Zod padronizados para validação consistente em todas as Edge Functions
 */

import { z } from "https://deno.land/x/zod@v3.23.8/mod.ts";

// ========================================
// SCHEMAS BASE
// ========================================

// UUID válido
export const uuidSchema = z.string().uuid({ message: "UUID inválido" });

// Email válido
export const emailSchema = z
  .string()
  .email({ message: "Email inválido" })
  .max(255, "Email muito longo");

// Telefone brasileiro
export const phoneSchema = z
  .string()
  .regex(/^(\+55\s?)?(\(?\d{2}\)?\s?)?\d{4,5}-?\d{4}$/, "Telefone inválido")
  .optional();

// CPF brasileiro
export const cpfSchema = z
  .string()
  .regex(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/, "CPF inválido")
  .optional();

// CNPJ brasileiro
export const cnpjSchema = z
  .string()
  .regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, "CNPJ inválido")
  .optional();

// CEP brasileiro
export const cepSchema = z
  .string()
  .regex(/^\d{5}-?\d{3}$/, "CEP inválido")
  .optional();

// Data ISO
export const dateSchema = z
  .string()
  .datetime({ message: "Data inválida" })
  .optional();

// Valor monetário
export const currencySchema = z
  .number()
  .min(0, "Valor deve ser positivo")
  .max(999999999.99, "Valor excede o limite");

// ========================================
// SCHEMAS DE ENTIDADES
// ========================================

// Lead/Prospect
export const leadSchema = z.object({
  email: emailSchema,
  nome: z.string().min(1, "Nome é obrigatório").max(255).optional(),
  telefone: phoneSchema,
  empresa: z.string().max(255).optional(),
  cargo: z.string().max(255).optional(),
  tipo_empresa: z
    .enum(["construtora", "engenharia", "arquitetura", "individual", "outro"])
    .optional(),
  porte_empresa: z.enum(["micro", "pequena", "media", "grande"]).optional(),
  numero_obras_mes: z.number().min(0).max(1000).optional(),
  orcamento_mensal: currencySchema.optional(),
  interesse_nivel: z
    .enum(["baixo", "medio", "alto", "muito_alto"])
    .default("medio"),
  origem: z.string().default("landing_page"),
  como_conheceu: z.string().max(500).optional(),
  principal_desafio: z.string().max(1000).optional(),
  previsao_inicio: z.string().max(100).optional(),
  observacoes: z.string().max(2000).optional(),
});

// Enum para tipos de projeto (condomínios)
export const tipoProjetoSchema = z.enum(
  ["UNICO", "CONDOMINIO_MASTER", "UNIDADE_CONDOMINIO"],
  {
    errorMap: () => ({ message: "Tipo de projeto inválido" }),
  }
);

// Obra
export const obraSchema = z
  .object({
    nome: z.string().min(1, "Nome é obrigatório").max(255),
    endereco: z.string().min(1, "Endereço é obrigatório").max(500),
    cidade: z.string().min(1, "Cidade é obrigatória").max(100),
    estado: z.string().length(2, "Estado deve ter 2 caracteres"),
    cep: cepSchema.refine((val) => val !== undefined, "CEP é obrigatório"),
    tipo_obra: z
      .enum([
        "RESIDENCIAL",
        "COMERCIAL",
        "INDUSTRIAL",
        "INFRAESTRUTURA",
        "REFORMA",
        "OUTRO",
      ])
      .optional(),
    padrao_obra: z.enum(["BAIXO", "MEDIO", "ALTO", "LUXO"]).optional(),
    area_construcao: z
      .number()
      .min(1, "Área deve ser positiva")
      .max(100000)
      .optional(),
    orcamento: currencySchema.optional(),
    data_inicio: dateSchema,
    data_prevista_termino: dateSchema,
    status: z
      .enum([
        "PLANEJAMENTO",
        "EM_ANDAMENTO",
        "PAUSADA",
        "CONCLUIDA",
        "CANCELADA",
      ])
      .default("PLANEJAMENTO")
      .optional(),
    // Campos para suporte a condomínios
    tipo_projeto: tipoProjetoSchema.default("UNICO"),
    parent_obra_id: uuidSchema.optional(),
    identificador_unidade: z
      .string()
      .max(50, "Identificador deve ter no máximo 50 caracteres")
      .regex(
        /^[A-Za-z0-9\-_\s]*$/,
        "Identificador deve conter apenas letras, números, hífens e underscores"
      )
      .optional(),
    construtora_id: uuidSchema.optional(),
    responsavel_tecnico: z.string().max(255).optional(),
    observacoes: z.string().max(2000).optional(),
  })
  .refine(
    (data) => {
      // Validação: UNIDADE_CONDOMINIO deve ter parent_obra_id
      if (data.tipo_projeto === "UNIDADE_CONDOMINIO" && !data.parent_obra_id) {
        return false;
      }
      return true;
    },
    {
      message: "Unidades de condomínio devem ter uma obra-mãe associada",
      path: ["parent_obra_id"],
    }
  )
  .refine(
    (data) => {
      // Validação: UNIDADE_CONDOMINIO deve ter identificador_unidade
      if (
        data.tipo_projeto === "UNIDADE_CONDOMINIO" &&
        !data.identificador_unidade?.trim()
      ) {
        return false;
      }
      return true;
    },
    {
      message: "Unidades de condomínio devem ter um identificador único",
      path: ["identificador_unidade"],
    }
  )
  .refine(
    (data) => {
      // Validação: CONDOMINIO_MASTER não deve ter parent_obra_id
      if (data.tipo_projeto === "CONDOMINIO_MASTER" && data.parent_obra_id) {
        return false;
      }
      return true;
    },
    {
      message: "Condomínios principais não podem ter obra-mãe",
      path: ["parent_obra_id"],
    }
  );

// Fornecedor PJ
export const fornecedorPJSchema = z.object({
  cnpj: cnpjSchema.refine((val) => val !== undefined, "CNPJ é obrigatório"),
  razao_social: z.string().min(1, "Razão social é obrigatória").max(255),
  nome_fantasia: z.string().max(255).optional(),
  email: emailSchema.optional(),
  telefone: phoneSchema,
  endereco: z.string().max(500).optional(),
  cidade: z.string().max(100).optional(),
  estado: z.string().length(2).optional(),
  cep: cepSchema,
  categoria: z
    .enum([
      "MATERIAL_CONSTRUCAO",
      "EQUIPAMENTOS",
      "SERVICOS_ESPECIALIZADOS",
      "TRANSPORTE_LOGISTICA",
      "CONSULTORIA_PROJETOS",
      "OUTRO",
    ])
    .optional(),
  observacoes: z.string().max(1000).optional(),
});

// Fornecedor PF
export const fornecedorPFSchema = z.object({
  cpf: cpfSchema.refine((val) => val !== undefined, "CPF é obrigatório"),
  nome: z.string().min(1, "Nome é obrigatório").max(255),
  email: emailSchema.optional(),
  telefone: phoneSchema,
  endereco: z.string().max(500).optional(),
  cidade: z.string().max(100).optional(),
  estado: z.string().length(2).optional(),
  cep: cepSchema,
  categoria: z
    .enum([
      "PEDREIRO",
      "ELETRICISTA",
      "ENCANADOR",
      "PINTOR",
      "CARPINTEIRO",
      "SERVICOS_GERAIS",
      "OUTRO",
    ])
    .optional(),
  observacoes: z.string().max(1000).optional(),
});

// Despesa
export const despesaSchema = z
  .object({
    obra_id: uuidSchema,
    categoria: z.enum([
      "MATERIAL_CONSTRUCAO",
      "MAO_DE_OBRA",
      "ALUGUEL_EQUIPAMENTOS",
      "TRANSPORTE_FRETE",
      "TAXAS_LICENCAS",
      "SERVICOS_TERCEIRIZADOS",
      "ADMINISTRATIVO",
      "IMPREVISTOS",
      "OUTROS",
    ]),
    descricao: z.string().min(1, "Descrição é obrigatória").max(500),
    valor: currencySchema,
    data_despesa: z.string().datetime(),
    fornecedor_pj_id: uuidSchema.optional(),
    fornecedor_pf_id: uuidSchema.optional(),
    nota_fiscal: z.string().max(100).optional(),
    observacoes: z.string().max(1000).optional(),

    // Novos campos Sistema Híbrido
    tipo_despesa: z.enum(["COMUM", "ESPECIFICA"]).default("COMUM"),
    unidade_especifica_id: uuidSchema.optional(),
    rateio_automatico: z.boolean().default(true),
    observacoes_unidade: z.string().max(1000).optional(),
  })
  .superRefine((data, ctx) => {
    // Validação para despesas específicas
    if (data.tipo_despesa === "ESPECIFICA") {
      if (!data.unidade_especifica_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Despesa específica deve ter unidade_especifica_id",
          path: ["unidade_especifica_id"],
        });
      }
      if (data.rateio_automatico) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message:
            "Despesas específicas não podem ser rateadas automaticamente",
          path: ["rateio_automatico"],
        });
      }
    }

    // Validação para despesas comuns
    if (data.tipo_despesa === "COMUM" && data.unidade_especifica_id) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Despesas comuns não devem ter unidade específica",
        path: ["unidade_especifica_id"],
      });
    }
  });

// ========================================
// SCHEMAS DE IA
// ========================================

// Chat IA
export const aiChatSchema = z.object({
  user_id: uuidSchema,
  obra_id: uuidSchema.optional(),
  message: z
    .string()
    .min(1, "Mensagem não pode estar vazia")
    .max(5000, "Mensagem excede o limite de 5000 caracteres")
    .trim(),
  context: z
    .enum([
      "obra_detalhes",
      "orcamento",
      "fornecedores",
      "licitacoes",
      "sinapi",
      "geral",
    ])
    .optional(),
  conversation_id: uuidSchema.optional(),
});

// Orçamento Paramétrico
export const orcamentoParametricoSchema = z.object({
  tipo_obra: z.enum([
    "RESIDENCIAL",
    "COMERCIAL",
    "INDUSTRIAL",
    "INFRAESTRUTURA",
  ]),
  padrao_obra: z.enum(["BAIXO", "MEDIO", "ALTO", "LUXO"]),
  area_construcao: z.number().min(1, "Área deve ser positiva").max(100000),
  estado: z.string().length(2, "Estado deve ter 2 caracteres"),
  cidade: z.string().min(1, "Cidade é obrigatória").max(100),
  incluir_terreno: z.boolean().default(false),
  incluir_documentacao: z.boolean().default(true),
  detalhamento_nivel: z
    .enum(["BASICO", "DETALHADO", "COMPLETO"])
    .default("BASICO"),
});

// Análise de Planta
export const analisePlantaSchema = z.object({
  arquivo_url: z.string().url("URL do arquivo inválida"),
  tipo_analise: z.enum(["basica", "completa", "orcamento"]).default("basica"),
  incluir_orcamento: z.boolean().default(false),
  obra_id: uuidSchema.optional(),
});

// ========================================
// SCHEMAS DE SISTEMA
// ========================================

// Paginação
export const paginationSchema = z.object({
  page: z.number().min(1, "Página deve ser maior que 0").default(1),
  limit: z
    .number()
    .min(1, "Limite deve ser maior que 0")
    .max(100, "Limite máximo é 100")
    .default(20),
  sort: z.string().optional(),
  order: z.enum(["asc", "desc"]).default("desc"),
});

// Filtros genéricos
export const filterSchema = z.object({
  search: z.string().max(255).optional(),
  status: z.string().optional(),
  categoria: z.string().optional(),
  data_inicio: dateSchema,
  data_fim: dateSchema,
});

// ========================================
// SCHEMAS COMPOSTOS
// ========================================

// Request com paginação
export const paginatedRequestSchema = z.object({
  pagination: paginationSchema.optional(),
  filters: filterSchema.optional(),
});

// Busca SINAPI
export const sinapiSearchSchema = z.object({
  query: z.string().min(1, "Query de busca é obrigatória").max(500),
  limite: z.number().min(1).max(50).default(10),
  incluir_similares: z.boolean().default(true),
  estado: z.string().length(2).optional(),
});

// Chat de Fornecedores
export const fornecedoresChatSchema = z.object({
  user_id: uuidSchema,
  obra_id: uuidSchema.optional(),
  message: z
    .string()
    .min(1, "Mensagem não pode estar vazia")
    .max(1000, "Mensagem muito longa")
    .trim(),
  context: z
    .enum([
      "buscar", // Buscar fornecedores
      "comparar", // Comparar fornecedores
      "avaliar", // Avaliar performance
      "cadastrar", // Sugerir cadastro
      "contatar", // Informações de contato
      "geral", // Consulta geral
    ])
    .optional()
    .default("geral"),
  filters: z
    .object({
      categoria: z
        .enum([
          "MATERIAL_CONSTRUCAO",
          "MAO_DE_OBRA",
          "EQUIPAMENTOS",
          "SERVICOS",
          "TRANSPORTE",
          "PEDREIRO",
          "ELETRICISTA",
          "ENCANADOR",
          "PINTOR",
          "CARPINTEIRO",
          "SERVICOS_GERAIS",
          "OUTRO",
        ])
        .optional(),
      cidade: z.string().max(100).optional(),
      estado: z.string().length(2).optional(),
      orcamento_max: z.number().min(0).max(1000000).optional(),
      rating_min: z.number().min(1).max(5).optional(),
      raio_km: z.number().min(1).max(100).optional(),
    })
    .optional(),
});

// ========================================
// SCHEMAS DE CONDOMÍNIOS
// ========================================

// Schema para dados de unidade individual
export const unidadeCondominioSchema = z.object({
  identificador_unidade: z
    .string()
    .min(1, "Identificador da unidade é obrigatório")
    .max(50, "Identificador deve ter no máximo 50 caracteres")
    .regex(
      /^[A-Za-z0-9\-_\s]+$/,
      "Identificador deve conter apenas letras, números, hífens e underscores"
    ),
  nome: z
    .string()
    .min(3, "Nome da unidade deve ter pelo menos 3 caracteres")
    .max(255, "Nome deve ter no máximo 255 caracteres"),
  descricao: z
    .string()
    .max(1000, "Descrição deve ter no máximo 1000 caracteres")
    .optional(),
  area_construcao: z
    .number()
    .min(1, "Área deve ser maior que 0 m²")
    .max(10000, "Área não pode exceder 10.000 m²")
    .optional(),
  orcamento: currencySchema.optional(),
});

// Schema para criação de condomínio
export const createCondominioSchema = z.object({
  condominio_data: obraSchema
    .omit({
      tipo_projeto: true,
      parent_obra_id: true,
      identificador_unidade: true,
    })
    .extend({
      tipo_projeto: z.literal("CONDOMINIO_MASTER"),
    }),
  unidades_data: z
    .array(unidadeCondominioSchema)
    .min(1, "Deve haver pelo menos 1 unidade no condomínio")
    .max(500, "Máximo de 500 unidades por condomínio")
    .refine(
      (unidades) => {
        // Validar identificadores únicos
        const identificadores = unidades.map((u) =>
          u.identificador_unidade.toLowerCase().trim()
        );
        const identificadoresUnicos = new Set(identificadores);
        return identificadores.length === identificadoresUnicos.size;
      },
      {
        message:
          "Identificadores de unidades devem ser únicos dentro do condomínio",
      }
    )
    .refine(
      (unidades) => {
        // Validar nomes únicos
        const nomes = unidades.map((u) => u.nome.toLowerCase().trim());
        const nomesUnicos = new Set(nomes);
        return nomes.length === nomesUnicos.size;
      },
      {
        message: "Nomes de unidades devem ser únicos dentro do condomínio",
      }
    ),
});

// Schema para filtros de obras com condomínios
export const obrasCondominioFilterSchema = z.object({
  tipo_projeto: tipoProjetoSchema.optional(),
  parent_obra_id: uuidSchema.optional(),
  incluir_unidades: z.boolean().default(false),
  apenas_condominios: z.boolean().default(false),
  search: z.string().max(255).optional(),
  status: z
    .enum(["PLANEJAMENTO", "EM_ANDAMENTO", "PAUSADA", "CONCLUIDA", "CANCELADA"])
    .optional(),
  construtora_id: uuidSchema.optional(),
  cidade: z.string().max(100).optional(),
  estado: z.string().length(2).optional(),
});

// ========================================
// UTILITÁRIOS DE VALIDAÇÃO
// ========================================

/**
 * Valida dados usando schema Zod e retorna resultado padronizado
 */
export function validateData<T extends z.ZodTypeAny>(
  data: unknown,
  schema: T
): {
  success: boolean;
  data?: z.infer<T>;
  errors?: Record<string, string[]>;
} {
  const result = schema.safeParse(data);

  if (result.success) {
    return {
      success: true,
      data: result.data,
    };
  }

  // Formatar erros do Zod
  const errors: Record<string, string[]> = {};
  result.error.errors.forEach((error) => {
    const path = error.path.join(".");
    if (!errors[path]) {
      errors[path] = [];
    }
    errors[path].push(error.message);
  });

  return {
    success: false,
    errors,
  };
}

/**
 * Middleware de validação para Edge Functions
 */
export function createValidationMiddleware<T extends z.ZodTypeAny>(schema: T) {
  return (data: unknown) => validateData(data, schema);
}

// ========================================
// EXPORTS ORGANIZADOS
// ========================================

export const SCHEMAS = {
  // Base
  uuid: uuidSchema,
  email: emailSchema,
  phone: phoneSchema,
  cpf: cpfSchema,
  cnpj: cnpjSchema,
  cep: cepSchema,
  date: dateSchema,
  currency: currencySchema,

  // Entidades
  lead: leadSchema,
  obra: obraSchema,
  fornecedorPJ: fornecedorPJSchema,
  fornecedorPF: fornecedorPFSchema,
  despesa: despesaSchema,

  // Condomínios
  tipoProjeto: tipoProjetoSchema,
  unidadeCondominio: unidadeCondominioSchema,
  createCondominio: createCondominioSchema,
  obrasCondominioFilter: obrasCondominioFilterSchema,

  // IA
  aiChat: aiChatSchema,
  orcamentoParametrico: orcamentoParametricoSchema,
  analisePlanta: analisePlantaSchema,
  sinapiSearch: sinapiSearchSchema,
  fornecedoresChat: fornecedoresChatSchema,

  // Sistema
  pagination: paginationSchema,
  filter: filterSchema,
  paginatedRequest: paginatedRequestSchema,
} as const;
