import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
};

// Interfaces para dados completos do formulário
interface DadosFormularioCompleto {
  // Modo legacy (compatibilidade)
  orcamento_id?: string;
  forcar_recalculo?: boolean;

  // Modo novo (dados completos)
  tipo_obra?: string;
  padrao_obra?: string;
  area_total?: number;
  area_construida?: number;
  estado?: string;
  cidade?: string;

  // Dados de condomínio (quando aplicável)
  tipo_condominio?: "VERTICAL" | "HORIZONTAL";
  numero_blocos?: number;
  andares_por_bloco?: number;
  unidades_por_andar?: number;
  numero_unidades?: number;
  area_lote?: number;
  area_construida_unidade?: number;

  // Metadados para auditoria
  nome_orcamento?: string;
  obra_id?: string;
}

interface ParametrosCalculo {
  tipo_obra: string;
  subtipo_calculo?: string;
  fator_complexidade: number;
  cub_utilizado: number;
  fatores_padrao: Record<string, number>;
  consultas_sinapi: string[];
  composicao_custos: Record<string, number>;
  parametros_condominio?: Record<string, any>;
  multiplicador_regional: number;
  versao_calculo: string;
}

interface ItemOrcamento {
  orcamento_id: string;
  categoria: string;
  etapa: string;
  insumo: string;
  quantidade_estimada: number;
  unidade_medida: string;
  valor_unitario_base: number;
  fonte_preco: string;
  codigo_sinapi?: string;
  estado_referencia_preco: string;
  usa_preco_sinapi: boolean;
  observacoes?: string;
  created_at?: string;
  updated_at?: string;
}

// Composição detalhada por etapa com múltiplos itens - PERCENTUAIS CORRIGIDOS PARA REALIDADE BRASILEIRA
const composicaoEtapas = {
  FUNDACAO: {
    proporcao: 0.127,
    itens: [
      {
        insumo: "ESCAVACAO",
        categoria: "SERVICOS_TERCEIRIZADOS",
        percentual: 0.12,
        unidade: "m³",
      },
      {
        insumo: "CONCRETO_USINADO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.28,
        unidade: "m³",
      },
      {
        insumo: "ACO_CA50",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.2,
        unidade: "kg",
      },
      {
        insumo: "FORMA_MADEIRA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.08,
        unidade: "m²",
      },
      {
        insumo: "IMPERMEABILIZACAO_FUND",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.07,
        unidade: "m²",
      },
      {
        insumo: "PEDREIRO",
        categoria: "MAO_DE_OBRA",
        percentual: 0.15,
        unidade: "h",
      },
      {
        insumo: "SERVENTE",
        categoria: "MAO_DE_OBRA",
        percentual: 0.1,
        unidade: "h",
      },
    ],
  },
  ESTRUTURA: {
    proporcao: 0.178,
    itens: [
      {
        insumo: "CONCRETO_USINADO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.3,
        unidade: "m³",
      },
      {
        insumo: "ACO_CA50",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.22,
        unidade: "kg",
      },
      {
        insumo: "FORMA_MADEIRA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.12,
        unidade: "m²",
      },
      {
        insumo: "TELA_SOLDADA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.04,
        unidade: "m²",
      },
      {
        insumo: "PEDREIRO",
        categoria: "MAO_DE_OBRA",
        percentual: 0.18,
        unidade: "h",
      },
      {
        insumo: "SERVENTE",
        categoria: "MAO_DE_OBRA",
        percentual: 0.14,
        unidade: "h",
      },
    ],
  },
  ALVENARIA: {
    proporcao: 0.101,
    itens: [
      {
        insumo: "TIJOLO_CERAMICO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.35,
        unidade: "un",
      },
      {
        insumo: "CIMENTO_CP2",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.15,
        unidade: "kg",
      },
      {
        insumo: "AREIA_MEDIA_LAVADA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.12,
        unidade: "m³",
      },
      {
        insumo: "CAL_HIDRATADA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.04,
        unidade: "kg",
      },
      {
        insumo: "VERGA_CONTRAVERGA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.06,
        unidade: "m",
      },
      {
        insumo: "PEDREIRO",
        categoria: "MAO_DE_OBRA",
        percentual: 0.18,
        unidade: "h",
      },
      {
        insumo: "SERVENTE",
        categoria: "MAO_DE_OBRA",
        percentual: 0.1,
        unidade: "h",
      },
    ],
  },
  COBERTURA: {
    proporcao: 0.091,
    itens: [
      {
        insumo: "MADEIRAMENTO_TELHADO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.25,
        unidade: "m³",
      },
      {
        insumo: "TELHA_CERAMICA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.22,
        unidade: "m²",
      },
      {
        insumo: "RUFO_CALHA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.1,
        unidade: "m",
      },
      {
        insumo: "MANTA_SUBCOBERTURA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.06,
        unidade: "m²",
      },
      {
        insumo: "CARPINTEIRO",
        categoria: "MAO_DE_OBRA",
        percentual: 0.22,
        unidade: "h",
      },
      {
        insumo: "SERVENTE",
        categoria: "MAO_DE_OBRA",
        percentual: 0.15,
        unidade: "h",
      },
    ],
  },
  INSTALACOES_ELETRICAS: {
    proporcao: 0.076,
    itens: [
      {
        insumo: "FIO_CABO_ELETRICO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.25,
        unidade: "m",
      },
      {
        insumo: "ELETRODUTO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.15,
        unidade: "m",
      },
      {
        insumo: "QUADRO_DISTRIBUICAO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.12,
        unidade: "un",
      },
      {
        insumo: "DISJUNTOR",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.08,
        unidade: "un",
      },
      {
        insumo: "TOMADA_INTERRUPTOR",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.06,
        unidade: "un",
      },
      {
        insumo: "ELETRICISTA",
        categoria: "MAO_DE_OBRA",
        percentual: 0.25,
        unidade: "h",
      },
      {
        insumo: "SERVENTE",
        categoria: "MAO_DE_OBRA",
        percentual: 0.09,
        unidade: "h",
      },
    ],
  },
  INSTALACOES_HIDRAULICAS: {
    proporcao: 0.061,
    itens: [
      {
        insumo: "TUBO_PVC_AGUA_FRIA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.18,
        unidade: "m",
      },
      {
        insumo: "TUBO_PVC_ESGOTO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.15,
        unidade: "m",
      },
      {
        insumo: "CONEXOES_HIDRAULICAS",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.12,
        unidade: "un",
      },
      {
        insumo: "REGISTRO_GAVETA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.09,
        unidade: "un",
      },
      {
        insumo: "CAIXA_DAGUA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.08,
        unidade: "un",
      },
      {
        insumo: "LOUCAS_METAIS",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.06,
        unidade: "un",
      },
      {
        insumo: "ENCANADOR",
        categoria: "MAO_DE_OBRA",
        percentual: 0.22,
        unidade: "h",
      },
      {
        insumo: "SERVENTE",
        categoria: "MAO_DE_OBRA",
        percentual: 0.1,
        unidade: "h",
      },
    ],
  },
  REVESTIMENTOS_INTERNOS: {
    proporcao: 0.112,
    itens: [
      {
        insumo: "PISO_CERAMICO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.22,
        unidade: "m²",
      },
      {
        insumo: "AZULEJO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.18,
        unidade: "m²",
      },
      {
        insumo: "REJUNTE_ACRILICO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.06,
        unidade: "kg",
      },
      {
        insumo: "ARGAMASSA_ASSENTAMENTO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.12,
        unidade: "kg",
      },
      {
        insumo: "RODAPE",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.04,
        unidade: "m",
      },
      {
        insumo: "GESSO_LISO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.06,
        unidade: "m²",
      },
      {
        insumo: "PEDREIRO",
        categoria: "MAO_DE_OBRA",
        percentual: 0.2,
        unidade: "h",
      },
      {
        insumo: "GESSEIRO",
        categoria: "MAO_DE_OBRA",
        percentual: 0.12,
        unidade: "h",
      },
    ],
  },
  PINTURA: {
    proporcao: 0.041,
    itens: [
      {
        insumo: "TINTA_LATEX_PVA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.25,
        unidade: "l",
      },
      {
        insumo: "MASSA_CORRIDA_PVA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.18,
        unidade: "kg",
      },
      {
        insumo: "SELADOR_ACRILICO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.12,
        unidade: "l",
      },
      {
        insumo: "LIXA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.04,
        unidade: "un",
      },
      {
        insumo: "ROLO_PINTURA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.02,
        unidade: "un",
      },
      {
        insumo: "TRINCHA_PINCEL",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.01,
        unidade: "un",
      },
      {
        insumo: "PINTOR",
        categoria: "MAO_DE_OBRA",
        percentual: 0.28,
        unidade: "h",
      },
      {
        insumo: "SERVENTE",
        categoria: "MAO_DE_OBRA",
        percentual: 0.1,
        unidade: "h",
      },
    ],
  },
  ACABAMENTOS: {
    proporcao: 0.127,
    itens: [
      {
        insumo: "PORTA_MADEIRA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.18,
        unidade: "un",
      },
      {
        insumo: "JANELA_ALUMINIO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.15,
        unidade: "m²",
      },
      {
        insumo: "FECHADURA_DOBRADICA",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.09,
        unidade: "un",
      },
      {
        insumo: "VIDRO_COMUM",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.08,
        unidade: "m²",
      },
      {
        insumo: "BANCADA_GRANITO",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.12,
        unidade: "m²",
      },
      {
        insumo: "SOLEIRA_PEITORIL",
        categoria: "MATERIAL_CONSTRUCAO",
        percentual: 0.06,
        unidade: "m",
      },
      {
        insumo: "CARPINTEIRO",
        categoria: "MAO_DE_OBRA",
        percentual: 0.2,
        unidade: "h",
      },
      {
        insumo: "MARMORISTA",
        categoria: "MAO_DE_OBRA",
        percentual: 0.12,
        unidade: "h",
      },
    ],
  },
  DOCUMENTACAO: {
    proporcao: 0.028,
    itens: [
      {
        insumo: "PROJETO_ARQUITETONICO",
        categoria: "SERVICOS_TERCEIRIZADOS",
        percentual: 0.35,
        unidade: "un",
      },
      {
        insumo: "PROJETO_ESTRUTURAL",
        categoria: "SERVICOS_TERCEIRIZADOS",
        percentual: 0.25,
        unidade: "un",
      },
      {
        insumo: "PROJETO_ELETRICO",
        categoria: "SERVICOS_TERCEIRIZADOS",
        percentual: 0.15,
        unidade: "un",
      },
      {
        insumo: "PROJETO_HIDRAULICO",
        categoria: "SERVICOS_TERCEIRIZADOS",
        percentual: 0.15,
        unidade: "un",
      },
      {
        insumo: "ART_RRT",
        categoria: "TAXAS_LICENCAS",
        percentual: 0.05,
        unidade: "un",
      },
      {
        insumo: "TAXA_PREFEITURA",
        categoria: "TAXAS_LICENCAS",
        percentual: 0.05,
        unidade: "un",
      },
    ],
  },
  OUTROS: {
    proporcao: 0.023,
    itens: [
      {
        insumo: "LIMPEZA_OBRA",
        categoria: "SERVICOS_TERCEIRIZADOS",
        percentual: 0.3,
        unidade: "h",
      },
      {
        insumo: "CONTAINER_ENTULHO",
        categoria: "TRANSPORTE_FRETE",
        percentual: 0.25,
        unidade: "un",
      },
      {
        insumo: "BETONEIRA",
        categoria: "ALUGUEL_EQUIPAMENTOS",
        percentual: 0.15,
        unidade: "dia",
      },
      {
        insumo: "ANDAIME",
        categoria: "ALUGUEL_EQUIPAMENTOS",
        percentual: 0.1,
        unidade: "dia",
      },
      { insumo: "EPI", categoria: "OUTROS", percentual: 0.08, unidade: "un" },
      {
        insumo: "AGUA_OBRA",
        categoria: "OUTROS",
        percentual: 0.07,
        unidade: "mês",
      },
      {
        insumo: "LUZ_OBRA",
        categoria: "OUTROS",
        percentual: 0.05,
        unidade: "mês",
      },
    ],
  },
};

// ===================================================================
// 🏗️ FUNÇÕES ESPECÍFICAS DE CÁLCULO POR TIPO DE OBRA
// ===================================================================

/**
 * Calcular orçamento para Residencial Unifamiliar (lógica padrão existente)
 */
async function calcularOrcamentoUnifamiliar(
  dadosCompletos: DadosFormularioCompleto,
  supabaseAdmin: any
): Promise<any> {
  const areaCalculada =
    dadosCompletos.area_construida || dadosCompletos.area_total || 100;

  // Custo base por padrão (valores tradicionais)
  let custoM2Base = 1800; // NORMAL
  if (dadosCompletos.padrao_obra === "ALTO") custoM2Base = 2500;
  if (dadosCompletos.padrao_obra === "BAIXO") custoM2Base = 1200;
  if (dadosCompletos.padrao_obra === "LUXO") custoM2Base = 3500;
  if (dadosCompletos.padrao_obra === "POPULAR") custoM2Base = 1200;

  // Multiplicador regional
  const multiplicadorRegional =
    dadosCompletos.estado === "SP"
      ? 1.2
      : dadosCompletos.estado === "RJ"
      ? 1.15
      : dadosCompletos.estado === "GO"
      ? 0.9
      : dadosCompletos.estado === "MG"
      ? 0.95
      : 1.0;

  const custoTotalObra = areaCalculada * custoM2Base * multiplicadorRegional;

  // Gerar itens usando composição padrão
  const todosItens = gerarItensDetalhados(
    dadosCompletos.orcamento_id || "novo",
    custoTotalObra,
    areaCalculada,
    dadosCompletos.estado || "GO"
  );

  // Parâmetros para auditoria
  const parametrosCalculo: ParametrosCalculo = {
    tipo_obra: "R1_UNIFAMILIAR",
    fator_complexidade: 1.0,
    cub_utilizado: custoM2Base,
    fatores_padrao: { [dadosCompletos.padrao_obra || "NORMAL"]: 1.0 },
    consultas_sinapi: ["composição técnica padrão"],
    composicao_custos: {
      estrutura: 0.178,
      instalacoes: 0.137,
      acabamentos: 0.268,
      outros: 0.417,
    },
    multiplicador_regional: multiplicadorRegional,
    versao_calculo: "12.0.0-UNIFAMILIAR",
  };

  return { custoTotalObra, todosItens, parametrosCalculo, areaCalculada };
}

/**
 * Calcular orçamento para Multifamiliar VERTICAL (Prédios/Torres)
 */
async function calcularOrcamentoMultifamiliarVertical(
  dadosCompletos: DadosFormularioCompleto,
  supabaseAdmin: any
): Promise<any> {
  const areaCalculada =
    dadosCompletos.area_construida || dadosCompletos.area_total || 100;

  // Custo base mais alto para verticais + fator de complexidade 1.45
  let custoM2Base = 2200; // Base mais alta para verticais
  if (dadosCompletos.padrao_obra === "ALTO") custoM2Base = 3200;
  if (dadosCompletos.padrao_obra === "BAIXO") custoM2Base = 1800;
  if (dadosCompletos.padrao_obra === "LUXO") custoM2Base = 4500;
  if (dadosCompletos.padrao_obra === "POPULAR") custoM2Base = 1500;

  // Fatores de padrão agressivos para multifamiliar vertical
  const fatoresPadrao = {
    POPULAR: 1.0,
    NORMAL: 1.5,
    ALTO: 2.2,
    LUXO: 3.0,
  };

  const fatorPadrao =
    fatoresPadrao[dadosCompletos.padrao_obra as keyof typeof fatoresPadrao] ||
    1.5;

  // Multiplicador regional
  const multiplicadorRegional =
    dadosCompletos.estado === "SP"
      ? 1.2
      : dadosCompletos.estado === "RJ"
      ? 1.15
      : dadosCompletos.estado === "GO"
      ? 0.9
      : dadosCompletos.estado === "MG"
      ? 0.95
      : 1.0;

  // Fator de complexidade para verticais (45% mais caro)
  const fatorComplexidade = 1.45;

  const custoTotalObra =
    areaCalculada *
    custoM2Base *
    multiplicadorRegional *
    fatorComplexidade *
    fatorPadrao;

  // Gerar itens usando composição padrão
  const todosItens = gerarItensDetalhados(
    dadosCompletos.orcamento_id || "novo",
    custoTotalObra,
    areaCalculada,
    dadosCompletos.estado || "GO"
  );

  // Parâmetros para auditoria incluindo dados de condomínio
  const parametrosCalculo: ParametrosCalculo = {
    tipo_obra: "R4_MULTIFAMILIAR",
    subtipo_calculo: "VERTICAL",
    fator_complexidade: fatorComplexidade,
    cub_utilizado: custoM2Base,
    fatores_padrao: { [dadosCompletos.padrao_obra || "NORMAL"]: fatorPadrao },
    consultas_sinapi: [
      "estrutura de concreto armado",
      "elevador",
      "instalações prediais",
      "sistema de pressurização",
      "central de gás",
    ],
    composicao_custos: {
      estrutura: 0.4,
      instalacoes: 0.3,
      acabamentos: 0.3,
    },
    parametros_condominio: {
      numero_blocos: dadosCompletos.numero_blocos,
      andares_por_bloco: dadosCompletos.andares_por_bloco,
      unidades_por_andar: dadosCompletos.unidades_por_andar,
      total_unidades_calculado:
        (dadosCompletos.numero_blocos || 1) *
        (dadosCompletos.andares_por_bloco || 1) *
        (dadosCompletos.unidades_por_andar || 1),
    },
    multiplicador_regional: multiplicadorRegional,
    versao_calculo: "12.0.0-MULTIFAMILIAR-VERTICAL",
  };

  return { custoTotalObra, todosItens, parametrosCalculo, areaCalculada };
}

/**
 * Calcular orçamento para Multifamiliar HORIZONTAL (Casas/Lotes)
 */
async function calcularOrcamentoMultifamiliarHorizontal(
  dadosCompletos: DadosFormularioCompleto,
  supabaseAdmin: any
): Promise<any> {
  const areaCalculada =
    dadosCompletos.area_construida || dadosCompletos.area_total || 100;

  // Custo base para horizontais + fator de complexidade 1.25
  let custoM2Base = 1900; // Base intermediária para horizontais
  if (dadosCompletos.padrao_obra === "ALTO") custoM2Base = 2700;
  if (dadosCompletos.padrao_obra === "BAIXO") custoM2Base = 1500;
  if (dadosCompletos.padrao_obra === "LUXO") custoM2Base = 3800;
  if (dadosCompletos.padrao_obra === "POPULAR") custoM2Base = 1200;

  // Fatores de padrão para multifamiliar horizontal
  const fatoresPadrao = {
    POPULAR: 1.0,
    NORMAL: 1.3,
    ALTO: 1.8,
    LUXO: 2.3,
  };

  const fatorPadrao =
    fatoresPadrao[dadosCompletos.padrao_obra as keyof typeof fatoresPadrao] ||
    1.3;

  // Multiplicador regional
  const multiplicadorRegional =
    dadosCompletos.estado === "SP"
      ? 1.2
      : dadosCompletos.estado === "RJ"
      ? 1.15
      : dadosCompletos.estado === "GO"
      ? 0.9
      : dadosCompletos.estado === "MG"
      ? 0.95
      : 1.0;

  // Fator de complexidade para horizontais (25% mais caro)
  const fatorComplexidade = 1.25;

  const custoTotalObra =
    areaCalculada *
    custoM2Base *
    multiplicadorRegional *
    fatorComplexidade *
    fatorPadrao;

  // Gerar itens usando composição padrão
  const todosItens = gerarItensDetalhados(
    dadosCompletos.orcamento_id || "novo",
    custoTotalObra,
    areaCalculada,
    dadosCompletos.estado || "GO"
  );

  // Parâmetros para auditoria incluindo dados de condomínio
  const parametrosCalculo: ParametrosCalculo = {
    tipo_obra: "R4_MULTIFAMILIAR",
    subtipo_calculo: "HORIZONTAL",
    fator_complexidade: fatorComplexidade,
    cub_utilizado: custoM2Base,
    fatores_padrao: { [dadosCompletos.padrao_obra || "NORMAL"]: fatorPadrao },
    consultas_sinapi: [
      "estrutura de concreto armado",
      "muros e portões",
      "pavimentação interna",
      "rede de distribuição",
      "paisagismo",
    ],
    composicao_custos: {
      estrutura: 0.3,
      instalacoes: 0.25,
      acabamentos: 0.25,
      infraestrutura: 0.2,
    },
    parametros_condominio: {
      numero_unidades: dadosCompletos.numero_unidades,
      area_lote: dadosCompletos.area_lote,
      area_construida_unidade: dadosCompletos.area_construida_unidade,
      area_total_lotes:
        (dadosCompletos.numero_unidades || 1) * (dadosCompletos.area_lote || 0),
    },
    multiplicador_regional: multiplicadorRegional,
    versao_calculo: "12.0.0-MULTIFAMILIAR-HORIZONTAL",
  };

  return { custoTotalObra, todosItens, parametrosCalculo, areaCalculada };
}

/**
 * Calcular orçamento para Galpão Comercial/Industrial
 */
async function calcularOrcamentoGalpao(
  dadosCompletos: DadosFormularioCompleto,
  supabaseAdmin: any
): Promise<any> {
  const areaCalculada =
    dadosCompletos.area_construida || dadosCompletos.area_total || 100;

  // Custo base para galpões (estrutura metálica)
  let custoM2Base = 1200; // Base menor devido à estrutura mais simples
  if (dadosCompletos.padrao_obra === "ALTO") custoM2Base = 1800;
  if (dadosCompletos.padrao_obra === "BAIXO") custoM2Base = 900;
  if (dadosCompletos.padrao_obra === "LUXO") custoM2Base = 2500;
  if (dadosCompletos.padrao_obra === "POPULAR") custoM2Base = 800;

  // Multiplicador regional
  const multiplicadorRegional =
    dadosCompletos.estado === "SP"
      ? 1.2
      : dadosCompletos.estado === "RJ"
      ? 1.15
      : dadosCompletos.estado === "GO"
      ? 0.9
      : dadosCompletos.estado === "MG"
      ? 0.95
      : 1.0;

  const custoTotalObra = areaCalculada * custoM2Base * multiplicadorRegional;

  // Gerar itens usando composição padrão
  const todosItens = gerarItensDetalhados(
    dadosCompletos.orcamento_id || "novo",
    custoTotalObra,
    areaCalculada,
    dadosCompletos.estado || "GO"
  );

  // Parâmetros para auditoria
  const parametrosCalculo: ParametrosCalculo = {
    tipo_obra: "COMERCIAL_GALPAO",
    fator_complexidade: 1.0,
    cub_utilizado: custoM2Base,
    fatores_padrao: { [dadosCompletos.padrao_obra || "NORMAL"]: 1.0 },
    consultas_sinapi: [
      "estrutura metálica",
      "cobertura industrial",
      "piso industrial",
      "instalações elétricas industriais",
    ],
    composicao_custos: {
      estrutura_metalica: 0.35,
      cobertura: 0.25,
      piso_industrial: 0.2,
      instalacoes: 0.2,
    },
    multiplicador_regional: multiplicadorRegional,
    versao_calculo: "12.0.0-GALPAO",
  };

  return { custoTotalObra, todosItens, parametrosCalculo, areaCalculada };
}

/**
 * Função auxiliar para gerar itens detalhados usando a composição existente
 */
function gerarItensDetalhados(
  orcamento_id: string,
  custoTotalObra: number,
  areaCalculada: number,
  estado: string
): ItemOrcamento[] {
  const agora = new Date().toISOString();
  const todosItens: ItemOrcamento[] = [];

  for (const [nomeEtapa, etapaData] of Object.entries(composicaoEtapas)) {
    const valorTotalEtapa = custoTotalObra * etapaData.proporcao;

    for (const item of etapaData.itens) {
      const valorItem = valorTotalEtapa * item.percentual;

      // Calcular quantidade e valor unitário baseado na unidade
      let quantidade = areaCalculada;

      // Ajustar quantidade baseada na unidade de medida
      switch (item.unidade) {
        case "m³":
          quantidade = areaCalculada * 0.3;
          break;
        case "kg":
          quantidade = areaCalculada * 25;
          break;
        case "un":
          quantidade = Math.max(1, Math.round(areaCalculada / 20));
          break;
        case "m":
          quantidade = areaCalculada * 0.5;
          break;
        case "l":
          quantidade = areaCalculada * 0.4;
          break;
        case "h":
          quantidade = areaCalculada * 0.8;
          break;
        case "dia":
          quantidade = Math.max(1, Math.round(areaCalculada / 50));
          break;
        case "mês":
          quantidade = Math.max(1, Math.round(areaCalculada / 100));
          break;
        default:
          quantidade = areaCalculada;
      }

      const valorUnitario = valorItem / quantidade;

      const itemOrcamento: ItemOrcamento = {
        orcamento_id: orcamento_id,
        categoria: item.categoria,
        etapa: nomeEtapa,
        insumo: item.insumo,
        quantidade_estimada: parseFloat(quantidade.toFixed(3)),
        unidade_medida: item.unidade,
        valor_unitario_base: parseFloat(valorUnitario.toFixed(2)),
        fonte_preco: "PROPORCAO_TECNICA_V12",
        estado_referencia_preco: estado,
        usa_preco_sinapi: false,
        observacoes: `v12.0.0 - ${nomeEtapa} (${(item.percentual * 100).toFixed(
          1
        )}% da etapa) - Tipos de Obra Diferenciados`,
        created_at: agora,
        updated_at: agora,
      };

      todosItens.push(itemOrcamento);
    }
  }

  return todosItens;
}

serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log(
      "🚀 AI-Calculate-Budget v11.1.0 - PERCENTUAIS CORRIGIDOS - MAO DE OBRA REALISTA"
    );

    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    const dadosRequisicao: DadosFormularioCompleto = await req.json();

    let orcamento_id = dadosRequisicao.orcamento_id;
    let orcamento: any = null;
    let dadosCompletos: DadosFormularioCompleto;

    // Verificar se é modo legacy (apenas orcamento_id) ou novo (dados completos)
    if (orcamento_id && !dadosRequisicao.tipo_obra) {
      console.log("🔄 Modo LEGACY: Buscando dados do orçamento existente...");

      // Buscar dados do orçamento existente
      const { data: orcamentoData, error: orcamentoError } = await supabaseAdmin
        .from("orcamentos_parametricos")
        .select(
          `
          *,
          obras!inner (
            tipo_condominio,
            numero_blocos,
            andares_por_bloco,
            unidades_por_andar,
            numero_unidades,
            area_lote,
            area_construida_unidade
          )
        `
        )
        .eq("id", orcamento_id)
        .single();

      if (orcamentoError || !orcamentoData) {
        throw new Error(`Orçamento não encontrado: ${orcamento_id}`);
      }

      orcamento = orcamentoData;

      // Converter dados do banco para formato esperado
      dadosCompletos = {
        orcamento_id,
        forcar_recalculo: dadosRequisicao.forcar_recalculo,
        tipo_obra: orcamento.tipo_obra,
        padrao_obra: orcamento.padrao_obra,
        area_total:
          parseFloat(orcamento.area_total) ||
          parseFloat(orcamento.area_construida) ||
          100,
        area_construida: parseFloat(orcamento.area_construida),
        estado: orcamento.estado,
        cidade: orcamento.cidade,
        nome_orcamento: orcamento.nome_orcamento,
        obra_id: orcamento.obra_id,

        // Dados de condomínio da obra vinculada (se existir)
        tipo_condominio: orcamento.obras?.tipo_condominio,
        numero_blocos: orcamento.obras?.numero_blocos,
        andares_por_bloco: orcamento.obras?.andares_por_bloco,
        unidades_por_andar: orcamento.obras?.unidades_por_andar,
        numero_unidades: orcamento.obras?.numero_unidades,
        area_lote: orcamento.obras?.area_lote,
        area_construida_unidade: orcamento.obras?.area_construida_unidade,
      };

      console.log("✅ Dados do orçamento carregados:", {
        tipo_obra: dadosCompletos.tipo_obra,
        tipo_condominio: dadosCompletos.tipo_condominio,
        area_total: dadosCompletos.area_total,
      });
    } else if (dadosRequisicao.tipo_obra) {
      console.log("🆕 Modo NOVO: Usando dados completos fornecidos...");
      dadosCompletos = dadosRequisicao;

      if (
        !dadosCompletos.tipo_obra ||
        !dadosCompletos.padrao_obra ||
        !dadosCompletos.area_total
      ) {
        throw new Error(
          "Dados obrigatórios faltando: tipo_obra, padrao_obra, area_total"
        );
      }
    } else {
      throw new Error(
        "orcamento_id ou dados completos (tipo_obra, padrao_obra, area_total) são obrigatórios"
      );
    }

    console.log(
      `✅ Dados preparados para cálculo: ${
        dadosCompletos.nome_orcamento || "Orçamento"
      }`
    );

    // Limpar itens existentes se necessário (apenas no modo legacy)
    if (orcamento_id && dadosCompletos.forcar_recalculo) {
      console.log("🗑️ Removendo itens existentes...");
      await supabaseAdmin
        .from("itens_orcamento")
        .delete()
        .eq("orcamento_id", orcamento_id);
    }

    // **NOVA LÓGICA: Switch/Case por tipo de obra**
    let resultadoCalculo: any;

    switch (dadosCompletos.tipo_obra) {
      case "R4_MULTIFAMILIAR":
        if (dadosCompletos.tipo_condominio === "VERTICAL") {
          console.log("🏢 Calculando: Multifamiliar VERTICAL (Prédios/Torres)");
          resultadoCalculo = await calcularOrcamentoMultifamiliarVertical(
            dadosCompletos,
            supabaseAdmin
          );
        } else if (dadosCompletos.tipo_condominio === "HORIZONTAL") {
          console.log("🏘️ Calculando: Multifamiliar HORIZONTAL (Casas/Lotes)");
          resultadoCalculo = await calcularOrcamentoMultifamiliarHorizontal(
            dadosCompletos,
            supabaseAdmin
          );
        } else {
          console.log(
            "🏢 Calculando: Multifamiliar PADRÃO (sem especificação de condomínio)"
          );
          resultadoCalculo = await calcularOrcamentoMultifamiliarVertical(
            dadosCompletos,
            supabaseAdmin
          );
        }
        break;

      case "COMERCIAL_GALPAO":
        console.log("🏭 Calculando: Galpão Comercial/Industrial");
        resultadoCalculo = await calcularOrcamentoGalpao(
          dadosCompletos,
          supabaseAdmin
        );
        break;

      case "R1_UNIFAMILIAR":
      default:
        console.log("🏠 Calculando: Residencial Unifamiliar (lógica padrão)");
        resultadoCalculo = await calcularOrcamentoUnifamiliar(
          dadosCompletos,
          supabaseAdmin
        );
        break;
    }

    const { custoTotalObra, todosItens, parametrosCalculo, areaCalculada } =
      resultadoCalculo;

    console.log(
      `📊 Cálculo concluído: Área ${areaCalculada}m² - Custo total: R$ ${custoTotalObra.toFixed(
        2
      )}`
    );

    const agora = new Date().toISOString();

    // Inserir itens no banco (apenas se orcamento_id existir)
    let itensInseridos = 0;
    let insertError = null;

    if (orcamento_id && todosItens && todosItens.length > 0) {
      console.log(
        `📥 Inserindo ${todosItens.length} itens detalhados no banco...`
      );

      const { data: result, error } = await supabaseAdmin
        .from("itens_orcamento")
        .insert(todosItens)
        .select("id, etapa, categoria");

      if (error) {
        console.error("❌ Erro ao inserir itens:", error);
        insertError = error;
      } else {
        itensInseridos = result?.length || 0;
        console.log(`✅ ${itensInseridos} itens inseridos com sucesso`);
      }
    }

    const custoTotal = todosItens
      ? todosItens.reduce(
          (total, item) =>
            total + item.quantidade_estimada * item.valor_unitario_base,
          0
        )
      : custoTotalObra;
    const custoM2 = areaCalculada > 0 ? custoTotal / areaCalculada : 0;

    // Atualizar orçamento (apenas se orcamento_id existir)
    if (orcamento_id) {
      console.log(`💾 Atualizando orçamento: R$ ${custoTotal.toFixed(2)}`);

      await supabaseAdmin
        .from("orcamentos_parametricos")
        .update({
          custo_estimado: custoTotal,
          custo_m2: custoM2,
          status: "CONCLUIDO",
          data_calculo: agora,
          parametros_calculo: parametrosCalculo, // Salvar parâmetros para auditoria
        })
        .eq("id", orcamento_id);
    }

    // Atualizar obra vinculada (apenas se orcamento_id e obra_id existirem)
    if (orcamento_id && dadosCompletos.obra_id) {
      console.log(`🏗️ Atualizando obra vinculada: ${dadosCompletos.obra_id}`);
      await supabaseAdmin
        .from("obras")
        .update({ valor_orcamento_parametrico: custoTotal })
        .eq("id", dadosCompletos.obra_id);
    }

    // Gerar composição detalhada por categoria (apenas se itens existirem)
    let composicaoDetalhada: any = {};

    if (todosItens && todosItens.length > 0) {
      const resumoCategorias = todosItens.reduce((acc, item) => {
        const valorItem = item.quantidade_estimada * item.valor_unitario_base;
        const existing = acc.find((cat) => cat.categoria === item.categoria);

        if (existing) {
          existing.valor_total += valorItem;
          existing.quantidade_itens += 1;
        } else {
          acc.push({
            categoria: item.categoria,
            valor_total: parseFloat(valorItem.toFixed(2)),
            percentual: 0, // Será calculado depois
            quantidade_itens: 1,
          });
        }

        return acc;
      }, [] as any[]);

      // Calcular percentuais por categoria
      resumoCategorias.forEach((cat) => {
        cat.percentual = parseFloat(
          ((cat.valor_total / custoTotal) * 100).toFixed(1)
        );
        cat.valor_total = parseFloat(cat.valor_total.toFixed(2));
      });

      // Gerar resumo por etapas usando composição padrão
      const resumoEtapas = Object.keys(composicaoEtapas).map((etapa) => {
        const itensEtapa = todosItens.filter((item) => item.etapa === etapa);
        const valorTotalEtapa = itensEtapa.reduce(
          (total, item) =>
            total + item.quantidade_estimada * item.valor_unitario_base,
          0
        );

        return {
          etapa: etapa,
          valor_total: parseFloat(valorTotalEtapa.toFixed(2)),
          percentual: parseFloat(
            ((valorTotalEtapa / custoTotal) * 100).toFixed(1)
          ),
          quantidade_itens: itensEtapa.length,
        };
      });

      composicaoDetalhada = {
        resumo_categorias: resumoCategorias.sort(
          (a, b) => b.valor_total - a.valor_total
        ),
        resumo_etapas: resumoEtapas.sort(
          (a, b) => b.valor_total - a.valor_total
        ),
        total_itens: todosItens.length,
        percentual_mao_obra:
          resumoCategorias.find((c) => c.categoria === "MAO_DE_OBRA")
            ?.percentual || 0,
        percentual_material:
          resumoCategorias.find((c) => c.categoria === "MATERIAL_CONSTRUCAO")
            ?.percentual || 0,
        percentual_servicos:
          resumoCategorias.find((c) => c.categoria === "SERVICOS_TERCEIRIZADOS")
            ?.percentual || 0,
      };
    }

    console.log(
      `🎯 Cálculo ${
        parametrosCalculo.tipo_obra
      } concluído: R$ ${custoTotal.toFixed(2)} (${itensInseridos}/${
        todosItens?.length || 0
      } itens)`
    );

    return new Response(
      JSON.stringify({
        success: true,
        message: `Orçamento ${parametrosCalculo.tipo_obra}${
          parametrosCalculo.subtipo_calculo
            ? " " + parametrosCalculo.subtipo_calculo
            : ""
        } v12.0.0 - Tipos de Obra Diferenciados`,
        custo_estimado: custoTotal,
        custo_m2: custoM2,
        itens_inseridos: itensInseridos,
        orcamento_id: orcamento_id,
        itens: todosItens || [],
        parametros_calculo: parametrosCalculo,
        composicao_detalhada: composicaoDetalhada,
        debug: {
          versao: "12.0.0 - Tipos de Obra Diferenciados",
          modo_calculo: orcamento_id ? "LEGACY" : "NOVO",
          area_calculada: areaCalculada,
          tipo_obra: parametrosCalculo.tipo_obra,
          subtipo_calculo: parametrosCalculo.subtipo_calculo,
          fator_complexidade: parametrosCalculo.fator_complexidade,
          etapas_geradas: Object.keys(composicaoEtapas).length,
          itens_inseridos_sucesso: itensInseridos,
          total_itens_gerados: todosItens?.length || 0,
          insert_error: insertError,
          baseado_proporcoes_tecnicas: true,
          zero_hardcode: true,
          multiplicador_regional: parametrosCalculo.multiplicador_regional,
          estado: dadosCompletos.estado,
        },
      }),
      {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
      }
    );
  } catch (error: any) {
    console.error("💥 Erro na Edge Function v12.0.0:", error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error?.message || "Erro interno do servidor",
        debug: {
          versao: "12.0.0 - Tipos de Obra Diferenciados",
        },
      }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
      }
    );
  }
});
