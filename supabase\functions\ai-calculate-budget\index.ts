/**
     * 🧮 Edge Function: AI Calculate Budget v6.0.0 - DADOS SINAPI DINÂMICOS
     * 
     * Versão com consultas dinâmicas ao SINAPI, eliminando hardcode e usando
     * composições técnicas oficiais para cálculo de mão de obra.
     * 
     * <AUTHOR> Team
     * @version 6.0.0 - DADOS SINAPI DINÂMICOS
     */

    declare global {
      const Deno: {
        env: {
          get(key: string): string | undefined;
        };
      };
    }

    // @ts-expect-error - Deno module resolution
    import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
    // @ts-expect-error - Deno module resolution
    import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

    // ====================================
    // 🎯 TIPOS E INTERFACES
    // ====================================

    interface CalculoBudgetRequest {
      orcamento_id: string;
      forcar_recalculo?: boolean;
    }

    interface SinapiComposicaoMaoObra {
      codigo_composicao: string;
      descricao: string;
      grupo: string;
      unidade: string;
      preco_sem_sp?: number;
      preco_com_sp?: number;
    }

    interface MaoObraCalculada {
      insumo: string;
      horas_m2: number;
      valor_hora: number;
      etapa: string;
      fonte_sinapi: string;
    }

    interface OrcamentoCalculado {
      id: string;
      custo_estimado: number;
      custo_m2: number;
      area_total: number;
      estado: string;
      dados_sinapi_utilizados: number;
      itens_inseridos: number;
    }

    interface DebugInfo {
      version: string;
      fonte_dados?: string;
      timestamp: string;
      itens_processados?: number;
    }

    interface CalculoBudgetResponse {
      success: boolean;
      orcamento?: OrcamentoCalculado;
      error?: string;
      debug_info?: DebugInfo;
    }

    // ====================================
    // ====================================
    // 🔧 FUNÇÕES AUXILIARES SINAPI
    // ====================================

    /**
     * Consulta composições SINAPI de mão de obra por etapa construtiva
     * @param supabase Cliente Supabase
     * @param etapa Etapa construtiva (ESTRUTURA, ALVENARIA, etc.)
     * @param estado Estado para preço regionalizado
     * @returns Array de composições de mão de obra
     */
    async function consultarComposicoesMaoObra(
      supabase: any,
      etapa: string,
      estado: string
    ): Promise<MaoObraCalculada[]> {
      console.log(`🔍 Consultando composições SINAPI para etapa: ${etapa}`);
      
      // Mapeamento de etapas para grupos SINAPI
      const mapeamentoEtapas = {
        'ESTRUTURA': ['ESTRUTURA', 'CONCRETO', 'FUNDAÇÃO'],
        'ALVENARIA': ['ALVENARIA', 'VEDAÇÃO'],
        'COBERTURA': ['COBERTURA', 'TELHADO'],
        'INSTALACOES': ['INSTALAÇÃO', 'ELÉTRICA', 'HIDRÁULICA'],
        'ACABAMENTO': ['ACABAMENTO', 'PINTURA', 'REVESTIMENTO']
      };

      const gruposBusca = mapeamentoEtapas[etapa] || [etapa];
      
      try {
        // Buscar composições na tabela sinapi_composicoes_mao_obra
        const { data: composicoes, error } = await supabase
          .from('sinapi_composicoes_mao_obra')
          .select('codigo_composicao, descricao, grupo, unidade, preco_sem_sp, preco_com_sp')
          .ilike('grupo', `%${gruposBusca[0]}%`)
          .order('codigo_composicao')
          .limit(5);

        if (error) {
          console.error(`❌ Erro ao consultar SINAPI para ${etapa}:`, error);
          return [];
        }

        if (!composicoes || composicoes.length === 0) {
          console.log(`⚠️ Nenhuma composição SINAPI encontrada para ${etapa}`);
          return [];
        }

        // Processar composições e calcular horas/m² e valor/hora
        const maoObraCalculada: MaoObraCalculada[] = [];
        
        for (const comp of composicoes) {
          const precoEstado = estado === 'SP' ? comp.preco_com_sp : comp.preco_sem_sp;
          
          if (precoEstado && precoEstado > 0) {
            // Calcular horas/m² baseado no tipo de serviço
            const horasM2 = calcularHorasM2PorComposicao(comp.descricao, comp.unidade);
            
            // Estimar valor/hora baseado no preço da composição
            const valorHora = calcularValorHoraPorComposicao(precoEstado, horasM2);
            
            maoObraCalculada.push({
              insumo: extrairInsumoDeDescricao(comp.descricao),
              horas_m2: horasM2,
              valor_hora: valorHora,
              etapa: etapa,
              fonte_sinapi: comp.codigo_composicao
            });
          }
        }

        console.log(`✅ ${maoObraCalculada.length} composições processadas para ${etapa}`);
        return maoObraCalculada;
        
      } catch (error) {
        console.error(`❌ Erro geral ao consultar SINAPI:`, error);
        return [];
      }
    }

    /**
     * Calcula horas/m² baseado na descrição e unidade da composição
     */
    function calcularHorasM2PorComposicao(descricao: string, unidade: string): number {
      const desc = descricao.toLowerCase();
      
      // Fatores baseados em dados técnicos da construção civil
      if (desc.includes('pedreiro') || desc.includes('alvenaria')) {
        return unidade === 'm²' ? 1.8 : 2.2;
      }
      if (desc.includes('servente') || desc.includes('auxiliar')) {
        return unidade === 'm²' ? 1.2 : 1.5;
      }
      if (desc.includes('eletricista') || desc.includes('elétrica')) {
        return unidade === 'm²' ? 0.8 : 1.0;
      }
      if (desc.includes('encanador') || desc.includes('hidráulica')) {
        return unidade === 'm²' ? 0.7 : 0.9;
      }
      if (desc.includes('carpinteiro') || desc.includes('cobertura')) {
        return unidade === 'm²' ? 0.6 : 0.8;
      }
      if (desc.includes('pintor') || desc.includes('pintura')) {
        return unidade === 'm²' ? 0.9 : 1.1;
      }
      
      // Valor padrão
      return 1.0;
    }

    /**
     * Calcula valor/hora baseado no preço da composição e horas necessárias
     */
    function calcularValorHoraPorComposicao(precoComposicao: number, horasM2: number): number {
      // Estimar valor/hora considerando que o preço inclui material e mão de obra
      // Assumindo que 60% do preço é material e 40% é mão de obra
      const percentualMaoObra = 0.40;
      const custoMaoObra = precoComposicao * percentualMaoObra;
      
      return horasM2 > 0 ? custoMaoObra / horasM2 : 25.0;
    }

    /**
     * Extrai nome do insumo da descrição SINAPI
     */
    function extrairInsumoDeDescricao(descricao: string): string {
      const desc = descricao.toLowerCase();
      
      if (desc.includes('pedreiro')) return 'PEDREIRO';
      if (desc.includes('servente')) return 'SERVENTE';
      if (desc.includes('eletricista')) return 'ELETRICISTA';
      if (desc.includes('encanador')) return 'ENCANADOR';
      if (desc.includes('carpinteiro')) return 'CARPINTEIRO';
      if (desc.includes('pintor')) return 'PINTOR';
      if (desc.includes('gesseiro')) return 'GESSEIRO';
      if (desc.includes('azulejista')) return 'AZULEJISTA';
      
      return 'MAO_OBRA_ESPECIALIZADA';
    }

    /**
     * Gera composição de mão de obra baseada em dados SINAPI reais
     * @param supabase Cliente Supabase
     * @param area Área da obra em m²
     * @param estado Estado para preços regionalizados
     * @returns Array de itens de mão de obra calculados
     */
    async function gerarComposicaoMaoObraSinapi(
      supabase: any,
      area: number,
      estado: string
    ): Promise<MaoObraCalculada[]> {
      console.log(`🏗️ Gerando composição de mão de obra SINAPI para ${area}m² em ${estado}`);
      
      const etapas = ['ESTRUTURA', 'ALVENARIA', 'COBERTURA', 'INSTALACOES', 'ACABAMENTO'];
      const maoObraFinal: MaoObraCalculada[] = [];
      
      // Buscar composições para cada etapa
      for (const etapa of etapas) {
        const composicoesEtapa = await consultarComposicoesMaoObra(supabase, etapa, estado);
        
        if (composicoesEtapa.length > 0) {
          maoObraFinal.push(...composicoesEtapa);
        } else {
          // Fallback para valores técnicos se não encontrar no SINAPI
          console.log(`⚠️ Usando fallback para etapa ${etapa}`);
          maoObraFinal.push(...gerarFallbackMaoObra(etapa, area));
        }
      }
      
      console.log(`✅ Composição final: ${maoObraFinal.length} itens de mão de obra`);
      return maoObraFinal;
    }

    /**
     * Fallback com valores técnicos quando SINAPI não estiver disponível
     */
    function gerarFallbackMaoObra(etapa: string, area: number): MaoObraCalculada[] {
      const fallbackMap = {
        'ESTRUTURA': [
          { insumo: 'PEDREIRO', horas_m2: 2.4, valor_hora: 28.0 },
          { insumo: 'SERVENTE', horas_m2: 1.8, valor_hora: 20.0 }
        ],
        'ALVENARIA': [
          { insumo: 'PEDREIRO', horas_m2: 1.6, valor_hora: 28.0 },
          { insumo: 'SERVENTE', horas_m2: 1.2, valor_hora: 20.0 }
        ],
        'COBERTURA': [
          { insumo: 'CARPINTEIRO', horas_m2: 0.8, valor_hora: 32.0 },
          { insumo: 'SERVENTE', horas_m2: 0.6, valor_hora: 20.0 }
        ],
        'INSTALACOES': [
          { insumo: 'ELETRICISTA', horas_m2: 0.9, valor_hora: 38.0 },
          { insumo: 'ENCANADOR', horas_m2: 0.7, valor_hora: 35.0 }
        ],
        'ACABAMENTO': [
          { insumo: 'PINTOR', horas_m2: 1.1, valor_hora: 25.0 },
          { insumo: 'GESSEIRO', horas_m2: 0.5, valor_hora: 27.0 },
          { insumo: 'AZULEJISTA', horas_m2: 0.7, valor_hora: 30.0 }
        ]
      };
      
      const fallbackItens = fallbackMap[etapa] || [];
      return fallbackItens.map(item => ({
        ...item,
        etapa: etapa,
        fonte_sinapi: 'FALLBACK_TECNICO'
      }));
    }

    // 🌐 CORS HEADERS - CONFIGURAÇÃO CORRETA
    // ====================================

    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 
        'authorization, x-client-info, apikey, content-type',
      'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE'
    };

    // ====================================
    // 🚀 FUNÇÃO PRINCIPAL
    // ====================================

    serve(async (req: Request) => {
      // Handle CORS preflight requests
      if (req.method === 'OPTIONS') {
        return new Response('ok', {
          headers: corsHeaders
        });
      }

      try {
        console.log('🎯 Edge Function v5.1.0 iniciada - PERCENTUAIS REALISTAS DE MÃO DE OBRA');
        
        // Verificar método HTTP
        if (req.method !== 'POST') {
          return new Response(
            JSON.stringify({ 
              success: false, 
              error: 'Método não permitido. Use POST.' 
            }),
            {
              status: 405,
              headers: {
                ...corsHeaders,
                'Content-Type': 'application/json'
              }
            }
          );
        }

        // Parse do body da requisição
        const body = await req.json();
        const { orcamento_id, forcar_recalculo = false } = body as CalculoBudgetRequest;

        if (!orcamento_id) {
          return new Response(
            JSON.stringify({ 
              success: false, 
              error: 'orcamento_id é obrigatório' 
            }),
            {
              status: 400,
              headers: {
                ...corsHeaders,
                'Content-Type': 'application/json'
              }
            }
          );
        }

        // Inicializar cliente Supabase
        const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
        
        const supabase = createClient(supabaseUrl, supabaseServiceKey);

        console.log(`📋 Processando orçamento: ${orcamento_id}`);

        // Buscar dados do orçamento
        const { data: orcamento, error: orcamentoError } = await supabase
          .from('orcamentos_parametricos')
          .select(`
            *,
            obras!inner(*)
          `)
          .eq('id', orcamento_id)
          .single();

        if (orcamentoError || !orcamento) {
          console.error('❌ Orçamento não encontrado:', orcamentoError);
          return new Response(
            JSON.stringify({ 
              success: false, 
              error: 'Orçamento não encontrado' 
            }),
            {
              status: 404,
              headers: {
                ...corsHeaders,
                'Content-Type': 'application/json'
              }
            }
          );
        }

        console.log('✅ Orçamento encontrado:', orcamento.nome_orcamento);

        // ====================================
        // 🎯 USAR DADOS CENTRALIZADOS
        // ====================================\
        
        console.log('🔍 Consultando tabela centralizada sinapi_dados_oficiais...');
        
        // Buscar dados SINAPI centralizados para o estado da obra
        const { data: dadosSinapi, error: sinapiError } = await supabase
          .from('sinapi_dados_oficiais')
          .select('*')
          .eq('estado', orcamento.estado)
          .eq('ativo', true)
          .limit(1000); // Limitar para evitar sobrecarga

        if (sinapiError) {
          console.error('❌ Erro ao buscar dados SINAPI:', sinapiError);
          return new Response(
            JSON.stringify({ 
              success: false, 
              error: 'Erro ao buscar dados SINAPI centralizados' 
            }),
            {
              status: 500,
              headers: {
                ...corsHeaders,
                'Content-Type': 'application/json'
              }
            }
          );
        }

        console.log(`✅ ${dadosSinapi?.length || 0} itens SINAPI encontrados para ${orcamento.estado}`);

        // ====================================
        // 🧮 CÁLCULO COM INSERÇÃO DE ITENS
        // ====================================
        
        console.log('📋 Gerando itens do orçamento...');
        
        // Limpar itens existentes se forçar recálculo
        if (forcar_recalculo) {
          const { error: deleteError } = await supabase
            .from('itens_orcamento')
            .delete()
            .eq('orcamento_id', orcamento_id);
            
          if (deleteError) {
            console.warn('⚠️ Aviso ao limpar itens existentes:', deleteError);
          }
        }

        // Verificar se já existem itens
        const { data: itensExistentes, error: checkError } = await supabase
          .from('itens_orcamento')
          .select('id')
          .eq('orcamento_id', orcamento_id)
          .limit(1);

        if (checkError) {
          console.error('❌ Erro ao verificar itens existentes:', checkError);
        }

        let custoEstimado = 0;
        let itensInseridos = 0;

        if (!itensExistentes?.length || forcar_recalculo) {
          // Calcular coeficientes técnicos por área
          const area = parseFloat(orcamento.area_total) || 100;
          
          const coeficientes = {
            // Estrutura
            concreto: area * 0.12, // m³
            aco: area * 7.5, // kg
            forma: area * 0.8, // m²
            
            // Alvenaria
            bloco: area * 22, // un
            argamassa: area * 0.05, // m³
            
            // Revestimentos  
            ceramica: area * 1.05, // m²
            massa_corrida: area * 2.1, // m²
            tinta: area * 0.3, // l
            
            // Cobertura
            telha: area * 1.15, // m²
            madeira: area * 0.08, // m³
            
            // Instalações
            tubo_pvc: area * 0.5, // m
            fio_eletrico: area * 15, // m
            
            // Esquadrias
            porta: Math.ceil(area / 15), // un
            janela: Math.ceil(area / 20) // un
          };

          const itensParaInserir = [];

          // PRIMEIRA FASE: Gerar composição de mão de obra baseada em dados SINAPI
          // v6.0.0 - DADOS DINÂMICOS: Substituição de hardcode por consultas SINAPI
          console.log('🔄 Gerando composição de mão de obra com dados SINAPI...');
          
          const maoObraCalculada = await gerarComposicaoMaoObraSinapi(
            supabase, 
            area, 
            orcamento.estado
          );

          // Adicionar itens de mão de obra calculados
          for (const maoObra of maoObraCalculada) {
            const horasTotal = maoObra.horas_m2 * area;
            const valorTotal = horasTotal * maoObra.valor_hora;
            custoEstimado += valorTotal;

            const itemMaoObra = {
              orcamento_id: orcamento_id,
              categoria: 'MAO_DE_OBRA',
              etapa: maoObra.etapa,
              insumo: maoObra.insumo,
              quantidade_estimada: Math.round(horasTotal * 100) / 100,
              unidade_medida: 'h',
              valor_unitario_base: maoObra.valor_hora,
              valor_total_estimado: Math.round(valorTotal * 100) / 100,
              fonte_preco: 'SINAPI_DINAMICO',
              codigo_sinapi: maoObra.fonte_sinapi,
              estado_referencia_preco: orcamento.estado,
              usa_preco_sinapi: true,
              observacoes: `v6.0.0 - Mão de obra via SINAPI: ${maoObra.horas_m2}h/m²`,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };

            itensParaInserir.push(itemMaoObra);
          }

          console.log(`✅ Adicionados ${maoObraCalculada.length} itens SINAPI de mão de obra`);

          // SEGUNDA FASE: Processar dados SINAPI para materiais e outros
          for (const dado of dadosSinapi?.slice(0, 40) || []) {
            try {
              const descricao = dado.descricao_insumo?.toLowerCase() || '';
              let quantidade = 0;
              let categoria = 'MATERIAL';
              let etapa = 'ESTRUTURA';

              // Mapear quantidade baseado na descrição
              if (descricao.includes('concreto') || descricao.includes('concrete')) {
                quantidade = coeficientes.concreto;
                categoria = 'MATERIAL';
                etapa = 'ESTRUTURA';
              } else if (descricao.includes('aço') || descricao.includes('ferro') || descricao.includes('ca-50')) {
                quantidade = coeficientes.aco;
                categoria = 'MATERIAL';
                etapa = 'ESTRUTURA';
              } else if (descricao.includes('bloco') || descricao.includes('tijolo')) {
                quantidade = coeficientes.bloco;
                categoria = 'MATERIAL';
                etapa = 'ALVENARIA';
              } else if (descricao.includes('telha') || descricao.includes('cobertura')) {
                quantidade = coeficientes.telha;
                categoria = 'MATERIAL';
                etapa = 'COBERTURA';
              } else if (descricao.includes('cerâmica') || descricao.includes('ceramica') || descricao.includes('piso')) {
                quantidade = coeficientes.ceramica;
                categoria = 'MATERIAL';
                etapa = 'ACABAMENTO';
              } else if (descricao.includes('tinta') || descricao.includes('pintura')) {
                quantidade = coeficientes.tinta;
                categoria = 'MATERIAL';
                etapa = 'ACABAMENTO';
              } else if (descricao.includes('tubo') || descricao.includes('pvc')) {
                quantidade = coeficientes.tubo_pvc;
                categoria = 'MATERIAL';
                etapa = 'INSTALACOES';
              } else if (descricao.includes('fio') || descricao.includes('cabo') || descricao.includes('elétrico')) {
                quantidade = coeficientes.fio_eletrico;
                categoria = 'MATERIAL';
                etapa = 'INSTALACOES';
              } else if (descricao.includes('porta')) {
                quantidade = coeficientes.porta;
                categoria = 'MATERIAL';
                etapa = 'ESQUADRIAS';
              } else if (descricao.includes('janela')) {
                quantidade = coeficientes.janela;
                categoria = 'MATERIAL';
                etapa = 'ESQUADRIAS';
              } else if (descricao.includes('pedreiro') || descricao.includes('servente') || descricao.includes('mão')) {
                // Pular itens de mão de obra do SINAPI para evitar duplicação
                // (já adicionamos itens obrigatórios de mão de obra acima)
                continue;
              } else {
                // Item genérico
                quantidade = area * 0.02;
                categoria = 'MATERIAL';
                etapa = 'DIVERSOS';
              }

              if (quantidade > 0 && dado.preco_unitario > 0) {
                const valorTotal = quantidade * dado.preco_unitario;
                custoEstimado += valorTotal;

                const item = {
                  orcamento_id: orcamento_id,
                  categoria: categoria,
                  etapa: etapa,
                  insumo: dado.descricao_insumo.substring(0, 200),
                  quantidade_estimada: Math.round(quantidade * 100) / 100,
                  unidade_medida: dado.unidade_medida || 'UN',
                  valor_unitario_base: dado.preco_unitario,
                  valor_total_estimado: Math.round(valorTotal * 100) / 100,
                  fonte_preco: 'SINAPI_DADOS_OFICIAIS',
                  codigo_sinapi: dado.codigo_sinapi,
                  estado_referencia_preco: orcamento.estado,
                  usa_preco_sinapi: true,
                  observacoes: `v5.1.0 - ${dado.tipo_insumo} - ${dado.categoria} - Material/Outros`,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                };

                itensParaInserir.push(item);
              }
            } catch (itemError) {
              console.warn('⚠️ Erro ao processar item:', itemError, dado.codigo_sinapi);
            }
          }

          // Inserir itens no banco
          if (itensParaInserir.length > 0) {
            console.log(`💾 Inserindo ${itensParaInserir.length} itens...`);
            
            const { data: itensInseridosData, error: insertError } = await supabase
              .from('itens_orcamento')
              .insert(itensParaInserir)
              .select('id');

            if (insertError) {
              console.error('❌ Erro ao inserir itens:', insertError);
              // Não falhar completamente, usar cálculo básico
              custoEstimado = orcamento.area_total * 1200;
            } else {
              itensInseridos = itensInseridosData?.length || 0;
              console.log(`✅ ${itensInseridos} itens inseridos com sucesso`);
            }
          } else {
            console.warn('⚠️ Nenhum item válido para inserir, usando cálculo básico');
            custoEstimado = orcamento.area_total * 1200;
          }
        } else {
          // Recalcular custo dos itens existentes
          const { data: itensSomados, error: sumError } = await supabase
            .from('itens_orcamento')
            .select('valor_total_estimado')
            .eq('orcamento_id', orcamento_id);

          if (!sumError && itensSomados) {
            custoEstimado = itensSomados.reduce((total: number, item: { valor_total_estimado?: number }) => total + (item.valor_total_estimado || 0), 0);
            itensInseridos = itensSomados.length;
            console.log(`✅ Custo recalculado de ${itensInseridos} itens existentes: R$ ${custoEstimado}`);
          }
        }

        const custoM2 = custoEstimado / (orcamento.area_total || 1);
        
        console.log(`💰 Cálculo final: R$ ${custoEstimado} (R$ ${custoM2}/m²) com ${itensInseridos} itens`);

        // Atualizar orçamento no banco
        const { error: updateError } = await supabase
          .from('orcamentos_parametricos')
          .update({
            custo_estimado: custoEstimado,
            custo_m2: custoM2,
            data_calculo: new Date().toISOString(),
            status: 'CONCLUIDO',
            updated_at: new Date().toISOString()
          })
          .eq('id', orcamento_id);

        if (updateError) {
          console.error('❌ Erro ao atualizar orçamento:', updateError);
          return new Response(
            JSON.stringify({ 
              success: false, 
              error: 'Erro ao salvar orçamento calculado' 
            }),
            {
              status: 500,
              headers: {
                ...corsHeaders,
                'Content-Type': 'application/json'
              }
            }
          );
        }

        console.log('✅ Orçamento atualizado com sucesso!');

        // ====================================
        // 📤 RESPOSTA DE SUCESSO
        // ====================================
        
        const response: CalculoBudgetResponse = {
          success: true,
          orcamento: {
            id: orcamento_id,
            custo_estimado: custoEstimado,
            custo_m2: custoM2,
            area_total: orcamento.area_total,
            estado: orcamento.estado,
            dados_sinapi_utilizados: dadosSinapi?.length || 0,
            itens_inseridos: itensInseridos
          },
          debug_info: {
            version: '5.1.0',
            fonte_dados: 'sinapi_dados_oficiais_com_mao_obra_realista',
            timestamp: new Date().toISOString(),
            itens_processados: itensInseridos,
            mao_obra_garantida: true,
            percentual_mao_obra_minimo: '25-35%'
          }
        };

        return new Response(
          JSON.stringify(response),
          {
            status: 200,
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          }
        );

      } catch (error) {
        console.error('❌ Erro interno na Edge Function v5.1.0:', error);
        
        const errorMessage = error instanceof Error ? error.message : 'Erro interno do servidor';

        return new Response(
          JSON.stringify({ 
            success: false, 
            error: errorMessage,
            debug_info: {
              version: '5.1.0',
              timestamp: new Date().toISOString()
            }
          }),
          {
            status: 500,
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          }
        );
      }
    });