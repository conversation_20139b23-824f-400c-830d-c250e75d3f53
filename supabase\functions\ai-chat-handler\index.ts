import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import {
  createClient,
  SupabaseClient,
} from "https://esm.sh/@supabase/supabase-js@2.29.0";

// ✅ Interfaces para tipagem
interface Obra {
  id: string;
  nome: string;
  endereco: string;
  cidade: string;
  estado: string;
  orcamento: number;
  data_inicio: string;
  data_prevista_termino: string;
}

interface Despesa {
  id: string;
  descricao: string;
  custo: number;
  data_despesa: string;
  categoria: string;
  pago: boolean;
  obra_id: string;
  fornecedores_pj?: { razao_social: string; nome_fantasia: string };
  fornecedores_pf?: { nome: string };
}

interface NotaFiscal {
  id: string;
  numero: string;
  data_emissao: string;
  obra_id: string;
}

interface ContextoObra {
  obra: {
    nome: string;
    endereco: string;
    orcamento: number;
    data_inicio: string;
    data_prevista_termino: string;
    dias_em_andamento: number;
  };
  financeiro: {
    orcamento_total: number;
    total_gasto: number;
    total_pago: number;
    total_pendente: number;
    percentual_gasto: string;
    saldo_disponivel: number;
  };
  despesas_recentes: Array<{
    descricao: string;
    valor: number;
    data: string;
    categoria: string;
    pago: boolean;
    fornecedor: string;
  }>;
  total_despesas: number;
  total_notas_fiscais: number;
}

interface ContextoGeral {
  obra?: ContextoObra;
  fornecedores: {
    total_pj: number;
    total_pf: number;
    total_geral: number;
    fornecedores_recentes: Array<{
      nome: string;
      tipo: 'PJ' | 'PF';
      categoria: string;
      cidade: string;
      telefone?: string;
      email?: string;
    }>;
  };
  vendas: {
    total_vendas: number;
    faturamento_total: number;
    vendas_recentes: Array<{
      nome_cliente: string;
      valor_venda: number;
      data_venda: string;
      status: string;
      comissao: number;
    }>;
  };
  orcamentos: {
    total_orcamentos: number;
    valor_total_orcamentos: number;
    orcamentos_recentes: Array<{
      nome_obra: string;
      valor_total: number;
      data_criacao: string;
      status: string;
    }>;
  };
  despesas: {
    total_despesas: number;
    valor_total_despesas: number;
    despesas_pendentes: number;
    despesas_recentes: Array<{
      descricao: string;
      valor: number;
      data: string;
      categoria: string;
      pago: boolean;
    }>;
  };
  notas_fiscais: {
    total_notas: number;
    notas_recentes: Array<{
      numero: string;
      data_emissao: string;
      valor: number;
      fornecedor: string;
    }>;
  };
  contratos: {
    total_contratos: number;
    contratos_ativos: number;
    contratos_recentes: Array<{
      numero: string;
      descricao: string;
      valor: number;
      data_inicio: string;
      status: string;
    }>;
  };
  controle_orcamentario: {
    obras_ativas: number;
    total_orcado: number;
    total_gasto: number;
    percentual_executado: number;
    obras_acima_orcamento: number;
  };
}

interface ChatRequest {
  message: string;
  obra_id?: string | null;
  user_id: string;
}

interface DeepSeekResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

interface ErrorWithCode extends Error {
  code?: string;
}

import {
  checkRateLimit,
  getPreflightHeaders,
  getSecureCorsHeaders,
  validateCSRFToken,
} from "../_shared/cors.ts";
import { aiChatSchema } from "../_shared/input-validation.ts";

// ✅ Headers de segurança aprimorados - agora usando configuração centralizada

// 🤖 Configuração da API DeepSeek
const DEEPSEEK_API_KEY = Deno.env.get("DEEPSEEK_API");
const DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions";

// Rate limiting agora é gerenciado pela configuração centralizada

// Validação CSRF agora é gerenciada pela configuração centralizada

/**
 * 🏢 Busca contexto dos fornecedores para a IA (com validação de segurança)
 */
async function buscarContextoFornecedores(
  supabase: SupabaseClient,
  user_id: string,
): Promise<any> {
  try {
    // 🛡️ SEGURANÇA: Buscar tenant_id do usuário autenticado
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('tenant_id')
      .eq('id', user_id)
      .single();

    if (!userProfile?.tenant_id) {
      console.error('❌ Usuário sem tenant_id válido:', user_id);
      return {
        total_pj: 0,
        total_pf: 0,
        total_geral: 0,
        fornecedores_recentes: [],
      };
    }

    // 🛡️ SEGURANÇA: Buscar fornecedores PJ COM filtro por tenant
    const { data: fornecedoresPJ, count: countPJ } = await supabase
      .from("fornecedores_pj")
      .select(`
        razao_social,
        nome_fantasia,
        categoria,
        cidade,
        estado,
        telefone_principal,
        email,
        created_at
      `, { count: 'exact' })
      .eq("tenant_id", userProfile.tenant_id)
      .order("created_at", { ascending: false })
      .limit(5);

    // 🛡️ SEGURANÇA: Buscar fornecedores PF COM filtro por tenant
    const { data: fornecedoresPF, count: countPF } = await supabase
      .from("fornecedores_pf")
      .select(`
        nome,
        tipo_fornecedor,
        cidade,
        estado,
        telefone_principal,
        email,
        created_at
      `, { count: 'exact' })
      .eq("tenant_id", userProfile.tenant_id)
      .order("created_at", { ascending: false })
      .limit(5);

    // Combinar fornecedores recentes (PJ + PF)
    const fornecedoresRecentes = [
      ...(fornecedoresPJ || []).map(f => ({
        nome: f.nome_fantasia || f.razao_social,
        tipo: 'PJ' as const,
        categoria: f.categoria || 'Não informado',
        cidade: f.cidade || 'Não informado',
        telefone: f.telefone_principal,
        email: f.email,
        created_at: f.created_at
      })),
      ...(fornecedoresPF || []).map(f => ({
        nome: f.nome,
        tipo: 'PF' as const,
        categoria: f.tipo_fornecedor || 'Não informado',
        cidade: f.cidade || 'Não informado',
        telefone: f.telefone_principal,
        email: f.email,
        created_at: f.created_at
      }))
    ]
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 8);

    return {
      total_pj: countPJ || 0,
      total_pf: countPF || 0,
      total_geral: (countPJ || 0) + (countPF || 0),
      fornecedores_recentes: fornecedoresRecentes,
    };

  } catch (error) {
    console.error("Erro ao buscar contexto dos fornecedores:", error);
    return {
      total_pj: 0,
      total_pf: 0,
      total_geral: 0,
      fornecedores_recentes: [],
    };
  }
}

/**
 * 💰 Busca contexto das vendas para a IA (com validação de segurança)
 */
async function buscarContextoVendas(
  supabase: SupabaseClient,
  user_id: string,
): Promise<any> {
  try {
    // 🛡️ SEGURANÇA: Buscar tenant_id do usuário autenticado
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('tenant_id')
      .eq('id', user_id)
      .single();

    if (!userProfile?.tenant_id) {
      return { total_vendas: 0, faturamento_total: 0, vendas_recentes: [] };
    }

    // 🛡️ SEGURANÇA: Buscar vendas COM filtro por tenant
    const { data: vendas, count: totalVendas } = await supabase
      .from("obras")
      .select("*", { count: 'exact' })
      .eq("tenant_id", userProfile.tenant_id)
      .not("valor_venda", "is", null)
      .not("data_venda", "is", null)
      .order("data_venda", { ascending: false })
      .limit(10);

    const faturamentoTotal = vendas?.reduce((sum, v) => sum + (v.valor_venda || 0), 0) || 0;

    const vendasRecentes = vendas?.slice(0, 8).map(v => ({
      nome_cliente: v.nome_cliente || v.nome,
      valor_venda: v.valor_venda || 0,
      data_venda: v.data_venda,
      status: v.status_venda || 'pendente',
      comissao: v.comissao_corretor_percentual || 0
    })) || [];

    return {
      total_vendas: totalVendas || 0,
      faturamento_total: faturamentoTotal,
      vendas_recentes: vendasRecentes,
    };
  } catch (error) {
    console.error("Erro ao buscar contexto de vendas:", error);
    return { total_vendas: 0, faturamento_total: 0, vendas_recentes: [] };
  }
}

/**
 * 📊 Busca contexto dos orçamentos para a IA (com validação de segurança)
 */
async function buscarContextoOrcamentos(
  supabase: SupabaseClient,
  user_id: string,
): Promise<any> {
  try {
    // 🛡️ SEGURANÇA: Buscar tenant_id do usuário autenticado
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('tenant_id')
      .eq('id', user_id)
      .single();

    if (!userProfile?.tenant_id) {
      return { total_orcamentos: 0, valor_total_orcamentos: 0, orcamentos_recentes: [] };
    }

    // 🛡️ SEGURANÇA: Buscar orçamentos COM filtro por tenant
    const { data: orcamentos, count: totalOrcamentos } = await supabase
      .from("obras")
      .select("*", { count: 'exact' })
      .eq("tenant_id", userProfile.tenant_id)
      .order("created_at", { ascending: false })
      .limit(10);

    const valorTotalOrcamentos = orcamentos?.reduce((sum, o) => sum + (o.orcamento || 0), 0) || 0;

    const orcamentosRecentes = orcamentos?.slice(0, 8).map(o => ({
      nome_obra: o.nome,
      valor_total: o.orcamento || 0,
      data_criacao: o.created_at,
      status: o.status || 'ativo'
    })) || [];

    return {
      total_orcamentos: totalOrcamentos || 0,
      valor_total_orcamentos: valorTotalOrcamentos,
      orcamentos_recentes: orcamentosRecentes,
    };
  } catch (error) {
    console.error("Erro ao buscar contexto de orçamentos:", error);
    return { total_orcamentos: 0, valor_total_orcamentos: 0, orcamentos_recentes: [] };
  }
}

/**
 * 💸 Busca contexto das despesas para a IA (com validação de segurança)
 */
async function buscarContextoDespesas(
  supabase: SupabaseClient,
  user_id: string,
): Promise<any> {
  try {
    // 🛡️ SEGURANÇA: Buscar tenant_id do usuário autenticado
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('tenant_id')
      .eq('id', user_id)
      .single();

    if (!userProfile?.tenant_id) {
      return { total_despesas: 0, valor_total_despesas: 0, despesas_pendentes: 0, despesas_recentes: [] };
    }

    // 🛡️ SEGURANÇA: Buscar despesas COM filtro por tenant
    const { data: despesas, count: totalDespesas } = await supabase
      .from("despesas")
      .select("*", { count: 'exact' })
      .eq("tenant_id", userProfile.tenant_id)
      .order("data_despesa", { ascending: false })
      .limit(10);

    const valorTotalDespesas = despesas?.reduce((sum, d) => sum + (d.custo || 0), 0) || 0;
    const despesasPendentes = despesas?.filter(d => !d.pago).reduce((sum, d) => sum + (d.custo || 0), 0) || 0;

    const despesasRecentes = despesas?.slice(0, 8).map(d => ({
      descricao: d.descricao,
      valor: d.custo || 0,
      data: d.data_despesa,
      categoria: d.categoria,
      pago: d.pago || false
    })) || [];

    return {
      total_despesas: totalDespesas || 0,
      valor_total_despesas: valorTotalDespesas,
      despesas_pendentes: despesasPendentes,
      despesas_recentes: despesasRecentes,
    };
  } catch (error) {
    console.error("Erro ao buscar contexto de despesas:", error);
    return { total_despesas: 0, valor_total_despesas: 0, despesas_pendentes: 0, despesas_recentes: [] };
  }
}

/**
 * 📄 Busca contexto das notas fiscais para a IA (com validação de segurança)
 */
async function buscarContextoNotasFiscais(
  supabase: SupabaseClient,
  user_id: string,
): Promise<any> {
  try {
    // 🛡️ SEGURANÇA: Buscar tenant_id do usuário autenticado
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('tenant_id')
      .eq('id', user_id)
      .single();

    if (!userProfile?.tenant_id) {
      return { total_notas: 0, notas_recentes: [] };
    }

    // 🛡️ SEGURANÇA: Buscar notas fiscais COM filtro por tenant
    const { data: notas, count: totalNotas } = await supabase
      .from("notas_fiscais")
      .select(`
        *,
        fornecedores_pj (nome_fantasia, razao_social),
        fornecedores_pf (nome)
      `, { count: 'exact' })
      .eq("tenant_id", userProfile.tenant_id)
      .order("data_emissao", { ascending: false })
      .limit(10);

    const notasRecentes = notas?.slice(0, 8).map(n => ({
      numero: n.numero,
      data_emissao: n.data_emissao,
      valor: n.valor || 0,
      fornecedor: n.fornecedores_pj?.nome_fantasia || n.fornecedores_pf?.nome || 'Não informado'
    })) || [];

    return {
      total_notas: totalNotas || 0,
      notas_recentes: notasRecentes,
    };
  } catch (error) {
    console.error("Erro ao buscar contexto de notas fiscais:", error);
    return { total_notas: 0, notas_recentes: [] };
  }
}

/**
 * 📋 Busca contexto dos contratos para a IA (com validação de segurança)
 */
async function buscarContextoContratos(
  supabase: SupabaseClient,
  user_id: string,
): Promise<any> {
  try {
    // 🛡️ SEGURANÇA: Buscar tenant_id do usuário autenticado
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('tenant_id')
      .eq('id', user_id)
      .single();

    if (!userProfile?.tenant_id) {
      return { total_contratos: 0, contratos_ativos: 0, contratos_recentes: [] };
    }

    // 🛡️ SEGURANÇA: Buscar contratos COM filtro por tenant
    const { data: contratos, count: totalContratos } = await supabase
      .from("contratos")
      .select("*", { count: 'exact' })
      .eq("tenant_id", userProfile.tenant_id)
      .order("data_inicio", { ascending: false })
      .limit(10);

    const contratosAtivos = contratos?.filter(c => c.status === 'ativo').length || 0;

    const contratosRecentes = contratos?.slice(0, 8).map(c => ({
      numero: c.numero,
      descricao: c.descricao,
      valor: c.valor_contrato || 0,
      data_inicio: c.data_inicio,
      status: c.status
    })) || [];

    return {
      total_contratos: totalContratos || 0,
      contratos_ativos: contratosAtivos,
      contratos_recentes: contratosRecentes,
    };
  } catch (error) {
    console.error("Erro ao buscar contexto de contratos:", error);
    return { total_contratos: 0, contratos_ativos: 0, contratos_recentes: [] };
  }
}

/**
 * 📈 Busca contexto do controle orçamentário para a IA (com validação de segurança)
 */
async function buscarContextoControleOrcamentario(
  supabase: SupabaseClient,
  user_id: string,
): Promise<any> {
  try {
    // 🛡️ SEGURANÇA: Buscar tenant_id do usuário autenticado
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('tenant_id')
      .eq('id', user_id)
      .single();

    if (!userProfile?.tenant_id) {
      return { obras_ativas: 0, total_orcado: 0, total_gasto: 0, percentual_executado: 0, obras_acima_orcamento: 0 };
    }

    // 🛡️ SEGURANÇA: Buscar obras ativas COM filtro por tenant
    const { data: obras, count: obrasAtivas } = await supabase
      .from("obras")
      .select("*", { count: 'exact' })
      .eq("tenant_id", userProfile.tenant_id)
      .eq("status", "ativo");

    const totalOrcado = obras?.reduce((sum, o) => sum + (o.orcamento || 0), 0) || 0;

    // Buscar despesas totais das obras ativas
    const obraIds = obras?.map(o => o.id) || [];
    let totalGasto = 0;
    let obrasAcimaOrcamento = 0;

    if (obraIds.length > 0) {
      const { data: despesas } = await supabase
        .from("despesas")
        .select("custo, obra_id")
        .in("obra_id", obraIds)
        .eq("tenant_id", userProfile.tenant_id);

      const gastosPorObra = despesas?.reduce((acc, d) => {
        acc[d.obra_id] = (acc[d.obra_id] || 0) + (d.custo || 0);
        return acc;
      }, {} as Record<string, number>) || {};

      totalGasto = Object.values(gastosPorObra).reduce((sum, gasto) => sum + gasto, 0);

      // Contar obras acima do orçamento
      obras?.forEach(obra => {
        const gastoObra = gastosPorObra[obra.id] || 0;
        if (gastoObra > (obra.orcamento || 0)) {
          obrasAcimaOrcamento++;
        }
      });
    }

    const percentualExecutado = totalOrcado > 0 ? (totalGasto / totalOrcado) * 100 : 0;

    return {
      obras_ativas: obrasAtivas || 0,
      total_orcado: totalOrcado,
      total_gasto: totalGasto,
      percentual_executado: Math.round(percentualExecutado * 100) / 100,
      obras_acima_orcamento: obrasAcimaOrcamento,
    };
  } catch (error) {
    console.error("Erro ao buscar contexto do controle orçamentário:", error);
    return { obras_ativas: 0, total_orcado: 0, total_gasto: 0, percentual_executado: 0, obras_acima_orcamento: 0 };
  }
}

/**
 * 🧠 Busca contexto completo da obra para a IA (com validação de segurança)
 */
async function buscarContextoObra(
  supabase: SupabaseClient,
  obra_id: string,
  user_id: string,
): Promise<ContextoObra | null> {
  try {
    // 🛡️ SEGURANÇA: Buscar tenant_id do usuário autenticado
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('tenant_id')
      .eq('id', user_id)
      .single();

    if (!userProfile?.tenant_id) {
      console.error('❌ Usuário sem tenant_id válido:', user_id);
      return null;
    }

    // 🛡️ SEGURANÇA: Buscar dados da obra COM filtro por tenant
    const { data: obra, error: obraError } = await supabase
      .from("obras")
      .select("*")
      .eq("id", obra_id)
      .eq("tenant_id", userProfile.tenant_id)  // ✅ FILTRO DE SEGURANÇA
      .single();

    if (obraError || !obra) {
      console.error("Erro ao buscar obra:", obraError);
      return null;
    }

    // 🛡️ SEGURANÇA: Buscar despesas da obra COM filtro por tenant
    const { data: despesas } = await supabase
      .from("despesas")
      .select(`
        *,
        fornecedores_pj (razao_social, nome_fantasia),
        fornecedores_pf (nome)
      `)
      .eq("obra_id", obra_id)
      .eq("tenant_id", userProfile.tenant_id)  // ✅ FILTRO DE SEGURANÇA
      .order("data_despesa", { ascending: false })
      .limit(10);

    // 🛡️ SEGURANÇA: Buscar notas fiscais COM filtro por tenant
    const { data: notasFiscais } = await supabase
      .from("notas_fiscais")
      .select("*")
      .eq("obra_id", obra_id)
      .eq("tenant_id", userProfile.tenant_id)  // ✅ FILTRO DE SEGURANÇA
      .order("data_emissao", { ascending: false })
      .limit(5);

    // Calcular estatísticas
    const totalDespesas = despesas?.reduce((sum: number, d: Despesa) =>
      sum + (d.custo || 0), 0) || 0;
    const despesasPagas = despesas?.filter((d: Despesa) =>
      d.pago
    ).reduce((sum: number, d: Despesa) =>
      sum + (d.custo || 0), 0) || 0;
    const despesasPendentes = totalDespesas - despesasPagas;

    return {
      obra: {
        nome: obra.nome,
        endereco: `${obra.endereco}, ${obra.cidade}/${obra.estado}`,
        orcamento: obra.orcamento,
        data_inicio: obra.data_inicio,
        data_prevista_termino: obra.data_prevista_termino,
        dias_em_andamento: obra.data_inicio
          ? Math.floor(
            (Date.now() - new Date(obra.data_inicio).getTime()) /
              (1000 * 60 * 60 * 24),
          )
          : 0,
      },
      financeiro: {
        orcamento_total: obra.orcamento,
        total_gasto: totalDespesas,
        total_pago: despesasPagas,
        total_pendente: despesasPendentes,
        percentual_gasto: obra.orcamento > 0
          ? (totalDespesas / obra.orcamento * 100).toFixed(2)
          : "0",
        saldo_disponivel: obra.orcamento - totalDespesas,
      },
      despesas_recentes: despesas?.slice(0, 5).map((d: Despesa) => ({
        descricao: d.descricao,
        valor: d.custo,
        data: d.data_despesa,
        categoria: d.categoria,
        pago: d.pago,
        fornecedor: d.fornecedores_pj?.nome_fantasia ||
          d.fornecedores_pf?.nome || "Não informado",
      })),
      total_despesas: despesas?.length || 0,
      total_notas_fiscais: notasFiscais?.length || 0,
    };
  } catch (error) {
    console.error("Erro ao buscar contexto:", error);
    return null;
  }
}

/**
 * 🤖 Processa a mensagem com DeepSeek
 */
async function processarComDeepSeek(
  prompt: string,
  contextoGeral: ContextoGeral,
): Promise<string> {
  try {
    const systemPrompt =
      `Você é um assistente especializado em gestão de obras da construção civil brasileira.
    
${
        contextoGeral.obra
          ? `CONTEXTO DA OBRA ATUAL:
- Nome: ${contextoGeral.obra.obra.nome}
- Endereço: ${contextoGeral.obra.obra.endereco}
- Orçamento: R$ ${contextoGeral.obra.financeiro.orcamento_total?.toLocaleString("pt-BR")}
- Total Gasto: R$ ${
            contextoGeral.obra.financeiro.total_gasto?.toLocaleString("pt-BR")
          } (${contextoGeral.obra.financeiro.percentual_gasto}%)
- Saldo Disponível: R$ ${
            contextoGeral.obra.financeiro.saldo_disponivel?.toLocaleString("pt-BR")
          }
- Despesas Pendentes: R$ ${
            contextoGeral.obra.financeiro.total_pendente?.toLocaleString("pt-BR")
          }
- Dias em andamento: ${contextoGeral.obra.obra.dias_em_andamento}
- Total de despesas registradas: ${contextoGeral.obra.total_despesas}
- Total de notas fiscais: ${contextoGeral.obra.total_notas_fiscais}

DESPESAS RECENTES:
${
            contextoGeral.obra.despesas_recentes?.map((d) =>
              `- ${d.descricao}: R$ ${
                d.valor?.toLocaleString("pt-BR")
              } (${d.data}) - ${d.pago ? "PAGO" : "PENDENTE"}`
            ).join("\n") || "Nenhuma despesa registrada"
          }
`
          : "Nenhuma obra selecionada no momento."
      }

CONTEXTO DOS FORNECEDORES:
- Total de fornecedores cadastrados: ${contextoGeral.fornecedores.total_geral}
- Fornecedores PJ (Pessoa Jurídica): ${contextoGeral.fornecedores.total_pj}
- Fornecedores PF (Pessoa Física): ${contextoGeral.fornecedores.total_pf}

FORNECEDORES RECENTES:
${
  contextoGeral.fornecedores.fornecedores_recentes?.map((f) =>
    `- ${f.nome} (${f.tipo}) - ${f.categoria} - ${f.cidade} ${f.telefone ? `- Tel: ${f.telefone}` : ''}`
  ).join("\n") || "Nenhum fornecedor cadastrado"
}

CONTEXTO DAS VENDAS:
- Total de vendas realizadas: ${contextoGeral.vendas.total_vendas}
- Faturamento total: R$ ${contextoGeral.vendas.faturamento_total?.toLocaleString("pt-BR")}

VENDAS RECENTES:
${
  contextoGeral.vendas.vendas_recentes?.map((v) =>
    `- ${v.nome_cliente}: R$ ${v.valor_venda?.toLocaleString("pt-BR")} (${v.data_venda}) - Status: ${v.status}`
  ).join("\n") || "Nenhuma venda registrada"
}

CONTEXTO DOS ORÇAMENTOS:
- Total de orçamentos: ${contextoGeral.orcamentos.total_orcamentos}
- Valor total dos orçamentos: R$ ${contextoGeral.orcamentos.valor_total_orcamentos?.toLocaleString("pt-BR")}

ORÇAMENTOS RECENTES:
${
  contextoGeral.orcamentos.orcamentos_recentes?.map((o) =>
    `- ${o.nome_obra}: R$ ${o.valor_total?.toLocaleString("pt-BR")} (${o.data_criacao}) - Status: ${o.status}`
  ).join("\n") || "Nenhum orçamento registrado"
}

CONTEXTO DAS DESPESAS:
- Total de despesas: ${contextoGeral.despesas.total_despesas}
- Valor total das despesas: R$ ${contextoGeral.despesas.valor_total_despesas?.toLocaleString("pt-BR")}
- Despesas pendentes: R$ ${contextoGeral.despesas.despesas_pendentes?.toLocaleString("pt-BR")}

DESPESAS RECENTES:
${
  contextoGeral.despesas.despesas_recentes?.map((d) =>
    `- ${d.descricao}: R$ ${d.valor?.toLocaleString("pt-BR")} (${d.data}) - ${d.pago ? "PAGO" : "PENDENTE"}`
  ).join("\n") || "Nenhuma despesa registrada"
}

CONTEXTO DAS NOTAS FISCAIS:
- Total de notas fiscais: ${contextoGeral.notas_fiscais.total_notas}

NOTAS FISCAIS RECENTES:
${
  contextoGeral.notas_fiscais.notas_recentes?.map((n) =>
    `- NF ${n.numero}: R$ ${n.valor?.toLocaleString("pt-BR")} (${n.data_emissao}) - Fornecedor: ${n.fornecedor}`
  ).join("\n") || "Nenhuma nota fiscal registrada"
}

CONTEXTO DOS CONTRATOS:
- Total de contratos: ${contextoGeral.contratos.total_contratos}
- Contratos ativos: ${contextoGeral.contratos.contratos_ativos}

CONTRATOS RECENTES:
${
  contextoGeral.contratos.contratos_recentes?.map((c) =>
    `- ${c.numero}: ${c.descricao} - R$ ${c.valor?.toLocaleString("pt-BR")} (${c.data_inicio}) - Status: ${c.status}`
  ).join("\n") || "Nenhum contrato registrado"
}

CONTEXTO DO CONTROLE ORÇAMENTÁRIO:
- Obras ativas: ${contextoGeral.controle_orcamentario.obras_ativas}
- Total orçado: R$ ${contextoGeral.controle_orcamentario.total_orcado?.toLocaleString("pt-BR")}
- Total gasto: R$ ${contextoGeral.controle_orcamentario.total_gasto?.toLocaleString("pt-BR")}
- Percentual executado: ${contextoGeral.controle_orcamentario.percentual_executado}%
- Obras acima do orçamento: ${contextoGeral.controle_orcamentario.obras_acima_orcamento}

DIRETRIZES:
- Responda sempre em português brasileiro
- Use conhecimento técnico da construção civil
- Considere normas ABNT e legislação brasileira
- Seja prático, objetivo e profissional
- Forneça insights úteis baseados nos dados disponíveis
- Se não houver dados suficientes, seja honesto e sugira o que pode ser feito
- Sempre considere o contexto financeiro da obra ao dar sugestões
- IMPORTANTE: Não use caracteres de formatação markdown como #, *, **, ___ ou similares em suas respostas. Responda sempre em texto simples e limpo, sem formatação especial. Use apenas texto corrido com quebras de linha quando necessário para organizar a informação.`;

    const response = await fetch(DEEPSEEK_API_URL, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${DEEPSEEK_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "deepseek-chat",
        messages: [
          {
            role: "system",
            content: systemPrompt,
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error("Erro na API DeepSeek:", response.status, errorData);
      throw new Error(`Erro na API DeepSeek: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error("Erro ao processar com DeepSeek:", error);
    throw error;
  }
}

serve(async (req) => {
  const startTime = Date.now();
  const origin = req.headers.get("origin");
  const corsHeaders = getSecureCorsHeaders(origin);

  // ✅ Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: getPreflightHeaders(origin) });
  }

  try {
    // ✅ Verificação de método HTTP
    if (req.method !== "POST") {
      return new Response(
        JSON.stringify({ error: "Method not allowed" }),
        {
          status: 405,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // ✅ Rate limiting
    const clientIP = req.headers.get("x-forwarded-for") ||
      req.headers.get("x-real-ip") ||
      "unknown";

    if (!checkRateLimit(clientIP, 10, 60000)) { // 10 requests por minuto
      return new Response(
        JSON.stringify({ error: "Rate limit exceeded. Try again later." }),
        {
          status: 429,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // ✅ Verificação CSRF
    const csrfToken = req.headers.get("x-csrf-token");

    if (!validateCSRFToken(csrfToken, origin)) {
      return new Response(
        JSON.stringify({ error: "Invalid CSRF token or unauthorized origin" }),
        {
          status: 403,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // ✅ Validação de autenticação
    const authHeader = req.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return new Response(
        JSON.stringify({ error: "Authorization required" }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    const requestBody = await req.json() as ChatRequest;

    // ✅ Validação de entrada robusta com Zod
    const validationResult = aiChatSchema.safeParse(requestBody);

    if (!validationResult.success) {
      return new Response(
        JSON.stringify({
          error: "Dados de entrada inválidos",
          details: validationResult.error.flatten().fieldErrors,
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // ✅ Usar dados validados e sanitizados pelo Zod
    const {
      message: sanitizedMessage,
      obra_id: sanitizedObraId,
      user_id: sanitizedUserId,
    } = validationResult.data;

    // Verificar se a API key do DeepSeek está configurada
    if (!DEEPSEEK_API_KEY) {
      console.error("DEEPSEEK_API key não configurada");
      return new Response(
        JSON.stringify({
          error:
            "Serviço de IA temporariamente indisponível. Por favor, tente novamente mais tarde.",
        }),
        {
          status: 503,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // Criar cliente do Supabase
    const supabaseUrl = Deno.env.get("SUPABASE_URL") as string;
    const supabaseServiceKey = Deno.env.get(
      "SUPABASE_SERVICE_ROLE_KEY",
    ) as string;

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Missing Supabase configuration");
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // ✅ Validação do JWT do usuário
    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !userData.user) {
      return new Response(
        JSON.stringify({ error: "Token de autenticação inválido" }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // Verificar se o user_id da requisição corresponde ao usuário autenticado
    if (sanitizedUserId !== userData.user.id) {
      return new Response(
        JSON.stringify({ error: "User ID não corresponde ao token" }),
        {
          status: 403,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        },
      );
    }

    // 🛡️ SEGURANÇA: Buscar contexto geral sempre incluindo todas as seções
    let contextoGeral: ContextoGeral = {
      fornecedores: {
        total_pj: 0,
        total_pf: 0,
        total_geral: 0,
        fornecedores_recentes: []
      },
      vendas: {
        total_vendas: 0,
        faturamento_total: 0,
        vendas_recentes: []
      },
      orcamentos: {
        total_orcamentos: 0,
        valor_total_orcamentos: 0,
        orcamentos_recentes: []
      },
      despesas: {
        total_despesas: 0,
        valor_total_despesas: 0,
        despesas_pendentes: 0,
        despesas_recentes: []
      },
      notas_fiscais: {
        total_notas: 0,
        notas_recentes: []
      },
      contratos: {
        total_contratos: 0,
        contratos_ativos: 0,
        contratos_recentes: []
      },
      controle_orcamentario: {
        obras_ativas: 0,
        total_orcado: 0,
        total_gasto: 0,
        percentual_executado: 0,
        obras_acima_orcamento: 0
      }
    };

    // Buscar contexto de todas as seções sempre (para qualquer pergunta)
    if (sanitizedUserId) {
      contextoGeral.fornecedores = await buscarContextoFornecedores(supabase, sanitizedUserId);
      contextoGeral.vendas = await buscarContextoVendas(supabase, sanitizedUserId);
      contextoGeral.orcamentos = await buscarContextoOrcamentos(supabase, sanitizedUserId);
      contextoGeral.despesas = await buscarContextoDespesas(supabase, sanitizedUserId);
      contextoGeral.notas_fiscais = await buscarContextoNotasFiscais(supabase, sanitizedUserId);
      contextoGeral.contratos = await buscarContextoContratos(supabase, sanitizedUserId);
      contextoGeral.controle_orcamentario = await buscarContextoControleOrcamentario(supabase, sanitizedUserId);
    }

    // Buscar contexto da obra se fornecido com validação de usuário
    if (sanitizedObraId && sanitizedUserId) {
      contextoGeral.obra = await buscarContextoObra(supabase, sanitizedObraId, sanitizedUserId);
    } else if (sanitizedObraId && !sanitizedUserId) {
      console.error('❌ SEGURANÇA: Tentativa de acesso à obra sem user_id');
      return new Response(
        JSON.stringify({ 
          error: 'Acesso negado: usuário não autenticado para acessar dados da obra' 
        }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // 🤖 Processar com DeepSeek
    let botResponse: string;
    try {
      botResponse = await processarComDeepSeek(sanitizedMessage, contextoGeral);
    } catch (error) {
      console.error("Erro ao processar com IA:", error);
      botResponse =
        "Desculpe, estou com dificuldades para processar sua mensagem no momento. Por favor, tente novamente em alguns instantes.";
    }

    // Inserir a mensagem e resposta no banco de dados
    const { data, error } = await supabase
      .from("chat_messages")
      .insert({
        usuario_id: sanitizedUserId,
        obra_id: sanitizedObraId || null,
        mensagem: sanitizedMessage,
        resposta_bot: botResponse,
      })
      .select("*")
      .single();

    if (error) throw error;

    // Calcular métricas
    const tempoResposta = Date.now() - startTime;

    return new Response(
      JSON.stringify({
        result: data,
        metrics: {
          tempo_resposta_ms: tempoResposta,
          contexto_usado: !!contextoGeral.obra,
          obra_nome: contextoGeral.obra?.obra?.nome || null,
          fornecedores_total: contextoGeral.fornecedores.total_geral,
          fornecedores_contexto_usado: true,
        },
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  } catch (error) {
    // ✅ Log seguro de erro
    const errorWithCode = error as ErrorWithCode;
    console.error("AI chat handler error:", {
      timestamp: new Date().toISOString(),
      errorCode: errorWithCode?.code,
      hasMessage: !!errorWithCode?.message,
      error: errorWithCode?.message || "Unknown error",
    });

    return new Response(
      JSON.stringify({ error: "Internal server error" }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      },
    );
  }
});