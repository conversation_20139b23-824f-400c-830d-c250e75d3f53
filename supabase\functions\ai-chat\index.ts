import { z } from "https://deno.land/x/zod@v3.23.8/mod.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Imports padronizados


// Schema de validação para o chat de métricas
const metricsSchema = z.object({
  leads: z.object({
    total: z.number().min(0),
    converted: z.number().min(0)
  }),
  users: z.object({
    total: z.number().min(0),
    active: z.number().min(0),
    churn: z.number().min(0).max(100)
  }),
  revenue: z.object({
    mrr: z.number().min(0),
    arr: z.number().min(0),
    ltv: z.number().min(0),
    cac: z.number().min(0)
  }),
  product: z.object({
    aiUsage: z.number().min(0),
    orcamentos: z.number().min(0)
  })
});

const chatRequestSchema = z.object({
  message: z.string()
    .min(1, "Mensagem não pode estar vazia")
    .max(1000, "Mensagem muito longa"),
  metrics: metricsSchema
});

type ChatRequest = z.infer<typeof chatRequestSchema>;

/**
 * Processa chat com IA usando OpenAI
 */
async function processAIChat(
  message: string,
  metrics: ChatRequest['metrics'],
  logger: any
): Promise<string> {
  const openaiApiKey = Deno.env.get('OPENAI_API_KEY');

  logger.info('Processing AI chat request', {
    hasApiKey: !!openaiApiKey,
    messageLength: message.length
  });

  if (!openaiApiKey) {
    logger.warn('OpenAI API Key not found, using fallback');
    return generateSmartFallback(message, metrics);
  }

  // Prompt especializado para ObrasAI
  const conversionRate = ((metrics.leads.converted / metrics.leads.total) * 100).toFixed(1);
  const ltvCacRatio = (metrics.revenue.ltv / metrics.revenue.cac).toFixed(1);

  const prompt = `
Você é um consultor especialista em métricas de negócio para o ObrasAI, uma plataforma de gestão de obras.

CONTEXTO DAS MÉTRICAS ATUAIS:
- Leads: ${metrics.leads.total} total, ${metrics.leads.converted} convertidos (${conversionRate}%)
- Usuários: ${metrics.users.total} total, ${metrics.users.active} ativos, ${metrics.users.churn}% churn
- Receita: R$ ${metrics.revenue.mrr.toLocaleString()} MRR, R$ ${metrics.revenue.arr.toLocaleString()} ARR
- LTV/CAC: R$ ${metrics.revenue.ltv.toLocaleString()} / R$ ${metrics.revenue.cac.toLocaleString()} = ${ltvCacRatio}x
- Produto: ${metrics.product.aiUsage} usos de IA, ${metrics.product.orcamentos} orçamentos gerados

PERGUNTA DO USUÁRIO: "${message}"

Responda de forma:
- Direta e prática (máximo 150 palavras)
- Com insights específicos baseados nos números
- Sugerindo ações concretas quando relevante
- Tom consultivo e profissional
- Focado no contexto de gestão de obras

Não mencione que você é uma IA ou que tem limitações.
`;

  try {
    // Chamada para OpenAI
    logger.debug('Making OpenAI API call');
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${openaiApiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: prompt + '\n\nIMPORTANTE: Não use caracteres de formatação markdown como #, *, **, ___ ou similares em suas respostas. Responda sempre em texto simples e limpo, sem formatação especial. Use apenas texto corrido com quebras de linha quando necessário para organizar a informação.'
          }
        ],
        max_tokens: 200,
        temperature: 0.7
      })
    });

    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text();
      logger.error('OpenAI API error', { status: openaiResponse.status, error: errorText });
      return generateSmartFallback(message, metrics);
    }

    const openaiData = await openaiResponse.json();
    const aiResponse = openaiData.choices[0]?.message?.content || 'Não consegui gerar uma resposta adequada.';

    logger.info('OpenAI response received successfully');
    return aiResponse;

  } catch (error) {
    logger.error('Error calling OpenAI API', error);
    return generateSmartFallback(message, metrics);
  }
}

// Implementação da Edge Function usando template padronizado
export default createEdgeFunction({
  name: 'ai-chat',
  version: '2.0.0',
  ...AI_FUNCTION_CONFIG,
  requiresAuth: false, // Chat público para métricas
  validation: {
    bodySchema: chatRequestSchema
  }
}, async ({ body, logger, requestId }) => {
  const { message, metrics } = body!;

  logger.info('Processing metrics chat request', {
    messageLength: message.length,
    metricsKeys: Object.keys(metrics)
  });

  try {
    const response = await processAIChat(message, metrics, logger);

    logger.info('Chat response generated successfully', {
      responseLength: response.length
    });

    return createSuccessResponse({
      response,
      requestId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error processing chat request', error);

    // Fallback em caso de erro
    const fallbackResponse = generateSmartFallback(message, metrics);

    return createSuccessResponse({
      response: fallbackResponse,
      requestId,
      timestamp: new Date().toISOString(),
      fallback: true
    });
  }
});

// 🤖 Função de fallback inteligente
function generateSmartFallback(message: string, metrics: ChatRequest['metrics']): string {
  const msg = message.toLowerCase()
  
  // Análise de conversão
  if (msg.includes('conversão') || msg.includes('convert')) {
    const conversionRate = ((metrics.leads.converted / metrics.leads.total) * 100).toFixed(1)
    return `Sua taxa de conversão atual é de ${conversionRate}%. ${
      parseFloat(conversionRate) < 3 
        ? 'Considere otimizar o funil de vendas e qualificar melhor os leads.' 
        : 'Boa performance! Continue monitorando e testando melhorias.'
    }`
  }

  // Análise de churn
  if (msg.includes('churn') || msg.includes('cancelamento')) {
    return `Seu churn rate está em ${metrics.users.churn}%. ${
      metrics.users.churn > 5 
        ? 'Foque em melhorar a experiência do usuário e suporte ao cliente.' 
        : 'Churn controlado. Mantenha o foco na satisfação dos clientes.'
    }`
  }

  // Análise LTV/CAC
  if (msg.includes('ltv') || msg.includes('cac') || msg.includes('retorno')) {
    const ratio = (metrics.revenue.ltv / metrics.revenue.cac).toFixed(1)
    return `Sua relação LTV/CAC é de ${ratio}x. ${
      parseFloat(ratio) < 3 
        ? 'Otimize os custos de aquisição ou aumente o valor do cliente.' 
        : 'Excelente! Seus investimentos em marketing estão gerando bom retorno.'
    }`
  }

  // Análise de receita
  if (msg.includes('receita') || msg.includes('mrr') || msg.includes('arr')) {
    return `Sua receita mensal recorrente é R$ ${metrics.revenue.mrr.toLocaleString()}, projetando R$ ${metrics.revenue.arr.toLocaleString()} anuais. Foque em aumentar o ticket médio e reduzir o churn para acelerar o crescimento.`
  }

  // Análise de produto
  if (msg.includes('ia') || msg.includes('produto') || msg.includes('uso')) {
    return `Seus usuários fizeram ${metrics.product.aiUsage} consultas à IA e geraram ${metrics.product.orcamentos} orçamentos. ${
      metrics.product.aiUsage > 1000 
        ? 'Alto engajamento com a IA! Considere expandir essas funcionalidades.' 
        : 'Promova mais o uso da IA para aumentar o valor percebido.'
    }`
  }

  // Resposta genérica
  return `Com base nas suas métricas atuais: ${metrics.leads.total} leads, ${((metrics.leads.converted / metrics.leads.total) * 100).toFixed(1)}% de conversão, ${metrics.users.churn}% de churn e LTV/CAC de ${(metrics.revenue.ltv / metrics.revenue.cac).toFixed(1)}x. Posso ajudar com análises específicas sobre conversão, churn, receita ou uso do produto.`
}