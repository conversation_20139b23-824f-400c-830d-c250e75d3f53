import "jsr:@supabase/functions-js/edge-runtime.d.ts"

interface ChatRequest {
  message: string
  visitor_id?: string
}

interface ChatResponse {
  response: string
  error?: string
}

Deno.serve(async (req: Request) => {
  // CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  }

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verificar método
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Método não permitido' }),
        { 
          status: 405, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse do body
    const { message, visitor_id }: ChatRequest = await req.json()

    if (!message) {
      return new Response(
        JSON.stringify({ error: 'Mensagem é obrigatória' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Rate limiting simples por visitor_id
    // TODO: Implementar rate limiting mais robusto se necessário

    // Obter API key do ambiente
    const deepseekApiKey = Deno.env.get('DEEPSEEK_API')

    console.log('🔑 DeepSeek API Key disponível:', deepseekApiKey ? 'SIM' : 'NÃO')

    if (!deepseekApiKey) {
      console.log('⚠️ DeepSeek API Key não encontrada, usando fallback')
      // Fallback inteligente
      const fallbackResponse = generateSmartFallback(message)
      
      return new Response(
        JSON.stringify({ response: fallbackResponse }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Prompt especializado para ObrasAI Landing Page
    const prompt = `
Você é um assistente especialista em construção civil e gestão de obras, representando o ObrasAI.

SOBRE O OBRASAI:
- Plataforma completa para gestão de obras com IA integrada
- Controle financeiro inteligente e orçamento automatizado
- Sistema SINAPI integrado para composições precisas
- Gestão de fornecedores e materiais
- Interface moderna e intuitiva
- Relatórios e dashboards em tempo real
- Integração com ferramentas de pagamento
- Suporte especializado em construção civil

FUNCIONALIDADES PRINCIPAIS:
- Orçamento inteligente com IA
- Controle de gastos em tempo real
- Gestão de cronograma de obras
- Controle de estoque de materiais
- Gestão de fornecedores
- Relatórios financeiros detalhados
- Sistema de alertas e notificações
- Integração com bancos e cartões

PERGUNTA DO USUÁRIO: "${message}"

Responda de forma:
- Amigável e consultiva
- Focada nos benefícios do ObrasAI
- Prática e objetiva (máximo 200 palavras)
- Destacando como o ObrasAI resolve problemas específicos
- Incentivando o interesse na plataforma
- Sem usar formatação markdown

Se a pergunta for sobre preços, mencione que temos planos flexíveis e que a equipe comercial pode apresentar a melhor opção.
Se for sobre funcionalidades específicas, explique como o ObrasAI resolve esse problema.
Se for uma pergunta geral sobre construção, responda e conecte com os benefícios do ObrasAI.
`

    // Chamada para DeepSeek
    const deepseekResponse = await fetch('https://api.deepseek.com/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${deepseekApiKey}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: prompt + '\n\nIMPORTANTE: Não use caracteres de formatação markdown como #, *, **, ___ ou similares em suas respostas. Responda sempre em texto simples e limpo, sem formatação especial. Use apenas texto corrido com quebras de linha quando necessário para organizar a informação.'
          },
          {
            role: 'user',
            content: message
          }
        ],
        max_tokens: 250,
        temperature: 0.7
      })
    })

    if (!deepseekResponse.ok) {
      const errorText = await deepseekResponse.text()
      console.error('❌ Erro DeepSeek:', errorText)
      
      // Fallback em caso de erro
      const fallbackResponse = generateSmartFallback(message)
      
      return new Response(
        JSON.stringify({ response: fallbackResponse }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const deepseekData = await deepseekResponse.json()
    const aiResponse = deepseekData.choices[0]?.message?.content || 'Não consegui gerar uma resposta adequada.'

    console.log('✅ Resposta DeepSeek recebida com sucesso')

    return new Response(
      JSON.stringify({ response: aiResponse }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Erro na Edge Function:', error)
    
    // Fallback em caso de erro
    const fallbackResponse = generateSmartFallback('')
    
    return new Response(
      JSON.stringify({ response: fallbackResponse }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

// 🤖 Função de fallback inteligente
function generateSmartFallback(message: string): string {
  const msg = message.toLowerCase()
  
  // Perguntas sobre orçamento
  if (msg.includes('orçamento') || msg.includes('orcamento') || msg.includes('custo') || msg.includes('preço')) {
    return 'O ObrasAI oferece orçamento inteligente com IA que utiliza a base SINAPI atualizada. Nossa ferramenta calcula automaticamente custos de materiais, mão de obra e equipamentos, gerando orçamentos precisos em minutos. Você pode comparar fornecedores e otimizar custos em tempo real!'
  }

  // Perguntas sobre controle financeiro
  if (msg.includes('financeiro') || msg.includes('gasto') || msg.includes('controle') || msg.includes('dinheiro')) {
    return 'Com o ObrasAI você tem controle financeiro completo da sua obra! Acompanhe gastos em tempo real, receba alertas de desvios no orçamento, gerencie fluxo de caixa e tenha relatórios detalhados. Nunca mais perca dinheiro por falta de controle!'
  }

  // Perguntas sobre SINAPI
  if (msg.includes('sinapi') || msg.includes('composição') || msg.includes('insumo')) {
    return 'O ObrasAI tem integração completa com o SINAPI! Acesse todas as composições atualizadas, busque insumos por região, compare preços e gere orçamentos oficiais. Nossa base é atualizada mensalmente e inclui desonerações automáticas.'
  }

  // Perguntas sobre gestão
  if (msg.includes('gestão') || msg.includes('gestao') || msg.includes('gerenciar') || msg.includes('administrar')) {
    return 'O ObrasAI é a plataforma mais completa para gestão de obras! Gerencie cronogramas, controle estoque, acompanhe fornecedores, monitore equipes e tenha dashboards em tempo real. Tudo integrado em uma única plataforma moderna e fácil de usar.'
  }

  // Perguntas sobre fornecedores
  if (msg.includes('fornecedor') || msg.includes('material') || msg.includes('compra')) {
    return 'Com o ObrasAI você gerencia todos os seus fornecedores em um só lugar! Compare preços, acompanhe entregas, controle qualidade e mantenha histórico completo. Nossa plataforma te ajuda a encontrar os melhores preços e negociar melhores condições.'
  }

  // Perguntas sobre relatórios
  if (msg.includes('relatório') || msg.includes('relatorio') || msg.includes('dashboard') || msg.includes('acompanhar')) {
    return 'O ObrasAI oferece relatórios e dashboards completos! Acompanhe o progresso da obra, analise custos por etapa, monitore produtividade das equipes e tenha insights valiosos para tomar melhores decisões. Tudo em tempo real e com gráficos intuitivos.'
  }

  // Perguntas sobre preços/planos
  if (msg.includes('plano') || msg.includes('valor') || msg.includes('quanto custa') || msg.includes('preço')) {
    return 'O ObrasAI oferece planos flexíveis para diferentes tipos de construtoras e profissionais. Temos opções desde pequenos projetos até grandes construtoras. Nossa equipe comercial pode apresentar o plano ideal para seu perfil e necessidades específicas!'
  }

  // Perguntas sobre funcionalidades
  if (msg.includes('funcionalidade') || msg.includes('recurso') || msg.includes('ferramenta')) {
    return 'O ObrasAI oferece gestão completa de obras com IA integrada! Principais recursos: orçamento inteligente, controle financeiro, gestão de fornecedores, sistema SINAPI, relatórios em tempo real, gestão de cronograma e muito mais. Tudo em uma plataforma moderna e intuitiva!'
  }

  // Resposta genérica
  return 'O ObrasAI é a plataforma mais completa para gestão de obras do Brasil! Oferecemos orçamento inteligente com IA, controle financeiro em tempo real, sistema SINAPI integrado, gestão de fornecedores e relatórios detalhados. Nossa equipe está pronta para mostrar como podemos transformar a gestão da sua obra!'
}