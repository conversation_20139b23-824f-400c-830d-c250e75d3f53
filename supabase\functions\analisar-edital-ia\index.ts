import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from "../_shared/cors.ts"
import { validateInput } from "../_shared/input-validation.ts"
import { securityHeaders } from "../_shared/security-headers.ts"

interface AnalisarEditalRequest {
  licitacao_id: string
  texto_edital?: string
  url_edital?: string
}

interface AnaliseIAResponse {
  resumo_objeto: string
  requisitos_principais: {
    qualificacao_tecnica: string[]
    qualificacao_economica: string[]
    documentos_habilitacao: string[]
    criterios_tecnicos: string[]
  }
  documentos_necessarios: string[]
  prazo_execucao: string
  valor_estimado_ia: string
  observacoes_ia: string
  nivel_complexidade: 'baixo' | 'medio' | 'alto'
}

const DEEPSEEK_API_URL = 'https://api.deepseek.com/chat/completions'

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Validação de autenticação
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Token de autorização necessário' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Validação de método HTTP
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Método não permitido' }),
        { 
          status: 405, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse e validação do corpo da requisição
    const body: AnalisarEditalRequest = await req.json()
    
    const validationResult = validateInput(body, {
      licitacao_id: { type: 'string', required: true },
      texto_edital: { type: 'string', optional: true, maxLength: 100000 },
      url_edital: { type: 'string', optional: true, maxLength: 500 }
    })

    if (!validationResult.isValid) {
      return new Response(
        JSON.stringify({ error: 'Dados inválidos', details: validationResult.errors }),
        { 
          status: 400, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!body.texto_edital && !body.url_edital) {
      return new Response(
        JSON.stringify({ error: 'É necessário fornecer texto_edital ou url_edital' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Conectar ao Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2')
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Extrair informações do usuário do JWT
    const jwt = authHeader.replace('Bearer ', '')
    const { data: { user }, error: userError } = await supabase.auth.getUser(jwt)
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Token inválido' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const tenantId = user.user_metadata?.tenant_id
    if (!tenantId) {
      return new Response(
        JSON.stringify({ error: 'Tenant ID não encontrado' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Verificar se a licitação existe
    const { data: licitacao, error: licitacaoError } = await supabase
      .from('licitacoes')
      .select('id, numero_licitacao, objeto, orgao')
      .eq('id', body.licitacao_id)
      .single()

    if (licitacaoError || !licitacao) {
      return new Response(
        JSON.stringify({ error: 'Licitação não encontrada' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Verificar se já existe análise em cache
    const { data: analiseExistente } = await supabase
      .from('licitacoes_analises_ia')
      .select('*')
      .eq('licitacao_id', body.licitacao_id)
      .eq('tenant_id', tenantId)
      .single()

    if (analiseExistente) {
      return new Response(
        JSON.stringify({
          success: true,
          data: {
            resumo_objeto: analiseExistente.resumo_objeto,
            requisitos_principais: analiseExistente.requisitos_principais,
            documentos_necessarios: analiseExistente.documentos_necessarios,
            prazo_execucao: analiseExistente.prazo_execucao,
            valor_estimado_ia: analiseExistente.valor_estimado_ia,
            observacoes_ia: analiseExistente.observacoes_ia,
            nivel_complexidade: analiseExistente.nivel_complexidade,
            analise_compatibilidade: analiseExistente.analise_compatibilidade
          },
          cached: true
        }),
        { 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Preparar texto para análise
    let textoParaAnalise = body.texto_edital || ''
    
    // Se apenas URL foi fornecida, fazer fetch do conteúdo (simplificado)
    if (!textoParaAnalise && body.url_edital) {
      try {
        const response = await fetch(body.url_edital)
        if (response.ok) {
          textoParaAnalise = await response.text()
          // Limitações básicas para evitar textos muito grandes
          if (textoParaAnalise.length > 50000) {
            textoParaAnalise = textoParaAnalise.substring(0, 50000) + '...'
          }
        }
      } catch (error) {
        console.error('Erro ao buscar edital da URL:', error)
        return new Response(
          JSON.stringify({ error: 'Não foi possível acessar a URL do edital' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
          }
        )
      }
    }

    if (!textoParaAnalise || textoParaAnalise.trim().length < 100) {
      return new Response(
        JSON.stringify({ error: 'Texto do edital muito curto ou inválido' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Prompt para análise de edital especializada em construção civil
    const prompt = `
Você é um especialista em licitações para construção civil. Analise o seguinte edital e extraia as informações mais importantes para uma construtora que está avaliando participar desta licitação.

CONTEXTO DA LICITAÇÃO:
- Número: ${licitacao.numero_licitacao}
- Objeto: ${licitacao.objeto}
- Órgão: ${licitacao.orgao}

TEXTO DO EDITAL:
${textoParaAnalise}

INSTRUÇÕES:
Responda APENAS com um JSON válido no seguinte formato, sem texto adicional:

{
  "resumo_objeto": "Resumo claro e objetivo do que será construído/executado",
  "requisitos_principais": {
    "qualificacao_tecnica": ["Requisito técnico 1", "Requisito técnico 2"],
    "qualificacao_economica": ["Requisito econômico 1", "Requisito econômico 2"],
    "documentos_habilitacao": ["Documento 1", "Documento 2"],
    "criterios_tecnicos": ["Critério técnico 1", "Critério técnico 2"]
  },
  "documentos_necessarios": ["Lista de documentos específicos necessários"],
  "prazo_execucao": "Prazo informado no edital",
  "valor_estimado_ia": "Valor estimado mencionado ou 'Não informado'",
  "observacoes_ia": "Observações importantes, cláusulas específicas, pontos de atenção",
  "nivel_complexidade": "baixo|medio|alto"
}

CRITÉRIOS PARA NÍVEL DE COMPLEXIDADE:
- baixo: Serviços simples, poucos requisitos técnicos
- medio: Obras padrão com requisitos moderados  
- alto: Projetos complexos, muitos requisitos técnicos específicos
`

    // Chamada para DeepSeek API  
    const deepseekApiKey = Deno.env.get('DEEPSEEK_API')
    if (!deepseekApiKey) {
      return new Response(
        JSON.stringify({ error: 'API key da IA não configurada' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const iaResponse = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${deepseekApiKey}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000
      })
    })

    if (!iaResponse.ok) {
      console.error('Erro na API DeepSeek:', await iaResponse.text())
      return new Response(
        JSON.stringify({ error: 'Erro na análise de IA' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const iaData = await iaResponse.json()
    const respostaIA = iaData.choices[0]?.message?.content

    if (!respostaIA) {
      return new Response(
        JSON.stringify({ error: 'Resposta inválida da IA' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse da resposta JSON da IA
    let analiseIA: AnaliseIAResponse
    try {
      analiseIA = JSON.parse(respostaIA)
    } catch (error) {
      console.error('Erro ao fazer parse da resposta da IA:', error)
      return new Response(
        JSON.stringify({ error: 'Formato de resposta inválido da IA' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Buscar perfil da construtora para análise de compatibilidade
    const { data: perfilConstrutora } = await supabase
      .from('construtora_perfil')
      .select('*')
      .eq('tenant_id', tenantId)
      .single()

    let analiseCompatibilidade = null

    // Se existe perfil da construtora, fazer análise de compatibilidade
    if (perfilConstrutora) {
      const promptCompatibilidade = `
Você é um especialista em análise de compatibilidade para licitações de construção civil.

DADOS DO EDITAL:
- Objeto: ${analiseIA.resumo_objeto}
- Requisitos Técnicos: ${JSON.stringify(analiseIA.requisitos_principais)}
- Documentos Necessários: ${analiseIA.documentos_necessarios.join(', ')}
- Nível de Complexidade: ${analiseIA.nivel_complexidade}
- Observações: ${analiseIA.observacoes_ia}

PERFIL DA CONSTRUTORA:
- Especialidades: ${perfilConstrutora.especialidades?.join(', ') || 'Não informado'}
- Capital Social: ${perfilConstrutora.capital_social || 'Não informado'}
- Experiências Anteriores: ${JSON.stringify(perfilConstrutora.experiencias_anteriores || {})}
- Equipe Técnica: ${JSON.stringify(perfilConstrutora.equipe_tecnica || {})}
- Capacidade Operacional: ${JSON.stringify(perfilConstrutora.capacidade_operacional || {})}

INSTRUÇÕES:
Analise a compatibilidade entre o perfil da construtora e os requisitos da licitação.
Responda APENAS com um JSON válido no seguinte formato:

{
  "nivel": "alto|medio|baixo",
  "score": 85,
  "justificativas": ["Justificativa 1", "Justificativa 2"],
  "recomendacao": "participar|avaliar|nao_participar",
  "pontos_fortes": ["Ponto forte 1", "Ponto forte 2"],
  "pontos_fracos": ["Ponto fraco 1", "Ponto fraco 2"]
}

CRITÉRIOS:
- alto (80-100): Excelente compatibilidade, recomendação "participar"
- medio (50-79): Compatibilidade moderada, recomendação "avaliar"
- baixo (0-49): Baixa compatibilidade, recomendação "nao_participar"
`

      try {
        const compatibilidadeResponse = await fetch(DEEPSEEK_API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${deepseekApiKey}`
          },
          body: JSON.stringify({
            model: 'deepseek-chat',
            messages: [
              {
                role: 'user',
                content: promptCompatibilidade
              }
            ],
            temperature: 0.1,
            max_tokens: 1000
          })
        })

        if (compatibilidadeResponse.ok) {
          const compatibilidadeData = await compatibilidadeResponse.json()
          const respostaCompatibilidade = compatibilidadeData.choices[0]?.message?.content

          if (respostaCompatibilidade) {
            try {
              analiseCompatibilidade = JSON.parse(respostaCompatibilidade)
            } catch (parseError) {
              console.error('Erro ao fazer parse da análise de compatibilidade:', parseError)
            }
          }
        }
      } catch (error) {
        console.error('Erro na análise de compatibilidade:', error)
      }
    }

    // Salvar resultado no cache
    const { error: insertError } = await supabase
      .from('licitacoes_analises_ia')
      .insert({
        licitacao_id: body.licitacao_id,
        tenant_id: tenantId,
        resumo_objeto: analiseIA.resumo_objeto,
        requisitos_principais: analiseIA.requisitos_principais,
        documentos_necessarios: analiseIA.documentos_necessarios,
        prazo_execucao: analiseIA.prazo_execucao,
        valor_estimado_ia: analiseIA.valor_estimado_ia,
        observacoes_ia: analiseIA.observacoes_ia,
        nivel_complexidade: analiseIA.nivel_complexidade,
        analise_compatibilidade: analiseCompatibilidade
      })

    if (insertError) {
      console.error('Erro ao salvar análise:', insertError)
      // Continua mesmo com erro de cache
    }

    return new Response(
      JSON.stringify({
        success: true,
        data: {
          ...analiseIA,
          analise_compatibilidade: analiseCompatibilidade
        },
        cached: false
      }),
      { 
        headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Erro na análise de edital:', error)
    return new Response(
      JSON.stringify({ error: 'Erro interno do servidor' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})