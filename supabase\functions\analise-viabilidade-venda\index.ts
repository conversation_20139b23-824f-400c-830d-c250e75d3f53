import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { corsHeaders, getSecureCorsHeaders, getPreflightHeaders } from "../_shared/cors.ts";
import { securityHeaders } from "../_shared/security-headers.ts";

const DEEPSEEK_API_KEY = Deno.env.get("DEEPSEEK_API");

interface LucratividadeData {
  id: string;
  nome: string;
  orcamento: number;
  valor_venda: number | null;
  status_venda: string;
  custo_total_real: number | null;
  lucro_bruto: number | null;
  lucro_liquido: number | null;
  margem_lucro_percentual: number | null;
  roi_percentual: number | null;
}

const ANALYSIS_PROMPT = `
Você é um consultor imobiliário especializado em análise de viabilidade de vendas e investimentos imobiliários.

Analize os dados financeiros desta obra/imóvel e forneça uma avaliação completa sobre:

1. **VIABILIDADE DE VENDA**:
   - Análise da margem de lucro atual
   - Comparação com padrões do mercado imobiliário
   - Indicadores de rentabilidade (ROI)

2. **ESTRATÉGIAS DE PRECIFICAÇÃO**:
   - Avaliação do preço de venda atual
   - Sugestões de ajustes de preço (se necessário)
   - Análise de ponto de equilíbrio

3. **OTIMIZAÇÃO DE CUSTOS**:
   - Identificação de possíveis economias
   - Análise de despesas da venda (comissões, taxas)
   - Sugestões para maximizar o lucro líquido

4. **RECOMENDAÇÕES ESTRATÉGICAS**:
   - Melhor momento para venda
   - Ações para aumentar o valor do imóvel
   - Considerações sobre o mercado atual

5. **RISCOS E OPORTUNIDADES**:
   - Fatores que podem impactar a venda
   - Oportunidades de melhoria
   - Análise de cenários otimista/pessimista

Seja específico, prático e forneça insights acionáveis baseados nos dados apresentados.
IMPORTANTE: Não use caracteres de formatação markdown como #, *, **, ___ ou similares em suas respostas. Responda sempre em texto simples e limpo, sem formatação especial. Use apenas texto corrido com quebras de linha quando necessário para organizar a informação.
`;

serve(async (req) => {
  const origin = req.headers.get("origin");
  const dynamicCorsHeaders = getSecureCorsHeaders(origin);

  console.log(`[ANALISE-VIABILIDADE] ${req.method} request from origin: ${origin}`);

  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    console.log("[ANALISE-VIABILIDADE] Handling OPTIONS preflight");
    return new Response("ok", { 
      headers: getPreflightHeaders(origin),
      status: 200
    });
  }

  try {
    // Validate request method
    if (req.method !== "POST") {
      return new Response(
        JSON.stringify({ error: "Método não permitido" }),
        {
          status: 405,
          headers: { ...dynamicCorsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Debug: Log environment variables
    console.log("DEEPSEEK_API value:", DEEPSEEK_API_KEY ? "[PRESENT]" : "[NOT FOUND]");
    console.log("All env vars starting with DEEP:", Object.keys(Deno.env.toObject()).filter(key => key.startsWith('DEEP')));
    
    // Validate API key
    if (!DEEPSEEK_API_KEY) {
      console.error("DEEPSEEK_API não configurada");
      return new Response(
        JSON.stringify({ 
          error: "Configuração de API inválida",
          debug: {
            deepseek_api_present: !!DEEPSEEK_API_KEY,
            env_keys_with_deep: Object.keys(Deno.env.toObject()).filter(key => key.startsWith('DEEP'))
          }
        }),
        {
          status: 500,
          headers: { ...dynamicCorsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Parse request body
    let obra_id;
    try {
      const body = await req.json();
      obra_id = body.obra_id;
      console.log(`[ANALISE-VIABILIDADE] Request body parsed, obra_id: ${obra_id}`);
    } catch (parseError) {
      console.error("[ANALISE-VIABILIDADE] Erro ao fazer parse do body:", parseError);
      return new Response(
        JSON.stringify({ error: "Body da requisição inválido" }),
        {
          status: 400,
          headers: { ...dynamicCorsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    if (!obra_id) {
      return new Response(
        JSON.stringify({ error: "ID da obra é obrigatório" }),
        {
          status: 400,
          headers: { ...dynamicCorsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get lucratividade data from view
    const { data: lucratividade, error: dbError } = await supabase
      .from("v_obras_lucratividade")
      .select("*")
      .eq("id", obra_id)
      .single();

    if (dbError) {
      console.error("Erro ao buscar dados de lucratividade:", dbError);
      return new Response(
        JSON.stringify({ error: "Erro ao buscar dados da obra" }),
        {
          status: 500,
          headers: { ...dynamicCorsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    if (!lucratividade) {
      return new Response(
        JSON.stringify({ error: "Obra não encontrada" }),
        {
          status: 404,
          headers: { ...dynamicCorsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Prepare data summary for AI analysis
    const dataForAnalysis = {
      nome_obra: lucratividade.nome,
      orcamento_inicial: lucratividade.orcamento,
      valor_venda: lucratividade.valor_venda,
      status_venda: lucratividade.status_venda,
      custo_total_real: lucratividade.custo_total_real,
      lucro_bruto: lucratividade.lucro_bruto,
      lucro_liquido: lucratividade.lucro_liquido,
      margem_lucro_percentual: lucratividade.margem_lucro_percentual,
      roi_percentual: lucratividade.roi_percentual,
    };

    const analysisContent = `
DADOS FINANCEIROS DA OBRA/IMÓVEL:

**Nome**: ${dataForAnalysis.nome_obra}
**Orçamento Inicial**: R$ ${dataForAnalysis.orcamento_inicial?.toLocaleString('pt-BR') || 'N/A'}
**Valor de Venda**: R$ ${dataForAnalysis.valor_venda?.toLocaleString('pt-BR') || 'Não definido'}
**Status da Venda**: ${dataForAnalysis.status_venda}
**Custo Total Real**: R$ ${dataForAnalysis.custo_total_real?.toLocaleString('pt-BR') || 'N/A'}
**Lucro Bruto**: R$ ${dataForAnalysis.lucro_bruto?.toLocaleString('pt-BR') || 'N/A'}
**Lucro Líquido**: R$ ${dataForAnalysis.lucro_liquido?.toLocaleString('pt-BR') || 'N/A'}
**Margem de Lucro**: ${dataForAnalysis.margem_lucro_percentual?.toFixed(2) || 'N/A'}%
**ROI**: ${dataForAnalysis.roi_percentual?.toFixed(2) || 'N/A'}%

Por favor, analise estes dados e forneça recomendações estratégicas.
`;

    // Call DeepSeek API
    const response = await fetch("https://api.deepseek.com/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${DEEPSEEK_API_KEY}`,
      },
      body: JSON.stringify({
        model: "deepseek-chat",
        messages: [
          {
            role: "system",
            content: ANALYSIS_PROMPT,
          },
          {
            role: "user",
            content: analysisContent,
          },
        ],
        max_tokens: 2000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Erro na API DeepSeek:", errorText);
      return new Response(
        JSON.stringify({ error: "Erro na análise de IA" }),
        {
          status: 500,
          headers: { ...dynamicCorsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    const aiResponse = await response.json();
    
    if (!aiResponse.choices?.[0]?.message?.content) {
      throw new Error("Resposta inválida da API de IA");
    }

    const analysis = aiResponse.choices[0].message.content;

    // Store analysis in database for future reference
    try {
      await supabase.from("ia_analises_viabilidade").insert({
        obra_id: obra_id,
        analise_conteudo: analysis,
        dados_utilizados: dataForAnalysis,
        created_at: new Date().toISOString(),
      });
    } catch (dbInsertError) {
      console.warn("Erro ao salvar análise no banco:", dbInsertError);
      // Continue mesmo se não conseguir salvar
    }

    return new Response(
      JSON.stringify({
        success: true,
        analysis,
        data: dataForAnalysis,
      }),
      {
        status: 200,
        headers: {
          ...dynamicCorsHeaders,
          ...securityHeaders,
          "Content-Type": "application/json",
        },
      }
    );

  } catch (error) {
    console.error("Erro geral:", error);
    return new Response(
      JSON.stringify({ 
        error: "Erro interno do servidor",
        details: error.message 
      }),
      {
        status: 500,
        headers: { ...dynamicCorsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});