import "jsr:@supabase/functions-js/edge-runtime.d.ts";

import { createEdgeFunction, PUBLIC_FUNCTION_CONFIG } from '../_shared/function-template.ts';
import { createSuccessResponse } from '../_shared/response-handler.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Configuração da função
const FUNCTION_CONFIG = {
  name: 'check-expired-trials',
  version: '1.0.0',
  ...PUBLIC_FUNCTION_CONFIG,
  requiresAuth: false, // Função será executada por cron job
  requiresTenant: false,
};

// Handler principal
export default createEdgeFunction(FUNCTION_CONFIG, async ({ logger }) => {
  const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Buscar trials expirados que ainda estão ativos
    const { data: expiredTrials, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('status', 'trialing')
      .lt('current_period_end', new Date().toISOString());

    if (error) {
      logger.error('Error fetching expired trials', error);
      throw error;
    }

    if (!expiredTrials || expiredTrials.length === 0) {
      logger.info('No expired trials found');
      return createSuccessResponse({
        success: true,
        message: 'Nenhum trial expirado encontrado',
        processed: 0,
      });
    }

    // Atualizar status dos trials expirados para 'canceled'
    const expiredIds = expiredTrials.map((trial) => trial.id);

    const { error: updateError } = await supabase
      .from('subscriptions')
      .update({ status: 'canceled' })
      .in('id', expiredIds);

    if (updateError) {
      logger.error('Error updating expired trials', updateError);
      throw updateError;
    }

    // Log dos trials processados
    logger.info('Processed expired trials', {
      count: expiredTrials.length,
      trials: expiredTrials.map((t) => ({
        user_id: t.user_id,
        expired_at: t.current_period_end,
      })),
    });

    // Opcional: Enviar notificações para usuários com trial expirado
    for (const trial of expiredTrials) {
      try {
        // Criar notificação no sistema
        await supabase
          .from('notifications')
          .insert({
            user_id: trial.user_id,
            type: 'trial_expired',
            title: 'Seu trial expirou',
            message: 'Seu período de teste de 7 dias terminou. Faça upgrade para continuar aproveitando o ObrasVision!',
            priority: 'high',
            action_url: '/subscription?plan=pro&source=trial_expired',
          });
      } catch (notificationError) {
        logger.error('Error creating notification', {
          userId: trial.user_id,
          error: notificationError,
        });
      }
    }

    return createSuccessResponse({
      success: true,
      message: `${expiredTrials.length} trials expirados processados`,
      processed: expiredTrials.length,
      expired_trials: expiredTrials.map((t) => ({
        user_id: t.user_id,
        expired_at: t.current_period_end,
      })),
    });

  } catch (error) {
    logger.error('Unexpected error in check-expired-trials', error);
    throw error;
  }
});