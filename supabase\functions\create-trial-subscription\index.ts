import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { z } from "https://deno.land/x/zod@v3.23.8/mod.ts";

import { createEdgeFunction, PUBLIC_FUNCTION_CONFIG } from '../_shared/function-template.ts';
import { createSuccessResponse, createErrorResponse } from '../_shared/response-handler.ts';
import { createFunctionLogger } from '../_shared/logger.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Schema de validação
const createTrialSchema = z.object({
  userId: z.string().uuid('ID do usuário deve ser um UUID válido')
});

// Configuração da função
const FUNCTION_CONFIG = {
  name: 'create-trial-subscription',
  version: '1.0.0',
  ...PUBLIC_FUNCTION_CONFIG,
  requiresAuth: true,
  requiresTenant: false,
  validation: {
    bodySchema: createTrialSchema
  }
};

// Handler principal
export default createEdgeFunction(FUNCTION_CONFIG, async ({ body, auth, logger, requestId }) => {
  const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Validar se o userId da requisição corresponde ao usuário autenticado
    if (body.userId !== auth?.user.id) {
      logger.warn('User ID mismatch', { 
        providedUserId: body.userId, 
        authUserId: auth?.user.id 
      });
      
      return createErrorResponse(
        'ID do usuário inválido',
        'INVALID_USER_ID'
      );
    }

    // Verificar se já existe assinatura ativa ou trial
    const { data: existingSubscription, error: checkError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', body.userId)
      .in('status', ['active', 'trialing'])
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      logger.error('Error checking existing subscription', checkError);
      throw checkError;
    }

    if (existingSubscription) {
      logger.info('User already has active subscription', {
        userId: body.userId,
        subscriptionId: existingSubscription.id,
        status: existingSubscription.status
      });

      return createSuccessResponse({
        success: true,
        message: 'Usuário já possui assinatura ativa',
        subscription: existingSubscription,
        alreadyExists: true
      });
    }

    // Criar assinatura trial de 7 dias
    const trialStart = new Date();
    const trialEnd = new Date();
    trialEnd.setDate(trialStart.getDate() + 7); // 7 dias a partir de hoje

    const { data: newSubscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .insert([
        {
          user_id: body.userId,
          plan_type: 'free',
          status: 'trialing',
          current_period_start: trialStart.toISOString(),
          current_period_end: trialEnd.toISOString(),
          stripe_subscription_id: null,
          stripe_customer_id: null,
        },
      ])
      .select()
      .single();

    if (subscriptionError) {
      logger.error('Error creating trial subscription', subscriptionError);
      throw subscriptionError;
    }

    // Criar entrada na tabela ai_trial_usage para tracking de orçamento total
    const { error: trialUsageError } = await supabase
      .from('ai_trial_usage')
      .insert([
        {
          user_id: body.userId,
          subscription_id: newSubscription.id,
          trial_start_date: trialStart.toISOString(),
          trial_end_date: trialEnd.toISOString(),
          total_budget_requests: 0,
        },
      ]);

    if (trialUsageError) {
      logger.error('Error creating trial usage tracking', trialUsageError);
      // Não falha a operação se não conseguir criar o tracking
    }

    logger.info('Trial subscription created successfully', {
      userId: body.userId,
      subscriptionId: newSubscription.id,
      trialStart: trialStart.toISOString(),
      trialEnd: trialEnd.toISOString(),
    });

    return createSuccessResponse({
      success: true,
      message: 'Trial de 7 dias criado com sucesso',
      subscription: newSubscription,
      trial_days: 7,
      expires_at: trialEnd.toISOString(),
    });

  } catch (error) {
    logger.error('Unexpected error in create-trial-subscription', error);
    
    return createErrorResponse(
      'Erro ao criar trial. Por favor, tente novamente.',
      'INTERNAL_ERROR'
    );
  }
});