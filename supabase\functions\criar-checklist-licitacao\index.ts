import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from "../_shared/cors.ts"
import { validateInput } from "../_shared/input-validation.ts"
import { securityHeaders } from "../_shared/security-headers.ts"

interface CriarChecklistRequest {
  licitacao_id: string
}

interface TarefaPadrao {
  descricao: string
  tipo: 'padrao' | 'ia'
  prioridade: 'baixa' | 'media' | 'alta' | 'critica'
  prazo_estimado: number
  ordem: number
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Validação de autenticação
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Token de autorização necessário' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Validação de método HTTP
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Método não permitido' }),
        { 
          status: 405, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Parse e validação do corpo da requisição
    const body: CriarChecklistRequest = await req.json()
    
    const validationResult = validateInput(body, {
      licitacao_id: { type: 'string', required: true }
    })

    if (!validationResult.isValid) {
      return new Response(
        JSON.stringify({ error: 'Dados inválidos', details: validationResult.errors }),
        { 
          status: 400, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Conectar ao Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2')
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Extrair informações do usuário do JWT
    const jwt = authHeader.replace('Bearer ', '')
    const { data: { user }, error: userError } = await supabase.auth.getUser(jwt)
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Token inválido' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const tenantId = user.user_metadata?.tenant_id
    if (!tenantId) {
      return new Response(
        JSON.stringify({ error: 'Tenant ID não encontrado' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Verificar se a licitação existe
    const { data: licitacao, error: licitacaoError } = await supabase
      .from('licitacoes')
      .select('id, numero_licitacao, objeto, modalidade')
      .eq('id', body.licitacao_id)
      .single()

    if (licitacaoError || !licitacao) {
      return new Response(
        JSON.stringify({ error: 'Licitação não encontrada' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Verificar se já existe checklist para esta licitação e tenant
    const { data: tarefasExistentes } = await supabase
      .from('licitacao_tarefas')
      .select('id')
      .eq('licitacao_id', body.licitacao_id)
      .eq('tenant_id', tenantId)

    if (tarefasExistentes && tarefasExistentes.length > 0) {
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Checklist já existe para esta licitação',
          tarefas_count: tarefasExistentes.length
        }),
        { 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Definir tarefas padrão baseadas na modalidade da licitação
    const tarefasPadrao: TarefaPadrao[] = [
      {
        descricao: 'Baixar e analisar o edital completo',
        tipo: 'padrao',
        prioridade: 'critica',
        prazo_estimado: 1,
        ordem: 1
      },
      {
        descricao: 'Verificar habilitação jurídica da empresa',
        tipo: 'padrao',
        prioridade: 'critica',
        prazo_estimado: 2,
        ordem: 2
      },
      {
        descricao: 'Obter Certidão Negativa de Débitos Federais',
        tipo: 'padrao',
        prioridade: 'alta',
        prazo_estimado: 1,
        ordem: 3
      },
      {
        descricao: 'Obter Certidão Negativa de FGTS',
        tipo: 'padrao',
        prioridade: 'alta',
        prazo_estimado: 1,
        ordem: 4
      },
      {
        descricao: 'Obter Certidão Negativa Trabalhista',
        tipo: 'padrao',
        prioridade: 'alta',
        prazo_estimado: 1,
        ordem: 5
      },
      {
        descricao: 'Verificar regularidade no CREA/CAU',
        tipo: 'padrao',
        prioridade: 'alta',
        prazo_estimado: 1,
        ordem: 6
      },
      {
        descricao: 'Preparar documentação de qualificação técnica',
        tipo: 'padrao',
        prioridade: 'alta',
        prazo_estimado: 3,
        ordem: 7
      },
      {
        descricao: 'Preparar demonstrações financeiras e balanços',
        tipo: 'padrao',
        prioridade: 'alta',
        prazo_estimado: 2,
        ordem: 8
      },
      {
        descricao: 'Elaborar proposta técnica',
        tipo: 'padrao',
        prioridade: 'critica',
        prazo_estimado: 5,
        ordem: 9
      },
      {
        descricao: 'Elaborar proposta de preços',
        tipo: 'padrao',
        prioridade: 'critica',
        prazo_estimado: 3,
        ordem: 10
      }
    ]

    // Adicionar tarefas específicas por modalidade
    if (licitacao.modalidade === 'pregao_eletronico' || licitacao.modalidade === 'pregao_presencial') {
      tarefasPadrao.push({
        descricao: 'Cadastrar-se no sistema de pregão eletrônico',
        tipo: 'padrao',
        prioridade: 'alta',
        prazo_estimado: 1,
        ordem: 11
      })
    }

    if (licitacao.modalidade === 'concorrencia') {
      tarefasPadrao.push({
        descricao: 'Preparar garantia de participação (se exigida)',
        tipo: 'padrao',
        prioridade: 'media',
        prazo_estimado: 2,
        ordem: 11
      })
    }

    // Verificar se existe análise de IA para adicionar tarefas específicas
    const { data: analiseIA } = await supabase
      .from('licitacoes_analises_ia')
      .select('*')
      .eq('licitacao_id', body.licitacao_id)
      .eq('tenant_id', tenantId)
      .single()

    if (analiseIA && analiseIA.documentos_necessarios) {
      // Adicionar tarefas baseadas na análise da IA
      analiseIA.documentos_necessarios.forEach((documento: string, index: number) => {
        if (!tarefasPadrao.some(t => t.descricao.toLowerCase().includes(documento.toLowerCase()))) {
          tarefasPadrao.push({
            descricao: `Obter/preparar: ${documento}`,
            tipo: 'ia',
            prioridade: 'alta',
            prazo_estimado: 2,
            ordem: 50 + index
          })
        }
      })
    }

    // Inserir tarefas no banco de dados
    const tarefasParaInserir = tarefasPadrao.map(tarefa => ({
      licitacao_id: body.licitacao_id,
      tenant_id: tenantId,
      descricao: tarefa.descricao,
      tipo: tarefa.tipo,
      prioridade: tarefa.prioridade,
      prazo_estimado: tarefa.prazo_estimado,
      ordem: tarefa.ordem,
      concluida: false
    }))

    const { data: tarefasInseridas, error: insertError } = await supabase
      .from('licitacao_tarefas')
      .insert(tarefasParaInserir)
      .select()

    if (insertError) {
      console.error('Erro ao inserir tarefas:', insertError)
      return new Response(
        JSON.stringify({ error: 'Erro ao criar checklist' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Adicionar licitação aos favoritos automaticamente
    const { error: favoritoError } = await supabase
      .from('licitacoes_favoritas')
      .upsert({
        licitacao_id: body.licitacao_id,
        usuario_id: user.id,
        tenant_id: tenantId
      }, {
        onConflict: 'licitacao_id,usuario_id,tenant_id'
      })

    if (favoritoError) {
      console.warn('Erro ao adicionar aos favoritos:', favoritoError)
      // Não falha a operação por causa disso
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Checklist criado com sucesso',
        data: {
          licitacao_id: body.licitacao_id,
          tarefas_criadas: tarefasInseridas?.length || 0,
          tarefas_padrao: tarefasPadrao.filter(t => t.tipo === 'padrao').length,
          tarefas_ia: tarefasPadrao.filter(t => t.tipo === 'ia').length,
          adicionado_favoritos: !favoritoError
        }
      }),
      { 
        headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Erro na criação do checklist:', error)
    return new Response(
      JSON.stringify({ error: 'Erro interno do servidor' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, ...securityHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})