import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.3";
import { checkAndIncrementAIUsage } from "../_shared/ai-quota-checker.ts";
import { getSecureCorsHeaders } from "../_shared/cors.ts";

const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
const supabaseServiceRoleKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;

serve(async (req) => {
  const origin = req.headers.get("Origin");
  const responseHeaders = getSecureCorsHeaders(origin);

  // Handle CORS
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: responseHeaders });
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

    // ✅ AUTENTICAÇÃO REATIVADA - Verificar token JWT
    const authHeader = req.headers.get("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      console.log("❌ Token de autenticação não fornecido");
      return new Response(
        JSON.stringify({ error: "Token de autenticação obrigatório" }),
        {
          status: 401,
          headers: { ...responseHeaders, "Content-Type": "application/json" },
        }
      );
    }

    const token = authHeader.split(" ")[1];
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser(token);

    if (authError || !user) {
      console.log("❌ Token inválido ou usuário não encontrado:", authError);
      return new Response(
        JSON.stringify({ error: "Usuário não autenticado" }),
        {
          status: 401,
          headers: { ...responseHeaders, "Content-Type": "application/json" },
        }
      );
    }

    console.log(`✅ Usuário autenticado com sucesso`);

    // ✅ VERIFICAÇÃO DE QUOTA DE IA - ORÇAMENTO
    console.log(`🔍 Verificando quota de orçamento para usuário`);
    const quotaResult = await checkAndIncrementAIUsage(
      supabaseUrl,
      supabaseServiceRoleKey,
      user.id,
      "budget"
    );

    console.log(`📊 Resultado da quota:`, {
      allowed: quotaResult.allowed,
      current: quotaResult.quotaResult?.current,
      limit: quotaResult.quotaResult?.limit,
      remaining: quotaResult.quotaResult?.remaining,
    });

    if (!quotaResult.allowed) {
      console.log(`❌ Quota de orçamento IA excedida para usuário`);
      return new Response(
        JSON.stringify({
          error:
            quotaResult.quotaResult.message ||
            "Limite de geração de orçamentos atingido",
          quota_info: {
            current: quotaResult.quotaResult.current,
            limit: quotaResult.quotaResult.limit,
            remaining: quotaResult.quotaResult.remaining,
          },
        }),
        {
          status: 429, // Too Many Requests
          headers: { ...responseHeaders, "Content-Type": "application/json" },
        }
      );
    }

    console.log(
      `✅ Quota de orçamento verificada - ${quotaResult.quotaResult.remaining} usos restantes`
    );

    if (req.method !== "POST") {
      return new Response(JSON.stringify({ error: "Method not allowed" }), {
        status: 405,
        headers: { ...responseHeaders, "Content-Type": "application/json" },
      });
    }

    const { obra_id } = await req.json();

    if (!obra_id) {
      return new Response(JSON.stringify({ error: "obra_id is required" }), {
        status: 400,
        headers: { ...responseHeaders, "Content-Type": "application/json" },
      });
    }

    // Buscar dados completos da obra
    console.log(`🔍 Buscando dados da obra para usuário`);
    const { data: obra, error: obraError } = await supabase
      .from("obras")
      .select(
        `
        id, nome, area_total, valor_orcamento_parametrico,
        tipo_projeto, tipo_condominio, numero_unidades,
        area_construida_unidade, numero_blocos, andares_por_bloco,
        unidades_por_andar, area_lote
      `
      )
      .eq("id", obra_id)
      .eq("usuario_id", user.id)
      .single();

    if (obraError || !obra) {
      console.error("❌ Erro ao buscar obra:", obraError);
      return new Response(
        JSON.stringify({
          error: "Obra not found or access denied",
          details: obraError,
        }),
        {
          status: 404,
          headers: { ...responseHeaders, "Content-Type": "application/json" },
        }
      );
    }

    console.log(`📊 Dados da obra encontrados:`, {
      id: obra.id,
      nome: obra.nome,
      area_total: obra.area_total,
      valor_orcamento_parametrico: obra.valor_orcamento_parametrico,
    });

    // Verificar se já possui orçamento paramétrico - PERMITIR REGENERAÇÃO
    if (
      obra.valor_orcamento_parametrico &&
      obra.valor_orcamento_parametrico > 0
    ) {
      console.log(
        `⚠️ Obra já possui orçamento paramétrico: R$ ${obra.valor_orcamento_parametrico}. Regenerando...`
      );
      // Não bloquear mais - permitir regeneração
    }

    // Buscar área total: primeiro da obra, depois da planta analisada como fallback
    let areaTotal = obra.area_total;
    console.log(`📐 Área total da obra: ${areaTotal}m²`);

    if (!areaTotal || areaTotal <= 0) {
      console.log(
        "🔍 Área total não encontrada na obra, buscando na planta analisada..."
      );
      const { data: planta, error: plantaError } = await supabase
        .from("plantas_analisadas")
        .select("area_total_construida")
        .eq("obra_id", obra_id)
        .order("created_at", { ascending: false })
        .limit(1)
        .single();

      if (plantaError) {
        console.log(
          `⚠️ Erro ao buscar planta analisada: ${plantaError.message}`
        );
      }

      if (planta && planta.area_total_construida > 0) {
        areaTotal = planta.area_total_construida;
        console.log(`✅ Área total encontrada na planta: ${areaTotal}m²`);
      } else {
        console.log(`❌ Nenhuma área total encontrada na planta`);
      }
    }

    if (!areaTotal || areaTotal <= 0) {
      console.error(
        `❌ ERRO: Área total inválida ou não encontrada. Valor: ${areaTotal}`
      );
      return new Response(
        JSON.stringify({
          error: "Obra deve ter área total definida",
          details:
            "Informe a área total da obra ou use a PlantaIA para analisar uma planta",
          debug: {
            obra_area_total: obra.area_total,
            area_calculada: areaTotal,
          },
        }),
        {
          status: 400,
          headers: { ...responseHeaders, "Content-Type": "application/json" },
        }
      );
    }

    console.log(`✅ Área total válida confirmada: ${areaTotal}m²`);

    // Consultar tabela sinapi_manutencoes para obter CUB médio
    // Buscamos alguns itens relacionados a construção/reforma para calcular um CUB médio
    const { data: sinapiData, error: sinapiError } = await supabase
      .from("sinapi_manutencoes")
      .select("preco_unitario")
      .ilike("descricao", "%construc%")
      .or(
        "descricao.ilike.%reforma%,descricao.ilike.%edificac%,descricao.ilike.%alvenaria%"
      )
      .not("preco_unitario", "is", null)
      .limit(20);

    if (sinapiError) {
      console.error("Error fetching SINAPI data:", sinapiError);
      return new Response(
        JSON.stringify({ error: "Error fetching SINAPI data" }),
        {
          status: 500,
          headers: { ...responseHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Calcular CUB médio dos itens encontrados
    let cubMedio = 1500; // Valor padrão caso não encontre dados SINAPI

    if (sinapiData && sinapiData.length > 0) {
      const precos = sinapiData
        .map((item) => parseFloat(item.preco_unitario))
        .filter((preco) => !isNaN(preco) && preco > 0);

      if (precos.length > 0) {
        cubMedio =
          precos.reduce((sum, preco) => sum + preco, 0) / precos.length;

        // Aplicar um fator de ajuste para construção (baseado em CUB típico)
        // Como os preços do SINAPI são por serviço, aplicamos um fator para chegar ao CUB
        cubMedio = cubMedio * 0.8; // Fator de ajuste baseado em análise típica

        // Garantir que o CUB esteja dentro de uma faixa razoável (R$ 800 - R$ 2500/m²)
        cubMedio = Math.max(800, Math.min(2500, cubMedio));
      }
    }

    // Calcular orçamento paramétrico básico (será recalculado pela Edge Function especializada)
    const orcamentoParametrico = 0.01; // Valor temporário

    // Buscar tenant_id do usuário
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("tenant_id")
      .eq("id", user.id)
      .single();

    if (profileError || !profile) {
      console.error("Error fetching user profile:", profileError);
      return new Response(
        JSON.stringify({ error: "Error fetching user profile" }),
        {
          status: 500,
          headers: { ...responseHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Determinar tipo de obra baseado nos dados da obra
    let tipoObra = "R1_UNIFAMILIAR";
    let padraoObra = "POPULAR";
    let areaParaCalculo = areaTotal;

    console.log(`📊 Analisando obra:`, {
      tipo_projeto: obra.tipo_projeto,
      tipo_condominio: obra.tipo_condominio,
      numero_unidades: obra.numero_unidades,
      area_construida_unidade: obra.area_construida_unidade,
      area_total: areaTotal,
    });

    // Lógica inteligente baseada no tipo de projeto
    if (obra.tipo_projeto === "CONDOMINIO_MASTER") {
      if (obra.tipo_condominio === "HORIZONTAL") {
        // Condomínio horizontal = múltiplas casas unifamiliares
        tipoObra = "R1_UNIFAMILIAR";
        padraoObra = "POPULAR";
        // Para cálculo, usar área por unidade (não área total do terreno)
        areaParaCalculo =
          obra.area_construida_unidade ||
          areaTotal / (obra.numero_unidades || 1);
        console.log(
          `🏘️ Condomínio HORIZONTAL: ${obra.numero_unidades} casas de ${areaParaCalculo}m² cada`
        );
      } else if (obra.tipo_condominio === "VERTICAL") {
        // Condomínio vertical = prédio multifamiliar
        tipoObra = "R4_MULTIFAMILIAR";
        padraoObra = "POPULAR";
        console.log(
          `🏢 Condomínio VERTICAL: ${obra.numero_blocos} blocos, ${obra.andares_por_bloco} andares`
        );
      }
    } else {
      // Obra única - determinar tipo baseado na área
      if (areaTotal > 10000) {
        tipoObra = "R4_MULTIFAMILIAR";
        padraoObra = "POPULAR";
      } else if (areaTotal > 5000) {
        tipoObra = "R2_MULTIFAMILIAR";
        padraoObra = "POPULAR";
      } else if (areaTotal > 1000) {
        tipoObra = "R1_UNIFAMILIAR";
        padraoObra = "NORMAL";
      } else {
        tipoObra = "R1_UNIFAMILIAR";
        padraoObra = "POPULAR";
      }
      console.log(
        `🏠 Obra ÚNICA: ${tipoObra} - ${padraoObra} (${areaTotal}m²)`
      );
    }

    console.log(
      `📊 Tipo de obra determinado: ${tipoObra} - ${padraoObra} (Área para cálculo: ${areaParaCalculo}m²)`
    );

    // Criar registro na tabela orcamentos_parametricos
    console.log(`📊 Criando orçamento paramétrico para obra ${obra_id}`);
    const { data: orcamentoData, error: orcamentoError } = await supabase
      .from("orcamentos_parametricos")
      .insert({
        obra_id: obra_id,
        usuario_id: user.id,
        tenant_id: profile.tenant_id,
        nome_orcamento: `Orçamento - ${obra.nome}`,
        descricao: `Orçamento paramétrico para a obra: ${obra.nome}`,
        tipo_obra: tipoObra,
        padrao_obra: padraoObra,
        estado: "GO", // TODO: Buscar do endereço da obra
        cidade: "Valparaíso de Goiás", // TODO: Buscar do endereço da obra
        area_total: areaTotal,
        custo_estimado: orcamentoParametrico,
        custo_m2: cubMedio,
        status: "RASCUNHO",
        parametros_entrada: {
          cub_utilizado: cubMedio,
          fonte_calculo: "SINAPI",
          data_calculo: new Date().toISOString(),
        },
      })
      .select()
      .single();

    if (orcamentoError) {
      console.error("❌ Error creating orcamento parametrico:", orcamentoError);
      return new Response(
        JSON.stringify({
          error: "Error creating orcamento parametrico",
          details: orcamentoError,
        }),
        {
          status: 500,
          headers: { ...responseHeaders, "Content-Type": "application/json" },
        }
      );
    }

    console.log(`✅ Orçamento paramétrico criado:`, orcamentoData);

    // Chamar Edge Function especializada para calcular o orçamento correto
    console.log(
      `🔄 Chamando Edge Function ai-calculate-budget-v11 para cálculo especializado...`
    );
    try {
      const { data: calculoData, error: calculoError } =
        await supabase.functions.invoke("ai-calculate-budget-v11", {
          body: {
            orcamento_id: orcamentoData.id,
            tipo_obra: tipoObra,
            padrao_obra: padraoObra,
            area_total: areaTotal,
            area_construida: areaParaCalculo,
            estado: "GO",
            cidade: "Valparaíso de Goiás",
            nome_orcamento: `Orçamento - ${obra.nome}`,
            // Usar dados reais da obra
            tipo_condominio: obra.tipo_condominio,
            numero_blocos: obra.numero_blocos,
            andares_por_bloco: obra.andares_por_bloco,
            unidades_por_andar: obra.unidades_por_andar,
            numero_unidades: obra.numero_unidades,
            area_construida_unidade: obra.area_construida_unidade,
            area_lote: obra.area_lote,
          },
        });

      if (calculoError) {
        console.error(
          "❌ Erro ao chamar ai-calculate-budget-v11:",
          calculoError
        );
      } else {
        console.log("✅ Cálculo especializado concluído:", calculoData);
      }
    } catch (error) {
      console.error("❌ Erro ao chamar Edge Function de cálculo:", error);
    }

    // Buscar o valor atualizado do orçamento após o cálculo especializado
    const { data: orcamentoAtualizado, error: orcamentoAtualizadoError } =
      await supabase
        .from("orcamentos_parametricos")
        .select("custo_estimado")
        .eq("id", orcamentoData.id)
        .single();

    const valorFinal = orcamentoAtualizadoError
      ? orcamentoParametrico
      : orcamentoAtualizado.custo_estimado;

    // Atualizar obra com o valor calculado
    console.log(`🔄 Atualizando obra ${obra_id} com valor ${valorFinal}`);
    const { data: updateData, error: updateError } = await supabase
      .from("obras")
      .update({ valor_orcamento_parametrico: valorFinal })
      .eq("id", obra_id)
      .eq("usuario_id", user.id) // Adicionar segurança extra
      .select();

    if (updateError) {
      console.error("❌ Error updating obra:", updateError);
      return new Response(
        JSON.stringify({ error: "Error updating obra", details: updateError }),
        {
          status: 500,
          headers: { ...responseHeaders, "Content-Type": "application/json" },
        }
      );
    }

    console.log(`✅ Obra atualizada com sucesso:`, updateData);
    console.log(`💰 Valor atualizado: R$ ${orcamentoParametrico}`);

    // Verificar se a atualização realmente funcionou
    const { data: verificacao, error: verificacaoError } = await supabase
      .from("obras")
      .select("valor_orcamento_parametrico")
      .eq("id", obra_id)
      .eq("usuario_id", user.id)
      .single();

    if (verificacaoError) {
      console.error("❌ Erro na verificação:", verificacaoError);
    } else {
      console.log(
        `🔍 Verificação - valor atual no banco: R$ ${verificacao.valor_orcamento_parametrico}`
      );
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: "Orçamento paramétrico gerado com sucesso",
        dados: {
          obra_id: obra.id,
          obra_nome: obra.nome,
          area_total: areaTotal,
          tipo_obra: tipoObra,
          padrao_obra: padraoObra,
          cub_utilizado: cubMedio,
          valor_orcamento_parametrico: valorFinal,
          valor_formatado: `R$ ${valorFinal.toLocaleString("pt-BR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}`,
        },
      }),
      {
        headers: { ...responseHeaders, "Content-Type": "application/json" },
      }
    );
  } catch (error) {
    console.error("Error in gerar-orcamento-parametrico:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { ...responseHeaders, "Content-Type": "application/json" },
    });
  }
});
