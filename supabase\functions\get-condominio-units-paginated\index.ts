import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface RequestBody {
  condominioId: string;
  page: number;
  pageSize: number;
  search?: string;
  statusFilter?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );

    // Get user from JWT
    const {
      data: { user },
      error: authError,
    } = await supabaseClient.auth.getUser();

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Parse request body
    const body: RequestBody = await req.json();
    const { condominioId, page = 1, pageSize = 50, search, statusFilter } = body;

    // Validate inputs
    if (!condominioId) {
      return new Response(
        JSON.stringify({ error: 'condominioId é obrigatório' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Calculate offset
    const offset = (page - 1) * pageSize;

    // Build query
    let query = supabaseClient
      .from('obras')
      .select('*', { count: 'exact' })
      .eq('parent_obra_id', condominioId)
      .eq('tipo_projeto', 'UNIDADE_CONDOMINIO')
      .eq('tenant_id', user.id)
      .order('identificador_unidade', { ascending: true })
      .range(offset, offset + pageSize - 1);

    // Apply search filter
    if (search && search.trim()) {
      query = query.or(`nome.ilike.%${search}%,identificador_unidade.ilike.%${search}%`);
    }

    // Apply status filter
    if (statusFilter && statusFilter !== '') {
      query = query.eq('status', statusFilter);
    }

    // Execute query
    const { data: unidades, error, count } = await query;

    if (error) {
      console.error('Database error:', error);
      return new Response(
        JSON.stringify({ error: 'Erro ao buscar unidades' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Calculate pagination info
    const total = count || 0;
    const totalPages = Math.ceil(total / pageSize);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    // Return paginated response
    const response = {
      data: unidades || [],
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Function error:', error);
    return new Response(
      JSON.stringify({ error: 'Erro interno do servidor' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
