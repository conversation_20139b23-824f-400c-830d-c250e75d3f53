import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { corsHeaders } from "../_shared/cors.ts";

interface MetricsRequest {
  period: "7d" | "30d" | "90d";
}

async function getMetricsForPeriod(
  supabaseClient: any,
  startDate: Date,
  endDate: Date,
) {
  try {
    console.log(
      `Fetching metrics for period: ${startDate.toISOString()} to ${endDate.toISOString()}`,
    );

    // Buscar dados da tabela plantas_analisadas
    const { data: plantasData, error: plantasError } = await supabaseClient
      .from("plantas_analisadas")
      .select(
        "id, area_total_construida, valor_estimado, valor_orcamento_parametrico, created_at, status",
      )
      .gte("created_at", startDate.toISOString())
      .lt("created_at", endDate.toISOString());

    if (plantasError) {
      console.error("Error fetching plantas_analisadas:", plantasError);
      // Não fazer throw do erro, apenas logar e continuar com array vazio
    }

    // Buscar obras geradas a partir das plantas (que têm url_planta)
    const { data: obrasData, error: obrasError } = await supabaseClient
      .from("obras")
      .select("id, url_planta, created_at")
      .gte("created_at", startDate.toISOString())
      .lt("created_at", endDate.toISOString())
      .not("url_planta", "is", null);

    if (obrasError) {
      console.error("Error fetching obras:", obrasError);
      // Não fazer throw do erro, apenas logar e continuar com array vazio
    }

    // Garantir que os dados não sejam null
    const plantas = plantasData || [];
    const obras = obrasData || [];

    console.log(`Found ${plantas.length} plantas and ${obras.length} obras`);

    const totalPlantas = plantas.length;
    const plantasAnalisadas = plantas.filter((d: any) =>
      d.status === "analisada"
    ).length;
    const obrasGeradas = obras.length;
    const areaTotal = plantas.reduce((sum: number, item: any) =>
      sum + (parseFloat(item.area_total_construida) || 0), 0);
    const valorTotalEstimado = plantas.reduce((sum: number, item: any) => {
      const estimado = item.valor_estimado
        ? parseFloat(item.valor_estimado)
        : 0;
      const orcamento = item.valor_orcamento_parametrico
        ? parseFloat(item.valor_orcamento_parametrico)
        : 0;
      return sum + (estimado || orcamento);
    }, 0);

    const result = {
      totalPlantas,
      plantasAnalisadas,
      obrasGeradas,
      areaTotal,
      valorTotalEstimado,
    };
    console.log("Metrics result:", result);

    return result;
  } catch (error) {
    console.error("Error in getMetricsForPeriod:", error);
    // Retornar métricas zeradas em caso de erro
    return {
      totalPlantas: 0,
      plantasAnalisadas: 0,
      obrasGeradas: 0,
      areaTotal: 0,
      valorTotalEstimado: 0,
    };
  }
}

serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? "",
      {
        global: {
          headers: { Authorization: req.headers.get("Authorization")! },
        },
      },
    );

    const body = await req.json();
    const { period } = body as MetricsRequest;

    if (!period || !["7d", "30d", "90d"].includes(period)) {
      throw new Error("Invalid period. Must be 7d, 30d, or 90d");
    }

    const now = new Date();
    let daysToSubtract = 30;
    if (period === "7d") daysToSubtract = 7;
    if (period === "90d") daysToSubtract = 90;

    const currentEndDate = new Date(now);
    const currentStartDate = new Date(
      now.getTime() - daysToSubtract * 24 * 60 * 60 * 1000,
    );

    const previousEndDate = new Date(currentStartDate);
    const previousStartDate = new Date(
      previousEndDate.getTime() - daysToSubtract * 24 * 60 * 60 * 1000,
    );

    const [currentPeriod, previousPeriod] = await Promise.all([
      getMetricsForPeriod(supabaseClient, currentStartDate, currentEndDate),
      getMetricsForPeriod(supabaseClient, previousStartDate, previousEndDate),
    ]);

    return new Response(JSON.stringify({ currentPeriod, previousPeriod }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error("Error in get-planta-ia-metrics:", error);
    return new Response(
      JSON.stringify({ error: error.message, stack: error.stack }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400,
      },
    );
  }
});
