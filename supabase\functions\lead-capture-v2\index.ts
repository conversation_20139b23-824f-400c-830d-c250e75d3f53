/**
 * 🎯 Lead Capture v2 - Versão Padronizada
 * 
 * Edge Function refatorada usando o template padronizado
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Imports padronizados
import { 
  createEdgeFunction, 
  PUBLIC_FUNCTION_CONFIG 
} from '../_shared/function-template.ts';

import { 
  createSuccessResponse, 
  createErrorResponse,
  ERROR_CODES
} from '../_shared/response-handler.ts';

import { 
  SCHEMAS 
} from '../_shared/validation-schemas.ts';

import { createServiceClient } from '../_shared/auth-handler.ts';

// Implementação da Edge Function
export default createEdgeFunction({
  name: 'lead-capture-v2',
  version: '2.0.0',
  ...PUBLIC_FUNCTION_CONFIG,
  rateLimit: {
    maxRequests: 10, // Mais restritivo para leads
    windowMs: 60000
  },
  validation: {
    bodySchema: SCHEMAS.lead
  }
}, async ({ body, logger, requestId }) => {
  const leadData = body!;
  
  logger.info('Processing lead capture', {
    email: leadData.email,
    empresa: leadData.empresa,
    origem: leadData.origem
  });

  try {
    // Criar cliente Supabase
    const supabase = createServiceClient();
    
    // Verificar se lead já existe
    const { data: existingLead, error: checkError } = await supabase
      .from('leads')
      .select('id, email, created_at')
      .eq('email', leadData.email)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      logger.error('Error checking existing lead', checkError);
      return createErrorResponse(
        'Erro ao verificar lead existente',
        'DATABASE_ERROR'
      );
    }

    if (existingLead) {
      logger.info('Lead already exists', { 
        leadId: existingLead.id,
        existingSince: existingLead.created_at 
      });
      
      // Atualizar dados do lead existente
      const { data: updatedLead, error: updateError } = await supabase
        .from('leads')
        .update({
          ...leadData,
          updated_at: new Date().toISOString(),
          last_interaction: new Date().toISOString()
        })
        .eq('id', existingLead.id)
        .select()
        .single();

      if (updateError) {
        logger.error('Error updating existing lead', updateError);
        return createErrorResponse(
          'Erro ao atualizar lead',
          'DATABASE_ERROR'
        );
      }

      return createSuccessResponse({
        lead: updatedLead,
        action: 'updated',
        message: 'Lead atualizado com sucesso'
      });
    }

    // Criar novo lead
    const { data: newLead, error: insertError } = await supabase
      .from('leads')
      .insert({
        ...leadData,
        id: crypto.randomUUID(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_interaction: new Date().toISOString(),
        status: 'novo'
      })
      .select()
      .single();

    if (insertError) {
      logger.error('Error creating new lead', insertError);
      return createErrorResponse(
        'Erro ao criar lead',
        'DATABASE_ERROR'
      );
    }

    logger.info('Lead created successfully', { 
      leadId: newLead.id,
      email: newLead.email 
    });

    // Trigger de notificação (opcional)
    try {
      await triggerLeadNotification(newLead, logger);
    } catch (notificationError) {
      logger.warn('Failed to send lead notification', notificationError);
      // Não falha a operação principal
    }

    return createSuccessResponse({
      lead: newLead,
      action: 'created',
      message: 'Lead capturado com sucesso'
    });

  } catch (error) {
    logger.error('Unexpected error in lead capture', error);
    return createErrorResponse(
      'Erro interno do servidor',
      'INTERNAL_ERROR'
    );
  }
});

/**
 * Trigger de notificação para novo lead
 */
async function triggerLeadNotification(lead: any, logger: any): Promise<void> {
  logger.debug('Triggering lead notification', { leadId: lead.id });
  
  // Aqui você pode integrar com:
  // - Slack/Discord webhook
  // - Email notification
  // - CRM integration
  // - Analytics tracking
  
  // Exemplo de webhook (descomentado quando configurado)
  /*
  const webhookUrl = Deno.env.get('LEAD_WEBHOOK_URL');
  if (webhookUrl) {
    await fetch(webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'new_lead',
        lead: {
          email: lead.email,
          empresa: lead.empresa,
          origem: lead.origem,
          created_at: lead.created_at
        }
      })
    });
  }
  */
  
  logger.info('Lead notification triggered successfully');
}
