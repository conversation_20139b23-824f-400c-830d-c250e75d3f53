import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

// Simple auth function
async function requireAuth(req: Request) {
  const authHeader = req.headers.get("Authorization");
  if (!authHeader) {
    return { success: false, error: "Missing authorization header" };
  }

  const token = authHeader.replace("Bearer ", "");
  const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
  const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY")!;

  const supabase = createClient(supabaseUrl, supabaseAnonKey, {
    auth: { persistSession: false },
  });

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser(token);

  if (error || !user) {
    return { success: false, error: "Invalid token" };
  }

  // Get tenant_id from user metadata
  const tenantId =
    user.user_metadata?.tenant_id || user.app_metadata?.tenant_id;

  return {
    success: true,
    user,
    tenantId,
  };
}

// Simple logger
const secureLogger = {
  info: (message: string, data?: unknown) =>
    console.log(`[INFO] ${message}`, data || ""),
  error: (message: string, error?: unknown) =>
    console.error(`[ERROR] ${message}`, error || ""),
};

/**
 * Edge Function para migrar dados existentes para o Sistema Híbrido de Despesas
 *
 * Esta função:
 * 1. Identifica despesas de unidades de condomínio
 * 2. Migra para o novo formato (obra_id = master, tipo_despesa = ESPECIFICA)
 * 3. Mantém compatibilidade com dados existentes
 */

interface MigrationResult {
  success: boolean;
  despesasMigradas: number;
  despesasJaMigradas: number;
  errors: string[];
}

serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Autenticação obrigatória
    const authResult = await requireAuth(req);
    if (!authResult.success) {
      return new Response(JSON.stringify({ error: authResult.error }), {
        status: 401,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    const { user, tenantId } = authResult;

    // Criar cliente Supabase
    const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    secureLogger.info("Iniciando migração de despesas para sistema híbrido", {
      userId: user.id,
      tenantId,
    });

    const result: MigrationResult = {
      success: true,
      despesasMigradas: 0,
      despesasJaMigradas: 0,
      errors: [],
    };

    // 1. Buscar todas as despesas que precisam ser migradas
    const { data: despesasParaMigrar, error: errorBusca } = await supabase
      .from("despesas")
      .select(
        `
        id,
        obra_id,
        tipo_despesa,
        unidade_especifica_id,
        obras!inner(
          id,
          tipo_projeto,
          parent_obra_id,
          identificador_unidade
        )
      `
      )
      .eq("tenant_id", tenantId)
      .eq("obras.tipo_projeto", "UNIDADE_CONDOMINIO");

    if (errorBusca) {
      secureLogger.error("Erro ao buscar despesas para migração", errorBusca);
      result.errors.push(`Erro na busca: ${errorBusca.message}`);
      result.success = false;
    } else {
      secureLogger.info(
        `Encontradas ${
          despesasParaMigrar?.length || 0
        } despesas de unidades para analisar`
      );

      // 2. Processar cada despesa
      for (const despesa of despesasParaMigrar || []) {
        try {
          // Verificar se já foi migrada
          if (
            despesa.tipo_despesa === "ESPECIFICA" &&
            despesa.unidade_especifica_id === despesa.obra_id &&
            despesa.obra_id === despesa.obras.parent_obra_id
          ) {
            result.despesasJaMigradas++;
            continue;
          }

          // Migrar despesa
          const { error: errorUpdate } = await supabase
            .from("despesas")
            .update({
              obra_id: despesa.obras.parent_obra_id, // Mover para o master
              tipo_despesa: "ESPECIFICA",
              unidade_especifica_id: despesa.obra_id, // ID da unidade original
              rateio_automatico: false,
              observacoes_unidade: `Migrado automaticamente - Unidade: ${
                despesa.obras.identificador_unidade || "N/A"
              }`,
            })
            .eq("id", despesa.id);

          if (errorUpdate) {
            secureLogger.error(
              `Erro ao migrar despesa ${despesa.id}`,
              errorUpdate
            );
            result.errors.push(`Despesa ${despesa.id}: ${errorUpdate.message}`);
          } else {
            result.despesasMigradas++;
            secureLogger.info(`Despesa ${despesa.id} migrada com sucesso`);
          }
        } catch (error) {
          secureLogger.error(
            `Erro inesperado ao processar despesa ${despesa.id}`,
            error
          );
          result.errors.push(`Despesa ${despesa.id}: Erro inesperado`);
        }
      }
    }

    // 3. Verificar se houve erros
    if (result.errors.length > 0) {
      result.success = false;
    }

    secureLogger.info("Migração concluída", {
      despesasMigradas: result.despesasMigradas,
      despesasJaMigradas: result.despesasJaMigradas,
      errors: result.errors.length,
      success: result.success,
    });

    return new Response(JSON.stringify(result), {
      status: result.success ? 200 : 207, // 207 = Multi-Status (sucesso parcial)
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error) {
    secureLogger.error("Erro fatal na migração", error);

    return new Response(
      JSON.stringify({
        success: false,
        error: "Erro interno do servidor",
        details: error instanceof Error ? error.message : "Erro desconhecido",
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
