// ============================================================================
// EDGE FUNCTION: ENVIO DE NOTIFICAÇÕES OBRASAI
// ============================================================================
// Função para processar e enviar notificações através de múltiplos canais
// Integração: Supabase Realtime + Email + Push Notifications
// ============================================================================

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'

// Tipos específicos do sistema de notificações
type NotificationType = 
  | 'obra_prazo_vencendo'
  | 'obra_orcamento_excedido'
  | 'obra_status_alterado'
  | 'novo_lead_capturado'
  | 'contrato_assinado'
  | 'contrato_vencendo'
  | 'ia_analise_pronta'
  | 'ia_orcamento_gerado'
  | 'sinapi_atualizado'
  | 'sistema_manutencao'
  | 'pagamento_vencendo'
  | 'pagamento_processado';

type NotificationChannel = 'in_app' | 'email' | 'push' | 'sms';
type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

interface CreateNotificationRequest {
  user_id: string;
  type: NotificationType;
  title: string;
  message: string;
  priority?: NotificationPriority;
  context_type?: string;
  context_id?: string;
  context_data?: Record<string, any>;
  channels?: NotificationChannel[];
  scheduled_for?: string;
}

interface NotificationPreferences {
  enabled: boolean;
  quiet_hours_start?: string;
  quiet_hours_end?: string;
  timezone: string;
  preferences: Record<string, {
    enabled: boolean;
    channels: NotificationChannel[];
    [key: string]: any;
  }>;
  email_digest_frequency: string;
}

// ============================================================================
// CLASSE PRINCIPAL DO SERVIÇO DE NOTIFICAÇÕES
// ============================================================================

class NotificationService {
  private supabase: any;
  
  constructor() {
    this.supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );
  }

  /**
   * Processa e envia uma notificação
   */
  async processNotification(request: CreateNotificationRequest): Promise<any> {
    try {
      // 1. Validar dados de entrada
      this.validateRequest(request);

      // 2. Obter preferências do usuário
      const preferences = await this.getUserPreferences(request.user_id);
      
      // 3. Verificar se o tipo de notificação está habilitado
      if (!this.isNotificationEnabled(request.type, preferences)) {
        return { success: true, message: 'Notification disabled by user preferences', skipped: true };
      }

      // 4. Determinar canais de envio
      const channels = this.determineChannels(request, preferences);
      
      // 5. Verificar horário silencioso
      if (this.isInQuietHours(preferences)) {
        // Agendar para depois do horário silencioso
        request.scheduled_for = this.calculateNextSendTime(preferences);
      }

      // 6. Criar registro da notificação no banco
      const notification = await this.createNotificationRecord({
        ...request,
        channels
      });

      // 7. Enviar através dos canais apropriados
      const deliveryResults = await this.sendThroughChannels(notification, channels, preferences);

      // 8. Registrar logs de entrega
      await this.logDeliveryResults(notification.id, deliveryResults);

      return { 
        success: true, 
        notification_id: notification.id,
        delivery_results: deliveryResults
      };

    } catch (error) {
      console.error('Erro ao processar notificação:', error);
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  /**
   * Valida os dados da requisição
   */
  private validateRequest(request: CreateNotificationRequest): void {
    if (!request.user_id || !request.type || !request.title || !request.message) {
      throw new Error('Campos obrigatórios: user_id, type, title, message');
    }
  }

  /**
   * Obtém as preferências de notificação do usuário
   */
  private async getUserPreferences(userId: string): Promise<NotificationPreferences> {
    const { data, error } = await this.supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') { // 'PGRST116' = not found
      throw new Error(`Erro ao obter preferências: ${error.message}`);
    }

    // Retorna preferências padrão se não existir
    return data || this.getDefaultPreferences();
  }

  /**
   * Verifica se a notificação está habilitada para o usuário
   */
  private isNotificationEnabled(type: NotificationType, preferences: NotificationPreferences): boolean {
    if (!preferences.enabled) return false;
    
    const typePreference = preferences.preferences[type];
    return typePreference?.enabled ?? true;
  }

  /**
   * Determina os canais de envio baseado na requisição e preferências
   */
  private determineChannels(request: CreateNotificationRequest, preferences: NotificationPreferences): NotificationChannel[] {
    if (request.channels) {
      return request.channels;
    }

    const typePreference = preferences.preferences[request.type];
    return typePreference?.channels || ['in_app'];
  }

  /**
   * Verifica se está no horário silencioso
   */
  private isInQuietHours(preferences: NotificationPreferences): boolean {
    if (!preferences.quiet_hours_start || !preferences.quiet_hours_end) {
      return false;
    }

    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM
    
    const start = preferences.quiet_hours_start;
    const end = preferences.quiet_hours_end;

    // Verificar se o horário atual está entre o início e fim do silencioso
    if (start <= end) {
      return currentTime >= start && currentTime <= end;
    } else {
      // Caso o horário silencioso cruze a meia-noite
      return currentTime >= start || currentTime <= end;
    }
  }

  /**
   * Calcula o próximo horário de envio após o período silencioso
   */
  private calculateNextSendTime(preferences: NotificationPreferences): string {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const [hours, minutes] = (preferences.quiet_hours_end || '08:00').split(':');
    tomorrow.setHours(parseInt(hours), parseInt(minutes), 0, 0);
    
    return tomorrow.toISOString();
  }

  /**
   * Cria o registro da notificação no banco
   */
  private async createNotificationRecord(data: any): Promise<any> {
    const { data: notification, error } = await this.supabase
      .from('notifications')
      .insert({
        user_id: data.user_id,
        type: data.type,
        title: data.title,
        message: data.message,
        priority: data.priority || 'medium',
        context_type: data.context_type,
        context_id: data.context_id,
        context_data: data.context_data,
        channels: data.channels,
        scheduled_for: data.scheduled_for
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Erro ao criar notificação: ${error.message}`);
    }

    return notification;
  }

  /**
   * Envia a notificação através dos canais especificados
   */
  private async sendThroughChannels(notification: any, channels: NotificationChannel[], preferences: NotificationPreferences): Promise<any[]> {
    const results = [];

    for (const channel of channels) {
      try {
        let result;
        
        switch (channel) {
          case 'in_app':
            result = await this.sendInAppNotification(notification);
            break;
          case 'email':
            result = await this.sendEmailNotification(notification, preferences);
            break;
          case 'push':
            result = await this.sendPushNotification(notification);
            break;
          case 'sms':
            result = await this.sendSMSNotification(notification);
            break;
          default:
            result = { channel, status: 'failed', error: 'Canal não suportado' };
        }

        results.push(result);
      } catch (error) {
        results.push({ 
          channel, 
          status: 'failed', 
          error: error.message 
        });
      }
    }

    return results;
  }

  /**
   * Envia notificação in-app via Supabase Realtime
   */
  private async sendInAppNotification(notification: any): Promise<any> {
    try {
      // Enviar via Supabase Realtime
      const { error } = await this.supabase
        .channel(`user:${notification.user_id}`)
        .send({
          type: 'broadcast',
          event: 'new_notification',
          payload: {
            id: notification.id,
            type: notification.type,
            title: notification.title,
            message: notification.message,
            priority: notification.priority,
            context_type: notification.context_type,
            context_id: notification.context_id,
            created_at: notification.created_at
          }
        });

      if (error) {
        throw error;
      }

      return { 
        channel: 'in_app', 
        status: 'sent', 
        provider: 'supabase_realtime',
        sent_at: new Date().toISOString()
      };
    } catch (error) {
      return { 
        channel: 'in_app', 
        status: 'failed', 
        error: error.message 
      };
    }
  }

  /**
   * Envia notificação por email
   */
  private async sendEmailNotification(notification: any, preferences: NotificationPreferences): Promise<any> {
    try {
      // Obter dados do usuário
      const { data: user } = await this.supabase.auth.admin.getUserById(notification.user_id);
      
      if (!user?.email) {
        throw new Error('Email do usuário não encontrado');
      }

      // Template de email baseado no tipo de notificação
      const emailContent = this.generateEmailContent(notification);

      // Enviar email via Supabase (ou provider externo)
      const { error } = await this.supabase.functions.invoke('send-email', {
        body: {
          to: user.email,
          subject: notification.title,
          html: emailContent,
          metadata: {
            notification_id: notification.id,
            type: notification.type
          }
        }
      });

      if (error) {
        throw error;
      }

      return { 
        channel: 'email', 
        status: 'sent', 
        provider: 'supabase',
        recipient: user.email,
        sent_at: new Date().toISOString()
      };
    } catch (error) {
      return { 
        channel: 'email', 
        status: 'failed', 
        error: error.message 
      };
    }
  }

  /**
   * Envia push notification (placeholder para implementação futura)
   */
  private async sendPushNotification(notification: any): Promise<any> {
    // TODO: Implementar integração com FCM/APNs
    return { 
      channel: 'push', 
      status: 'pending', 
      message: 'Push notifications não implementado ainda' 
    };
  }

  /**
   * Envia SMS (placeholder para implementação futura)
   */
  private async sendSMSNotification(notification: any): Promise<any> {
    // TODO: Implementar integração com Twilio/AWS SNS
    return { 
      channel: 'sms', 
      status: 'pending', 
      message: 'SMS não implementado ainda' 
    };
  }

  /**
   * Gera conteúdo de email baseado no tipo de notificação
   */
  private generateEmailContent(notification: any): string {
    const baseTemplate = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${notification.title}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #1e40af; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
          .priority-high { border-left: 4px solid #dc2626; }
          .priority-urgent { border-left: 4px solid #dc2626; background: #fee2e2; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>🏗️ ObrasAI</h2>
          </div>
          <div class="content ${notification.priority === 'high' || notification.priority === 'urgent' ? 'priority-' + notification.priority : ''}">
            <h3>${notification.title}</h3>
            <p>${notification.message}</p>
            ${this.getContextualContent(notification)}
          </div>
          <div class="footer">
            <p>Esta notificação foi enviada pelo sistema ObrasAI.<br>
            Para alterar suas preferências de notificação, acesse as configurações da sua conta.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return baseTemplate;
  }

  /**
   * Gera conteúdo contextual baseado no tipo de notificação
   */
  private getContextualContent(notification: any): string {
    const contextData = notification.context_data || {};
    
    switch (notification.type) {
      case 'obra_prazo_vencendo':
        return `<p><strong>Obra:</strong> ${contextData.obra_nome}<br>
                <strong>Prazo:</strong> ${contextData.prazo_final}<br>
                <strong>Dias restantes:</strong> ${contextData.dias_restantes}</p>`;
                
      case 'obra_orcamento_excedido':
        return `<p><strong>Obra:</strong> ${contextData.obra_nome}<br>
                <strong>Orçamento original:</strong> R$ ${contextData.orcamento_original}<br>
                <strong>Valor atual:</strong> R$ ${contextData.valor_atual}<br>
                <strong>Excesso:</strong> ${contextData.percentual_excesso}%</p>`;
                
      case 'novo_lead_capturado':
        return `<p><strong>Nome:</strong> ${contextData.nome}<br>
                <strong>Email:</strong> ${contextData.email}<br>
                <strong>Telefone:</strong> ${contextData.telefone}<br>
                <strong>Origem:</strong> ${contextData.origem}</p>`;
                
      default:
        return '';
    }
  }

  /**
   * Registra os resultados de entrega no log
   */
  private async logDeliveryResults(notificationId: string, results: any[]): Promise<void> {
    const logs = results.map(result => ({
      notification_id: notificationId,
      channel: result.channel,
      status: result.status,
      provider: result.provider || null,
      provider_response: result,
      error_message: result.error || null,
      sent_at: result.sent_at || null
    }));

    const { error } = await this.supabase
      .from('notification_delivery_log')
      .insert(logs);

    if (error) {
      console.error('Erro ao registrar logs de entrega:', error);
    }
  }

  /**
   * Retorna preferências padrão
   */
  private getDefaultPreferences(): NotificationPreferences {
    return {
      enabled: true,
      timezone: 'America/Sao_Paulo',
      email_digest_frequency: 'daily',
      preferences: {
        'obra_prazo_vencendo': { enabled: true, channels: ['in_app', 'email'], advance_hours: 48 },
        'obra_orcamento_excedido': { enabled: true, channels: ['in_app', 'email'], threshold_percentage: 110 },
        'obra_status_alterado': { enabled: true, channels: ['in_app'] },
        'novo_lead_capturado': { enabled: true, channels: ['in_app', 'email'], immediate: true },
        'contrato_assinado': { enabled: true, channels: ['in_app', 'email'] },
        'contrato_vencendo': { enabled: true, channels: ['in_app', 'email'], advance_days: 30 },
        'ia_analise_pronta': { enabled: true, channels: ['in_app'] },
        'ia_orcamento_gerado': { enabled: true, channels: ['in_app', 'email'] },
        'sinapi_atualizado': { enabled: false, channels: ['in_app'] },
        'sistema_manutencao': { enabled: true, channels: ['in_app', 'email'] },
        'pagamento_vencendo': { enabled: true, channels: ['in_app', 'email'], advance_days: 7 },
        'pagamento_processado': { enabled: true, channels: ['in_app'] }
      }
    };
  }
}

// ============================================================================
// HANDLER PRINCIPAL DA EDGE FUNCTION
// ============================================================================

serve(async (req: Request) => {
  // CORS Headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS'
  };

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Verificar método HTTP
    if (req.method !== 'POST') {
      throw new Error('Método não permitido. Use POST.');
    }

    // Parse do body
    const body = await req.json();
    
    // Criar instância do serviço
    const notificationService = new NotificationService();
    
    // Processar notificação
    const result = await notificationService.processNotification(body);

    // Retornar resultado
    return new Response(
      JSON.stringify(result),
      {
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        },
        status: result.success ? 200 : 400
      }
    );

  } catch (error) {
    console.error('Erro na Edge Function:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message 
      }),
      {
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        },
        status: 500
      }
    );
  }
});