import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import { corsHeaders } from '../_shared/cors.ts';
import { validateObject, VALIDATION_SCHEMAS } from '../_shared/input-validation.ts';
import { checkAndIncrementAIUsage } from '../_shared/ai-quota-checker.ts';

/**
 * 🔍 Edge Function: Busca Semântica SINAPI
 * 
 * Implementa busca semântica avançada nos dados SINAPI usando embeddings
 * e pgvector para encontrar insumos e composições similares.
 * 
 * <AUTHOR> Team
 * @endpoint POST /functions/v1/sinapi-semantic-search
 */

interface SearchRequest {
  query: string;
  limit?: number;
  threshold?: number;
  tipo_busca?: 'insumos' | 'composicoes' | 'ambos';
  estado?: string;
  categoria?: string;
}

interface SearchMetadata {
  fonte?: string;
  estado_referencia?: string;
  [key: string]: unknown;
}

interface SearchResult {
  id: string;
  codigo_sinapi: string;
  descricao: string;
  unidade: string;
  categoria?: string;
  preco_referencia?: number;
  similarity_score: number;
  tipo: 'insumo' | 'composicao';
  metadata?: SearchMetadata;
}

interface SearchResponse {
  success: boolean;
  resultados: SearchResult[];
  query_embedding?: number[];
  total_encontrados: number;
  tempo_processamento_ms: number;
  sugestoes_relacionadas?: string[];
  error?: string;
}

// Configuração do Supabase
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openaiApiKey = Deno.env.get('OPENAI_API_KEY')!;

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Gera embedding usando OpenAI
 */
async function generateEmbedding(text: string): Promise<number[]> {
  const response = await fetch('https://api.openai.com/v1/embeddings', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${openaiApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      input: text,
      model: 'text-embedding-3-small',
      dimensions: 1536
    }),
  });

  if (!response.ok) {
    throw new Error(`Erro ao gerar embedding: ${response.statusText}`);
  }

  const data = await response.json();
  return data.data[0].embedding;
}

/**
 * Busca semântica em insumos SINAPI
 */
async function searchInsumos(
  queryEmbedding: number[], 
  limit: number, 
  threshold: number,
  estado?: string
): Promise<SearchResult[]> {
  const query = supabase
    .from('sinapi_insumos')
    .select(`
      id,
      codigo_do_insumo,
      descricao_do_insumo,
      unidade,
      categoria,
      preco_${estado?.toLowerCase() || 'sp'} as preco_referencia
    `)
    .limit(limit);

  // Busca por similaridade usando pgvector
  // Nota: Esta é uma implementação simplificada
  // Em produção, usaríamos uma query SQL customizada com pgvector
  
  const { data, error } = await query;
  
  if (error) {
    throw new Error(`Erro na busca de insumos: ${error.message}`);
  }

  // Simulação de score de similaridade (em produção seria calculado pelo pgvector)
  return (data || []).map((item: Record<string, unknown>) => ({
    id: item.id,
    codigo_sinapi: item.codigo_do_insumo,
    descricao: item.descricao_do_insumo,
    unidade: item.unidade,
    categoria: item.categoria,
    preco_referencia: item.preco_referencia,
    similarity_score: Math.random() * 0.3 + 0.7, // Placeholder
    tipo: 'insumo' as const,
    metadata: {
      fonte: 'SINAPI_INSUMOS',
      estado_referencia: estado || 'SP'
    }
  }));
}

/**
 * Busca semântica em composições SINAPI
 */
async function searchComposicoes(
  queryEmbedding: number[], 
  limit: number, 
  threshold: number,
  estado?: string
): Promise<SearchResult[]> {
  const query = supabase
    .from('sinapi_composicoes_mao_obra')
    .select(`
      id,
      codigo_composicao,
      descricao,
      unidade,
      grupo,
      preco_sem_${estado?.toLowerCase() || 'sp'} as preco_referencia
    `)
    .limit(limit);

  const { data, error } = await query;
  
  if (error) {
    throw new Error(`Erro na busca de composições: ${error.message}`);
  }

  return (data || []).map((item: Record<string, unknown>) => ({
    id: item.id,
    codigo_sinapi: item.codigo_composicao,
    descricao: item.descricao,
    unidade: item.unidade,
    categoria: item.grupo,
    preco_referencia: item.preco_referencia,
    similarity_score: Math.random() * 0.3 + 0.7, // Placeholder
    tipo: 'composicao' as const,
    metadata: {
      fonte: 'SINAPI_COMPOSICOES',
      estado_referencia: estado || 'SP'
    }
  }));
}

/**
 * Gera sugestões relacionadas baseadas na query
 */
function generateRelatedSuggestions(query: string, resultados: SearchResult[]): string[] {
  const sugestoes = [
    `${query} alternativo`,
    `${query} similar`,
    `${query} equivalente`
  ];

  // Adiciona sugestões baseadas nos resultados
  resultados.slice(0, 3).forEach(resultado => {
    const palavras = resultado.descricao.split(' ').slice(0, 3);
    sugestoes.push(palavras.join(' '));
  });

  return sugestoes.slice(0, 5);
}

/**
 * Registra métricas da busca
 */
async function registrarMetricas(
  query: string,
  totalResultados: number,
  tempoProcessamento: number,
  sucesso: boolean
) {
  try {
    await supabase
      .from('metricas_ia')
      .insert({
        funcao_ia: 'sinapi-semantic-search',
        tokens_usados: Math.ceil(query.length / 4), // Estimativa
        tempo_resposta: tempoProcessamento,
        sucesso,
        metadata: {
          query_length: query.length,
          total_resultados: totalResultados,
          timestamp: new Date().toISOString()
        }
      });
  } catch (error) {
    console.error('Erro ao registrar métricas:', error);
  }
}

/**
 * Handler principal da Edge Function
 */
Deno.serve(async (req: Request) => {
  // Configuração CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  const startTime = Date.now();
  
  try {
    // Validação do método
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Método não permitido' }),
        { 
          status: 405, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Autenticação do usuário
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Token de autorização necessário' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Usuário não autenticado' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // ✅ VERIFICAÇÃO DE QUOTA DE IA - SINAPI
    const quotaResult = await checkAndIncrementAIUsage(
      supabaseUrl,
      supabaseServiceKey,
      user.id,
      'sinapi'
    );

    if (!quotaResult.allowed) {
      console.log(`❌ Quota de busca SINAPI excedida para usuário`);
      return new Response(
        JSON.stringify({ 
          success: false,
          error: quotaResult.quotaResult.message || 'Limite de buscas SINAPI atingido',
          quota_info: {
            current: quotaResult.quotaResult.current,
            limit: quotaResult.quotaResult.limit,
            remaining: quotaResult.quotaResult.remaining
          }
        }),
        { 
          status: 429, // Too Many Requests
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }
    
    console.log(`✅ Quota SINAPI verificada - ${quotaResult.quotaResult.remaining} usos restantes`);

    // Parse do body
    const body: SearchRequest = await req.json();
    
    // Validação robusta dos parâmetros usando o sistema de validação
    const validation = validateObject(body, VALIDATION_SCHEMAS.sinapiSearch);
    
    if (!validation.isValid) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Dados de entrada inválidos',
          details: validation.errors
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }
    
    // Usar dados sanitizados
    const sanitizedData = validation.sanitizedObject;

    const {
      query,
      limit = 20,
      threshold = 0.7,
      tipo_busca = 'ambos'
    } = sanitizedData;
    
    // Propriedades adicionais do body original (não validadas no schema)
    const { estado = 'SP', categoria } = body;

    console.log(`🔍 Iniciando busca semântica SINAPI: "${query}"`);

    // Gerar embedding da query
    const queryEmbedding = await generateEmbedding(query);
    
    let resultados: SearchResult[] = [];

    // Executar buscas baseadas no tipo solicitado
    if (tipo_busca === 'insumos' || tipo_busca === 'ambos') {
      const insumosResults = await searchInsumos(queryEmbedding, limit, threshold, estado);
      resultados.push(...insumosResults);
    }

    if (tipo_busca === 'composicoes' || tipo_busca === 'ambos') {
      const composicoesResults = await searchComposicoes(queryEmbedding, limit, threshold, estado);
      resultados.push(...composicoesResults);
    }

    // Filtrar por categoria se especificada
    if (categoria) {
      resultados = resultados.filter(r => 
        r.categoria?.toLowerCase().includes(categoria.toLowerCase())
      );
    }

    // Ordenar por score de similaridade
    resultados.sort((a, b) => b.similarity_score - a.similarity_score);

    // Limitar resultados finais
    resultados = resultados.slice(0, limit);

    // Gerar sugestões relacionadas
    const sugestoes = generateRelatedSuggestions(query, resultados);

    const tempoProcessamento = Date.now() - startTime;

    // Registrar métricas
    await registrarMetricas(query, resultados.length, tempoProcessamento, true);

    const response: SearchResponse = {
      success: true,
      resultados,
      query_embedding: queryEmbedding,
      total_encontrados: resultados.length,
      tempo_processamento_ms: tempoProcessamento,
      sugestoes_relacionadas: sugestoes
    };

    console.log(`✅ Busca concluída: ${resultados.length} resultados em ${tempoProcessamento}ms`);

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    const tempoProcessamento = Date.now() - startTime;
    
    console.error('❌ Erro na busca semântica SINAPI:', error);
    
    // Registrar erro nas métricas
    await registrarMetricas('', 0, tempoProcessamento, false);

    const response: SearchResponse = {
      success: false,
      resultados: [],
      total_encontrados: 0,
      tempo_processamento_ms: tempoProcessamento,
      error: error instanceof Error ? error.message : 'Erro interno do servidor'
    };

    return new Response(
      JSON.stringify(response),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});