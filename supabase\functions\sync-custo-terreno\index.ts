import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { requireAuthAndTenant } from "../_shared/auth-handler.ts";
import { getPreflightHeaders, getSecureCorsHeaders } from "../_shared/cors.ts";
import { logger } from "../_shared/logger.ts";

interface SyncCustoTerrenoRequest {
  obra_id: string;
  action: "sync" | "recalculate";
}

interface SyncCustoTerrenoResponse {
  success: boolean;
  obra_id: string;
  custo_terreno_anterior: number;
  custo_terreno_atual: number;
  total_despesas_aquisicao: number;
  message: string;
}

serve(async (req) => {
  const origin = req.headers.get("origin");

  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: getPreflightHeaders(origin) });
  }

  try {
    logger.info("Iniciando verificação de autenticação", {
      method: req.method,
      url: req.url,
      origin,
    });

    // Verificar autenticação e tenant
    const authResult = await requireAuthAndTenant(req);

    logger.info("Resultado da autenticação", {
      success: authResult.success,
      hasContext: !!authResult.context,
      hasTenantId: !!authResult.tenantId,
      tenantId: authResult.tenantId,
    });

    if (!authResult.success || !authResult.context || !authResult.tenantId) {
      logger.error("Falha na autenticação ou tenant", {
        success: authResult.success,
        hasContext: !!authResult.context,
        hasTenantId: !!authResult.tenantId,
        tenantId: authResult.tenantId,
      });

      return (
        authResult.response ||
        new Response(
          JSON.stringify({ success: false, error: "Não autorizado" }),
          {
            status: 401,
            headers: {
              ...getSecureCorsHeaders(origin),
              "Content-Type": "application/json",
            },
          }
        )
      );
    }

    const { context: user, tenantId } = authResult;

    // Criar cliente Supabase
    const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse do body da requisição
    const { obra_id, action = "sync" }: SyncCustoTerrenoRequest =
      await req.json();

    if (!obra_id) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "obra_id é obrigatório",
        }),
        {
          status: 400,
          headers: {
            ...getSecureCorsHeaders(origin),
            "Content-Type": "application/json",
          },
        }
      );
    }

    logger.info("Iniciando sincronização de custo do terreno", {
      obra_id,
      action,
      user_id: user.user.id,
      tenant_id: tenantId,
    });

    // 1. Verificar se a obra existe e pertence ao tenant
    const { data: obra, error: obraError } = await supabase
      .from("obras")
      .select("id, nome, custo_terreno, orcamento")
      .eq("id", obra_id)
      .eq("tenant_id", tenantId)
      .single();

    if (obraError || !obra) {
      logger.error("Obra não encontrada ou sem permissão", {
        obra_id,
        tenant_id: tenantId,
        error: obraError,
      });

      return new Response(
        JSON.stringify({
          success: false,
          error: "Obra não encontrada ou sem permissão de acesso",
        }),
        {
          status: 404,
          headers: {
            ...getSecureCorsHeaders(origin),
            "Content-Type": "application/json",
          },
        }
      );
    }

    const custo_terreno_anterior = obra.custo_terreno || 0;

    // 2. Calcular total das despesas de aquisição de terreno/área e imóveis
    const { data: despesas, error: despesasError } = await supabase
      .from("despesas")
      .select("id, custo, descricao, categoria")
      .eq("obra_id", obra_id)
      .in("categoria", ["AQUISICAO_TERRENO_AREA", "AQUISICAO_IMOVEL_REFORMA_LEILAO"]);

    if (despesasError) {
      logger.error("Erro ao buscar despesas de aquisição", {
        obra_id,
        error: despesasError,
      });

      return new Response(
        JSON.stringify({
          success: false,
          error: "Erro ao buscar despesas de aquisição",
        }),
        {
          status: 500,
          headers: {
            ...getSecureCorsHeaders(origin),
            "Content-Type": "application/json",
          },
        }
      );
    }

    // 3. Calcular novo custo do terreno
    const total_despesas_aquisicao =
      despesas?.reduce((total, despesa) => {
        return total + (despesa.custo || 0);
      }, 0) || 0;

    // 4. Atualizar o campo custo_terreno na obra
    const { error: updateError } = await supabase
      .from("obras")
      .update({
        custo_terreno: total_despesas_aquisicao,
        updated_at: new Date().toISOString(),
      })
      .eq("id", obra_id)
      .eq("tenant_id", tenantId);

    if (updateError) {
      logger.error("Erro ao atualizar custo do terreno", {
        obra_id,
        error: updateError,
      });

      return new Response(
        JSON.stringify({
          success: false,
          error: "Erro ao atualizar custo do terreno",
        }),
        {
          status: 500,
          headers: {
            ...getSecureCorsHeaders(origin),
            "Content-Type": "application/json",
          },
        }
      );
    }

    // 5. Log de sucesso
    logger.info("Custo do terreno sincronizado com sucesso", {
      obra_id,
      obra_nome: obra.nome,
      custo_terreno_anterior,
      custo_terreno_atual: total_despesas_aquisicao,
      total_despesas_aquisicao,
      quantidade_despesas: despesas?.length || 0,
      categorias_incluidas: ["AQUISICAO_TERRENO_AREA", "AQUISICAO_IMOVEL_REFORMA_LEILAO"],
      despesas_por_categoria: despesas?.reduce((acc, d) => {
        acc[d.categoria] = (acc[d.categoria] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      user_id: user.user.id,
      tenant_id: tenantId,
    });

    // 6. Preparar resposta
    const response: SyncCustoTerrenoResponse = {
      success: true,
      obra_id,
      custo_terreno_anterior,
      custo_terreno_atual: total_despesas_aquisicao,
      total_despesas_aquisicao,
      message: `Custo do terreno atualizado de R$ ${custo_terreno_anterior.toFixed(
        2
      )} para R$ ${total_despesas_aquisicao.toFixed(2)}`,
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        ...getSecureCorsHeaders(origin),
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    logger.error("Erro interno na sincronização de custo do terreno", error);

    return new Response(
      JSON.stringify({
        success: false,
        error: "Erro interno do servidor",
      }),
      {
        status: 500,
        headers: {
          ...getSecureCorsHeaders(origin),
          "Content-Type": "application/json",
        },
      }
    );
  }
});
