{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "WebWorker"], "module": "ESNext", "moduleResolution": "bundler", "allowJs": true, "checkJs": false, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowImportingTsExtensions": true, "allowArbitraryExtensions": true, "noEmit": true, "isolatedModules": true, "verbatimModuleSyntax": false}, "include": ["**/*.ts", "**/*.d.ts"], "exclude": ["node_modules"]}