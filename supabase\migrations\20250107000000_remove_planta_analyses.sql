-- Migration: Remove planta_analyses table and all related dependencies
-- Created: 2025-01-07
-- Description: Remove complete Plantas IA functionality from database

-- Drop policies first
DROP POLICY IF EXISTS "Usuários podem ver suas próprias análises" ON "public"."planta_analyses";
DROP POLICY IF EXISTS "Usuários podem inserir suas próprias análises" ON "public"."planta_analyses";
DROP POLICY IF EXISTS "Usuários podem atualizar suas próprias análises" ON "public"."planta_analyses";
DROP POLICY IF EXISTS "Usuários podem deletar suas próprias análises" ON "public"."planta_analyses";

-- Drop triggers
DROP TRIGGER IF EXISTS "update_planta_analyses_updated_at" ON "public"."planta_analyses";

-- Drop indexes
DROP INDEX IF EXISTS "idx_planta_analyses_created_at";
DROP INDEX IF EXISTS "idx_planta_analyses_obra_id";
DROP INDEX IF EXISTS "idx_planta_analyses_user_id";

-- Drop foreign key constraints
ALTER TABLE IF EXISTS "public"."planta_analyses" DROP CONSTRAINT IF EXISTS "planta_analyses_obra_id_fkey";
ALTER TABLE IF EXISTS "public"."planta_analyses" DROP CONSTRAINT IF EXISTS "planta_analyses_user_id_fkey";

-- Drop primary key constraint
ALTER TABLE IF EXISTS "public"."planta_analyses" DROP CONSTRAINT IF EXISTS "planta_analyses_pkey";

-- Drop the table
DROP TABLE IF EXISTS "public"."planta_analyses";

-- Remove any grants that were given to the table
-- (These will be automatically removed when the table is dropped, but being explicit)

COMMENT ON SCHEMA public IS 'Planta analyses table and related functionality removed'; 