-- Migração para criar bucket 'plantas' no Supabase Storage
-- Data: 2025-01-07
-- Descrição: Cria bucket para armazenar plantas arquitetônicas analisadas pela IA

-- Criar bucket 'plantas' se não existir
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'plantas',
    'plantas',
    true,  -- público para facilitar visualização das plantas
    52428800,  -- 50MB limit para plantas arquitetônicas
    ARRAY['image/jpeg', 'image/png', 'image/jpg', 'image/webp', 'application/pdf']
)
ON CONFLICT (id) DO NOTHING;

-- Políticas RLS para o bucket plantas

-- Política para INSERT - Upload de plantas
CREATE POLICY "Usuários autenticados podem fazer upload de plantas"
ON storage.objects
FOR INSERT
WITH CHECK (
    bucket_id = 'plantas' 
    AND auth.uid() IS NOT NULL
);

-- Política para SELECT - Visualização de plantas
CREATE POLICY "Usuários autenticados podem ver plantas"
ON storage.objects
FOR SELECT
USING (
    bucket_id = 'plantas' 
    AND auth.uid() IS NOT NULL
);

-- Política para UPDATE - Atualização de plantas
CREATE POLICY "Usuários autenticados podem atualizar suas plantas"
ON storage.objects
FOR UPDATE
USING (
    bucket_id = 'plantas' 
    AND auth.uid() IS NOT NULL
);

-- Política para DELETE - Exclusão de plantas
CREATE POLICY "Usuários autenticados podem deletar suas plantas"
ON storage.objects
FOR DELETE
USING (
    bucket_id = 'plantas' 
    AND auth.uid() IS NOT NULL
);

