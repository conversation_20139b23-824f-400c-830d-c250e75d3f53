-- Migração: Adicionar campo categoria para fornecedores
-- Data: 2025-01-13
-- Descrição: Adiciona campo categoria na tabela fornecedores_pj para melhor classificação

-- 1. Adicionar campo categoria na tabela fornecedores_pj
ALTER TABLE fornecedores_pj 
ADD COLUMN IF NOT EXISTS categoria VARCHAR(50);

-- 2. <PERSON><PERSON><PERSON> índice para performance nas consultas por categoria
CREATE INDEX IF NOT EXISTS idx_fornecedores_pj_categoria 
ON fornecedores_pj(categoria);

-- 3. Criar índice composto para tenant_id + categoria (otimização para busca)
CREATE INDEX IF NOT EXISTS idx_fornecedores_pj_tenant_categoria 
ON fornecedores_pj(tenant_id, categoria);

-- 4. Atualizar registros existentes baseado em observações (análise inteligente)
UPDATE fornecedores_pj 
SET categoria = CASE 
  -- Material de Construção
  WHEN observacoes ILIKE '%material%' 
    OR observacoes ILIKE '%loja%' 
    OR observacoes ILIKE '%cimento%'
    OR observacoes ILIKE '%tijolo%'
    OR observacoes ILIKE '%areia%'
    OR observacoes ILIKE '%brita%'
    OR observacoes ILIKE '%ferro%'
    OR observacoes ILIKE '%tinta%'
    OR razao_social ILIKE '%material%'
    OR nome_fantasia ILIKE '%material%'
    THEN 'MATERIAL_CONSTRUCAO'
  
  -- Equipamentos e Ferramentas
  WHEN observacoes ILIKE '%equipamento%' 
    OR observacoes ILIKE '%aluguel%'
    OR observacoes ILIKE '%ferramenta%'
    OR observacoes ILIKE '%betoneira%'
    OR observacoes ILIKE '%andaime%'
    OR observacoes ILIKE '%guindaste%'
    OR observacoes ILIKE '%máquina%'
    OR razao_social ILIKE '%equipamento%'
    OR nome_fantasia ILIKE '%equipamento%'
    THEN 'EQUIPAMENTOS'
  
  -- Transporte e Logística
  WHEN observacoes ILIKE '%transporte%' 
    OR observacoes ILIKE '%frete%'
    OR observacoes ILIKE '%entrega%'
    OR observacoes ILIKE '%logística%'
    OR observacoes ILIKE '%caminhão%'
    OR razao_social ILIKE '%transporte%'
    OR nome_fantasia ILIKE '%transporte%'
    THEN 'TRANSPORTE_LOGISTICA'
  
  -- Serviços Especializados
  WHEN observacoes ILIKE '%serviço%' 
    OR observacoes ILIKE '%consultoria%'
    OR observacoes ILIKE '%projeto%'
    OR observacoes ILIKE '%engenharia%'
    OR observacoes ILIKE '%arquitetura%'
    OR razao_social ILIKE '%serviço%'
    OR nome_fantasia ILIKE '%serviço%'
    THEN 'SERVICOS_ESPECIALIZADOS'
  
  -- Consultoria e Projetos
  WHEN observacoes ILIKE '%consultoria%'
    OR observacoes ILIKE '%projeto%'
    OR observacoes ILIKE '%planejamento%'
    OR observacoes ILIKE '%gestão%'
    OR razao_social ILIKE '%consultoria%'
    OR nome_fantasia ILIKE '%consultoria%'
    THEN 'CONSULTORIA_PROJETOS'
  
  -- Padrão para casos não identificados
  ELSE 'OUTRO'
END
WHERE categoria IS NULL;

-- 5. Comentários para documentação
COMMENT ON COLUMN fornecedores_pj.categoria IS 'Categoria do fornecedor PJ: MATERIAL_CONSTRUCAO, EQUIPAMENTOS, SERVICOS_ESPECIALIZADOS, TRANSPORTE_LOGISTICA, CONSULTORIA_PROJETOS, OUTRO';

-- 6. Verificar resultado da migração (para logs)
DO $$
DECLARE
    total_registros INTEGER;
    registros_categorizados INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_registros FROM fornecedores_pj;
    SELECT COUNT(*) INTO registros_categorizados FROM fornecedores_pj WHERE categoria IS NOT NULL;
    
    RAISE NOTICE 'Migração concluída: % registros totais, % categorizados', total_registros, registros_categorizados;
END $$;
