-- Migration: Add condominio support to obras table
-- Date: 2025-01-16
-- Description: Adds hierarchical structure support for condominium projects

-- Create the project_type enum
CREATE TYPE project_type AS ENUM ('UNICO', 'CONDOMINIO_MASTER', 'UNIDADE_CONDOMINIO');

-- Add new columns to obras table
ALTER TABLE obras 
ADD COLUMN parent_obra_id UUID REFERENCES obras(id) ON DELETE RESTRICT,
ADD COLUMN tipo_projeto project_type NOT NULL DEFAULT 'UNICO',
ADD COLUMN identificador_unidade TEXT;

-- Create indexes for performance optimization
CREATE INDEX idx_obras_parent_obra_id ON obras(parent_obra_id);
CREATE INDEX idx_obras_tipo_projeto ON obras(tipo_projeto);
CREATE INDEX idx_obras_tenant_tipo ON obras(tenant_id, tipo_projeto);

-- Add constraints for data integrity
ALTER TABLE obras 
ADD CONSTRAINT chk_condominio_master_no_parent 
CHECK (
  (tipo_projeto = 'CO<PERSON><PERSON>IN<PERSON>_MASTER' AND parent_obra_id IS NULL) OR
  (tipo_projeto != 'CONDOMINIO_MASTER')
);

ALTER TABLE obras 
ADD CONSTRAINT chk_unidade_has_parent 
CHECK (
  (tipo_projeto = 'UNIDADE_CONDOMINIO' AND parent_obra_id IS NOT NULL) OR
  (tipo_projeto != 'UNIDADE_CONDOMINIO')
);

ALTER TABLE obras 
ADD CONSTRAINT chk_unico_no_parent 
CHECK (
  (tipo_projeto = 'UNICO' AND parent_obra_id IS NULL) OR
  (tipo_projeto != 'UNICO')
);

-- Add constraint to ensure identificador_unidade is unique within the same condominio
CREATE UNIQUE INDEX idx_obras_identificador_unidade_unique 
ON obras(parent_obra_id, identificador_unidade) 
WHERE parent_obra_id IS NOT NULL AND identificador_unidade IS NOT NULL;

-- Add comment to document the new structure
COMMENT ON COLUMN obras.parent_obra_id IS 'ID da obra-mãe para unidades de condomínio. NULL para obras únicas e condomínios principais.';
COMMENT ON COLUMN obras.tipo_projeto IS 'Tipo do projeto: UNICO (obra única), CONDOMINIO_MASTER (condomínio principal), UNIDADE_CONDOMINIO (unidade do condomínio)';
COMMENT ON COLUMN obras.identificador_unidade IS 'Identificador único da unidade dentro do condomínio (ex: "Casa 1", "Apto 101", etc.)';

-- Update RLS policies to handle the new structure
-- The existing RLS policies should work with the new columns since they filter by tenant_id
-- But we need to ensure that parent-child relationships respect tenant isolation

-- Add a function to validate tenant consistency in parent-child relationships
CREATE OR REPLACE FUNCTION validate_obra_tenant_consistency()
RETURNS TRIGGER AS $$
BEGIN
  -- If this is a child obra (has parent_obra_id), ensure parent belongs to same tenant
  IF NEW.parent_obra_id IS NOT NULL THEN
    IF NOT EXISTS (
      SELECT 1 FROM obras 
      WHERE id = NEW.parent_obra_id 
      AND tenant_id = NEW.tenant_id
    ) THEN
      RAISE EXCEPTION 'Parent obra must belong to the same tenant';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to enforce tenant consistency
CREATE TRIGGER trigger_validate_obra_tenant_consistency
  BEFORE INSERT OR UPDATE ON obras
  FOR EACH ROW
  EXECUTE FUNCTION validate_obra_tenant_consistency();