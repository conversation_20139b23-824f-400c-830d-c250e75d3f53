-- ============================================================================
-- EXTEND RLS POLICIES FOR CONDOMÍNIO SUPPORT
-- Date: 2025-01-16
-- Description: Extends RLS policies to properly handle parent-child relationships
--              in condomínio projects while maintaining strict tenant isolation.
-- ============================================================================

-- Helper function to check if user can access a specific obra
CREATE OR REPLACE FUNCTION can_access_obra(obra_id UUID) 
RETURNS BOOLEAN AS $$
DECLARE
  current_tenant_id UUID;
  obra_tenant_id UUID;
BEGIN
  -- Get current user's tenant_id
  current_tenant_id := get_current_tenant_id();
  
  -- If no tenant_id, deny access
  IF current_tenant_id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Get the obra's tenant_id
  SELECT tenant_id INTO obra_tenant_id 
  FROM obras 
  WHERE id = obra_id;
  
  -- Allow access if tenant_id matches
  RETURN (obra_tenant_id = current_tenant_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user can access parent obra of a given obra
CREATE OR REPLACE FUNCTION can_access_parent_obra(child_obra_id UUID) 
RETURNS BOOLEAN AS $$
DECLARE
  parent_id UUID;
BEGIN
  -- Get parent_obra_id
  SELECT parent_obra_id INTO parent_id 
  FROM obras 
  WHERE id = child_obra_id;
  
  -- If no parent, return true (no additional restriction)
  IF parent_id IS NULL THEN
    RETURN TRUE;
  END IF;
  
  -- Check if user can access the parent
  RETURN can_access_obra(parent_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop old conflicting RLS policies
DROP POLICY IF EXISTS "obras_tenant_isolation" ON public.obras;
DROP POLICY IF EXISTS "obras_correct_isolation" ON public.obras;
DROP POLICY IF EXISTS "obras_hybrid_isolation" ON public.obras;
DROP POLICY IF EXISTS "obras_strict_isolation" ON public.obras;

-- CREATE COMPREHENSIVE RLS POLICIES FOR CONDOMÍNIO SUPPORT

-- SELECT Policy: Users can see obras from their tenant with parent-child validation
CREATE POLICY "obras_select_policy" ON public.obras
  FOR SELECT
  USING (
    tenant_id = get_current_tenant_id()
    AND (parent_obra_id IS NULL OR can_access_parent_obra(id))
  );

-- INSERT Policy: Users can insert obras in their tenant with proper parent validation
CREATE POLICY "obras_insert_policy" ON public.obras
  FOR INSERT
  WITH CHECK (
    tenant_id = get_current_tenant_id()
    AND (
      -- If it's a parent obra (condomínio master or única), no additional checks
      parent_obra_id IS NULL
      OR
      -- If it's a child obra (unidade), ensure parent exists and belongs to same tenant
      (parent_obra_id IS NOT NULL AND can_access_obra(parent_obra_id))
    )
  );

-- UPDATE Policy: Users can update obras in their tenant
CREATE POLICY "obras_update_policy" ON public.obras
  FOR UPDATE
  USING (
    tenant_id = get_current_tenant_id()
    AND (parent_obra_id IS NULL OR can_access_parent_obra(id))
  )
  WITH CHECK (
    tenant_id = get_current_tenant_id()
    AND (
      parent_obra_id IS NULL 
      OR can_access_obra(parent_obra_id)
    )
  );

-- DELETE Policy: Users can delete obras in their tenant
CREATE POLICY "obras_delete_policy" ON public.obras
  FOR DELETE
  USING (
    tenant_id = get_current_tenant_id()
    AND (parent_obra_id IS NULL OR can_access_parent_obra(id))
  );

-- PERFORMANCE OPTIMIZATION INDEXES

-- Index to optimize RLS policy performance for parent-child lookups
CREATE INDEX IF NOT EXISTS idx_obras_tenant_parent_lookup 
ON obras(tenant_id, parent_obra_id) 
WHERE parent_obra_id IS NOT NULL;

-- Index for parent obra lookups
CREATE INDEX IF NOT EXISTS idx_obras_parent_tenant_lookup 
ON obras(parent_obra_id, tenant_id) 
WHERE parent_obra_id IS NOT NULL;

-- Index for RLS policy optimization on tenant_id lookups
CREATE INDEX IF NOT EXISTS idx_obras_tenant_id_rls 
ON obras(tenant_id);

-- DOCUMENTATION COMMENTS

COMMENT ON POLICY "obras_select_policy" ON public.obras IS 
'Allows users to select obras from their tenant, including proper parent-child relationship validation for condomínios';

COMMENT ON POLICY "obras_insert_policy" ON public.obras IS 
'Allows users to insert obras in their tenant with validation that parent obras (for unidades) belong to the same tenant';

COMMENT ON POLICY "obras_update_policy" ON public.obras IS 
'Allows users to update obras in their tenant while ensuring parent-child consistency';

COMMENT ON POLICY "obras_delete_policy" ON public.obras IS 
'Allows users to delete obras from their tenant with proper parent-child relationship validation';

COMMENT ON FUNCTION can_access_obra(UUID) IS 
'Helper function for RLS policies to check if current user can access a specific obra based on tenant isolation';

COMMENT ON FUNCTION can_access_parent_obra(UUID) IS 
'Helper function for RLS policies to validate parent-child relationships in condomínio structures';