-- Migration: Add create_condominio_project RPC function
-- Date: 2025-01-17
-- Description: Creates a transactional function to add a new condominium project with its units.

CREATE OR REPLACE FUNCTION public.create_condominio_project(
  condominio_data jsonb,
  unidades_data jsonb[]
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  obra_mae_id uuid;
  unidade jsonb;
  new_obra_mae jsonb;
BEGIN
  -- Insert the master project (CONDOMINIO_MASTER)
  INSERT INTO public.obras (
    nome,
    tenant_id,
    endereco,
    cidade,
    estado,
    cep,
    data_inicio,
    data_prevista_termino,
    orcamento,
    status,
    tipo_projeto,
    -- Include other relevant fields from condominio_data
    usuario_id,
    construtora_id
  )
  VALUES (
    condominio_data->>'nome',
    (condominio_data->>'tenant_id')::uuid,
    condominio_data->>'endereco',
    condominio_data->>'cidade',
    condominio_data->>'estado',
    condominio_data->>'cep',
    (condominio_data->>'data_inicio')::date,
    (condominio_data->>'data_prevista_termino')::date,
    (condominio_data->>'orcamento')::numeric,
    (condominio_data->>'status'),
    'CONDOMINIO_MASTER',
    (condominio_data->>'usuario_id')::uuid,
    (condominio_data->>'construtora_id')::uuid
  )
  RETURNING id INTO obra_mae_id;

  -- Loop through the units and insert them as child projects (UNIDADE_CONDOMINIO)
  FOREACH unidade IN ARRAY unidades_data
  LOOP
    INSERT INTO public.obras (
      nome,
      tenant_id,
      parent_obra_id,
      identificador_unidade,
      tipo_projeto,
      -- Inherit fields from parent or use specific unit data
      endereco,
      cidade,
      estado,
      cep,
      data_inicio,
      data_prevista_termino,
      orcamento,
      status,
      usuario_id,
      construtora_id
    )
    VALUES (
      unidade->>'nome',
      (condominio_data->>'tenant_id')::uuid,
      obra_mae_id,
      unidade->>'identificador_unidade',
      'UNIDADE_CONDOMINIO',
      condominio_data->>'endereco',
      condominio_data->>'cidade',
      condominio_data->>'estado',
      condominio_data->>'cep',
      (condominio_data->>'data_inicio')::date,
      (condominio_data->>'data_prevista_termino')::date,
      (unidade->>'orcamento')::numeric,
      (condominio_data->>'status'),
      (condominio_data->>'usuario_id')::uuid,
      (condominio_data->>'construtora_id')::uuid
    );
  END LOOP;

  -- Return the newly created master project
  SELECT to_jsonb(o) INTO new_obra_mae FROM obras o WHERE o.id = obra_mae_id;
  RETURN new_obra_mae;

EXCEPTION
  WHEN OTHERS THEN
    -- On any error, the transaction will be rolled back automatically.
    RAISE INFO 'Error creating condominium project. Rolling back transaction.';
    RAISE;
END;
$$;

COMMENT ON FUNCTION public.create_condominio_project(jsonb, jsonb[]) IS 'Creates a condominium master project and all its child units in a single transaction.';