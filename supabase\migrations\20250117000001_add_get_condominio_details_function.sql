CREATE OR REPLACE FUNCTION get_condominio_details(p_obra_id uuid)
RETURNS jsonb AS $$
DECLARE
    condominio_master jsonb;
    unidades jsonb;
    result jsonb;
    v_tenant_id uuid;
BEGIN
    -- Obter o tenant_id do usuário atual
    SELECT auth.uid() INTO v_tenant_id;
    
    -- <PERSON><PERSON><PERSON><PERSON> que o projeto solicitado é um CONDOMINIO_MASTER e pertence ao tenant
    IF NOT EXISTS (
        SELECT 1
        FROM obras
        WHERE id = p_obra_id 
        AND tipo_projeto = 'CONDOMINIO_MASTER'
        AND tenant_id = v_tenant_id
    ) THEN
        RAISE EXCEPTION 'Projeto com ID % não encontrado ou não é um condomínio master válido.', p_obra_id;
    END IF;

    -- <PERSON><PERSON> os detalhes da obra "mãe" (master)
    SELECT to_jsonb(o.*)
    INTO condominio_master
    FROM obras o
    WHERE o.id = p_obra_id
    AND o.tenant_id = v_tenant_id;

    -- <PERSON><PERSON> os detalhes das obras "filhas" (unidades)
    SELECT jsonb_agg(to_jsonb(u.*))
    INTO unidades
    FROM obras u
    WHERE u.parent_obra_id = p_obra_id
    AND u.tenant_id = v_tenant_id;

    -- Montar o resultado final
    result := jsonb_build_object(
        'master', condominio_master,
        'unidades', COALESCE(unidades, '[]'::jsonb)
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql;