-- Migration: Fix create_condominio_project RPC function
-- Date: 2025-01-18
-- Description: Fixes field names in the create_condominio_project function to match the actual table structure.

CREATE OR REPLACE FUNCTION public.create_condominio_project(
  condominio_data jsonb,
  unidades_data jsonb[]
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  obra_mae_id uuid;
  unidade jsonb;
  new_obra_mae jsonb;
  unidades_criadas jsonb[] := '{}';
  unidade_criada jsonb;
BEGIN
  -- Insert the master project (CONDOMINIO_MASTER)
  INSERT INTO public.obras (
    nome,
    tenant_id,
    endereco,
    cidade,
    estado,
    cep,
    data_inicio,
    data_prevista_termino,
    orcamento,
    area_total,
    status,
    tipo_projeto,
    tipo_condominio,
    numero_blocos,
    andares_por_bloco,
    unidades_por_andar,
    numero_unidades,
    area_lote,
    area_construida_unidade,
    usuario_id,
    construtora_id
  )
  VALUES (
    condominio_data->>'nome',
    (condominio_data->>'tenant_id')::uuid,
    condominio_data->>'endereco',
    condominio_data->>'cidade',
    condominio_data->>'estado',
    condominio_data->>'cep',
    (condominio_data->>'data_inicio')::date,
    (condominio_data->>'data_prevista_termino')::date,
    (condominio_data->>'orcamento')::numeric,
    (condominio_data->>'area_total')::numeric,
    COALESCE(condominio_data->>'status', 'planejamento'),
    'CONDOMINIO_MASTER'::project_type,
    (condominio_data->>'tipo_condominio')::condominio_type,
    (condominio_data->>'numero_blocos')::integer,
    (condominio_data->>'andares_por_bloco')::integer,
    (condominio_data->>'unidades_por_andar')::integer,
    (condominio_data->>'numero_unidades')::integer,
    (condominio_data->>'area_lote')::numeric,
    (condominio_data->>'area_construida_unidade')::numeric,
    (condominio_data->>'usuario_id')::uuid,
    (condominio_data->>'construtora_id')::uuid
  )
  RETURNING id INTO obra_mae_id;

  -- Loop through the units and insert them as child projects (UNIDADE_CONDOMINIO)
  FOREACH unidade IN ARRAY unidades_data
  LOOP
    INSERT INTO public.obras (
      nome,
      tenant_id,
      parent_obra_id,
      identificador_unidade,
      tipo_projeto,
      endereco,
      cidade,
      estado,
      cep,
      data_inicio,
      data_prevista_termino,
      orcamento,
      area_total,
      status,
      usuario_id,
      construtora_id
    )
    VALUES (
      unidade->>'nome',
      (condominio_data->>'tenant_id')::uuid,
      obra_mae_id,
      unidade->>'identificador_unidade',
      'UNIDADE_CONDOMINIO'::project_type,
      condominio_data->>'endereco',
      condominio_data->>'cidade',
      condominio_data->>'estado',
      condominio_data->>'cep',
      (condominio_data->>'data_inicio')::date,
      (condominio_data->>'data_prevista_termino')::date,
      (unidade->>'orcamento')::numeric,
      (unidade->>'area_total')::numeric,
      COALESCE(condominio_data->>'status', 'planejamento'),
      (condominio_data->>'usuario_id')::uuid,
      (condominio_data->>'construtora_id')::uuid
    )
    RETURNING to_jsonb(obras.*) INTO unidade_criada;
    
    -- Add to array of created units
    unidades_criadas := unidades_criadas || unidade_criada;
  END LOOP;

  -- Return the newly created master project with units
  SELECT jsonb_build_object(
    'obra_mae', to_jsonb(o.*),
    'unidades', unidades_criadas,
    'total_unidades', array_length(unidades_data, 1)
  ) INTO new_obra_mae 
  FROM obras o 
  WHERE o.id = obra_mae_id;
  
  RETURN new_obra_mae;

EXCEPTION
  WHEN OTHERS THEN
    -- On any error, the transaction will be rolled back automatically.
    RAISE INFO 'Error creating condominium project: %', SQLERRM;
    RAISE;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.create_condominio_project(jsonb, jsonb[]) TO authenticated;

COMMENT ON FUNCTION public.create_condominio_project(jsonb, jsonb[]) IS 'Creates a condominium master project and all its child units in a single transaction. Fixed field names to match table structure.';
