-- Migration: Add get_unidades_paginadas RPC function
-- Date: 2025-01-18
-- Description: Adds server-side pagination function for condominio units

CREATE OR REPLACE FUNCTION public.get_unidades_paginadas(
  p_condominio_id uuid,
  p_page integer DEFAULT 1,
  p_page_size integer DEFAULT 50,
  p_search text DEFAULT NULL,
  p_status_filter text DEFAULT NULL
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_tenant_id uuid;
  v_offset integer;
  v_total_count integer;
  v_total_pages integer;
  v_unidades jsonb;
  v_result jsonb;
BEGIN
  -- Get tenant_id from current user
  SELECT auth.uid() INTO v_tenant_id;
  
  IF v_tenant_id IS NULL THEN
    RAISE EXCEPTION 'User not authenticated';
  END IF;

  -- Validate inputs
  IF p_condominio_id IS NULL THEN
    RAISE EXCEPTION 'condominio_id is required';
  END IF;

  IF p_page < 1 THEN
    p_page := 1;
  END IF;

  IF p_page_size < 1 OR p_page_size > 100 THEN
    p_page_size := 50;
  END IF;

  -- Calculate offset
  v_offset := (p_page - 1) * p_page_size;

  -- Build base query for count
  WITH filtered_unidades AS (
    SELECT *
    FROM obras
    WHERE parent_obra_id = p_condominio_id
      AND tipo_projeto = 'UNIDADE_CONDOMINIO'
      AND tenant_id = v_tenant_id
      AND (
        p_search IS NULL 
        OR p_search = '' 
        OR nome ILIKE '%' || p_search || '%'
        OR identificador_unidade ILIKE '%' || p_search || '%'
      )
      AND (
        p_status_filter IS NULL 
        OR p_status_filter = '' 
        OR status = p_status_filter
      )
  )
  SELECT COUNT(*) INTO v_total_count FROM filtered_unidades;

  -- Calculate total pages
  v_total_pages := CEIL(v_total_count::numeric / p_page_size::numeric)::integer;

  -- Get paginated data
  WITH filtered_unidades AS (
    SELECT *
    FROM obras
    WHERE parent_obra_id = p_condominio_id
      AND tipo_projeto = 'UNIDADE_CONDOMINIO'
      AND tenant_id = v_tenant_id
      AND (
        p_search IS NULL 
        OR p_search = '' 
        OR nome ILIKE '%' || p_search || '%'
        OR identificador_unidade ILIKE '%' || p_search || '%'
      )
      AND (
        p_status_filter IS NULL 
        OR p_status_filter = '' 
        OR status = p_status_filter
      )
    ORDER BY identificador_unidade ASC
    LIMIT p_page_size
    OFFSET v_offset
  )
  SELECT COALESCE(jsonb_agg(to_jsonb(filtered_unidades.*)), '[]'::jsonb)
  INTO v_unidades
  FROM filtered_unidades;

  -- Build response
  v_result := jsonb_build_object(
    'data', v_unidades,
    'pagination', jsonb_build_object(
      'page', p_page,
      'pageSize', p_page_size,
      'total', v_total_count,
      'totalPages', v_total_pages,
      'hasNextPage', p_page < v_total_pages,
      'hasPreviousPage', p_page > 1
    )
  );

  RETURN v_result;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_unidades_paginadas(uuid, integer, integer, text, text) TO authenticated;

-- Add comment
COMMENT ON FUNCTION public.get_unidades_paginadas IS 'Returns paginated list of condominio units with search and filter capabilities';
