-- Atualizar função de dashboard do condomínio para usar cálculo híbrido de progresso
-- Esta migração implementa o novo sistema de progresso que considera:
-- 1. Status da unidade (40% do peso)
-- 2. Progresso temporal baseado em datas (30% do peso) 
-- 3. Progresso financeiro do master (30% do peso)

CREATE OR REPLACE FUNCTION public.get_condominio_dashboard(p_obra_id uuid)
RETURNS jsonb AS $$
DECLARE
    result jsonb;
    v_tenant_id uuid;
    total_gasto_master numeric := 0;
    orcamento_master numeric := 0;
    progresso_financeiro_master numeric := 0;
BEGIN
    -- Obter o tenant_id do usuário atual
    SELECT auth.uid() INTO v_tenant_id;
    
    -- Garantir que o projeto solicitado é um CONDOMINIO_MASTER e pertence ao tenant
    IF NOT EXISTS (
        SELECT 1
        FROM obras
        WHERE id = p_obra_id 
        AND tipo_projeto = 'CONDOMINIO_MASTER'
        AND tenant_id = v_tenant_id
    ) THEN
        RAISE EXCEPTION 'Projeto com ID % não encontrado ou não é um condomínio master válido.', p_obra_id;
    END IF;

    -- Buscar orçamento do master e calcular progresso financeiro
    SELECT COALESCE(orcamento, 0) INTO orcamento_master
    FROM obras 
    WHERE id = p_obra_id AND tenant_id = v_tenant_id;

    -- Calcular total gasto no master
    SELECT COALESCE(SUM(custo), 0) INTO total_gasto_master
    FROM despesas 
    WHERE obra_id = p_obra_id AND tenant_id = v_tenant_id;

    -- Calcular progresso financeiro do master
    progresso_financeiro_master := CASE 
        WHEN orcamento_master > 0 THEN (total_gasto_master / orcamento_master) * 100
        ELSE 0 
    END;

    -- Função para calcular progresso híbrido baseado em status, datas e despesas do master
    WITH progresso_calculado AS (
        SELECT 
            o.*,
            -- 1. Progresso por status (peso 40%)
            CASE 
                WHEN o.status = 'concluida' THEN 100
                WHEN o.status = 'em_andamento' THEN 60
                WHEN o.status = 'pausada' THEN 30
                WHEN o.status = 'planejamento' THEN 10
                ELSE 0
            END as progresso_status,
            
            -- 2. Progresso por datas (peso 30%)
            CASE 
                WHEN o.data_inicio IS NULL THEN 0
                WHEN o.data_inicio > CURRENT_DATE THEN 5
                WHEN o.data_prevista_termino IS NOT NULL AND o.data_prevista_termino < CURRENT_DATE THEN 100
                WHEN o.data_inicio IS NOT NULL AND o.data_prevista_termino IS NOT NULL THEN
                    GREATEST(0, LEAST(100, 
                        ((EXTRACT(EPOCH FROM CURRENT_DATE) - EXTRACT(EPOCH FROM o.data_inicio)) / 
                         (EXTRACT(EPOCH FROM o.data_prevista_termino) - EXTRACT(EPOCH FROM o.data_inicio))) * 100
                    ))
                ELSE 50
            END as progresso_datas,
            
            -- 3. Progresso financeiro do master (peso 30%)
            LEAST(progresso_financeiro_master, 100) as progresso_financeiro,
            
            -- Calcular progresso híbrido ponderado
            ROUND(
                (CASE 
                    WHEN o.status = 'concluida' THEN 100
                    WHEN o.status = 'em_andamento' THEN 60
                    WHEN o.status = 'pausada' THEN 30
                    WHEN o.status = 'planejamento' THEN 10
                    ELSE 0
                END * 0.4) +
                
                (CASE 
                    WHEN o.data_inicio IS NULL THEN 0
                    WHEN o.data_inicio > CURRENT_DATE THEN 5
                    WHEN o.data_prevista_termino IS NOT NULL AND o.data_prevista_termino < CURRENT_DATE THEN 100
                    WHEN o.data_inicio IS NOT NULL AND o.data_prevista_termino IS NOT NULL THEN
                        GREATEST(0, LEAST(100, 
                            ((EXTRACT(EPOCH FROM CURRENT_DATE) - EXTRACT(EPOCH FROM o.data_inicio)) / 
                             (EXTRACT(EPOCH FROM o.data_prevista_termino) - EXTRACT(EPOCH FROM o.data_inicio))) * 100
                        ))
                    ELSE 50
                END * 0.3) +
                
                (LEAST(progresso_financeiro_master, 100) * 0.3)
            ) as progresso_hibrido
            
        FROM public.obras o
        WHERE o.parent_obra_id = p_obra_id
        AND o.tipo_projeto = 'UNIDADE_CONDOMINIO'
        AND o.tenant_id = v_tenant_id
    )
    
    SELECT jsonb_build_object(
        'estatisticas', jsonb_build_object(
            'total_unidades', COUNT(*),
            'progresso_medio', COALESCE(AVG(progresso_hibrido), 0),
            'custo_total', COALESCE(SUM(COALESCE(orcamento, 0)), 0),
            'unidades_concluidas', COUNT(*) FILTER (WHERE progresso_hibrido >= 95)
        ),
        'unidades', COALESCE(jsonb_agg(
            jsonb_build_object(
                'id', id,
                'identificador_unidade', identificador_unidade,
                'nome', nome,
                'progresso', progresso_hibrido
            )
        ), '[]'::jsonb)
    ) INTO result
    FROM progresso_calculado;

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Garantir permissões
GRANT EXECUTE ON FUNCTION public.get_condominio_dashboard(uuid) TO authenticated;

-- Comentário da migração
COMMENT ON FUNCTION public.get_condominio_dashboard(uuid) IS 
'Função atualizada para calcular progresso híbrido das unidades considerando status (40%), datas (30%) e progresso financeiro do master (30%)';
