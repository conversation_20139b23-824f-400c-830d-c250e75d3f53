-- Migração para corrigir a tabela leads e adicionar campos necessários
-- Data: 2025-01-30
-- Descrição: Adiciona campos necessários para a Edge Function lead-capture funcionar

-- Adicionar campos que estão faltando na tabela leads
ALTER TABLE leads 
ADD COLUMN IF NOT EXISTS tipo_empresa VARCHAR(50),
ADD COLUMN IF NOT EXISTS porte_empresa VARCHAR(20),
ADD COLUMN IF NOT EXISTS numero_obras_mes INTEGER,
ADD COLUMN IF NOT EXISTS principal_desafio TEXT,
ADD COLUMN IF NOT EXISTS como_conheceu VARCHAR(100),
ADD COLUMN IF NOT EXISTS interesse_nivel VARCHAR(20) DEFAULT 'medio',
ADD COLUMN IF NOT EXISTS orcamento_mensal DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS previsao_inicio VARCHAR(50),
ADD COLUMN IF NOT EXISTS observacoes TEXT,
ADD COLUMN IF NOT EXISTS utm_source VARCHAR(100),
ADD COLUMN IF NOT EXISTS utm_medium VARCHAR(100),
ADD COLUMN IF NOT EXISTS utm_campaign VARCHAR(100),
ADD COLUMN IF NOT EXISTS prioridade VARCHAR(20) DEFAULT 'normal',
ADD COLUMN IF NOT EXISTS ip_address VARCHAR(45),
ADD COLUMN IF NOT EXISTS user_agent TEXT,
ADD COLUMN IF NOT EXISTS id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Remover a constraint de chave primária atual se existir
ALTER TABLE leads DROP CONSTRAINT IF EXISTS leads_pkey;

-- Adicionar nova chave primária no campo id
ALTER TABLE leads ADD CONSTRAINT leads_pkey PRIMARY KEY (id);

-- Criar índice único no email para evitar duplicatas
CREATE UNIQUE INDEX IF NOT EXISTS leads_email_unique ON leads(email);

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS leads_prioridade_idx ON leads(prioridade);
CREATE INDEX IF NOT EXISTS leads_interesse_nivel_idx ON leads(interesse_nivel);
CREATE INDEX IF NOT EXISTS leads_origem_idx ON leads(origem);
CREATE INDEX IF NOT EXISTS leads_created_at_idx ON leads(created_at);

-- Atualizar registros existentes que não têm ID
UPDATE leads SET id = gen_random_uuid() WHERE id IS NULL;

-- Atualizar registros existentes que não têm timestamps
UPDATE leads SET 
  created_at = COALESCE(data_lead, NOW()),
  updated_at = COALESCE(data_lead, NOW())
WHERE created_at IS NULL OR updated_at IS NULL;

-- Criar trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_leads_updated_at ON leads;
CREATE TRIGGER update_leads_updated_at
    BEFORE UPDATE ON leads
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Comentários para documentação
COMMENT ON TABLE leads IS 'Tabela de leads capturados pela landing page e chatbot';
COMMENT ON COLUMN leads.interesse_nivel IS 'Nível de interesse: baixo, medio, alto, muito_alto';
COMMENT ON COLUMN leads.prioridade IS 'Prioridade do lead: baixa, normal, alta, urgente';
COMMENT ON COLUMN leads.tipo_empresa IS 'Tipo da empresa: construtora, engenharia, arquitetura, individual, outro';
COMMENT ON COLUMN leads.porte_empresa IS 'Porte da empresa: micro, pequena, media, grande';