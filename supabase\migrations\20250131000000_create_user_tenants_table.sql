-- Migração para criar tabela user_tenants
-- Data: 2025-01-31
-- Descrição: Cria tabela para relacionar usuários com tenants (multi-tenancy)

-- Criar tabela user_tenants
CREATE TABLE IF NOT EXISTS public.user_tenants (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  role TEXT DEFAULT 'user' CHECK (role IN ('admin', 'user', 'viewer')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- <PERSON><PERSON><PERSON><PERSON> que um usuário não tenha múltiplas entradas para o mesmo tenant
  UNIQUE(user_id, tenant_id)
);

-- Comentários
COMMENT ON TABLE public.user_tenants IS 'Relaciona usuários com tenants para isolamento multi-tenant';
COMMENT ON COLUMN public.user_tenants.user_id IS 'ID do usuário';
COMMENT ON COLUMN public.user_tenants.tenant_id IS 'ID do tenant';
COMMENT ON COLUMN public.user_tenants.role IS 'Papel do usuário no tenant';

-- Índices
CREATE INDEX idx_user_tenants_user_id ON public.user_tenants(user_id);
CREATE INDEX idx_user_tenants_tenant_id ON public.user_tenants(tenant_id);

-- Habilitar RLS
ALTER TABLE public.user_tenants ENABLE ROW LEVEL SECURITY;

-- Políticas RLS
CREATE POLICY "Usuários podem ver seus próprios tenants" 
ON public.user_tenants FOR SELECT 
TO authenticated 
USING (user_id = auth.uid());

CREATE POLICY "Usuários podem inserir seus próprios tenants" 
ON public.user_tenants FOR INSERT 
TO authenticated 
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Usuários podem atualizar seus próprios tenants" 
ON public.user_tenants FOR UPDATE 
TO authenticated 
USING (user_id = auth.uid()) 
WITH CHECK (user_id = auth.uid());

-- Trigger para atualizar updated_at
CREATE OR REPLACE FUNCTION public.handle_user_tenants_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_tenants_updated_at
  BEFORE UPDATE ON public.user_tenants
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_user_tenants_updated_at();

-- Inserir dados de exemplo para os usuários existentes
-- Primeiro, vamos buscar um tenant_id existente das obras
DO $$
DECLARE
  sample_tenant_id UUID;
  user_record RECORD;
BEGIN
  -- Buscar um tenant_id existente
  SELECT tenant_id INTO sample_tenant_id 
  FROM public.obras 
  WHERE tenant_id IS NOT NULL 
  LIMIT 1;
  
  -- Se encontrou um tenant_id, inserir para os usuários existentes
  IF sample_tenant_id IS NOT NULL THEN
    FOR user_record IN 
      SELECT id FROM auth.users 
      WHERE email IN ('<EMAIL>', '<EMAIL>')
    LOOP
      INSERT INTO public.user_tenants (user_id, tenant_id, role)
      VALUES (user_record.id, sample_tenant_id, 'admin')
      ON CONFLICT (user_id, tenant_id) DO NOTHING;
    END LOOP;
  END IF;
END $$;