-- <PERSON><PERSON><PERSON> que a tabela seja recriada do zero para evitar inconsistências
DROP TABLE IF EXISTS subscriptions CASCADE;

-- Criar tabela de assinaturas para controle de planos premium
CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  plan_type TEXT NOT NULL CHECK (plan_type IN ('free', 'basic', 'pro', 'enterprise')),
  status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'past_due', 'trialing')),
  current_period_start TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  current_period_end TIMESTAMPTZ NOT NULL DEFAULT NOW() + INTERVAL '1 month',
  stripe_subscription_id TEXT,
  stripe_customer_id TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  
  -- <PERSON><PERSON><PERSON><PERSON> que cada usuário tenha apenas uma assinatura ativa
  UNIQUE(user_id, status) DEFERRABLE INITIALLY DEFERRED
);

-- Trigger para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_subscriptions_updated_at 
  BEFORE UPDATE ON subscriptions 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- RLS policies
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Usuários só podem ver suas próprias assinaturas
CREATE POLICY "Users can view own subscriptions" ON subscriptions
  FOR SELECT USING (auth.uid() = user_id);

-- Usuários só podem inserir suas próprias assinaturas
CREATE POLICY "Users can insert own subscriptions" ON subscriptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Usuários só podem atualizar suas próprias assinaturas
CREATE POLICY "Users can update own subscriptions" ON subscriptions
  FOR UPDATE USING (auth.uid() = user_id);

-- Comentários
COMMENT ON TABLE subscriptions IS 'Tabela para gerenciar assinaturas e planos dos usuários';
COMMENT ON COLUMN subscriptions.plan_type IS 'Tipo do plano: free, basic, pro, enterprise';
COMMENT ON COLUMN subscriptions.status IS 'Status da assinatura: active, canceled, past_due, trialing';
COMMENT ON COLUMN subscriptions.stripe_subscription_id IS 'ID da assinatura no Stripe';
COMMENT ON COLUMN subscriptions.stripe_customer_id IS 'ID do cliente no Stripe';