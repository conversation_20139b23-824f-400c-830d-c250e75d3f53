-- Migração para o Módulo de Licitações - Fase 1
-- Criação das tabelas principais: licitacoes, licitacoes_favoritas, licitacoes_analises_ia

-- Tabela principal de licitações
CREATE TABLE IF NOT EXISTS licitacoes (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  numero_licitacao text NOT NULL,
  objeto text NOT NULL,
  orgao text NOT NULL,
  modalidade text,
  valor_estimado numeric,
  data_abertura timestamp with time zone,
  data_limite_entrega timestamp with time zone,
  situacao text DEFAULT 'em_andamento',
  link_edital text,
  link_portal text,
  endereco_entrega text,
  observacoes text,
  fonte text, -- 'manual', 'api_gov', etc.
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Tabela de licitações favoritas pelos usuários
CREATE TABLE IF NOT EXISTS licitacoes_favoritas (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  licitacao_id uuid REFERENCES licitacoes(id) ON DELETE CASCADE NOT NULL,
  usuario_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  tenant_id uuid NOT NULL,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(licitacao_id, usuario_id, tenant_id)
);

-- Tabela para cache das análises de IA dos editais
CREATE TABLE IF NOT EXISTS licitacoes_analises_ia (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  licitacao_id uuid REFERENCES licitacoes(id) ON DELETE CASCADE NOT NULL,
  tenant_id uuid NOT NULL,
  resumo_objeto text,
  requisitos_principais jsonb,
  documentos_necessarios text[],
  prazo_execucao text,
  valor_estimado_ia text,
  observacoes_ia text,
  nivel_complexidade text, -- 'baixo', 'medio', 'alto'
  analise_compatibilidade jsonb, -- Para fase 2
  processed_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  UNIQUE(licitacao_id, tenant_id)
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_licitacoes_numero ON licitacoes(numero_licitacao);
CREATE INDEX IF NOT EXISTS idx_licitacoes_orgao ON licitacoes(orgao);
CREATE INDEX IF NOT EXISTS idx_licitacoes_modalidade ON licitacoes(modalidade);
CREATE INDEX IF NOT EXISTS idx_licitacoes_situacao ON licitacoes(situacao);
CREATE INDEX IF NOT EXISTS idx_licitacoes_data_abertura ON licitacoes(data_abertura);
CREATE INDEX IF NOT EXISTS idx_licitacoes_valor_estimado ON licitacoes(valor_estimado);

CREATE INDEX IF NOT EXISTS idx_licitacoes_favoritas_usuario ON licitacoes_favoritas(usuario_id, tenant_id);
CREATE INDEX IF NOT EXISTS idx_licitacoes_favoritas_licitacao ON licitacoes_favoritas(licitacao_id);

CREATE INDEX IF NOT EXISTS idx_licitacoes_analises_ia_licitacao ON licitacoes_analises_ia(licitacao_id);
CREATE INDEX IF NOT EXISTS idx_licitacoes_analises_ia_tenant ON licitacoes_analises_ia(tenant_id);

-- RLS (Row Level Security) - Obrigatório conforme CLAUDE.md
ALTER TABLE licitacoes ENABLE ROW LEVEL SECURITY;
ALTER TABLE licitacoes_favoritas ENABLE ROW LEVEL SECURITY;
ALTER TABLE licitacoes_analises_ia ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para licitacoes (dados públicos, mas criação restrita)
CREATE POLICY "Licitacoes são visíveis para todos usuários autenticados" 
ON licitacoes FOR SELECT 
TO authenticated 
USING (true);

CREATE POLICY "Apenas admins podem inserir licitacoes" 
ON licitacoes FOR INSERT 
TO authenticated 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid() 
    AND auth.users.raw_user_meta_data->>'role' = 'admin'
  )
);

CREATE POLICY "Apenas admins podem atualizar licitacoes" 
ON licitacoes FOR UPDATE 
TO authenticated 
USING (
  EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid() 
    AND auth.users.raw_user_meta_data->>'role' = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM auth.users 
    WHERE auth.users.id = auth.uid() 
    AND auth.users.raw_user_meta_data->>'role' = 'admin'
  )
);

-- Políticas RLS para licitacoes_favoritas (isolamento por tenant)
CREATE POLICY "Usuários podem ver apenas seus favoritos" 
ON licitacoes_favoritas FOR SELECT 
TO authenticated 
USING (
  usuario_id = auth.uid() 
  AND tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid
);

CREATE POLICY "Usuários podem inserir seus próprios favoritos"
ON licitacoes_favoritas FOR INSERT
TO authenticated
WITH CHECK (
    usuario_id = auth.uid()
    AND tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid
);

CREATE POLICY "Usuários podem atualizar seus próprios favoritos"
ON licitacoes_favoritas FOR UPDATE
TO authenticated
USING (
    usuario_id = auth.uid()
    AND tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid
)
WITH CHECK (
    usuario_id = auth.uid()
    AND tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid
);

CREATE POLICY "Usuários podem apagar seus próprios favoritos"
ON licitacoes_favoritas FOR DELETE
TO authenticated
USING (
    usuario_id = auth.uid()
    AND tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid
);

-- Políticas RLS para licitacoes_analises_ia (isolamento por tenant)
CREATE POLICY "Análises de IA visíveis apenas para o tenant" 
ON licitacoes_analises_ia FOR SELECT 
TO authenticated 
USING (tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid);

CREATE POLICY "Apenas edge functions podem gerenciar análises" 
ON licitacoes_analises_ia FOR ALL 
TO service_role 
USING (true)
WITH CHECK (true);

-- Triggers para updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_licitacoes_updated_at 
BEFORE UPDATE ON licitacoes 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comentários para documentação
COMMENT ON TABLE licitacoes IS 'Tabela principal de licitações públicas para construção civil';
COMMENT ON TABLE licitacoes_favoritas IS 'Licitações marcadas como favoritas pelos usuários';
COMMENT ON TABLE licitacoes_analises_ia IS 'Cache das análises de editais processadas pela IA';