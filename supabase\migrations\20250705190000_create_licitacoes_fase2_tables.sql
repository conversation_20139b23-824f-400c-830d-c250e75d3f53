-- Migração para o Módulo de Licitações - Fase 2
-- Criação das tabelas: construtora_perfil, licitacao_tarefas

-- Tabela para perfil da construtora (usado na análise de compatibilidade)
CREATE TABLE IF NOT EXISTS construtora_perfil (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id uuid NOT NULL,
  especialidades text[] DEFAULT '{}',
  capital_social numeric,
  documentos_padrao jsonb DEFAULT '{}',
  certidoes jsonb DEFAULT '{}',
  experiencias_anteriores jsonb DEFAULT '{}',
  equipe_tecnica jsonb DEFAULT '{}',
  capacidade_operacional jsonb DEFAULT '{}',
  observacoes text,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Tabela para checklist de tarefas por licitação
CREATE TABLE IF NOT EXISTS licitacao_tarefas (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  licitacao_id uuid REFERENCES licitacoes(id) ON DELETE CASCADE NOT NULL,
  tenant_id uuid NOT NULL,
  descricao text NOT NULL,
  tipo text DEFAULT 'padrao', -- 'padrao', 'ia', 'personalizada'
  prioridade text DEFAULT 'media', -- 'baixa', 'media', 'alta', 'critica'
  prazo_estimado integer, -- dias estimados para conclusão
  concluida boolean DEFAULT false,
  data_conclusao timestamp with time zone,
  observacoes text,
  ordem integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_construtora_perfil_tenant ON construtora_perfil(tenant_id);
CREATE INDEX IF NOT EXISTS idx_construtora_perfil_especialidades ON construtora_perfil USING GIN(especialidades);

CREATE INDEX IF NOT EXISTS idx_licitacao_tarefas_licitacao ON licitacao_tarefas(licitacao_id);
CREATE INDEX IF NOT EXISTS idx_licitacao_tarefas_tenant ON licitacao_tarefas(tenant_id);
CREATE INDEX IF NOT EXISTS idx_licitacao_tarefas_concluida ON licitacao_tarefas(concluida);
CREATE INDEX IF NOT EXISTS idx_licitacao_tarefas_prioridade ON licitacao_tarefas(prioridade);

-- RLS (Row Level Security)
ALTER TABLE construtora_perfil ENABLE ROW LEVEL SECURITY;
ALTER TABLE licitacao_tarefas ENABLE ROW LEVEL SECURITY;

-- Políticas RLS para construtora_perfil (isolamento por tenant)
CREATE POLICY "Perfil da construtora visível apenas para o tenant" 
ON construtora_perfil FOR SELECT 
TO authenticated 
USING (tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid);

CREATE POLICY "Usuários podem inserir perfil da sua construtora" 
ON construtora_perfil FOR INSERT 
TO authenticated 
WITH CHECK (tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid);

CREATE POLICY "Usuários podem atualizar perfil da sua construtora" 
ON construtora_perfil FOR UPDATE 
TO authenticated 
USING (tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid)
WITH CHECK (tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid);

CREATE POLICY "Usuários podem deletar perfil da sua construtora" 
ON construtora_perfil FOR DELETE 
TO authenticated 
USING (tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid);

-- Políticas RLS para licitacao_tarefas (isolamento por tenant)
CREATE POLICY "Tarefas de licitação visíveis apenas para o tenant" 
ON licitacao_tarefas FOR SELECT 
TO authenticated 
USING (tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid);

CREATE POLICY "Usuários podem inserir tarefas da sua empresa" 
ON licitacao_tarefas FOR INSERT 
TO authenticated 
WITH CHECK (tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid);

CREATE POLICY "Usuários podem atualizar tarefas da sua empresa" 
ON licitacao_tarefas FOR UPDATE 
TO authenticated 
USING (tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid)
WITH CHECK (tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid);

CREATE POLICY "Usuários podem deletar tarefas da sua empresa" 
ON licitacao_tarefas FOR DELETE 
TO authenticated 
USING (tenant_id = (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::uuid);

-- Edge functions também podem gerenciar tarefas
CREATE POLICY "Edge functions podem gerenciar tarefas" 
ON licitacao_tarefas FOR ALL 
TO service_role 
USING (true)
WITH CHECK (true);

-- Triggers para updated_at
CREATE TRIGGER update_construtora_perfil_updated_at 
BEFORE UPDATE ON construtora_perfil 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_licitacao_tarefas_updated_at 
BEFORE UPDATE ON licitacao_tarefas 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger para atualizar data_conclusao quando tarefa é marcada como concluída
CREATE OR REPLACE FUNCTION update_data_conclusao_tarefa()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.concluida = true AND OLD.concluida = false THEN
        NEW.data_conclusao = timezone('utc'::text, now());
    ELSIF NEW.concluida = false AND OLD.concluida = true THEN
        NEW.data_conclusao = NULL;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_data_conclusao_licitacao_tarefas
BEFORE UPDATE ON licitacao_tarefas
FOR EACH ROW EXECUTE FUNCTION update_data_conclusao_tarefa();

-- Função para buscar perfil da construtora ou criar um vazio se não existir
CREATE OR REPLACE FUNCTION get_or_create_construtora_perfil(tenant_uuid uuid)
RETURNS construtora_perfil AS $$
DECLARE
    perfil construtora_perfil;
BEGIN
    SELECT * INTO perfil 
    FROM construtora_perfil 
    WHERE tenant_id = tenant_uuid 
    LIMIT 1;
    
    IF NOT FOUND THEN
        INSERT INTO construtora_perfil (tenant_id) 
        VALUES (tenant_uuid) 
        RETURNING * INTO perfil;
    END IF;
    
    RETURN perfil;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comentários para documentação
COMMENT ON TABLE construtora_perfil IS 'Perfil da construtora para análise de compatibilidade com licitações';
COMMENT ON TABLE licitacao_tarefas IS 'Checklist de tarefas para preparação de licitações';

COMMENT ON COLUMN construtora_perfil.especialidades IS 'Array de especialidades da construtora (ex: edificacoes, saneamento, urbanizacao)';
COMMENT ON COLUMN construtora_perfil.documentos_padrao IS 'JSON com links/status de documentos padrão (certidões, balanços, etc)';
COMMENT ON COLUMN construtora_perfil.certidoes IS 'JSON com informações sobre certidões da empresa';
COMMENT ON COLUMN construtora_perfil.experiencias_anteriores IS 'JSON com histórico de obras similares executadas';
COMMENT ON COLUMN construtora_perfil.equipe_tecnica IS 'JSON com informações da equipe técnica disponível';
COMMENT ON COLUMN construtora_perfil.capacidade_operacional IS 'JSON com informações sobre capacidade operacional';

COMMENT ON COLUMN licitacao_tarefas.tipo IS 'Origem da tarefa: padrao (sistema), ia (sugerida pela IA), personalizada (criada pelo usuário)';
COMMENT ON COLUMN licitacao_tarefas.prioridade IS 'Prioridade da tarefa: baixa, media, alta, critica';
COMMENT ON COLUMN licitacao_tarefas.prazo_estimado IS 'Número de dias estimados para conclusão da tarefa';
COMMENT ON COLUMN licitacao_tarefas.ordem IS 'Ordem de exibição/execução das tarefas';