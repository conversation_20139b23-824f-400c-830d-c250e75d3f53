CREATE OR REPLACE VIEW public.v_obras_lucratividade AS
SELECT
    o.id,
    o.nome,
    o.orcamento,
    o.valor_venda,
    o.status_venda,
    (SELECT SUM(d.custo) FROM public.despesas d WHERE d.obra_id = o.id) AS custo_total_real,
    (
        o.valor_venda - (SELECT SUM(d.custo) FROM public.despesas d WHERE d.obra_id = o.id)
    ) AS lucro_bruto,
    (
        o.valor_venda - 
        COALESCE((SELECT SUM(d.custo) FROM public.despesas d WHERE d.obra_id = o.id), 0) - 
        COALESCE((o.valor_venda * (o.comissao_corretor_percentual / 100)), 0) - 
        COALESCE(o.outras_despesas_venda, 0)
    ) AS lucro_liquido,
    CASE 
        WHEN o.valor_venda > 0 THEN
            (
                (
                    o.valor_venda - COALESCE((SELECT SUM(d.custo) FROM public.despesas d WHERE d.obra_id = o.id), 0)
                ) / o.valor_venda
            ) * 100
        ELSE 0 
    END AS margem_lucro_percentual,
    CASE
        WHEN COALESCE((SELECT SUM(d.custo) FROM public.despesas d WHERE d.obra_id = o.id), 0) > 0 THEN
            (
                (
                    o.valor_venda - COALESCE((SELECT SUM(d.custo) FROM public.despesas d WHERE d.obra_id = o.id), 0)
                ) / COALESCE((SELECT SUM(d.custo) FROM public.despesas d WHERE d.obra_id = o.id), 1) -- Evita divisão por zero se custo for 0
            ) * 100
        ELSE 0
    END AS roi_percentual -- Retorno Sobre o Investimento
FROM
    public.obras o;

COMMENT ON VIEW public.v_obras_lucratividade IS 'Visão que calcula a lucratividade de cada obra, comparando o valor de venda com os custos totais reais.';
