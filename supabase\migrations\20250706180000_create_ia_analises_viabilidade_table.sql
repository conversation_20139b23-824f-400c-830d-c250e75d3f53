-- Criação da tabela para armazenar análises de viabilidade de venda
CREATE TABLE public.ia_analises_viabilidade (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    obra_id UUID NOT NULL REFERENCES public.obras(id) ON DELETE CASCADE,
    analise_conteudo TEXT NOT NULL,
    dados_utilizados JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Comentários nas colunas
COMMENT ON TABLE public.ia_analises_viabilidade IS 'Armazena análises de viabilidade de venda geradas pela IA';
COMMENT ON COLUMN public.ia_analises_viabilidade.obra_id IS 'Referência à obra analisada';
COMMENT ON COLUMN public.ia_analises_viabilidade.analise_conteudo IS 'Conteúdo da análise gerada pela IA';
COMMENT ON COLUMN public.ia_analises_viabilidade.dados_utilizados IS 'Dados financeiros utilizados na análise';

-- Índices para performance
CREATE INDEX idx_ia_analises_viabilidade_obra_id ON public.ia_analises_viabilidade(obra_id);
CREATE INDEX idx_ia_analises_viabilidade_created_at ON public.ia_analises_viabilidade(created_at);

-- RLS (Row Level Security)
ALTER TABLE public.ia_analises_viabilidade ENABLE ROW LEVEL SECURITY;

-- Política para usuários autenticados verem apenas suas análises
CREATE POLICY "Users can view their own viability analyses" ON public.ia_analises_viabilidade
    FOR SELECT USING (
        obra_id IN (
            SELECT o.id FROM public.obras o 
            WHERE o.construtora_id IN (
                SELECT c.id FROM public.construtoras c 
                WHERE c.tenant_id = auth.jwt() ->> 'tenant_id'
            )
        )
    );

-- Política para usuários autenticados criarem análises
CREATE POLICY "Users can create viability analyses for their works" ON public.ia_analises_viabilidade
    FOR INSERT WITH CHECK (
        obra_id IN (
            SELECT o.id FROM public.obras o 
            WHERE o.construtora_id IN (
                SELECT c.id FROM public.construtoras c 
                WHERE c.tenant_id = auth.jwt() ->> 'tenant_id'
            )
        )
    );

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_ia_analises_viabilidade_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para updated_at
CREATE TRIGGER update_ia_analises_viabilidade_updated_at
    BEFORE UPDATE ON public.ia_analises_viabilidade
    FOR EACH ROW
    EXECUTE FUNCTION update_ia_analises_viabilidade_updated_at();