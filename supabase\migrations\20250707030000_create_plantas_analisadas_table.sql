-- Criar tabela plantas_analisadas para histórico completo de análises de plantas
CREATE TABLE IF NOT EXISTS public.plantas_analisadas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Relacionamentos
  usuario_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL,
  obra_id UUID REFERENCES public.obras(id) ON DELETE SET NULL,
  
  -- Dados do arquivo
  nome_projeto TEXT NOT NULL,
  nome_arquivo TEXT NOT NULL,
  url_planta TEXT NOT NULL,
  tamanho_arquivo INTEGER,
  tipo_arquivo TEXT,
  
  -- Dados da análise IA
  resumo_analise TEXT,
  dados_estruturados JSONB,
  
  -- Métricas extraídas
  area_total_construida NUMERIC,
  numero_quartos INTEGER,
  numero_banheiros INTEGER,
  numero_pavimentos INTEGER,
  outros_comodos TEXT[],
  
  -- Orçamento e valores
  valor_orcamento_parametrico NUMERIC,
  valor_estimado NUMERIC,
  
  -- Status e controle
  status TEXT DEFAULT 'analisada' CHECK (status IN ('analisada', 'obra_criada', 'arquivada')),
  
  -- Timestamps
  data_analise TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Índices para performance
CREATE INDEX idx_plantas_analisadas_usuario_id ON public.plantas_analisadas(usuario_id);
CREATE INDEX idx_plantas_analisadas_tenant_id ON public.plantas_analisadas(tenant_id);
CREATE INDEX idx_plantas_analisadas_obra_id ON public.plantas_analisadas(obra_id);
CREATE INDEX idx_plantas_analisadas_status ON public.plantas_analisadas(status);
CREATE INDEX idx_plantas_analisadas_data_analise ON public.plantas_analisadas(data_analise DESC);

-- Trigger para atualizar updated_at
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER plantas_analisadas_updated_at
  BEFORE UPDATE ON public.plantas_analisadas
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- RLS (Row Level Security) para isolamento por tenant
ALTER TABLE public.plantas_analisadas ENABLE ROW LEVEL SECURITY;

-- Policy para usuários autenticados (isolamento por tenant)
CREATE POLICY "plantas_analisadas_tenant_isolation" ON public.plantas_analisadas
  FOR ALL
  TO authenticated
  USING (
    tenant_id = COALESCE(
      current_setting('app.current_tenant_id', true)::uuid,
      (SELECT tenant_id FROM public.user_tenants WHERE user_id = auth.uid() LIMIT 1)
    )
  )
  WITH CHECK (
    tenant_id = COALESCE(
      current_setting('app.current_tenant_id', true)::uuid,
      (SELECT tenant_id FROM public.user_tenants WHERE user_id = auth.uid() LIMIT 1)
    )
  );

-- Policy para insert (garantir que o usuário tenha acesso ao tenant)
CREATE POLICY "plantas_analisadas_insert_policy" ON public.plantas_analisadas
  FOR INSERT
  TO authenticated
  WITH CHECK (
    usuario_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.user_tenants 
      WHERE user_id = auth.uid() AND tenant_id = plantas_analisadas.tenant_id
    )
  );

-- Comentários para documentação
COMMENT ON TABLE public.plantas_analisadas IS 'Histórico completo de plantas analisadas pela IA';
COMMENT ON COLUMN public.plantas_analisadas.dados_estruturados IS 'JSON com dados estruturados retornados pela IA';
COMMENT ON COLUMN public.plantas_analisadas.outros_comodos IS 'Array de cômodos identificados pela IA';
COMMENT ON COLUMN public.plantas_analisadas.status IS 'Status: analisada, obra_criada, arquivada';