-- Migration: Create AI Usage Tracking Table
-- Description: <PERSON><PERSON><PERSON> para controlar quotas de uso de IA por usuário
-- Author: <PERSON> (ObrasAI Team)
-- Date: 2025-07-11

-- Tabela para tracking de uso de IA por usuário
CREATE TABLE ai_usage_tracking (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    
    -- Contadores específicos por funcionalidade
    chat_requests INTEGER DEFAULT 0 CHECK (chat_requests >= 0),
    budget_requests INTEGER DEFAULT 0 CHECK (budget_requests >= 0),
    contract_requests INTEGER DEFAULT 0 CHECK (contract_requests >= 0),
    sinapi_requests INTEGER DEFAULT 0 CHECK (sinapi_requests >= 0),
    
    -- Tracking de tokens para monitoramento de custos
    total_tokens INTEGER DEFAULT 0 CHECK (total_tokens >= 0),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Constraint para garantir uma linha por usuário por dia
    UNIQUE(user_id, date)
);

-- Índices para performance
CREATE INDEX idx_ai_usage_tracking_user_date ON ai_usage_tracking(user_id, date);
CREATE INDEX idx_ai_usage_tracking_date ON ai_usage_tracking(date);

-- Row Level Security (RLS)
ALTER TABLE ai_usage_tracking ENABLE ROW LEVEL SECURITY;

-- Policy: Usuários só podem ver/editar seus próprios dados
CREATE POLICY "Users can view their own AI usage" ON ai_usage_tracking
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own AI usage" ON ai_usage_tracking
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own AI usage" ON ai_usage_tracking
    FOR UPDATE USING (auth.uid() = user_id);

-- Função para atualizar timestamp automaticamente
CREATE OR REPLACE FUNCTION update_ai_usage_tracking_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para atualizar updated_at automaticamente
CREATE TRIGGER update_ai_usage_tracking_updated_at
    BEFORE UPDATE ON ai_usage_tracking
    FOR EACH ROW
    EXECUTE FUNCTION update_ai_usage_tracking_updated_at();

-- Função para limpar dados antigos (opcional - manter últimos 30 dias)
CREATE OR REPLACE FUNCTION cleanup_old_ai_usage_tracking()
RETURNS void AS $$
BEGIN
    DELETE FROM ai_usage_tracking 
    WHERE date < CURRENT_DATE - INTERVAL '30 days';
END;
$$ language 'plpgsql';

-- Comentários para documentação
COMMENT ON TABLE ai_usage_tracking IS 'Tracking de uso de funcionalidades de IA por usuário para controle de quotas';
COMMENT ON COLUMN ai_usage_tracking.chat_requests IS 'Número de requests de chat IA por dia';
COMMENT ON COLUMN ai_usage_tracking.budget_requests IS 'Número de orçamentos gerados por IA por dia';
COMMENT ON COLUMN ai_usage_tracking.contract_requests IS 'Número de análises de contrato por IA por dia';
COMMENT ON COLUMN ai_usage_tracking.sinapi_requests IS 'Número de consultas SINAPI por dia';
COMMENT ON COLUMN ai_usage_tracking.total_tokens IS 'Total de tokens utilizados por dia (para monitoramento de custos)';