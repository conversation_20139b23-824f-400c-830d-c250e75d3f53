-- Cria<PERSON> da tabela user_preferences para armazenar configurações do usuário
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Configurações de Notificações
    notifications JSONB DEFAULT '{
        "email_enabled": true,
        "push_enabled": true,
        "obra_alerts": true,
        "despesa_alerts": true,
        "contrato_alerts": true,
        "orcamento_alerts": true,
        "marketing_emails": false,
        "weekly_reports": true,
        "alert_frequency": "immediate",
        "quiet_hours": {
            "enabled": false,
            "start_time": "22:00",
            "end_time": "08:00"
        }
    }'::jsonb,
    
    -- Configuraç<PERSON><PERSON> de Aparência
    appearance JSONB DEFAULT '{
        "theme": "system",
        "sidebar_collapsed": false,
        "compact_mode": false,
        "animations_enabled": true,
        "high_contrast": false,
        "font_size": "medium",
        "color_scheme": "default"
    }'::jsonb,
    
    -- Configurações de Segurança
    security JSONB DEFAULT '{
        "two_factor_enabled": false,
        "session_timeout": 480,
        "login_notifications": true,
        "suspicious_activity_alerts": true,
        "data_export_notifications": true,
        "password_change_notifications": true,
        "device_login_notifications": true
    }'::jsonb,
    
    -- Configurações de Idioma
    language JSONB DEFAULT '{
        "language": "pt-BR",
        "timezone": "America/Sao_Paulo",
        "date_format": "DD/MM/YYYY",
        "time_format": "24h",
        "currency": "BRL",
        "number_format": "pt-BR"
    }'::jsonb,
    
    -- Configurações de Dispositivos
    devices JSONB DEFAULT '{
        "remember_devices": true,
        "auto_logout_inactive": false,
        "max_concurrent_sessions": 5,
        "mobile_notifications": true,
        "desktop_notifications": true
    }'::jsonb,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_preferences_unique_user ON user_preferences(user_id);

-- RLS (Row Level Security)
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- Política para usuários autenticados só verem suas próprias preferências
CREATE POLICY "Users can view own preferences" ON user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own preferences" ON user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own preferences" ON user_preferences
    FOR DELETE USING (auth.uid() = user_id);

-- Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_user_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_user_preferences_updated_at
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_user_preferences_updated_at();

-- Função para criar preferências padrão para novos usuários
CREATE OR REPLACE FUNCTION create_default_user_preferences()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_preferences (user_id)
    VALUES (NEW.id)
    ON CONFLICT (user_id) DO NOTHING;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para criar preferências padrão quando um usuário é criado
CREATE TRIGGER trigger_create_default_preferences
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION create_default_user_preferences();

-- Comentários para documentação
COMMENT ON TABLE user_preferences IS 'Armazena todas as preferências e configurações personalizadas do usuário';
COMMENT ON COLUMN user_preferences.notifications IS 'Configurações de notificações (email, push, alertas específicos)';
COMMENT ON COLUMN user_preferences.appearance IS 'Configurações de aparência (tema, layout, acessibilidade)';
COMMENT ON COLUMN user_preferences.security IS 'Configurações de segurança (2FA, timeouts, alertas)';
COMMENT ON COLUMN user_preferences.language IS 'Configurações de idioma e localização';
COMMENT ON COLUMN user_preferences.devices IS 'Configurações relacionadas a dispositivos e sessões';
