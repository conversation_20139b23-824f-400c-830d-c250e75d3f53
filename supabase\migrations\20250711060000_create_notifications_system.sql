-- ============================================================================
-- SISTEMA DE NOTIFICAÇÕES OBRASAI
-- ============================================================================
-- Migração para implementar sistema completo de notificações
-- Criado para: ObrasAI 2.2 - Sistema de gestão de obras
-- Data: 2025-07-11
-- ============================================================================

-- Enum para tipos de notificação específicos do ObrasAI
create type notification_type as enum (
  'obra_prazo_vencendo',
  'obra_orcamento_excedido', 
  'obra_status_alterado',
  'novo_lead_capturado',
  'contrato_assinado',
  'contrato_vencendo',
  'ia_analise_pronta',
  'ia_orcamento_gerado',
  'sinapi_atualizado',
  'sistema_manutencao',
  'pagamento_vencendo',
  'pagamento_processado'
);

-- Enum para canais de notificação
create type notification_channel as enum (
  'in_app',
  'email', 
  'push',
  'sms'
);

-- Enum para prioridade
create type notification_priority as enum (
  'low',
  'medium', 
  'high',
  'urgent'
);

-- Tabela principal de notificações
create table notifications (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users(id) on delete cascade not null,
  
  -- Conteúdo da notificação
  type notification_type not null,
  title text not null,
  message text not null,
  priority notification_priority default 'medium',
  
  -- Contexto específico (referência à entidade relacionada)
  context_type text, -- 'obra', 'contrato', 'lead', 'orcamento'
  context_id uuid, -- ID da entidade relacionada
  context_data jsonb, -- Dados adicionais de contexto
  
  -- Controle de envio
  channels notification_channel[] default array['in_app'],
  
  -- Status de entrega
  is_read boolean default false,
  read_at timestamp with time zone,
  
  -- Agendamento
  scheduled_for timestamp with time zone,
  sent_at timestamp with time zone,
  
  -- Metadados
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now(),
  expires_at timestamp with time zone
);

-- Tabela para preferências de notificação do usuário
create table notification_preferences (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references auth.users(id) on delete cascade unique not null,
  
  -- Configurações gerais
  enabled boolean default true,
  quiet_hours_start time,
  quiet_hours_end time,
  timezone text default 'America/Sao_Paulo',
  
  -- Configurações por tipo
  preferences jsonb default '{
    "obra_prazo_vencendo": {"enabled": true, "channels": ["in_app", "email"], "advance_hours": 48},
    "obra_orcamento_excedido": {"enabled": true, "channels": ["in_app", "email"], "threshold_percentage": 110},
    "obra_status_alterado": {"enabled": true, "channels": ["in_app"]},
    "novo_lead_capturado": {"enabled": true, "channels": ["in_app", "email"], "immediate": true},
    "contrato_assinado": {"enabled": true, "channels": ["in_app", "email"]},
    "contrato_vencendo": {"enabled": true, "channels": ["in_app", "email"], "advance_days": 30},
    "ia_analise_pronta": {"enabled": true, "channels": ["in_app", "push"]},
    "ia_orcamento_gerado": {"enabled": true, "channels": ["in_app", "email"]},
    "sinapi_atualizado": {"enabled": false, "channels": ["in_app"]},
    "sistema_manutencao": {"enabled": true, "channels": ["in_app", "email"]},
    "pagamento_vencendo": {"enabled": true, "channels": ["in_app", "email"], "advance_days": 7},
    "pagamento_processado": {"enabled": true, "channels": ["in_app"]}
  }'::jsonb,
  
  -- Frequência de digest de email
  email_digest_frequency text default 'daily', -- 'never', 'daily', 'weekly'
  email_digest_time time default '08:00:00',
  
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now()
);

-- Tabela para log de entrega de notificações
create table notification_delivery_log (
  id uuid primary key default gen_random_uuid(),
  notification_id uuid references notifications(id) on delete cascade not null,
  channel notification_channel not null,
  status text not null, -- 'pending', 'sent', 'delivered', 'failed', 'bounced'
  provider text, -- 'supabase', 'sendgrid', 'fcm', 'twilio'
  provider_response jsonb,
  error_message text,
  sent_at timestamp with time zone,
  delivered_at timestamp with time zone,
  created_at timestamp with time zone default now()
);

-- ============================================================================
-- ÍNDICES PARA PERFORMANCE
-- ============================================================================

create index idx_notifications_user_id on notifications(user_id);
create index idx_notifications_type on notifications(type);
create index idx_notifications_context on notifications(context_type, context_id);
create index idx_notifications_unread on notifications(user_id, is_read) where is_read = false;
create index idx_notifications_scheduled on notifications(scheduled_for) where scheduled_for is not null;
create index idx_notifications_created_at on notifications(created_at desc);

create index idx_notification_preferences_user_id on notification_preferences(user_id);

create index idx_delivery_log_notification_id on notification_delivery_log(notification_id);
create index idx_delivery_log_status on notification_delivery_log(status);

-- ============================================================================
-- FUNÇÕES E TRIGGERS
-- ============================================================================

-- Função para atualizar updated_at automaticamente
create or replace function update_updated_at_column()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

-- Triggers para updated_at
create trigger update_notifications_updated_at
  before update on notifications
  for each row execute function update_updated_at_column();

create trigger update_notification_preferences_updated_at
  before update on notification_preferences
  for each row execute function update_updated_at_column();

-- Função para marcar notificação como lida
create or replace function mark_notification_as_read(notification_uuid uuid)
returns void as $$
begin
  update notifications 
  set is_read = true, read_at = now()
  where id = notification_uuid and user_id = auth.uid();
end;
$$ language plpgsql security definer;

-- Função para criar preferências padrão para novos usuários
create or replace function create_default_notification_preferences()
returns trigger as $$
begin
  insert into notification_preferences (user_id)
  values (new.id)
  on conflict (user_id) do nothing;
  return new;
end;
$$ language plpgsql security definer;

-- Trigger para criar preferências ao criar usuário
create trigger create_notification_preferences_on_signup
  after insert on auth.users
  for each row execute function create_default_notification_preferences();

-- ============================================================================
-- ROW LEVEL SECURITY (RLS)
-- ============================================================================

-- Habilitar RLS em todas as tabelas
alter table notifications enable row level security;
alter table notification_preferences enable row level security;
alter table notification_delivery_log enable row level security;

-- Políticas para notifications
create policy "Users can view their own notifications"
  on notifications for select
  using (user_id = auth.uid());

create policy "Users can update their own notifications"
  on notifications for update
  using (user_id = auth.uid());

-- Políticas para notification_preferences  
create policy "Users can manage their own preferences"
  on notification_preferences for all
  using (user_id = auth.uid());

-- Políticas para delivery_log (somente leitura para usuários)
create policy "Users can view delivery logs for their notifications"
  on notification_delivery_log for select
  using (
    notification_id in (
      select id from notifications where user_id = auth.uid()
    )
  );

-- ============================================================================
-- FUNÇÕES UTILITÁRIAS
-- ============================================================================

-- Função para contar notificações não lidas
create or replace function get_unread_notifications_count(user_uuid uuid default auth.uid())
returns bigint as $$
begin
  return (
    select count(*)
    from notifications
    where user_id = user_uuid 
    and is_read = false
    and (expires_at is null or expires_at > now())
  );
end;
$$ language plpgsql security definer;

-- Função para obter notificações recentes
create or replace function get_recent_notifications(
  user_uuid uuid default auth.uid(),
  limit_count integer default 10
)
returns table (
  id uuid,
  type notification_type,
  title text,
  message text,
  priority notification_priority,
  context_type text,
  context_id uuid,
  context_data jsonb,
  is_read boolean,
  created_at timestamp with time zone
) as $$
begin
  return query
  select 
    n.id, n.type, n.title, n.message, n.priority,
    n.context_type, n.context_id, n.context_data,
    n.is_read, n.created_at
  from notifications n
  where n.user_id = user_uuid
  and (n.expires_at is null or n.expires_at > now())
  order by n.created_at desc
  limit limit_count;
end;
$$ language plpgsql security definer;

-- ============================================================================
-- COMENTÁRIOS PARA DOCUMENTAÇÃO
-- ============================================================================

comment on table notifications is 'Tabela principal para notificações do sistema ObrasAI';
comment on table notification_preferences is 'Preferências de notificação personalizadas por usuário';
comment on table notification_delivery_log is 'Log de entrega de notificações por canal';

comment on column notifications.context_type is 'Tipo de entidade relacionada (obra, contrato, lead, etc.)';
comment on column notifications.context_id is 'ID da entidade relacionada à notificação';
comment on column notifications.context_data is 'Dados JSON adicionais específicos do contexto';
comment on column notifications.channels is 'Array de canais onde a notificação deve ser enviada';

comment on function get_unread_notifications_count is 'Retorna quantidade de notificações não lidas do usuário';
comment on function get_recent_notifications is 'Retorna notificações recentes do usuário com limite configurável';