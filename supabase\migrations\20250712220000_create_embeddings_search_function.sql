-- Migration: <PERSON><PERSON>r função RPC para busca de embeddings
-- Criado: 2025-07-12
-- Descrição: Função para busca semântica na base de conhecimento vetorizada

-- Função para busca de embeddings (versão simplificada)
CREATE OR REPLACE FUNCTION search_embeddings_conhecimento(
  query_embedding vector(1536),
  match_threshold float DEFAULT 0.75,
  match_count int DEFAULT 5,
  filter_tipo text DEFAULT NULL
)
RETURNS TABLE (
  id uuid,
  titulo text,
  conteudo text,
  conteudo_resumido text,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  -- Se não há embedding vetorial, buscar por tipo
  IF query_embedding IS NULL THEN
    RETURN QUERY
    SELECT 
      ec.id,
      ec.titulo,
      ec.conteudo,
      ec.conteudo_resumido,
      0.8 as similarity
    FROM embeddings_conhecimento ec
    WHERE 
      (filter_tipo IS NULL OR ec.tipo_conteudo = filter_tipo)
    ORDER BY ec.created_at DESC
    LIMIT match_count;
  ELSE
    -- Busca vetorial se embedding disponível
    RETURN QUERY
    SELECT 
      ec.id,
      ec.titulo,
      ec.conteudo,
      ec.conteudo_resumido,
      CASE 
        WHEN ec.embedding IS NOT NULL THEN 1 - (ec.embedding <=> query_embedding)
        ELSE 0.5
      END as similarity
    FROM embeddings_conhecimento ec
    WHERE 
      (filter_tipo IS NULL OR ec.tipo_conteudo = filter_tipo)
      AND (
        ec.embedding IS NULL 
        OR (ec.embedding IS NOT NULL AND 1 - (ec.embedding <=> query_embedding) > match_threshold)
      )
    ORDER BY 
      CASE 
        WHEN ec.embedding IS NOT NULL THEN ec.embedding <=> query_embedding
        ELSE 0.5
      END
    LIMIT match_count;
  END IF;
END;
$$;

-- Garantir permissões adequadas
GRANT EXECUTE ON FUNCTION search_embeddings_conhecimento TO authenticated;
GRANT EXECUTE ON FUNCTION search_embeddings_conhecimento TO service_role;

-- Comentário para documentação
COMMENT ON FUNCTION search_embeddings_conhecimento IS 'Função para busca semântica na base de conhecimento vetorizada. Suporta busca por similaridade de embeddings ou busca simples por tipo de conteúdo.';