-- Migração para corrigir vulnerabilidades de segurança identificadas
-- Data: 2025-07-15
-- Descrição: Corrige isolamento de dados por tenant em tabelas críticas

-- ============================================================================
-- 1. CORREÇÃO CRÍTICA: Isolamento da tabela leads por tenant
-- ============================================================================

-- Primeiro, vamos verificar se a tabela leads tem o campo tenant_id
-- Se não tiver, precisamos adicionar
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'leads' AND column_name = 'tenant_id'
    ) THEN
        ALTER TABLE "public"."leads" ADD COLUMN "tenant_id" UUID;
        
        -- Criar índice para performance
        CREATE INDEX IF NOT EXISTS "idx_leads_tenant_id" ON "public"."leads" ("tenant_id");
    END IF;
END $$;

-- Remover políticas RLS permissivas existentes na tabela leads
DROP POLICY IF EXISTS "Allow insert leads" ON "public"."leads";
DROP POLICY IF EXISTS "Allow read leads" ON "public"."leads";
DROP POLICY IF EXISTS "Allow update leads" ON "public"."leads";
DROP POLICY IF EXISTS "Allow delete leads" ON "public"."leads";

-- Criar nova política RLS com isolamento por tenant para leads
CREATE POLICY "leads_tenant_isolation" ON "public"."leads"
    FOR ALL
    TO "authenticated"
    USING (
        tenant_id = (
            SELECT tenant_id 
            FROM "public"."profiles" 
            WHERE id = auth.uid()
        )
    )
    WITH CHECK (
        tenant_id = (
            SELECT tenant_id 
            FROM "public"."profiles" 
            WHERE id = auth.uid()
        )
    );

-- Criar trigger para auto-preenchimento do tenant_id em leads
CREATE OR REPLACE FUNCTION "public"."set_tenant_id_on_leads"()
RETURNS TRIGGER AS $$
BEGIN
    NEW.tenant_id = (
        SELECT tenant_id 
        FROM "public"."profiles" 
        WHERE id = auth.uid()
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Aplicar trigger na tabela leads
DROP TRIGGER IF EXISTS "set_tenant_id_on_leads_trigger" ON "public"."leads";
CREATE TRIGGER "set_tenant_id_on_leads_trigger"
    BEFORE INSERT ON "public"."leads"
    FOR EACH ROW
    EXECUTE FUNCTION "public"."set_tenant_id_on_leads"();

-- ============================================================================
-- 2. CORREÇÃO CRÍTICA: Política RLS da tabela embeddings_conhecimento
-- ============================================================================

-- Primeiro, vamos verificar se a tabela embeddings_conhecimento tem tenant_id
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'embeddings_conhecimento' AND column_name = 'tenant_id'
    ) THEN
        ALTER TABLE "public"."embeddings_conhecimento" ADD COLUMN "tenant_id" UUID;
        
        -- Criar índice para performance
        CREATE INDEX IF NOT EXISTS "idx_embeddings_conhecimento_tenant_id" ON "public"."embeddings_conhecimento" ("tenant_id");
    END IF;
END $$;

-- Remover política RLS existente que pode ser vulnerável
DROP POLICY IF EXISTS "embeddings_conhecimento_select_policy" ON "public"."embeddings_conhecimento";

-- Criar nova política RLS com isolamento por tenant para embeddings_conhecimento
CREATE POLICY "embeddings_conhecimento_tenant_isolation" ON "public"."embeddings_conhecimento"
    FOR ALL
    TO "authenticated"
    USING (
        tenant_id = (
            SELECT tenant_id 
            FROM "public"."profiles" 
            WHERE id = auth.uid()
        )
    )
    WITH CHECK (
        tenant_id = (
            SELECT tenant_id 
            FROM "public"."profiles" 
            WHERE id = auth.uid()
        )
    );

-- Criar trigger para auto-preenchimento do tenant_id em embeddings_conhecimento
CREATE OR REPLACE FUNCTION "public"."set_tenant_id_on_embeddings_conhecimento"()
RETURNS TRIGGER AS $$
BEGIN
    NEW.tenant_id = (
        SELECT tenant_id 
        FROM "public"."profiles" 
        WHERE id = auth.uid()
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Aplicar trigger na tabela embeddings_conhecimento
DROP TRIGGER IF EXISTS "set_tenant_id_on_embeddings_conhecimento_trigger" ON "public"."embeddings_conhecimento";
CREATE TRIGGER "set_tenant_id_on_embeddings_conhecimento_trigger"
    BEFORE INSERT ON "public"."embeddings_conhecimento"
    FOR EACH ROW
    EXECUTE FUNCTION "public"."set_tenant_id_on_embeddings_conhecimento"();

-- ============================================================================
-- 3. MELHORIA: Política de analytics_events mais restritiva
-- ============================================================================

-- Remover política muito permissiva para usuários anônimos
DROP POLICY IF EXISTS "Anonymous can insert analytics events" ON "public"."analytics_events";

-- Criar nova política mais restritiva para analytics anônimos
CREATE POLICY "restricted_anonymous_analytics_insert" ON "public"."analytics_events"
    FOR INSERT
    TO "anon"
    WITH CHECK (
        -- Verificar se o evento é recente (últimos 5 minutos)
        created_at >= NOW() - INTERVAL '5 minutes'
        AND created_at <= NOW() + INTERVAL '1 minute'
        -- Verificar se user_agent não é nulo (indicador de bot)
        AND user_agent IS NOT NULL
        AND LENGTH(user_agent) > 10
        -- Verificar se o evento não é suspeito
        AND event_type IN ('page_view', 'button_click', 'form_interaction', 'lead_capture')
    );

-- ============================================================================
-- 4. FUNÇÃO AUXILIAR: Validação de acesso por tenant
-- ============================================================================

-- Função para validar se o usuário tem acesso ao tenant
CREATE OR REPLACE FUNCTION "public"."validate_tenant_access"(target_tenant_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM "public"."profiles" 
        WHERE id = auth.uid() 
        AND tenant_id = target_tenant_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 5. AUDITORIA: Tabela para log de acessos suspeitos
-- ============================================================================

-- Criar tabela para auditoria de acessos suspeitos
CREATE TABLE IF NOT EXISTS "public"."security_audit_log" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" UUID REFERENCES auth.users(id),
    "tenant_id" UUID,
    "event_type" TEXT NOT NULL,
    "table_name" TEXT,
    "record_id" UUID,
    "ip_address" INET,
    "user_agent" TEXT,
    "suspicious_activity" BOOLEAN DEFAULT FALSE,
    "details" JSONB,
    "created_at" TIMESTAMPTZ DEFAULT NOW()
);

-- Habilitar RLS na tabela de auditoria
ALTER TABLE "public"."security_audit_log" ENABLE ROW LEVEL SECURITY;

-- Política para que apenas admins possam ver logs de auditoria
CREATE POLICY "security_audit_log_admin_only" ON "public"."security_audit_log"
    FOR ALL
    TO "authenticated"
    USING (
        EXISTS (
            SELECT 1 FROM "public"."profiles" 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- ============================================================================
-- 6. FUNÇÃO: Limpeza automática de dados antigos
-- ============================================================================

-- Função para limpeza automática de dados de auditoria antigos
CREATE OR REPLACE FUNCTION "public"."cleanup_security_audit_log"()
RETURNS void AS $$
BEGIN
    -- Manter apenas logs dos últimos 90 dias
    DELETE FROM "public"."security_audit_log"
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    -- Log da limpeza
    INSERT INTO "public"."security_audit_log" (
        event_type, 
        details, 
        created_at
    ) VALUES (
        'system_cleanup',
        '{"action": "cleanup_security_audit_log", "retention_days": 90}',
        NOW()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 7. COMENTÁRIOS E DOCUMENTAÇÃO
-- ============================================================================

-- Comentários nas tabelas para documentar as correções
COMMENT ON POLICY "leads_tenant_isolation" ON "public"."leads" IS 
'Política de isolamento por tenant para leads - corrige vulnerabilidade de acesso cross-tenant';

COMMENT ON POLICY "embeddings_conhecimento_tenant_isolation" ON "public"."embeddings_conhecimento" IS 
'Política de isolamento por tenant para embeddings - corrige vulnerabilidade de busca semântica';

COMMENT ON POLICY "restricted_anonymous_analytics_insert" ON "public"."analytics_events" IS 
'Política restritiva para analytics anônimos - previne spam e ataques';

COMMENT ON TABLE "public"."security_audit_log" IS 
'Tabela para auditoria de acessos suspeitos e monitoramento de segurança';

-- ============================================================================
-- 8. VERIFICAÇÃO DE INTEGRIDADE
-- ============================================================================

-- Verificar se todas as tabelas críticas têm RLS habilitado
DO $$
DECLARE
    table_name TEXT;
    tables_to_check TEXT[] := ARRAY['leads', 'embeddings_conhecimento', 'profiles', 'obras', 'contratos', 'fornecedores_pf', 'fornecedores_pj'];
BEGIN
    FOREACH table_name IN ARRAY tables_to_check
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM pg_tables 
            WHERE tablename = table_name 
            AND rowsecurity = true
        ) THEN
            RAISE NOTICE 'AVISO: Tabela % não tem RLS habilitado!', table_name;
        END IF;
    END LOOP;
END $$;

-- Verificar se todas as tabelas críticas têm campo tenant_id
DO $$
DECLARE
    table_name TEXT;
    tables_to_check TEXT[] := ARRAY['leads', 'embeddings_conhecimento', 'profiles', 'obras', 'contratos'];
BEGIN
    FOREACH table_name IN ARRAY tables_to_check
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = table_name 
            AND column_name = 'tenant_id'
        ) THEN
            RAISE NOTICE 'AVISO: Tabela % não tem campo tenant_id!', table_name;
        END IF;
    END LOOP;
END $$;

-- ============================================================================
-- FIM DA MIGRAÇÃO
-- ============================================================================

-- Log de sucesso da migração
INSERT INTO "public"."security_audit_log" (
    event_type, 
    details, 
    created_at
) VALUES (
    'security_migration_applied',
    '{"migration": "20250715000000_fix_security_vulnerabilities", "fixes": ["leads_tenant_isolation", "embeddings_rls_fix", "analytics_restrictions", "audit_logging"]}',
    NOW()
);