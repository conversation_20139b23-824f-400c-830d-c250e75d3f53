-- Migration: Add condominium details to obras table
-- Date: 2025-07-16
-- Description: Adds detailed fields for vertical and horizontal condominiums to the obras table.

-- Create the condominio_type enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'condominio_type') THEN
        CREATE TYPE condominio_type AS ENUM ('VERTICAL', 'HORIZONTAL');
    END IF;
END$$;


-- Add new columns to obras table for condominium details
ALTER TABLE obras
ADD COLUMN IF NOT EXISTS tipo_condominio condominio_type,
ADD COLUMN IF NOT EXISTS numero_blocos INTEGER,
ADD COLUMN IF NOT EXISTS andares_por_bloco INTEGER,
ADD COLUMN IF NOT EXISTS unidades_por_andar INTEGER,
ADD COLUMN IF NOT EXISTS numero_unidades INTEGER,
ADD COLUMN IF NOT EXISTS area_lote NUMERIC(10, 2),
ADD COLUMN IF NOT EXISTS area_construida_unidade NUMERIC(10, 2);

-- Add comments to document the new columns
COMMENT ON COLUMN obras.tipo_condominio IS 'Tipo do condomínio: VERTICAL (Prédios) ou HORIZONTAL (Casas/Lotes). Aplicável apenas para tipo_projeto = CONDOMINIO_MASTER.';
COMMENT ON COLUMN obras.numero_blocos IS 'Número de blocos ou torres. Aplicável para condomínios verticais.';
COMMENT ON COLUMN obras.andares_por_bloco IS 'Número de andares por bloco. Aplicável para condomínios verticais.';
COMMENT ON COLUMN obras.unidades_por_andar IS 'Número de unidades por andar. Aplicável para condomínios verticais.';
COMMENT ON COLUMN obras.numero_unidades IS 'Número total de unidades. Aplicável para condomínios horizontais.';
COMMENT ON COLUMN obras.area_lote IS 'Área do lote por unidade. Aplicável para condomínios horizontais.';
COMMENT ON COLUMN obras.area_construida_unidade IS 'Área construída por unidade. Aplicável para condomínios horizontais.';

-- Add check constraints for data integrity based on condominio_type
-- Note: These constraints are simplified to avoid issues with partial updates.
-- The application layer should enforce the presence of these fields.

ALTER TABLE obras
ADD CONSTRAINT chk_vertical_condo_fields_optional
CHECK (
  (tipo_condominio != 'VERTICAL') OR
  (tipo_projeto != 'CONDOMINIO_MASTER') OR
  (
    (numero_blocos IS NULL OR numero_blocos > 0) AND
    (andares_por_bloco IS NULL OR andares_por_bloco > 0) AND
    (unidades_por_andar IS NULL OR unidades_por_andar > 0)
  )
);

ALTER TABLE obras
ADD CONSTRAINT chk_horizontal_condo_fields_optional
CHECK (
  (tipo_condominio != 'HORIZONTAL') OR
  (tipo_projeto != 'CONDOMINIO_MASTER') OR
  (
    (numero_unidades IS NULL OR numero_unidades > 0) AND
    (area_construida_unidade IS NULL OR area_construida_unidade > 0)
  )
);