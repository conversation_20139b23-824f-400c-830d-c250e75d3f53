-- Migration: Add progresso column to obras table
-- Date: 2025-07-17
-- Description: Adds progresso (progress percentage) column to track work completion

-- Add progresso column to obras table
ALTER TABLE public.obras 
ADD COLUMN progresso NUMERIC(5,2) DEFAULT 0.00 
CHECK (progresso >= 0 AND progresso <= 100);

-- Add comment to document the column
COMMENT ON COLUMN public.obras.progresso IS 'Percentual de progresso da obra (0-100%)';

-- Create index for performance
CREATE INDEX idx_obras_progresso ON public.obras(progresso);

-- Update existing rows to have 0% progress
UPDATE public.obras 
SET progresso = 0.00 
WHERE progresso IS NULL;