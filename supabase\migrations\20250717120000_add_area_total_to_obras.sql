-- Migration: Add area_total column to obras table
-- Date: 2025-07-17
-- Description: Adds area_total field to obras table for construction area tracking

-- Add area_total column to obras table
ALTER TABLE public.obras 
ADD COLUMN area_total NUMERIC(10,2) DEFAULT 0.00 
CHECK (area_total >= 0);

-- Add comment to document the column
COMMENT ON COLUMN public.obras.area_total IS 'Área total da construção em metros quadrados (necessária para orçamento paramétrico)';

-- Create index for performance
CREATE INDEX idx_obras_area_total ON public.obras(area_total);

-- Update existing rows to have 0 as default area_total
UPDATE public.obras 
SET area_total = 0.00 
WHERE area_total IS NULL;
