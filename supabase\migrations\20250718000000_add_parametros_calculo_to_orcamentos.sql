-- Migration: Add parametros_calculo JSONB column to orcamentos_parametricos
-- Date: 2025-07-18
-- Description: Adds audit trail column to store calculation parameters for budget estimates

-- Add parametros_calculo column to store calculation audit trail
ALTER TABLE orcamentos_parametricos 
ADD COLUMN IF NOT EXISTS parametros_calculo JSONB;

-- Add comment to document the new column
COMMENT ON COLUMN orcamentos_parametricos.parametros_calculo IS 'JSONB containing audit trail of calculation parameters including complexity factors, CUB values, SINAPI queries, cost composition, and condominium-specific parameters used for budget estimation';

-- Create index for efficient JSONB queries on parametros_calculo
CREATE INDEX IF NOT EXISTS idx_orcamentos_parametricos_parametros_calculo 
ON orcamentos_parametricos USING GIN (parametros_calculo);

-- Add example of expected JSON structure in comment
COMMENT ON INDEX idx_orcamentos_parametricos_parametros_calculo IS 'GIN index for JSONB queries on calculation parameters. Expected structure: {"tipo_obra": "R4_MULTIFAMILIAR", "subtipo_calculo": "VERTICAL", "fator_complexidade": 1.45, "cub_utilizado": 2200.00, "fatores_padrao": {"NORMAL": 1.5}, "consultas_sinapi": [...], "composicao_custos": {...}, "parametros_condominio": {...}}';