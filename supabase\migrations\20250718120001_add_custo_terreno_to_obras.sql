-- Migração: Adicionar campo custo_terreno à tabela obras
-- Data: 18/07/2025
-- Objetivo: Permitir que o custo de aquisição de terreno/área seja considerado no orçamento total da obra

-- Adicionar campo custo_terreno à tabela obras
ALTER TABLE public.obras 
ADD COLUMN IF NOT EXISTS custo_terreno NUMERIC(12,2) DEFAULT 0.00;

-- Adicionar comentário explicativo
COMMENT ON COLUMN public.obras.custo_terreno IS 'Custo de aquisição do terreno/área para a obra. Será somado ao orçamento de construção para calcular o investimento total.';

-- Criar função para calcular orçamento total (construção + terreno)
CREATE OR REPLACE FUNCTION public.get_orcamento_total_obra(obra_id UUID)
RETURNS NUMERIC(12,2)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    orcamento_construcao NUMERIC(12,2) := 0;
    custo_terreno_valor NUMERIC(12,2) := 0;
    total NUMERIC(12,2) := 0;
BEGIN
    -- Buscar orçamento de construção e custo do terreno
    SELECT 
        COALESCE(orcamento, 0),
        COALESCE(custo_terreno, 0)
    INTO 
        orcamento_construcao,
        custo_terreno_valor
    FROM public.obras 
    WHERE id = obra_id;
    
    -- Calcular total
    total := orcamento_construcao + custo_terreno_valor;
    
    RETURN total;
END;
$$;

-- Criar função para sincronizar despesas de aquisição com custo_terreno
CREATE OR REPLACE FUNCTION public.sync_custo_terreno_from_despesas()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    total_aquisicao NUMERIC(12,2) := 0;
BEGIN
    -- Calcular total das despesas de aquisição de terreno/área para a obra
    SELECT COALESCE(SUM(custo), 0)
    INTO total_aquisicao
    FROM public.despesas
    WHERE obra_id = COALESCE(NEW.obra_id, OLD.obra_id)
    AND categoria = 'AQUISICAO_TERRENO_AREA';
    
    -- Atualizar o campo custo_terreno na obra
    UPDATE public.obras
    SET custo_terreno = total_aquisicao,
        updated_at = NOW()
    WHERE id = COALESCE(NEW.obra_id, OLD.obra_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$;

-- Criar trigger para sincronização automática
DROP TRIGGER IF EXISTS trigger_sync_custo_terreno ON public.despesas;
CREATE TRIGGER trigger_sync_custo_terreno
    AFTER INSERT OR UPDATE OR DELETE ON public.despesas
    FOR EACH ROW
    WHEN (
        (TG_OP = 'INSERT' AND NEW.categoria = 'AQUISICAO_TERRENO_AREA') OR
        (TG_OP = 'UPDATE' AND (NEW.categoria = 'AQUISICAO_TERRENO_AREA' OR OLD.categoria = 'AQUISICAO_TERRENO_AREA')) OR
        (TG_OP = 'DELETE' AND OLD.categoria = 'AQUISICAO_TERRENO_AREA')
    )
    EXECUTE FUNCTION public.sync_custo_terreno_from_despesas();

-- Sincronizar dados existentes (executar uma vez)
DO $$
DECLARE
    obra_record RECORD;
    total_aquisicao NUMERIC(12,2);
BEGIN
    -- Para cada obra, calcular e atualizar o custo do terreno baseado nas despesas existentes
    FOR obra_record IN 
        SELECT DISTINCT id FROM public.obras
    LOOP
        -- Calcular total das despesas de aquisição para esta obra
        SELECT COALESCE(SUM(custo), 0)
        INTO total_aquisicao
        FROM public.despesas
        WHERE obra_id = obra_record.id
        AND categoria = 'AQUISICAO_TERRENO_AREA';
        
        -- Atualizar o campo custo_terreno
        UPDATE public.obras
        SET custo_terreno = total_aquisicao
        WHERE id = obra_record.id;
    END LOOP;
END;
$$;

-- Adicionar política RLS para o novo campo (herda as políticas existentes da tabela obras)
-- Não é necessário criar novas políticas pois o campo faz parte da tabela obras existente

-- Log da migração
INSERT INTO public.migration_log (migration_name, executed_at, description)
VALUES (
    '20250718120001_add_custo_terreno_to_obras',
    NOW(),
    'Adicionado campo custo_terreno à tabela obras com sincronização automática das despesas de aquisição'
) ON CONFLICT (migration_name) DO NOTHING;
