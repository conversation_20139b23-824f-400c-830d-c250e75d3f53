-- =====================================================
-- Migração: Corrigir distinção entre orçamento de 
-- investimento e orçamento paramétrico
-- =====================================================

-- Remover função existente
DROP FUNCTION IF EXISTS delete_condominio_unit(UUID, UUID);

-- Criar função corrigida que NÃO altera o investimento
CREATE OR REPLACE FUNCTION delete_condominio_unit(
  p_unidade_id UUID,
  p_usuario_id UUID
) RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_condominio_id UUID;
  v_total_unidades INTEGER;
  v_area_unidade_excluida NUMERIC;
  v_area_total_condominio NUMERIC;
  v_nova_area_total NUMERIC;
  v_orcamento_parametrico_id UUID;
  v_custo_estimado_original NUMERIC;
  v_custo_m2_original NUMERIC;
  v_novo_custo_estimado NUMERIC;
  v_novo_custo_m2 NUMERIC;
  v_novo_valor_por_unidade NUMERIC;
  v_investimento_original NUMERIC;
  v_result jsonb;
  v_recalculo_realizado BOOLEAN := false;
BEGIN
  -- ✅ 1. VALIDAÇÕES INICIAIS
  -- Validar se a unidade existe e pertence ao usuário
  SELECT parent_obra_id, area_total 
  INTO v_condominio_id, v_area_unidade_excluida
  FROM obras 
  WHERE id = p_unidade_id 
    AND usuario_id = p_usuario_id 
    AND tipo_projeto = 'UNIDADE_CONDOMINIO';
  
  IF v_condominio_id IS NULL THEN
    RAISE EXCEPTION 'Unidade não encontrada ou não pertence ao usuário';
  END IF;
  
  -- Buscar investimento original (NÃO DEVE SER ALTERADO)
  SELECT orcamento INTO v_investimento_original
  FROM obras 
  WHERE id = v_condominio_id;
  
  -- Contar total de unidades antes da exclusão
  SELECT COUNT(*) INTO v_total_unidades
  FROM obras 
  WHERE parent_obra_id = v_condominio_id 
    AND tipo_projeto = 'UNIDADE_CONDOMINIO';
  
  -- Validar se não é a última unidade
  IF v_total_unidades <= 1 THEN
    RAISE EXCEPTION 'Não é possível excluir a última unidade do condomínio';
  END IF;

  -- ✅ 2. BUSCAR DADOS DO ORÇAMENTO PARAMÉTRICO
  SELECT op.id, op.custo_estimado, op.custo_m2, op.area_total
  INTO v_orcamento_parametrico_id, v_custo_estimado_original, v_custo_m2_original, v_area_total_condominio
  FROM orcamentos_parametricos op
  WHERE op.obra_id = v_condominio_id
  ORDER BY op.created_at DESC
  LIMIT 1;

  -- ✅ 3. CALCULAR NOVOS VALORES (APENAS PARAMÉTRICO)
  IF v_orcamento_parametrico_id IS NOT NULL THEN
    -- Calcular nova área total
    v_nova_area_total := v_area_total_condominio - COALESCE(v_area_unidade_excluida, 0);
    
    -- Calcular novo custo estimado (proporcional à área)
    IF v_area_total_condominio > 0 THEN
      v_novo_custo_estimado := v_custo_estimado_original * (v_nova_area_total / v_area_total_condominio);
    ELSE
      v_novo_custo_estimado := v_custo_estimado_original;
    END IF;
    
    -- Calcular novo custo por m²
    IF v_nova_area_total > 0 THEN
      v_novo_custo_m2 := v_novo_custo_estimado / v_nova_area_total;
    ELSE
      v_novo_custo_m2 := v_custo_m2_original;
    END IF;
    
    -- Calcular novo valor por unidade (distribuição igual)
    v_novo_valor_por_unidade := v_novo_custo_estimado / (v_total_unidades - 1);
    
    v_recalculo_realizado := true;
  END IF;

  -- ✅ 4. EXCLUIR A UNIDADE
  DELETE FROM obras WHERE id = p_unidade_id;

  -- ✅ 5. ATUALIZAR ORÇAMENTO PARAMÉTRICO
  IF v_orcamento_parametrico_id IS NOT NULL THEN
    UPDATE orcamentos_parametricos 
    SET 
      custo_estimado = v_novo_custo_estimado,
      custo_m2 = v_novo_custo_m2,
      area_total = v_nova_area_total,
      updated_at = NOW()
    WHERE id = v_orcamento_parametrico_id;
  END IF;

  -- ✅ 6. ATUALIZAR CONDOMÍNIO MASTER (SEM ALTERAR INVESTIMENTO)
  UPDATE obras 
  SET 
    area_total = COALESCE(v_nova_area_total, area_total - COALESCE(v_area_unidade_excluida, 0)),
    numero_unidades = v_total_unidades - 1,
    -- ❌ NÃO ALTERAR: orcamento (investimento permanece inalterado)
    valor_orcamento_parametrico = COALESCE(v_novo_custo_estimado, valor_orcamento_parametrico),
    updated_at = NOW()
  WHERE id = v_condominio_id;

  -- ✅ 7. ATUALIZAR UNIDADES RESTANTES (APENAS PARAMÉTRICO)
  IF v_recalculo_realizado THEN
    UPDATE obras 
    SET 
      -- ❌ NÃO ALTERAR: orcamento (investimento das unidades)
      valor_orcamento_parametrico = v_novo_valor_por_unidade,
      updated_at = NOW()
    WHERE parent_obra_id = v_condominio_id 
      AND tipo_projeto = 'UNIDADE_CONDOMINIO';
  END IF;

  -- ✅ 8. RETORNAR RESULTADO DETALHADO
  SELECT jsonb_build_object(
    'success', true,
    'message', 'Unidade excluída e custo paramétrico recalculado',
    'condominio_id', v_condominio_id,
    'unidades_restantes', v_total_unidades - 1,
    'area_original', v_area_total_condominio,
    'nova_area_total', v_nova_area_total,
    'custo_original', v_custo_estimado_original,
    'novo_custo_estimado', v_novo_custo_estimado,
    'novo_custo_m2', v_novo_custo_m2,
    'novo_valor_por_unidade', v_novo_valor_por_unidade,
    'investimento_preservado', v_investimento_original,
    'margem_disponivel', v_investimento_original - COALESCE(v_novo_custo_estimado, 0),
    'recalculo_realizado', v_recalculo_realizado,
    'orcamento_parametrico_atualizado', v_orcamento_parametrico_id IS NOT NULL
  ) INTO v_result;
  
  RETURN v_result;

EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', SQLERRM,
      'error_detail', SQLSTATE
    );
END;
$$;

-- Comentário da migração
COMMENT ON FUNCTION delete_condominio_unit(UUID, UUID) IS 
'Função corrigida que preserva o orçamento de investimento e altera apenas o orçamento paramétrico.
IMPORTANTE: orcamento = investimento disponível (nunca alterado automaticamente)
valor_orcamento_parametrico = custo estimado da obra (recalculado automaticamente)';
