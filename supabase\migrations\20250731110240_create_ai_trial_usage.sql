-- Migration: Create AI Trial Usage Table
-- Description: <PERSON><PERSON><PERSON> para tracking de uso total durante período de trial
-- Author: <PERSON> (ObrasAI Team)
-- Date: 2025-07-31

-- Criar tabela para tracking de uso total durante o trial
CREATE TABLE IF NOT EXISTS ai_trial_usage (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
  trial_start_date TIMESTAMPTZ NOT NULL,
  trial_end_date TIMESTAMPTZ NOT NULL,
  
  -- Contador total de orçamentos durante todo o trial (não resetado diariamente)
  total_budget_requests INTEGER DEFAULT 0 CHECK (total_budget_requests >= 0),
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  
  -- Constraint para garantir uma linha por usuário
  UNIQUE(user_id)
);

-- Índices para performance
CREATE INDEX idx_ai_trial_usage_user_id ON ai_trial_usage(user_id);
CREATE INDEX idx_ai_trial_usage_subscription_id ON ai_trial_usage(subscription_id);

-- Row Level Security (RLS)
ALTER TABLE ai_trial_usage ENABLE ROW LEVEL SECURITY;

-- Policy: Usuários só podem ver/editar seus próprios dados
CREATE POLICY "Users can view their own trial usage" ON ai_trial_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own trial usage" ON ai_trial_usage
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own trial usage" ON ai_trial_usage
    FOR UPDATE USING (auth.uid() = user_id);

-- Função para atualizar timestamp automaticamente
CREATE OR REPLACE FUNCTION update_ai_trial_usage_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para atualizar updated_at automaticamente
CREATE TRIGGER update_ai_trial_usage_updated_at
    BEFORE UPDATE ON ai_trial_usage
    FOR EACH ROW
    EXECUTE FUNCTION update_ai_trial_usage_updated_at();

-- Comentários para documentação
COMMENT ON TABLE ai_trial_usage IS 'Tracking de uso total de funcionalidades durante período de trial';
COMMENT ON COLUMN ai_trial_usage.user_id IS 'ID do usuário em trial';
COMMENT ON COLUMN ai_trial_usage.subscription_id IS 'ID da assinatura trial relacionada';
COMMENT ON COLUMN ai_trial_usage.trial_start_date IS 'Data de início do trial';
COMMENT ON COLUMN ai_trial_usage.trial_end_date IS 'Data de término do trial';
COMMENT ON COLUMN ai_trial_usage.total_budget_requests IS 'Total de orçamentos gerados durante todo o período de trial (máximo 1)';

-- Função para verificar se usuário em trial já usou seu orçamento único
CREATE OR REPLACE FUNCTION check_trial_budget_quota(p_user_id UUID)
RETURNS TABLE (
  can_use BOOLEAN,
  current_usage INTEGER,
  limit_quota INTEGER,
  message TEXT
) AS $$
DECLARE
  v_subscription RECORD;
  v_trial_usage RECORD;
BEGIN
  -- Buscar assinatura do usuário
  SELECT * INTO v_subscription
  FROM subscriptions
  WHERE user_id = p_user_id
    AND status = 'trialing'
  LIMIT 1;
  
  -- Se não está em trial, retornar que pode usar (será verificado por outras regras)
  IF v_subscription IS NULL THEN
    RETURN QUERY
    SELECT 
      TRUE::BOOLEAN as can_use,
      0::INTEGER as current_usage,
      -1::INTEGER as limit_quota,
      'Usuário não está em trial'::TEXT as message;
    RETURN;
  END IF;
  
  -- Buscar uso do trial
  SELECT * INTO v_trial_usage
  FROM ai_trial_usage
  WHERE user_id = p_user_id;
  
  -- Se não tem registro, pode usar
  IF v_trial_usage IS NULL THEN
    RETURN QUERY
    SELECT 
      TRUE::BOOLEAN as can_use,
      0::INTEGER as current_usage,
      1::INTEGER as limit_quota,
      '1 orçamento disponível no trial'::TEXT as message;
    RETURN;
  END IF;
  
  -- Verificar se já usou o orçamento único
  IF v_trial_usage.total_budget_requests >= 1 THEN
    RETURN QUERY
    SELECT 
      FALSE::BOOLEAN as can_use,
      v_trial_usage.total_budget_requests::INTEGER as current_usage,
      1::INTEGER as limit_quota,
      'Você já usou seu 1 orçamento do trial. Faça upgrade para orçamentos ilimitados.'::TEXT as message;
  ELSE
    RETURN QUERY
    SELECT 
      TRUE::BOOLEAN as can_use,
      v_trial_usage.total_budget_requests::INTEGER as current_usage,
      1::INTEGER as limit_quota,
      '1 orçamento disponível no trial'::TEXT as message;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT SELECT ON ai_trial_usage TO authenticated;
GRANT INSERT, UPDATE ON ai_trial_usage TO authenticated;