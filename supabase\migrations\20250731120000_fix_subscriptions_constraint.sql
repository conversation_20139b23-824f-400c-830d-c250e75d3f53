-- Migration: Fix Subscriptions Table Constraint
-- Description: Remove problematic UNIQUE constraint and add proper constraint
-- Author: <PERSON> (ObrasAI Team)
-- Date: 2025-07-31

-- Remove a constraint problemática
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS subscriptions_user_id_status_key;

-- Adicionar constraint mais específica: apenas uma assinatura ativa por usuário
-- Permite múltiplas assinaturas canceladas/expiradas
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriptions_user_active
ON subscriptions (user_id) 
WHERE status IN ('active', 'trialing');

-- Comentário explicativo
COMMENT ON INDEX idx_subscriptions_user_active IS 'Garante que cada usuário tenha apenas uma assinatura ativa ou em trial por vez';
