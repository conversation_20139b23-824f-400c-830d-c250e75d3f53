-- Migration: Fix handle_new_user Function
-- Description: Corrige a função handle_new_user para incluir tenant_id
-- Author: <PERSON> (ObrasAI Team)
-- Date: 2025-07-31

-- Corrigir a função handle_new_user para incluir tenant_id
CREATE OR REPLACE FUNCTION "public"."handle_new_user"() 
R<PERSON><PERSON><PERSON> "trigger"
LANGUAGE "plpgsql" 
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, tenant_id)
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data->>'first_name', NEW.raw_user_meta_data->>'given_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', NEW.raw_user_meta_data->>'family_name', ''),
    gen_random_uuid() -- Gerar tenant_id único para cada novo usuário
  );
  RETURN NEW;
END;
$$;

-- Coment<PERSON><PERSON> explicativo
COMMENT ON FUNCTION "public"."handle_new_user"() IS 'Cria perfil para novos usuários com tenant_id único';
