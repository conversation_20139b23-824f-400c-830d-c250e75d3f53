// Script para fazer deploy da função sinapi-semantic-search via API
const fs = require('fs');
const FormData = require('form-data');
const fetch = require('node-fetch');

async function deployFunction() {
  try {
    // Ler o código da função
    const functionCode = fs.readFileSync('supabase/functions/sinapi-semantic-search/index.ts', 'utf8');
    
    // Criar FormData
    const form = new FormData();
    
    // Adicionar metadata
    const metadata = {
      entrypoint_path: "index.ts",
      name: "sinapi-semantic-search",
      verify_jwt: true
    };
    
    form.append('metadata', JSON.stringify(metadata));
    form.append('file', functionCode, {
      filename: 'index.ts',
      contentType: 'text/typescript'
    });
    
    // Fazer o deploy
    const response = await fetch(
      'https://api.supabase.com/v1/projects/anrphijuostbgbscxmzx/functions/deploy?slug=sinapi-semantic-search',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.SUPABASE_ACCESS_TOKEN}`,
          ...form.getHeaders()
        },
        body: form
      }
    );
    
    const result = await response.json();
    console.log('Deploy result:', result);
    
    if (response.ok) {
      console.log('✅ sinapi-semantic-search deployada com sucesso!');
    } else {
      console.error('❌ Erro no deploy:', result);
    }
    
  } catch (error) {
    console.error('❌ Erro:', error);
  }
}

deployFunction();
