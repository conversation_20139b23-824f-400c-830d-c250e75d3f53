#!/usr/bin/env node

/**
 * Script de testes automatizados para ai-chat-handler-v2
 * Executa testes completos antes de reportar sucesso
 */

import https from 'https';

const SUPABASE_URL = 'https://anrphijuostbgbscxmzx.supabase.co';
const FUNCTION_URL = `${SUPABASE_URL}/functions/v1/ai-chat-handler-v2`;

// Token de teste (substitua por um token válido)
const TEST_TOKEN = process.env.SUPABASE_AUTH_TOKEN || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJhdXRoZW50aWNhdGVkIiwiZXhwIjoxNzM2ODc5NzU5LCJpYXQiOjE3MzY4NzYxNTksImlzcyI6Imh0dHBzOi8vYW5ycGhpanVvc3RiZ2JzY3htenguzdXBhYmFzZS5jbyIsInN1YiI6IjQyZGY4ZGY4LTNkNzMtNGY4Zi1hZjY5LWY5YjU5ZjU5ZjU5ZiIsImVtYWlsIjoidGVzdEBleGFtcGxlLmNvbSIsInBob25lIjoiIiwiYXBwX21ldGFkYXRhIjp7InByb3ZpZGVyIjoiZW1haWwiLCJwcm92aWRlcnMiOlsiZW1haWwiXX0sInVzZXJfbWV0YWRhdGEiOnt9LCJyb2xlIjoiYXV0aGVudGljYXRlZCIsImFhbCI6ImFhbDEiLCJhbXIiOlt7Im1ldGhvZCI6InBhc3N3b3JkIiwidGltZXN0YW1wIjoxNzM2ODc2MTU5fV0sInNlc3Npb25faWQiOiI5YzQyZGY4ZC0zZDczLTRmOGYtYWY2OS1mOWI1OWY1OWY1OWYifQ.fake_signature_for_testing';

const tests = [
  {
    name: 'Teste 1: Requisição válida básica',
    data: {
      message: 'Olá, como calcular fundação?',
      user_id: '42df8df8-3d73-4f8f-af69-f9b59f59f59f',
      context: 'geral'
    },
    expectedStatus: 200
  },
  {
    name: 'Teste 2: Requisição com obra_id',
    data: {
      message: 'Qual o status desta obra?',
      user_id: '42df8df8-3d73-4f8f-af69-f9b59f59f59f',
      obra_id: '123e4567-e89b-12d3-a456-426614174000',
      context: 'obra_detalhes'
    },
    expectedStatus: 200
  },
  {
    name: 'Teste 3: Requisição sem mensagem (deve falhar)',
    data: {
      user_id: '42df8df8-3d73-4f8f-af69-f9b59f59f59f'
    },
    expectedStatus: 400
  },
  {
    name: 'Teste 4: Requisição sem user_id (deve falhar)',
    data: {
      message: 'Teste sem user_id'
    },
    expectedStatus: 400
  },
  {
    name: 'Teste 5: Requisição com SINAPI',
    data: {
      message: 'Preciso de preços SINAPI para concreto',
      user_id: '42df8df8-3d73-4f8f-af69-f9b59f59f59f',
      incluir_sinapi: true,
      context: 'orcamento'
    },
    expectedStatus: 200
  }
];

function makeRequest(testData, token) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(testData);
    
    const options = {
      hostname: 'anrphijuostbgbscxmzx.supabase.co',
      port: 443,
      path: '/functions/v1/ai-chat-handler-v2',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData,
            headers: res.headers
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data,
            headers: res.headers
          });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    req.write(postData);
    req.end();
  });
}

async function runTests() {
  console.log('🧪 Iniciando testes automatizados da ai-chat-handler-v2...\n');
  
  let passedTests = 0;
  let failedTests = 0;
  
  for (const test of tests) {
    try {
      console.log(`⏳ Executando: ${test.name}`);
      
      const result = await makeRequest(test.data, TEST_TOKEN);
      
      if (result.status === test.expectedStatus) {
        console.log(`✅ PASSOU: Status ${result.status} (esperado: ${test.expectedStatus})`);
        
        if (result.status === 200 && result.data.resposta_bot) {
          console.log(`   📝 Resposta: ${result.data.resposta_bot.substring(0, 100)}...`);
        }
        
        passedTests++;
      } else {
        console.log(`❌ FALHOU: Status ${result.status} (esperado: ${test.expectedStatus})`);
        console.log(`   📄 Resposta: ${JSON.stringify(result.data, null, 2)}`);
        failedTests++;
      }
      
    } catch (error) {
      console.log(`❌ ERRO: ${error.message}`);
      failedTests++;
    }
    
    console.log(''); // Linha em branco
  }
  
  console.log('📊 RESULTADO DOS TESTES:');
  console.log(`✅ Passou: ${passedTests}`);
  console.log(`❌ Falhou: ${failedTests}`);
  console.log(`📈 Taxa de sucesso: ${((passedTests / tests.length) * 100).toFixed(1)}%`);
  
  if (failedTests === 0) {
    console.log('\n🎉 TODOS OS TESTES PASSARAM! Sistema pronto para uso.');
    process.exit(0);
  } else {
    console.log('\n⚠️ Alguns testes falharam. Verifique os logs acima.');
    process.exit(1);
  }
}

// Executar testes
runTests().catch(console.error);
