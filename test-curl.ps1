# Teste PowerShell para ai-chat-handler-v2
$url = "https://anrphijuostbgbscxmzx.supabase.co/functions/v1/ai-chat-handler-v2"
$token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJhdXRoZW50aWNhdGVkIiwiZXhwIjoxNzM2ODc5NzU5LCJpYXQiOjE3MzY4NzYxNTksImlzcyI6Imh0dHBzOi8vYW5ycGhpanVvc3RiZ2JzY3htengusupabase.co","sub":"42df8df8-3d73-4f8f-af69-f9b59f59f59f","email":"<EMAIL>"}"

$body = @{
    message = "Teste de funcionamento"
    user_id = "42df8df8-3d73-4f8f-af69-f9b59f59f59f"
    context = "geral"
} | ConvertTo-Json

$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer $token"
}

Write-Host "🧪 Testando ai-chat-handler-v2..." -ForegroundColor Yellow
Write-Host "📤 URL: $url" -ForegroundColor Cyan
Write-Host "📤 Body: $body" -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri $url -Method POST -Body $body -Headers $headers -ErrorAction Stop
    Write-Host "✅ SUCESSO!" -ForegroundColor Green
    Write-Host "📥 Resposta:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "❌ ERRO:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "📥 Resposta de erro: $responseBody" -ForegroundColor Red
    }
}
