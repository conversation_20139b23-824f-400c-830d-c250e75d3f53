#!/usr/bin/env node

/**
 * Script de teste para o sistema RAG do chat ObrasAI
 * 
 * Este script testa:
 * 1. Chat sem obra específica (genérico)
 * 2. Chat com obra específica (RAG ativado)
 * 3. Geração de embeddings para uma obra
 */

const SUPABASE_URL = 'https://anrphijuostbgbscxmzx.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFucnBoaWp1b3N0Ymdic2N4bXp4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTk2NzgwMzksImV4cCI6MjAzNTI1NDAzOX0.lqbXYs8e_v4UOxZFClaiEaAF0K0YBe8FKAqBjKNlwrs';

// ⚠️ SUBSTITUA ESTES VALORES PELOS SEUS DADOS REAIS
const AUTH_TOKEN = 'SEU_TOKEN_AQUI'; // Pegar do localStorage ou fazer login
const USER_ID = 'SEU_USER_ID_AQUI'; // Pegar do auth do usuário
const OBRA_ID_TESTE = 'SEU_OBRA_ID_AQUI'; // ID de uma obra existente

async function testeChatGenerico() {
  console.log('\n🤖 === TESTE 1: Chat Genérico (sem obra) ===');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat-handler-v2`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json',
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        message: 'Como calcular o volume de concreto para uma laje?',
        user_id: USER_ID,
        obra_id: null // Chat genérico
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${await response.text()}`);
    }

    const data = await response.json();
    console.log('✅ Resposta recebida:', {
      sucesso: !!data.result,
      tamanho_resposta: data.result?.resposta_bot?.length || 0,
      tipo: 'genérico'
    });
    
    if (data.result?.resposta_bot) {
      console.log('📝 Resposta (primeiros 200 chars):', data.result.resposta_bot.substring(0, 200) + '...');
    }
    
  } catch (error) {
    console.error('❌ Erro no teste genérico:', error.message);
  }
}

async function testeChatComRAG() {
  console.log('\n🔍 === TESTE 2: Chat com RAG (obra específica) ===');
  
  if (!OBRA_ID_TESTE || OBRA_ID_TESTE === 'SEU_OBRA_ID_AQUI') {
    console.log('⚠️ OBRA_ID_TESTE não configurado. Pulando teste RAG.');
    return;
  }
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat-handler-v2`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json',
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        message: 'Qual o valor total gasto nesta obra até agora?',
        user_id: USER_ID,
        obra_id: OBRA_ID_TESTE // Chat com RAG
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${await response.text()}`);
    }

    const data = await response.json();
    console.log('✅ Resposta com RAG recebida:', {
      sucesso: !!data.result,
      tamanho_resposta: data.result?.resposta_bot?.length || 0,
      tipo: 'RAG',
      obra_id: OBRA_ID_TESTE
    });
    
    if (data.result?.resposta_bot) {
      console.log('📝 Resposta (primeiros 300 chars):', data.result.resposta_bot.substring(0, 300) + '...');
      
      // Verificar se a resposta contém dados específicos da obra
      const contemDadosEspecificos = data.result.resposta_bot.includes('R$') || 
                                   data.result.resposta_bot.includes('obra') ||
                                   data.result.resposta_bot.includes('despesa') ||
                                   data.result.resposta_bot.includes('gasto');
                                   
      console.log('🎯 Contém dados específicos:', contemDadosEspecificos ? '✅ SIM' : '❌ NÃO');
    }
    
  } catch (error) {
    console.error('❌ Erro no teste RAG:', error.message);
  }
}

async function testeGerarEmbeddings() {
  console.log('\n⚡ === TESTE 3: Gerar Embeddings ===');
  
  if (!OBRA_ID_TESTE || OBRA_ID_TESTE === 'SEU_OBRA_ID_AQUI') {
    console.log('⚠️ OBRA_ID_TESTE não configurado. Pulando teste de embeddings.');
    return;
  }
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/gerar-embeddings-obra`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json',
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        obra_id: OBRA_ID_TESTE,
        tipo_conteudo: 'todos' // Gerar embeddings para todos os tipos
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${await response.text()}`);
    }

    const data = await response.json();
    console.log('✅ Embeddings gerados:', {
      sucesso: data.sucesso,
      quantidade: data.embeddings_gerados,
      tipos: data.tipos_processados
    });
    
  } catch (error) {
    console.error('❌ Erro ao gerar embeddings:', error.message);
  }
}

async function verificarConfiguracao() {
  console.log('🔍 === VERIFICAÇÃO DE CONFIGURAÇÃO ===');
  
  const problemas = [];
  
  if (!AUTH_TOKEN || AUTH_TOKEN === 'SEU_TOKEN_AQUI') {
    problemas.push('AUTH_TOKEN não configurado');
  }
  
  if (!USER_ID || USER_ID === 'SEU_USER_ID_AQUI') {
    problemas.push('USER_ID não configurado');
  }
  
  if (!OBRA_ID_TESTE || OBRA_ID_TESTE === 'SEU_OBRA_ID_AQUI') {
    problemas.push('OBRA_ID_TESTE não configurado (testes RAG serão pulados)');
  }
  
  if (problemas.length > 0) {
    console.log('⚠️ Problemas encontrados:');
    problemas.forEach(p => console.log(`   - ${p}`));
    console.log('\n📋 Para configurar:');
    console.log('1. Faça login no sistema ObrasAI');
    console.log('2. Abra o DevTools do navegador (F12)');
    console.log('3. Execute: localStorage.getItem("supabase.auth.token")');
    console.log('4. Execute: JSON.parse(localStorage.getItem("supabase.auth.token")).user.id');
    console.log('5. Pegue o ID de uma obra existente na tabela obras');
    console.log('6. Substitua os valores no topo deste script\n');
  } else {
    console.log('✅ Configuração OK!');
  }
  
  return problemas.length === 0;
}

async function main() {
  console.log('🚀 === TESTE DO SISTEMA RAG - OBRASAI ===');
  
  const configOK = await verificarConfiguracao();
  
  if (!configOK) {
    console.log('\n❌ Configure as variáveis antes de executar os testes.');
    return;
  }
  
  await testeChatGenerico();
  await testeChatComRAG();
  await testeGerarEmbeddings();
  
  console.log('\n✅ === TESTES CONCLUÍDOS ===');
  console.log('\n📊 Resultados esperados:');
  console.log('- Chat genérico: Resposta padrão sobre construção civil');
  console.log('- Chat com RAG: Resposta específica com dados da obra');
  console.log('- Embeddings: Confirmação de geração bem-sucedida');
}

// Executar testes
main().catch(console.error);