// Teste simples para verificar se a função está funcionando
const testUrl = 'https://anrphijuostbgbscxmzx.supabase.co/functions/v1/ai-chat-handler-v2';

// Token de teste válido (substitua por um token real se necessário)
const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************.co","sub":"42df8df8-3d73-4f8f-af69-f9b59f59f59f","email":"<EMAIL>","phone":"","app_metadata":{"provider":"email","providers":["email"]},"user_metadata":{},"role":"authenticated","aal":"aal1","amr":[{"method":"password","timestamp":**********}],"session_id":"9c42df8d-3d73-4f8f-af69-f9b59f59f59f"}';

const testData = {
  message: 'Olá, como calcular fundação?',
  user_id: '42df8df8-3d73-4f8f-af69-f9b59f59f59f',
  context: 'geral'
};

async function testFunction() {
  try {
    console.log('🧪 Testando ai-chat-handler-v2...');
    console.log('📤 Enviando:', testData);
    
    const response = await fetch(testUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${testToken}`
      },
      body: JSON.stringify(testData)
    });
    
    console.log('📊 Status:', response.status);
    console.log('📋 Headers:', Object.fromEntries(response.headers.entries()));
    
    const result = await response.text();
    console.log('📥 Resposta bruta:', result);
    
    try {
      const jsonResult = JSON.parse(result);
      console.log('✅ Resposta JSON:', jsonResult);
      
      if (response.ok && jsonResult.result) {
        console.log('🎉 TESTE PASSOU! Função funcionando corretamente.');
        console.log('🤖 Resposta da IA:', jsonResult.result.resposta_bot);
      } else {
        console.log('❌ TESTE FALHOU! Erro na resposta.');
      }
    } catch (e) {
      console.log('❌ Erro ao parsear JSON:', e.message);
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  }
}

testFunction();
