#!/usr/bin/env node

/**
 * Teste rápido do chat - versão simplificada
 */

const SUPABASE_URL = 'https://anrphijuostbgbscxmzx.supabase.co';

async function testarChat(pergunta, obraId = null) {
  console.log(`\\n🔍 Testando: "${pergunta}"`);
  console.log(`📍 Obra ID: ${obraId || 'Nenhuma (chat genérico)'}`);
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat-handler-v2`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message: pergunta,
        user_id: 'test-user',
        obra_id: obraId
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ HTTP Error:', response.status, errorText);
      return;
    }
    
    const data = await response.json();
    console.log('📄 Resposta completa:', JSON.stringify(data, null, 2));
    
    if (data.result) {
      console.log('✅ Resposta recebida');
      console.log('📊 Metadados:', {
        tokens: data.result.tokens_utilizados,
        tempo: data.result.tempo_resposta + 's',
        rag_usado: data.result.rag_usado || false,
        contexto_tamanho: data.result.contexto_tamanho || 0
      });
      
      // Mostrar parte da resposta
      const resposta = data.result.resposta_bot || '';
      console.log('💬 Resposta (200 chars):', resposta.substring(0, 200) + '...');
      
      // Verificar se parece usar dados específicos
      const contemValores = resposta.includes('R$') || resposta.includes('real') || resposta.includes('reais');
      const contemEspecifico = resposta.includes('obra') || resposta.includes('Esplanada') || resposta.includes('FRITSCHE');
      
      console.log('🎯 Análise:', {
        contem_valores: contemValores,
        contem_dados_especificos: contemEspecifico,
        parece_rag: contemValores && contemEspecifico
      });
      
    } else {
      console.log('❌ Erro:', data.error);
    }
    
  } catch (error) {
    console.error('❌ Erro na requisição:', error.message);
  }
}

async function main() {
  console.log('🚀 === TESTE RÁPIDO DO CHAT RAG ===');
  
  // Teste 1: Chat genérico
  await testarChat('Como calcular volume de concreto?');
  
  // Teste 2: Chat com obra específica (Esplanada)
  await testarChat('Qual o valor total gasto nesta obra?', 'c34e85ec-6bde-45ad-9ce1-8f66370fdfd8');
  
  // Teste 3: Chat com obra específica (FRITSCHE)
  await testarChat('Quantas despesas tem esta obra?', '53a0e632-92ce-4e50-a568-7e2d61081a86');
  
  console.log('\\n📋 === RESULTADOS ===');
  console.log('✅ Se RAG está funcionando: resposta específica com valores R$');
  console.log('❌ Se RAG não está funcionando: resposta genérica sem dados específicos');
  console.log('\\n💡 Próximos passos:');
  console.log('1. Execute: node gerar-embeddings-obras.js (configure credenciais primeiro)');
  console.log('2. Teste novamente após gerar os embeddings');
}

main().catch(console.error);