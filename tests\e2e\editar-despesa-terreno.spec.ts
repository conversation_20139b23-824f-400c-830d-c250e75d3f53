import { test, expect } from '@playwright/test';

test.describe('Edição de Despesas de Terreno', () => {
  test.beforeEach(async ({ page }) => {
    // Navegar para a página de login
    await page.goto('/login');
    
    // Fazer login (assumindo que há credenciais de teste)
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'senha123');
    await page.click('[data-testid="login-button"]');
    
    // Aguardar redirecionamento para dashboard
    await page.waitForURL('/dashboard');
  });

  test('deve mostrar campos específicos para despesas de terreno na edição', async ({ page }) => {
    // Navegar para a lista de despesas
    await page.goto('/dashboard/despesas');
    
    // Aguardar carregamento da lista
    await page.waitForSelector('[data-testid="despesas-list"]');
    
    // Procurar por uma despesa de terreno existente ou criar uma nova
    const despesaTerrenoButton = page.locator('[data-testid="despesa-terreno-edit"]').first();
    
    if (await despesaTerrenoButton.count() === 0) {
      // Se não há despesa de terreno, criar uma nova primeiro
      await page.click('[data-testid="nova-despesa-button"]');
      await page.waitForURL('/dashboard/despesas/nova');
      
      // Preencher dados básicos
      await page.selectOption('[data-testid="obra-select"]', { index: 1 });
      await page.fill('[data-testid="descricao-input"]', 'Terreno para teste automatizado');
      
      // Selecionar categoria de terreno
      await page.selectOption('[data-testid="categoria-select"]', 'AQUISICAO_TERRENO_AREA');
      
      // Verificar se campos específicos aparecem
      await expect(page.locator('[data-testid="area-total-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="preco-por-m2-input"]')).toBeVisible();
      await expect(page.locator('[data-testid="descricao-terreno-input"]')).toBeVisible();
      
      // Preencher campos específicos
      await page.fill('[data-testid="area-total-input"]', '750');
      await page.fill('[data-testid="preco-por-m2-input"]', '347.22');
      await page.fill('[data-testid="descricao-terreno-input"]', 'Terreno residencial no centro');
      
      // Salvar despesa
      await page.click('[data-testid="salvar-despesa-button"]');
      await page.waitForURL('/dashboard/despesas');
      
      // Aguardar toast de sucesso
      await expect(page.locator('.sonner-toast')).toContainText('custo do terreno sincronizado');
    }
    
    // Agora editar a despesa de terreno
    await page.click('[data-testid="despesa-terreno-edit"]');
    await page.waitForURL(/\/dashboard\/despesas\/editar\/.+/);
    
    // Verificar se os campos específicos de terreno estão visíveis
    await expect(page.locator('text=🏞️ Aquisição de Terreno/Área')).toBeVisible();
    await expect(page.locator('text=📐 Área Total')).toBeVisible();
    await expect(page.locator('text=💰 Preço por m²')).toBeVisible();
    await expect(page.locator('text=🏞️ Descrição do Terreno/Área')).toBeVisible();
    
    // Verificar se a unidade está fixada como m²
    await expect(page.locator('[data-testid="unidade-input"]')).toHaveValue('m²');
    await expect(page.locator('[data-testid="unidade-input"]')).toBeDisabled();
  });

  test('deve calcular valor total automaticamente ao editar área e preço', async ({ page }) => {
    // Navegar para edição de despesa de terreno
    await page.goto('/dashboard/despesas');
    await page.click('[data-testid="despesa-terreno-edit"]');
    await page.waitForURL(/\/dashboard\/despesas\/editar\/.+/);
    
    // Limpar e preencher nova área
    await page.fill('[data-testid="area-total-input"]', '800');
    
    // Limpar e preencher novo preço por m²
    await page.fill('[data-testid="preco-por-m2-input"]', '400.00');
    
    // Verificar se o valor total foi calculado automaticamente (800 × 400 = 320.000)
    await expect(page.locator('[data-testid="valor-total-display"]')).toContainText('R$ 320.000,00');
    
    // Verificar se o preview também foi atualizado
    await page.click('[data-testid="visualizar-button"]');
    await expect(page.locator('[data-testid="preview-valor-total"]')).toContainText('R$ 320.000,00');
  });

  test('deve salvar edição e sincronizar custo do terreno automaticamente', async ({ page }) => {
    // Navegar para edição de despesa de terreno
    await page.goto('/dashboard/despesas');
    await page.click('[data-testid="despesa-terreno-edit"]');
    await page.waitForURL(/\/dashboard\/despesas\/editar\/.+/);
    
    // Alterar valores
    await page.fill('[data-testid="area-total-input"]', '850');
    await page.fill('[data-testid="preco-por-m2-input"]', '380.50');
    
    // Salvar alterações
    await page.click('[data-testid="salvar-edicao-button"]');
    
    // Verificar toast de sucesso com sincronização
    await expect(page.locator('.sonner-toast')).toContainText('custo do terreno sincronizado automaticamente');
    
    // Verificar redirecionamento para lista
    await page.waitForURL('/dashboard/despesas');
    
    // Verificar se os valores foram salvos corretamente
    await page.click('[data-testid="despesa-terreno-edit"]');
    await expect(page.locator('[data-testid="area-total-input"]')).toHaveValue('850');
    await expect(page.locator('[data-testid="preco-por-m2-input"]')).toHaveValue('380.5');
  });

  test('deve mostrar campos padrão para outras categorias', async ({ page }) => {
    // Navegar para edição de despesa de terreno
    await page.goto('/dashboard/despesas');
    await page.click('[data-testid="despesa-terreno-edit"]');
    await page.waitForURL(/\/dashboard\/despesas\/editar\/.+/);
    
    // Alterar categoria para uma que não seja terreno
    await page.selectOption('[data-testid="categoria-select"]', 'MATERIAL_CONSTRUCAO');
    
    // Verificar se campos específicos de terreno desapareceram
    await expect(page.locator('text=🏞️ Aquisição de Terreno/Área')).not.toBeVisible();
    await expect(page.locator('text=📐 Área Total')).not.toBeVisible();
    
    // Verificar se campos padrão aparecem
    await expect(page.locator('[data-testid="quantidade-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="valor-unitario-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="unidade-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="unidade-select"]')).not.toBeDisabled();
  });

  test('deve validar campos obrigatórios para despesas de terreno', async ({ page }) => {
    // Navegar para edição de despesa de terreno
    await page.goto('/dashboard/despesas');
    await page.click('[data-testid="despesa-terreno-edit"]');
    await page.waitForURL(/\/dashboard\/despesas\/editar\/.+/);
    
    // Limpar campos obrigatórios
    await page.fill('[data-testid="area-total-input"]', '');
    await page.fill('[data-testid="preco-por-m2-input"]', '');
    
    // Tentar salvar
    await page.click('[data-testid="salvar-edicao-button"]');
    
    // Verificar mensagens de validação
    await expect(page.locator('text=Campo obrigatório')).toBeVisible();
    
    // Verificar que não houve redirecionamento
    await expect(page).toHaveURL(/\/dashboard\/despesas\/editar\/.+/);
  });
});
