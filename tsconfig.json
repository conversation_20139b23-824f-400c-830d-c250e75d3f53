{"files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "strict": true, "noImplicitAny": true, "noUnusedParameters": true, "skipLibCheck": true, "allowJs": false, "noUnusedLocals": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitReturns": true, "noImplicitThis": true, "alwaysStrict": true}}